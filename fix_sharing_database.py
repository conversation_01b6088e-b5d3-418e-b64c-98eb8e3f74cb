#!/usr/bin/env python3
"""
Script to fix the shared_content_access table schema
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from sqlalchemy import text
from app.db.session import engine

def fix_shared_content_access_table():
    """Fix the shared_content_access table schema"""
    
    print("🔧 Fixing shared_content_access table schema...")
    
    with engine.connect() as connection:
        # Start a transaction
        trans = connection.begin()
        
        try:
            # Check if the table exists and what columns it has
            result = connection.execute(text("""
                SELECT column_name, data_type 
                FROM information_schema.columns 
                WHERE table_name = 'shared_content_access'
                ORDER BY ordinal_position;
            """))
            
            columns = result.fetchall()
            print(f"Current columns: {[col[0] for col in columns]}")
            
            # Check if accessed_by_id exists
            has_accessed_by_id = any(col[0] == 'accessed_by_id' for col in columns)
            has_user_id = any(col[0] == 'user_id' for col in columns)
            
            if has_accessed_by_id and not has_user_id:
                print("✅ Renaming accessed_by_id to user_id...")
                
                # Drop the old index first
                try:
                    connection.execute(text("DROP INDEX IF EXISTS ix_shared_content_access_accessed_by_id;"))
                    print("✅ Dropped old index")
                except Exception as e:
                    print(f"⚠️ Could not drop old index: {e}")
                
                # Rename the column
                connection.execute(text("ALTER TABLE shared_content_access RENAME COLUMN accessed_by_id TO user_id;"))
                print("✅ Renamed accessed_by_id to user_id")
                
                # Create new index
                connection.execute(text("CREATE INDEX ix_shared_content_access_user_id ON shared_content_access (user_id);"))
                print("✅ Created new index")
                
            elif has_user_id:
                print("✅ Column user_id already exists")
            else:
                print("❌ Neither accessed_by_id nor user_id found")
                return False
            
            # Check if access_type exists and rename to access_method
            has_access_type = any(col[0] == 'access_type' for col in columns)
            has_access_method = any(col[0] == 'access_method' for col in columns)
            
            if has_access_type and not has_access_method:
                print("✅ Renaming access_type to access_method...")
                connection.execute(text("ALTER TABLE shared_content_access RENAME COLUMN access_type TO access_method;"))
                print("✅ Renamed access_type to access_method")
            elif has_access_method:
                print("✅ Column access_method already exists")
            
            # Remove unnecessary columns if they exist
            for col_to_remove in ['ip_address', 'user_agent']:
                if any(col[0] == col_to_remove for col in columns):
                    print(f"✅ Removing column {col_to_remove}...")
                    connection.execute(text(f"ALTER TABLE shared_content_access DROP COLUMN IF EXISTS {col_to_remove};"))
                    print(f"✅ Removed column {col_to_remove}")
            
            # Add unique constraint if it doesn't exist
            try:
                connection.execute(text("""
                    ALTER TABLE shared_content_access 
                    ADD CONSTRAINT unique_access_per_user 
                    UNIQUE (shared_content_id, user_id);
                """))
                print("✅ Added unique constraint")
            except Exception as e:
                if "already exists" in str(e):
                    print("✅ Unique constraint already exists")
                else:
                    print(f"⚠️ Could not add unique constraint: {e}")
            
            # Commit the transaction
            trans.commit()
            print("✅ All changes committed successfully!")
            return True
            
        except Exception as e:
            # Rollback on error
            trans.rollback()
            print(f"❌ Error fixing table schema: {e}")
            return False

def verify_schema():
    """Verify the schema is correct"""
    print("\n🔍 Verifying schema...")
    
    with engine.connect() as connection:
        # Check final schema
        result = connection.execute(text("""
            SELECT column_name, data_type, is_nullable
            FROM information_schema.columns 
            WHERE table_name = 'shared_content_access'
            ORDER BY ordinal_position;
        """))
        
        columns = result.fetchall()
        print("Final schema:")
        for col in columns:
            print(f"  - {col[0]} ({col[1]}) {'NULL' if col[2] == 'YES' else 'NOT NULL'}")
        
        # Check constraints
        result = connection.execute(text("""
            SELECT constraint_name, constraint_type
            FROM information_schema.table_constraints
            WHERE table_name = 'shared_content_access';
        """))
        
        constraints = result.fetchall()
        print("Constraints:")
        for constraint in constraints:
            print(f"  - {constraint[0]} ({constraint[1]})")

if __name__ == "__main__":
    print("🚀 Fixing Shared Content Access Database Schema")
    print("=" * 60)
    
    success = fix_shared_content_access_table()
    
    if success:
        verify_schema()
        print("\n" + "=" * 60)
        print("✅ Database schema fix completed successfully!")
        print("The sharing functionality should now work properly.")
    else:
        print("\n" + "=" * 60)
        print("❌ Database schema fix failed!")
        print("Please check the error messages above.")
