# Render.com deployment configuration
services:
  # Backend API Service
  - type: web
    name: campuspq-api
    env: docker
    dockerfilePath: ./backend/Dockerfile.free
    plan: free
    region: oregon
    branch: main
    healthCheckPath: /api/v1/health
    envVars:
      - key: PORT
        value: 10000
      - key: ENVIRONMENT
        value: production
      - key: DEBUG
        value: false
      - key: API_V1_PREFIX
        value: /api/v1
      - key: PROJECT_NAME
        value: CampusPQ
      
      # Database (your Supabase instance)
      - key: DATABASE_URL
        value: postgresql://postgres:GKNvV4kL28Li!<EMAIL>:5432/postgres
      
      # Redis Cloud (free tier)
      - key: REDIS_URL
        fromService:
          type: redis
          name: campuspq-redis
          property: connectionString
      
      # Vector Database (you'll need to deploy Qdrant separately)
      - key: QDRANT_URL
        value: https://your-qdrant-instance.com:6333
      - key: QDRANT_COLLECTION
        value: campuspq_embeddings
      
      # Application secrets (set these in Render dashboard)
      - key: SECRET_KEY
        sync: false
      - key: OPENAI_API_KEY
        sync: false
      - key: GEMINI_API_KEY
        sync: false
      - key: AI_PROVIDER
        value: gemini
      
      # File storage (Cloudinary free tier)
      - key: CLOUDINARY_URL
        sync: false
      - key: CLOUDINARY_API_KEY
        sync: false
      - key: CLOUDINARY_API_SECRET
        sync: false
      
      # Email (free SMTP)
      - key: SMTP_USER
        sync: false
      - key: SMTP_PASSWORD
        sync: false
      - key: EMAILS_FROM_EMAIL
        value: <EMAIL>
      
      # Frontend URL (Vercel)
      - key: FRONTEND_URL
        value: https://campuspq.vercel.app
      
      # CORS settings
      - key: BACKEND_CORS_ORIGINS
        value: '["https://campuspq.vercel.app", "http://localhost:3000", "http://localhost:5173"]'

  # Background Worker Service
  - type: worker
    name: campuspq-worker
    env: docker
    dockerfilePath: ./backend/Dockerfile.worker.free
    plan: free
    region: oregon
    branch: main
    envVars:
      # Same environment variables as the API service
      - key: DATABASE_URL
        value: postgresql://postgres:GKNvV4kL28Li!<EMAIL>:5432/postgres
      - key: REDIS_URL
        fromService:
          type: redis
          name: campuspq-redis
          property: connectionString
      - key: QDRANT_URL
        value: https://your-qdrant-instance.com:6333
      - key: QDRANT_COLLECTION
        value: campuspq_embeddings
      - key: SECRET_KEY
        sync: false
      - key: OPENAI_API_KEY
        sync: false
      - key: GEMINI_API_KEY
        sync: false
      - key: AI_PROVIDER
        value: gemini
      - key: CLOUDINARY_URL
        sync: false
      - key: CLOUDINARY_API_KEY
        sync: false
      - key: CLOUDINARY_API_SECRET
        sync: false

# Redis service (free tier)
databases:
  - name: campuspq-redis
    plan: free
    region: oregon
