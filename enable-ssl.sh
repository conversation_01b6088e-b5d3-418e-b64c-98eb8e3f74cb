#!/bin/bash

# Smart SSL enabler for CampusPQ API
# Only enables SSL if certificates exist, otherwise runs HTTP only

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${GREEN}[✓]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[!]${NC} $1"
}

print_error() {
    echo -e "${RED}[✗]${NC} $1"
}

print_info() {
    echo -e "${BLUE}[i]${NC} $1"
}

# Configuration
DOMAIN="api.campuspq.com"
CERT_PATH="/etc/letsencrypt/live/$DOMAIN"
NGINX_CONFIG="/home/<USER>/campuspq-deployment/nginx.conf"

# Check if SSL certificates exist
check_ssl_certificates() {
    if [ -f "$CERT_PATH/fullchain.pem" ] && [ -f "$CERT_PATH/privkey.pem" ]; then
        print_status "SSL certificates found for $DOMAIN"
        return 0
    else
        print_warning "SSL certificates not found for $DOMAIN"
        return 1
    fi
}

# Enable SSL in nginx config
enable_ssl_config() {
    print_info "Enabling SSL configuration in nginx.conf..."
    
    # Uncomment the SSL server block
    sed -i 's/^    # server {/    server {/' $NGINX_CONFIG
    sed -i 's/^    #     /    /' $NGINX_CONFIG
    sed -i 's/^    # }/    }/' $NGINX_CONFIG
    
    print_status "SSL configuration enabled"
}

# Generate SSL certificates
generate_ssl_certificates() {
    print_info "Generating SSL certificates for $DOMAIN..."
    
    # Stop nginx temporarily
    cd /home/<USER>/campuspq-deployment
    docker-compose stop nginx
    
    # Generate certificate
    sudo certbot certonly \
        --standalone \
        --email <EMAIL> \
        --agree-tos \
        --no-eff-email \
        -d $DOMAIN
    
    if [ $? -eq 0 ]; then
        print_status "SSL certificates generated successfully"
        return 0
    else
        print_error "Failed to generate SSL certificates"
        return 1
    fi
}

# Update docker-compose for SSL
update_docker_compose_ssl() {
    print_info "Updating docker-compose for SSL support..."
    
    cd /home/<USER>/campuspq-deployment
    
    # Check if SSL mount already exists
    if grep -q "/etc/letsencrypt:/etc/letsencrypt:ro" docker-compose.yml; then
        print_status "SSL mount already configured in docker-compose.yml"
    else
        # Add SSL certificate mount to nginx service
        sed -i '/- \.\/nginx\.conf:\/etc\/nginx\/nginx\.conf:ro/a\      - /etc/letsencrypt:/etc/letsencrypt:ro' docker-compose.yml
        print_status "Added SSL certificate mount to docker-compose.yml"
    fi
}

# Restart services
restart_services() {
    print_info "Restarting services..."
    
    cd /home/<USER>/campuspq-deployment
    docker-compose up -d
    
    # Wait for services to start
    sleep 10
    
    print_status "Services restarted"
}

# Test SSL configuration
test_ssl() {
    print_info "Testing SSL configuration..."
    
    # Test HTTP
    if curl -f http://localhost/health >/dev/null 2>&1; then
        print_status "HTTP is working"
    else
        print_warning "HTTP test failed"
    fi
    
    # Test HTTPS if certificates exist
    if check_ssl_certificates; then
        if curl -f https://$DOMAIN/health >/dev/null 2>&1; then
            print_status "HTTPS is working"
        else
            print_warning "HTTPS test failed"
        fi
    fi
}

# Main function
main() {
    print_info "🔒 CampusPQ Smart SSL Setup"
    print_info "=========================="
    
    # Check if we're running as root
    if [ "$EUID" -ne 0 ]; then
        print_error "This script must be run as root (use sudo)"
        exit 1
    fi
    
    # Check if certificates already exist
    if check_ssl_certificates; then
        print_info "SSL certificates found. Enabling SSL configuration..."
        enable_ssl_config
        update_docker_compose_ssl
        restart_services
        test_ssl
        print_status "✅ SSL is now enabled!"
    else
        print_info "No SSL certificates found. Setting up SSL..."
        
        # Check if domain resolves to this server
        DOMAIN_IP=$(nslookup $DOMAIN | grep -A1 "Name:" | tail -1 | awk '{print $2}' || echo "")
        SERVER_IP=$(curl -s ifconfig.me || echo "")
        
        if [ "$DOMAIN_IP" = "$SERVER_IP" ]; then
            print_status "Domain $DOMAIN resolves to this server ($SERVER_IP)"
            
            # Generate certificates
            if generate_ssl_certificates; then
                enable_ssl_config
                update_docker_compose_ssl
                restart_services
                test_ssl
                print_status "✅ SSL setup completed successfully!"
            else
                print_error "SSL certificate generation failed"
                restart_services
                print_info "Running in HTTP-only mode"
            fi
        else
            print_warning "Domain $DOMAIN does not resolve to this server"
            print_info "Expected: $SERVER_IP, Got: $DOMAIN_IP"
            print_info "Please update your DNS settings and try again"
            print_info "Running in HTTP-only mode for now"
            restart_services
        fi
    fi
    
    echo ""
    print_info "🌐 Your API is available at:"
    if check_ssl_certificates; then
        print_info "  - HTTPS: https://$DOMAIN"
        print_info "  - API Docs: https://$DOMAIN/api/v1/docs"
    fi
    print_info "  - HTTP: http://$SERVER_IP"
    print_info "  - API Docs: http://$SERVER_IP/api/v1/docs"
}

# Run main function
main "$@"
