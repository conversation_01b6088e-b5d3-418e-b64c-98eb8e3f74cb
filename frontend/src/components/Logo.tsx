import React from 'react';
import { Box, SxProps, Theme } from '@mui/material';

interface LogoProps {
  size?: number | string;
  sx?: SxProps<Theme>;
  onClick?: () => void;
}

const Logo: React.FC<LogoProps> = ({ size = 56, sx = {}, onClick }) => {
  const imageSrc = '/android-chrome-192x192.png';

  return (
    <Box
      component="img"
      src={imageSrc}
      alt="CampusPQ Logo"
      onClick={onClick}
      sx={{
        height: size,
        width: 'auto',
        cursor: onClick ? 'pointer' : 'default',
        transition: 'all 0.3s ease-in-out',
        verticalAlign: 'middle',
        display: 'inline-block',
        '&:hover': onClick ? {
          transform: 'scale(1.05)',
        } : {},
        ...sx,
      }}
    />
  );
};

export default Logo;
