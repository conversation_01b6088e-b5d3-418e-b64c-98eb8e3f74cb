import React, { useState } from 'react';
import { 
  Box, 
  Fab, 
  Zoom, 
  useTheme, 
  SpeedDial, 
  SpeedDialIcon, 
  SpeedDialAction,
  useScrollTrigger
} from '@mui/material';
import { 
  Add as AddIcon,
  Close as CloseIcon,
  QuestionAnswer as QuestionIcon,
  Event as EventIcon,
  School as SchoolIcon,
  Quiz as QuizIcon
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';

interface FloatingActionButtonProps {
  showOnScroll?: boolean;
}

const FloatingActionButton: React.FC<FloatingActionButtonProps> = ({ 
  showOnScroll = true 
}) => {
  const theme = useTheme();
  const navigate = useNavigate();
  const { user } = useAuth();
  const [open, setOpen] = useState(false);
  
  // Show button only when scrolling down if showOnScroll is true
  const trigger = useScrollTrigger({
    disableHysteresis: true,
    threshold: 100,
  });
  
  const handleOpen = () => {
    setOpen(true);
  };

  const handleClose = () => {
    setOpen(false);
  };

  // Define actions based on user role
  const actions = [
    { 
      icon: <QuestionIcon />, 
      name: 'Practice Question', 
      action: () => navigate('/questions') 
    },
    { 
      icon: <EventIcon />, 
      name: 'Book Session', 
      action: () => navigate('/sessions/new') 
    }
  ];
  
  // Add student-specific actions
  if (user?.role === 'student') {
    actions.push({ 
      icon: <QuizIcon />, 
      name: 'Take Quiz', 
      action: () => navigate('/mcq') 
    });
  }
  
  // Add tutor-specific actions
  if (user?.role === 'tutor') {
    actions.push({ 
      icon: <SchoolIcon />, 
      name: 'Create Course', 
      action: () => navigate('/courses/new') 
    });
  }

  return (
    <Zoom in={!showOnScroll || trigger}>
      <Box
        sx={{
          position: 'fixed',
          bottom: 80, // Position above bottom navigation
          right: 16,
          zIndex: 1000,
        }}
      >
        <SpeedDial
          ariaLabel="Quick actions"
          icon={<SpeedDialIcon icon={<AddIcon />} openIcon={<CloseIcon />} />}
          onClose={handleClose}
          onOpen={handleOpen}
          open={open}
          direction="up"
          FabProps={{
            sx: {
              bgcolor: theme.palette.primary.main,
              '&:hover': {
                bgcolor: theme.palette.primary.dark,
              },
              boxShadow: '0 4px 20px rgba(0,0,0,0.1)',
            }
          }}
        >
          {actions.map((action) => (
            <SpeedDialAction
              key={action.name}
              icon={action.icon}
              tooltipTitle={action.name}
              tooltipOpen
              onClick={() => {
                action.action();
                handleClose();
              }}
              FabProps={{
                sx: {
                  bgcolor: theme.palette.background.paper,
                  boxShadow: '0 2px 10px rgba(0,0,0,0.1)',
                }
              }}
            />
          ))}
        </SpeedDial>
      </Box>
    </Zoom>
  );
};

export default FloatingActionButton;
