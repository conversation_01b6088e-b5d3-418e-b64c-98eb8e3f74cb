import React, { useState } from 'react';
import { Fab, Drawer, useMediaQuery, useTheme, Zoom, Tooltip, alpha } from '@mui/material';
import AutoAwesomeIcon from '@mui/icons-material/AutoAwesome';
import AIAssistantChat from './AIAssistantChat';

const FloatingAssistantButton: React.FC = () => {
  const [isOpen, setIsOpen] = useState(false);
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  const toggleDrawer = () => {
    setIsOpen(!isOpen);
  };

  return (
    <>
      <Zoom in={true} style={{ transitionDelay: '500ms' }}>
        <Tooltip title="AI Assistant" placement="left" TransitionComponent={Zoom}>
          <Fab
            size="medium"
            color="primary"
            aria-label="AI Assistant"
            sx={{
              position: 'fixed',
              bottom: isMobile ? 88 : 24, // Move up on mobile to avoid bottom nav
              right: 24,
              zIndex: 1000,
              width: 56,
              height: 56,
              background: theme.palette.mode === 'dark'
                ? `linear-gradient(45deg, ${theme.palette.primary.dark}, ${theme.palette.secondary.dark})`
                : `linear-gradient(45deg, ${theme.palette.primary.main}, ${theme.palette.primary.dark})`,
              boxShadow: `0 4px 20px ${alpha(theme.palette.primary.main, 0.5)}`,
              transition: 'all 0.3s ease-in-out',
              '&:hover': {
                transform: 'scale(1.05)',
                boxShadow: `0 6px 25px ${alpha(theme.palette.primary.main, 0.6)}`,
              },
            }}
            onClick={toggleDrawer}
          >
            <AutoAwesomeIcon
              sx={{
                animation: 'pulse 2s infinite',
                '@keyframes pulse': {
                  '0%': { opacity: 0.8, transform: 'scale(1)' },
                  '50%': { opacity: 1, transform: 'scale(1.1)' },
                  '100%': { opacity: 0.8, transform: 'scale(1)' },
                }
              }}
            />
          </Fab>
        </Tooltip>
      </Zoom>

      <Drawer
        anchor="right"
        open={isOpen}
        onClose={toggleDrawer}
        transitionDuration={{ enter: 500, exit: 300 }}
        PaperProps={{
          sx: {
            width: isMobile ? '100%' : '400px',
            borderTopLeftRadius: isMobile ? 0 : 16,
            borderBottomLeftRadius: isMobile ? 0 : 16,
            overflow: 'hidden',
            boxShadow: theme.shadows[10],
            background: theme.palette.mode === 'dark'
              ? theme.palette.background.paper
              : 'white',
          },
        }}
        SlideProps={{
          appear: true,
          direction: "left"
        }}
      >
        <AIAssistantChat onClose={toggleDrawer} />
      </Drawer>
    </>
  );
};

export default FloatingAssistantButton;
