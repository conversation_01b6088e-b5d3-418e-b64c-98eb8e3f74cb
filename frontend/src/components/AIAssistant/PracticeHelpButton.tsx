import React, { useState, useRef, useEffect } from 'react';
import { <PERSON><PERSON>, <PERSON><PERSON>, DialogContent, DialogTitle, IconButton, CircularProgress, Typography, Box, Fade } from '@mui/material';
import HelpOutlineIcon from '@mui/icons-material/HelpOutline';
import CloseIcon from '@mui/icons-material/Close';
import { streamPracticeHelp, AIAssistantEventType } from '../../api/ai-assistant';
import MathMarkdown from '../common/MathMarkdown';

interface PracticeHelpButtonProps {
  questionId: number;
}

const PracticeHelpButton: React.FC<PracticeHelpButtonProps> = ({ questionId }) => {
  const [open, setOpen] = useState(false);
  const [loading, setLoading] = useState(false);
  const [isTyping, setIsTyping] = useState(false);
  const [helpContent, setHelpContent] = useState<string>('');
  const [error, setError] = useState<string | null>(null);
  const abortStreamRef = useRef<(() => void) | null>(null);

  // Scroll to bottom when content updates
  const contentEndRef = useRef<HTMLDivElement>(null);

  useEffect(() => {
    if (contentEndRef.current && isTyping) {
      contentEndRef.current.scrollIntoView({ behavior: 'smooth' });
    }
  }, [helpContent, isTyping]);

  const handleOpen = () => {
    setOpen(true);
    setLoading(true);
    setIsTyping(true);
    setError(null);
    setHelpContent('');

    // Use streaming for faster response
    const abortStream = streamPracticeHelp(questionId, (event) => {
      switch (event.type) {
        case AIAssistantEventType.START:
          // Stream has started, we're already showing loading indicator
          break;

        case AIAssistantEventType.TOKEN:
          // Append the new token to the content
          if (event.content) {
            setHelpContent(prev => prev + event.content);
            setLoading(false); // Hide main loader once we start getting tokens
          }
          break;

        case AIAssistantEventType.ERROR:
          // Handle error
          setError(event.error || 'Failed to load help content. Please try again later.');
          setLoading(false);
          setIsTyping(false);
          break;

        case AIAssistantEventType.END:
          // Stream completed
          setLoading(false);
          setIsTyping(false);
          break;
      }
    });

    // Store the abort function
    abortStreamRef.current = abortStream;
  };

  const handleClose = () => {
    // Abort any ongoing stream
    if (abortStreamRef.current) {
      abortStreamRef.current();
      abortStreamRef.current = null;
    }

    setOpen(false);
    // Reset state when dialog is closed
    setHelpContent('');
    setError(null);
    setIsTyping(false);
  };

  return (
    <>
      <Button
        variant="outlined"
        color="primary"
        startIcon={<HelpOutlineIcon />}
        onClick={handleOpen}
        sx={{ mt: 2 }}
      >
        Get AI Help
      </Button>

      <Dialog
        open={open}
        onClose={handleClose}
        maxWidth="md"
        fullWidth
        PaperProps={{
          sx: {
            borderRadius: 2,
            maxHeight: '80vh',
          },
        }}
      >
        <DialogTitle
          sx={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            bgcolor: 'primary.main',
            color: 'white',
          }}
        >
          <Typography variant="h6">AI Explanation</Typography>
          <IconButton edge="end" color="inherit" onClick={handleClose} aria-label="close">
            <CloseIcon />
          </IconButton>
        </DialogTitle>
        <DialogContent sx={{ p: 3, minHeight: '300px', maxHeight: '60vh', overflow: 'auto' }}>
          {loading && !helpContent ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '200px' }}>
              <CircularProgress />
            </Box>
          ) : error ? (
            <Typography color="error" sx={{ my: 2 }}>
              {error}
            </Typography>
          ) : (
            <Box sx={{
                mt: 2,
                position: 'relative',
                '& .katex-display': {
                  backgroundColor: 'rgba(0, 0, 0, 0.02)',
                  borderRadius: '4px',
                },
                '& code': {
                  backgroundColor: 'rgba(0, 0, 0, 0.05)',
                  padding: '2px 4px',
                  borderRadius: '4px',
                  fontFamily: 'monospace'
                },
                '& pre': {
                  backgroundColor: 'rgba(0, 0, 0, 0.05)',
                  padding: '8px',
                  borderRadius: '4px',
                  overflow: 'auto'
                }
              }}>
              <MathMarkdown className="practice-help-content">{helpContent}</MathMarkdown>

              {isTyping && (
                <Box sx={{ display: 'flex', mt: 2, mb: 1 }}>
                  <Fade in={true} style={{ transitionDelay: '0ms' }}>
                    <CircularProgress size={12} sx={{ mr: 1 }} />
                  </Fade>
                  <Fade in={true} style={{ transitionDelay: '150ms' }}>
                    <CircularProgress size={12} sx={{ mr: 1 }} />
                  </Fade>
                  <Fade in={true} style={{ transitionDelay: '300ms' }}>
                    <CircularProgress size={12} />
                  </Fade>
                </Box>
              )}

              {/* Invisible element for scrolling to bottom */}
              <div ref={contentEndRef} />
            </Box>
          )}
        </DialogContent>
      </Dialog>
    </>
  );
};

export default PracticeHelpButton;
