import React, { useState, useRef, useEffect } from 'react';
import {
  Box,
  TextField,
  Button,
  Typography,
  Paper,
  CircularProgress,
  IconButton,
  Fade,
  Tooltip,
  Divider,
  useTheme,
  alpha,
  Zoom,
  Slide
} from '@mui/material';
import SendIcon from '@mui/icons-material/Send';
import CloseIcon from '@mui/icons-material/Close';
import DeleteIcon from '@mui/icons-material/Delete';
import AutoAwesomeIcon from '@mui/icons-material/AutoAwesome';
import {
  askAssistant,
  streamAssistant,
  AIAssistantError,
  AIAssistantErrorType,
  AIAssistantEventType,
  AIAssistantStreamEvent
} from '../../api/ai-assistant';
import MathMarkdown from '../common/MathMarkdown';

interface Message {
  id: string;
  content: string;
  isUser: boolean;
  timestamp: Date;
  isTyping?: boolean;
  error?: {
    type: string;
    message: string;
  };
  sources?: string[];
}

// Local storage key for saving chat history
const CHAT_HISTORY_KEY = 'campuspq_assistant_history';

interface AIAssistantChatProps {
  onClose?: () => void;
  initialMessage?: string;
}

const AIAssistantChat: React.FC<AIAssistantChatProps> = ({ onClose, initialMessage }) => {
  // Get theme for dark mode support
  const theme = useTheme();
  const isDarkMode = theme.palette.mode === 'dark';

  // Generate a unique ID for messages
  const generateId = () => Math.random().toString(36).substring(2, 11);

  // Initialize messages from localStorage or with default welcome message
  const [messages, setMessages] = useState<Message[]>(() => {
    const savedMessages = localStorage.getItem(CHAT_HISTORY_KEY);
    if (savedMessages) {
      try {
        // Parse the saved messages and convert string timestamps back to Date objects
        const parsedMessages = JSON.parse(savedMessages).map((msg: any) => ({
          ...msg,
          timestamp: new Date(msg.timestamp)
        }));
        return parsedMessages;
      } catch (e) {
        console.error('Error parsing saved messages:', e);
      }
    }

    // Default welcome message
    return [
      {
        id: generateId(),
        content: "Hi! I'm the CampusPQ AI Assistant. How can I help you today?",
        isUser: false,
        timestamp: new Date(),
      },
    ];
  });

  const [input, setInput] = useState(initialMessage || '');
  const [isLoading, setIsLoading] = useState(false);
  const [isTyping, setIsTyping] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Save messages to localStorage whenever they change
  useEffect(() => {
    localStorage.setItem(CHAT_HISTORY_KEY, JSON.stringify(messages));
    scrollToBottom();
  }, [messages]);

  // Handle initial message if provided
  useEffect(() => {
    if (initialMessage) {
      handleSendMessage();
    }
  }, []);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  // Clear chat history with animation
  const clearChatHistory = () => {
    const welcomeMessage = {
      id: generateId(),
      content: "Chat history cleared. How can I help you today?",
      isUser: false,
      timestamp: new Date(),
    };
    // First set empty array to trigger animation effect
    setMessages([]);
    // Then add welcome message after a short delay
    setTimeout(() => {
      setMessages([welcomeMessage]);
    }, 300);
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setInput(e.target.value);
  };

  const handleSendMessage = () => {
    if (!input.trim()) return;

    // Create and add user message
    const userMessage: Message = {
      id: generateId(),
      content: input,
      isUser: true,
      timestamp: new Date(),
    };

    // Store the query before clearing the input
    const query = input.trim();

    setMessages((prev) => [...prev, userMessage]);
    setInput('');
    setIsLoading(true);

    // Add typing indicator message
    const assistantMessageId = generateId();
    setIsTyping(true);
    setMessages((prev) => [
      ...prev,
      {
        id: assistantMessageId,
        content: '',
        isUser: false,
        timestamp: new Date(),
        isTyping: true,
      },
    ]);

    // Use streaming API
    const abortStream = streamAssistant(query, (event) => {
      switch (event.type) {
        case AIAssistantEventType.START:
          // The stream has started, but we already have a typing indicator
          break;

        case AIAssistantEventType.TOKEN:
          // Append the new token to the current message
          if (event.content) {
            setMessages((prev) =>
              prev.map(msg =>
                msg.id === assistantMessageId
                  ? {
                      ...msg,
                      content: msg.content + event.content,
                      isTyping: false,
                    }
                  : msg
              )
            );
          }
          break;

        case AIAssistantEventType.ERROR:
          // Handle error
          const errorMessage = event.error || "I'm sorry, I encountered an error. Please try again later.";
          setMessages((prev) =>
            prev.map(msg =>
              msg.id === assistantMessageId
                ? {
                    ...msg,
                    content: errorMessage,
                    isTyping: false,
                    error: {
                      type: "streaming",
                      message: errorMessage
                    }
                  }
                : msg
            )
          );
          setIsLoading(false);
          setIsTyping(false);
          break;

        case AIAssistantEventType.END:
          // Stream completed
          setMessages((prev) =>
            prev.map(msg =>
              msg.id === assistantMessageId
                ? {
                    ...msg,
                    content: event.content || msg.content,
                    isTyping: false,
                    sources: event.sources
                  }
                : msg
            )
          );
          setIsLoading(false);
          setIsTyping(false);
          break;
      }
    });

    // Store the abort function to clean up on unmount
    const currentAbortStream = abortStream;

    // Clean up function
    return () => {
      currentAbortStream();
    };
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  return (
    <Paper
      elevation={6}
      sx={{
        width: '100%',
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        borderRadius: 2,
        overflow: 'hidden',
        bgcolor: isDarkMode ? 'background.paper' : 'white',
        boxShadow: theme.shadows[10],
        transition: 'all 0.3s ease-in-out',
      }}
    >
      <Box
        sx={{
          p: 2,
          background: theme.palette.mode === 'dark'
            ? `linear-gradient(45deg, ${theme.palette.primary.dark}, ${theme.palette.secondary.dark})`
            : `linear-gradient(45deg, ${theme.palette.primary.main}, ${theme.palette.primary.dark})`,
          color: 'white',
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          boxShadow: theme.shadows[3],
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <AutoAwesomeIcon sx={{ mr: 1, animation: 'pulse 2s infinite' }} />
          <Typography variant="h6" sx={{ fontWeight: 'bold' }}>AI Assistant</Typography>
        </Box>
        <Box>
          <Tooltip title="Clear chat history" TransitionComponent={Zoom}>
            <IconButton
              onClick={clearChatHistory}
              color="inherit"
              size="small"
              sx={{
                mr: 1,
                transition: 'transform 0.2s',
                '&:hover': {
                  transform: 'scale(1.1)',
                }
              }}
            >
              <DeleteIcon />
            </IconButton>
          </Tooltip>
          {onClose && (
            <IconButton
              onClick={onClose}
              color="inherit"
              size="small"
              sx={{
                transition: 'transform 0.2s',
                '&:hover': {
                  transform: 'scale(1.1)',
                }
              }}
            >
              <CloseIcon />
            </IconButton>
          )}
        </Box>
      </Box>

      <Box
        sx={{
          p: 2,
          flexGrow: 1,
          overflowY: 'auto',
          display: 'flex',
          flexDirection: 'column',
          gap: 2,
          bgcolor: isDarkMode ? alpha(theme.palette.background.default, 0.7) : '#f8f9fa',
          backgroundImage: isDarkMode
            ? 'radial-gradient(circle at 25px 25px, rgba(255, 255, 255, 0.05) 2%, transparent 0%), radial-gradient(circle at 75px 75px, rgba(255, 255, 255, 0.05) 2%, transparent 0%)'
            : 'radial-gradient(circle at 25px 25px, rgba(0, 0, 0, 0.02) 2%, transparent 0%), radial-gradient(circle at 75px 75px, rgba(0, 0, 0, 0.02) 2%, transparent 0%)',
          backgroundSize: '100px 100px',
        }}
      >
        {messages.map((message, index) => (
          <Zoom
            in={true}
            style={{
              transitionDelay: `${index * 100}ms`,
              transformOrigin: message.isUser ? 'right' : 'left'
            }}
            key={message.id}
          >
            <Box
              sx={{
                alignSelf: message.isUser ? 'flex-end' : 'flex-start',
                maxWidth: '80%',
                animation: 'fadeIn 0.5s ease-in-out',
              }}
            >
              <Paper
                elevation={message.isUser ? 2 : 1}
                sx={{
                  p: 2,
                  backgroundColor: message.isUser
                    ? theme.palette.primary.main
                    : message.error
                      ? isDarkMode ? alpha(theme.palette.error.dark, 0.2) : '#fff8f8' // Error background
                      : isDarkMode ? alpha(theme.palette.background.paper, 0.8) : 'white',
                  color: message.isUser
                    ? 'white'
                    : message.error
                      ? theme.palette.error.main
                      : theme.palette.text.primary,
                  borderRadius: message.isUser ? '18px 18px 4px 18px' : '18px 18px 18px 4px',
                  border: message.error
                    ? `1px solid ${isDarkMode ? theme.palette.error.dark : '#ffcdd2'}`
                    : 'none',
                  boxShadow: message.isUser
                    ? `0 2px 8px ${alpha(theme.palette.primary.main, 0.25)}`
                    : `0 2px 8px ${alpha(theme.palette.common.black, 0.05)}`,
                  transition: 'all 0.2s ease-in-out',
                  '&:hover': {
                    boxShadow: `0 4px 12px ${alpha(theme.palette.common.black, 0.1)}`,
                  },
                }}
              >
                {message.isTyping ? (
                  <Box sx={{ display: 'flex', alignItems: 'center', height: '24px' }}>
                    <Fade in={true} style={{ transitionDelay: '0ms' }}>
                      <CircularProgress size={12} sx={{ mr: 1, color: theme.palette.primary.main }} />
                    </Fade>
                    <Fade in={true} style={{ transitionDelay: '150ms' }}>
                      <CircularProgress size={12} sx={{ mr: 1, color: theme.palette.primary.main }} />
                    </Fade>
                    <Fade in={true} style={{ transitionDelay: '300ms' }}>
                      <CircularProgress size={12} sx={{ color: theme.palette.primary.main }} />
                    </Fade>
                  </Box>
                ) : message.isUser ? (
                  <Typography sx={{ fontWeight: 500 }}>{message.content}</Typography>
                ) : (
                  <Box sx={{
                    width: '100%',
                    '& a': {
                      color: theme.palette.primary.main,
                      textDecoration: 'none',
                      '&:hover': { textDecoration: 'underline' }
                    },
                    '& code': {
                      backgroundColor: isDarkMode ? alpha(theme.palette.common.white, 0.1) : alpha(theme.palette.common.black, 0.05),
                      padding: '2px 4px',
                      borderRadius: '4px',
                      fontFamily: 'monospace'
                    },
                    '& pre': {
                      backgroundColor: isDarkMode ? alpha(theme.palette.common.white, 0.1) : alpha(theme.palette.common.black, 0.05),
                      padding: '8px',
                      borderRadius: '4px',
                      overflow: 'auto'
                    },
                    '& .katex-display': {
                      backgroundColor: isDarkMode ? alpha(theme.palette.common.white, 0.05) : alpha(theme.palette.common.black, 0.02),
                    }
                  }}>
                    <MathMarkdown className="ai-response-content">{message.content}</MathMarkdown>

                    {message.sources && message.sources.length > 0 && (
                      <Box sx={{
                        mt: 2,
                        pt: 1,
                        borderTop: `1px solid ${alpha(theme.palette.divider, 0.5)}`,
                        fontSize: '0.75rem',
                        color: alpha(theme.palette.text.secondary, 0.8)
                      }}>
                        <Typography variant="caption" sx={{ fontWeight: 'bold' }}>
                          Sources:
                        </Typography>
                        <Box component="ul" sx={{ mt: 0.5, pl: 2 }}>
                          {message.sources.map((source, idx) => (
                            <Box component="li" key={idx} sx={{ mb: 0.5 }}>
                              {source}
                            </Box>
                          ))}
                        </Box>
                      </Box>
                    )}
                  </Box>
                )}
              </Paper>
              <Typography
                variant="caption"
                sx={{
                  ml: message.isUser ? 0 : 1,
                  mr: message.isUser ? 1 : 0,
                  color: alpha(theme.palette.text.secondary, 0.8),
                  display: 'block',
                  mt: 0.5,
                  fontStyle: message.error ? 'italic' : 'normal',
                  textAlign: message.isUser ? 'right' : 'left',
                }}
              >
                {message.error
                  ? 'Error: ' + message.error.type
                  : message.timestamp.toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
              </Typography>
            </Box>
          </Zoom>
        ))}
        <div ref={messagesEndRef} />
      </Box>

      <Box
        sx={{
          p: 2,
          backgroundColor: isDarkMode ? alpha(theme.palette.background.paper, 0.8) : '#fff',
          borderTop: '1px solid',
          borderColor: isDarkMode ? alpha(theme.palette.divider, 0.2) : theme.palette.divider,
          display: 'flex',
          gap: 1,
        }}
      >
        <TextField
          fullWidth
          variant="outlined"
          placeholder="Type your message..."
          value={input}
          onChange={handleInputChange}
          onKeyPress={handleKeyPress}
          size="small"
          multiline
          maxRows={4}
          disabled={isLoading}
          sx={{
            '& .MuiOutlinedInput-root': {
              borderRadius: '18px',
              transition: 'all 0.2s ease-in-out',
              '&.Mui-focused': {
                boxShadow: `0 0 0 2px ${alpha(theme.palette.primary.main, 0.25)}`,
              },
              '& fieldset': {
                borderColor: isDarkMode ? alpha(theme.palette.divider, 0.5) : theme.palette.divider,
              },
              '&:hover fieldset': {
                borderColor: theme.palette.primary.main,
              },
            },
          }}
          InputProps={{
            sx: {
              pr: 1,
              pl: 2,
            }
          }}
        />
        <Button
          variant="contained"
          color="primary"
          endIcon={<SendIcon />}
          onClick={handleSendMessage}
          disabled={!input.trim() || isLoading}
          sx={{
            borderRadius: '18px',
            px: 2,
            boxShadow: theme.shadows[2],
            transition: 'all 0.2s ease-in-out',
            '&:hover': {
              transform: 'translateY(-2px)',
              boxShadow: theme.shadows[4],
            },
            '&:active': {
              transform: 'translateY(0)',
            },
            '&.Mui-disabled': {
              bgcolor: isDarkMode ? alpha(theme.palette.primary.main, 0.2) : alpha(theme.palette.primary.main, 0.1),
            }
          }}
        >
          Send
        </Button>
      </Box>
    </Paper>
  );
};

export default AIAssistantChat;
