import React, { useState, useEffect } from 'react';
import { 
  Box, 
  FormControl, 
  InputLabel, 
  Select, 
  MenuItem, 
  Typography, 
  Alert, 
  Paper,
  CircularProgress
} from '@mui/material';
import { useQuery } from '@tanstack/react-query';
import { getCourses } from '../../api/courses';

/**
 * Course selector component for manual course selection
 * 
 * @param {Object} props Component props
 * @param {number|null} props.detectedCourseId The auto-detected course ID (if any)
 * @param {string[]} props.detectedTopics The auto-detected topics (if any)
 * @param {string} props.confidence The confidence level of the auto-detection
 * @param {Function} props.onCourseSelect Callback when a course is selected
 * @param {boolean} props.required Whether course selection is required
 * @returns {JSX.Element} The course selector component
 */
const CourseSelector = ({ 
  detectedCourseId, 
  detectedTopics = [], 
  confidence = 'NONE',
  onCourseSelect,
  required = false
}) => {
  const [selectedCourseId, setSelectedCourseId] = useState(detectedCourseId);
  
  // Fetch available courses
  const { data: courses, isLoading, error } = useQuery({
    queryKey: ['courses'],
    queryFn: getCourses,
  });

  // Update selected course when detected course changes
  useEffect(() => {
    if (detectedCourseId) {
      setSelectedCourseId(detectedCourseId);
      if (onCourseSelect) onCourseSelect(detectedCourseId);
    }
  }, [detectedCourseId, onCourseSelect]);

  // Handle course selection change
  const handleCourseChange = (event) => {
    const courseId = event.target.value;
    setSelectedCourseId(courseId);
    if (onCourseSelect) onCourseSelect(courseId);
  };

  // Get confidence level color
  const getConfidenceColor = () => {
    switch (confidence) {
      case 'HIGH':
        return 'success.main';
      case 'MEDIUM':
        return 'warning.main';
      case 'LOW':
        return 'error.main';
      default:
        return 'text.secondary';
    }
  };

  // Get detected course name
  const getDetectedCourseName = () => {
    if (!detectedCourseId || !courses) return 'None';
    const course = courses.find(c => c.id === detectedCourseId);
    return course ? `${course.name} (${course.code})` : 'Unknown';
  };

  if (isLoading) {
    return <CircularProgress size={24} />;
  }

  if (error) {
    return <Alert severity="error">Error loading courses: {error.message}</Alert>;
  }

  return (
    <Paper elevation={0} sx={{ p: 2, mb: 2, border: '1px solid', borderColor: 'divider' }}>
      {detectedCourseId && (
        <Box mb={2}>
          <Typography variant="subtitle2" gutterBottom>
            Auto-detected course:
          </Typography>
          <Typography variant="body1" fontWeight="medium">
            {getDetectedCourseName()}
          </Typography>
          <Box display="flex" alignItems="center" mt={0.5}>
            <Typography variant="caption" color={getConfidenceColor()} fontWeight="medium">
              Confidence: {confidence}
            </Typography>
          </Box>
          {detectedTopics.length > 0 && (
            <Box mt={1}>
              <Typography variant="subtitle2" gutterBottom>
                Detected topics:
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {detectedTopics.join(', ')}
              </Typography>
            </Box>
          )}
        </Box>
      )}

      <FormControl fullWidth required={required}>
        <InputLabel id="course-select-label">
          {detectedCourseId ? 'Confirm or change course' : 'Select course'}
        </InputLabel>
        <Select
          labelId="course-select-label"
          id="course-select"
          value={selectedCourseId || ''}
          label={detectedCourseId ? 'Confirm or change course' : 'Select course'}
          onChange={handleCourseChange}
        >
          {courses?.map((course) => (
            <MenuItem key={course.id} value={course.id}>
              {course.name} ({course.code})
            </MenuItem>
          ))}
        </Select>
      </FormControl>
    </Paper>
  );
};

export default CourseSelector;
