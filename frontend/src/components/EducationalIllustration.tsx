import React from 'react';
import { Box, Typography, useTheme } from '@mui/material';
import { motion } from 'framer-motion';
import { 
  School, 
  MenuBook, 
  Lightbulb, 
  Psychology, 
  EmojiObjects, 
  Computer 
} from '@mui/icons-material';

interface EducationalIllustrationProps {
  title?: string;
  subtitle?: string;
}

const EducationalIllustration: React.FC<EducationalIllustrationProps> = ({
  title = "Learning Reimagined",
  subtitle = "Expand your knowledge with our comprehensive learning platform"
}) => {
  const theme = useTheme();

  const iconSize = 40;
  const iconColor = theme.palette.mode === 'light' ? theme.palette.primary.main : theme.palette.primary.light;
  
  return (
    <Box
      sx={{
        display: { xs: 'none', md: 'flex' },
        flexDirection: 'column',
        justifyContent: 'center',
        alignItems: 'center',
        height: '100%',
        p: 4,
      }}
    >
      <motion.div
        initial={{ opacity: 0, scale: 0.8 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.8 }}
      >
        <Typography 
          variant="h4" 
          component="h2" 
          fontWeight="bold" 
          gutterBottom
          sx={{ 
            textAlign: 'center',
            background: `linear-gradient(90deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
            WebkitBackgroundClip: 'text',
            WebkitTextFillColor: 'transparent',
            mb: 2
          }}
        >
          {title}
        </Typography>
      </motion.div>
      
      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ duration: 0.8, delay: 0.2 }}
      >
        <Typography 
          variant="body1" 
          color="text.secondary" 
          sx={{ 
            textAlign: 'center',
            mb: 4,
            maxWidth: 300
          }}
        >
          {subtitle}
        </Typography>
      </motion.div>
      
      <Box
        sx={{
          display: 'grid',
          gridTemplateColumns: 'repeat(3, 1fr)',
          gap: 2,
          width: '100%',
          maxWidth: 300,
        }}
      >
        {[
          { icon: <School sx={{ fontSize: iconSize, color: iconColor }} />, delay: 0.3 },
          { icon: <MenuBook sx={{ fontSize: iconSize, color: iconColor }} />, delay: 0.4 },
          { icon: <Lightbulb sx={{ fontSize: iconSize, color: iconColor }} />, delay: 0.5 },
          { icon: <Psychology sx={{ fontSize: iconSize, color: iconColor }} />, delay: 0.6 },
          { icon: <EmojiObjects sx={{ fontSize: iconSize, color: iconColor }} />, delay: 0.7 },
          { icon: <Computer sx={{ fontSize: iconSize, color: iconColor }} />, delay: 0.8 },
        ].map((item, index) => (
          <motion.div
            key={index}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: item.delay }}
            style={{
              display: 'flex',
              justifyContent: 'center',
              alignItems: 'center',
            }}
          >
            {item.icon}
          </motion.div>
        ))}
      </Box>
    </Box>
  );
};

export default EducationalIllustration;
