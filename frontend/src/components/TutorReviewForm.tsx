import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  DialogTitle,
  DialogContent,
  <PERSON>alog<PERSON><PERSON>,
  Button,
  TextField,
  Rating,
  Box,
  Typography,
  Alert,
  CircularProgress,
  Avatar,
  Chip,
  Divider,
  FormHelperText,
  useTheme
} from '@mui/material';
import {
  Star as StarIcon,
  Send as SendIcon,
  Close as CloseIcon
} from '@mui/icons-material';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { createTutorReview, TutorReviewCreate, TutorProfile } from '../api/tutors';
import { motion, AnimatePresence } from 'framer-motion';
import { useToast } from '../contexts/ToastContext';

interface TutorReviewFormProps {
  open: boolean;
  onClose: () => void;
  tutor: TutorProfile;
  sessionId?: number;
}

const TutorReviewForm: React.FC<TutorReviewFormProps> = ({
  open,
  onClose,
  tutor,
  sessionId
}) => {
  const queryClient = useQueryClient();
  const { showSuccess, showError } = useToast();
  const theme = useTheme();
  const [rating, setRating] = useState<number>(0);
  const [comment, setComment] = useState('');
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [submitSuccess, setSubmitSuccess] = useState(false);

  const createReviewMutation = useMutation({
    mutationFn: (data: TutorReviewCreate) => createTutorReview(tutor.id, data),
    onSuccess: () => {
      setSubmitSuccess(true);
      showSuccess('Review submitted successfully! Thank you for your feedback.');

      // Invalidate relevant queries to refresh data
      queryClient.invalidateQueries({ queryKey: ['tutor', tutor.id] });
      queryClient.invalidateQueries({ queryKey: ['tutors'] });
      queryClient.invalidateQueries({ queryKey: ['tutor-reviews', tutor.id] });
      queryClient.invalidateQueries({ queryKey: ['existing-review', tutor.id] });
      queryClient.invalidateQueries({ queryKey: ['my-tutor-profile'] });
      queryClient.invalidateQueries({ queryKey: ['my-tutor-reviews'] });

      // Close dialog after a short delay to show success message
      setTimeout(() => {
        handleClose();
      }, 2000);
    },
    onError: (error: any) => {
      const errorMessage = error.response?.data?.detail || 'Failed to submit review. Please try again.';

      // Handle specific error cases and show toast
      if (errorMessage.includes('already reviewed')) {
        const message = 'You have already submitted a review for this tutor. You can only review each tutor once.';
        setErrors({ submit: message });
        showError(message);
      } else if (errorMessage.includes('cannot review yourself')) {
        const message = 'You cannot review yourself.';
        setErrors({ submit: message });
        showError(message);
      } else if (errorMessage.includes('Only students can create reviews')) {
        const message = 'Only students can submit reviews.';
        setErrors({ submit: message });
        showError(message);
      } else {
        setErrors({ submit: errorMessage });
        showError(errorMessage);
      }
    }
  });

  const handleClose = () => {
    setRating(0);
    setComment('');
    setErrors({});
    setSubmitSuccess(false);
    onClose();
  };

  const handleSubmit = () => {
    const newErrors: Record<string, string> = {};

    if (rating === 0) {
      newErrors.rating = 'Please provide a rating';
    }

    if (comment.trim().length > 1000) {
      newErrors.comment = 'Comment must be less than 1000 characters';
    }

    setErrors(newErrors);

    if (Object.keys(newErrors).length === 0) {
      const reviewData: TutorReviewCreate = {
        tutor_profile_id: tutor.id,
        rating,
        comment: comment.trim() || undefined, // Send undefined if empty
        ...(sessionId && { session_id: sessionId })
      };

      createReviewMutation.mutate(reviewData);
    }
  };

  const getRatingLabel = (value: number) => {
    const labels = {
      1: 'Poor',
      2: 'Fair',
      3: 'Good',
      4: 'Very Good',
      5: 'Excellent'
    };
    return labels[value as keyof typeof labels] || '';
  };

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      maxWidth="sm"
      fullWidth
      slotProps={{
        paper: {
          sx: {
            borderRadius: 2,
            maxHeight: '90vh',
            bgcolor: 'background.paper' // Ensure proper theme background
          }
        }
      }}
    >
      <DialogTitle sx={{ pb: 1 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <Typography variant="h6">
            Rate & Review Tutor
          </Typography>
          <Button
            onClick={handleClose}
            size="small"
            sx={{ minWidth: 'auto', p: 1 }}
          >
            <CloseIcon />
          </Button>
        </Box>
      </DialogTitle>

      <DialogContent sx={{ pt: 1 }}>
        <AnimatePresence mode="wait">
          {submitSuccess ? (
            <motion.div
              key="success"
              initial={{ opacity: 0, scale: 0.8 }}
              animate={{ opacity: 1, scale: 1 }}
              exit={{ opacity: 0, scale: 0.8 }}
              transition={{ duration: 0.3 }}
            >
              <Box sx={{ textAlign: 'center', py: 3 }}>
                <motion.div
                  initial={{ scale: 0 }}
                  animate={{ scale: 1 }}
                  transition={{ delay: 0.2, type: 'spring', stiffness: 200 }}
                >
                  <StarIcon sx={{ fontSize: 60, color: 'warning.main', mb: 2 }} />
                </motion.div>
                <Typography variant="h6" gutterBottom>
                  Review Submitted Successfully!
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Thank you for your feedback. Your review helps other students find great tutors.
                </Typography>
              </Box>
            </motion.div>
          ) : (
            <motion.div
              key="form"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              exit={{ opacity: 0 }}
            >
              {/* Tutor Info */}
              <Box sx={{
                mb: 3,
                p: 2,
                bgcolor: theme.palette.mode === 'dark' ? 'grey.800' : 'grey.50',
                borderRadius: 1
              }}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                  <Avatar
                    src={tutor.user?.profile_picture_url}
                    sx={{ mr: 2, width: 50, height: 50 }}
                  >
                    {tutor.user?.full_name?.charAt(0)}
                  </Avatar>
                  <Box>
                    <Typography variant="h6">
                      {tutor.user?.full_name}
                    </Typography>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mt: 0.5 }}>
                      <Rating value={tutor.average_rating || 0} readOnly size="small" />
                      <Typography variant="body2" color="text.secondary">
                        ({tutor.total_reviews} reviews)
                      </Typography>
                    </Box>
                  </Box>
                </Box>
                {tutor.specializations && tutor.specializations.length > 0 && (
                  <Box sx={{ display: 'flex', gap: 0.5, flexWrap: 'wrap' }}>
                    {tutor.specializations.slice(0, 3).map((spec) => (
                      <Chip
                        key={spec.id}
                        label={spec.name}
                        size="small"
                        variant="outlined"
                      />
                    ))}
                  </Box>
                )}
              </Box>

              <Divider sx={{ mb: 3 }} />

              {/* Rating Section */}
              <Box sx={{ mb: 3 }}>
                <Typography variant="subtitle1" gutterBottom>
                  How would you rate this tutor? *
                </Typography>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 1 }}>
                  <Rating
                    value={rating}
                    onChange={(_, newValue) => {
                      setRating(newValue || 0);
                      if (errors.rating) {
                        setErrors(prev => ({ ...prev, rating: '' }));
                      }
                    }}
                    size="large"
                    precision={1}
                  />
                  {rating > 0 && (
                    <Typography variant="body2" color="text.secondary">
                      {getRatingLabel(rating)}
                    </Typography>
                  )}
                </Box>
                {errors.rating && (
                  <FormHelperText error>{errors.rating}</FormHelperText>
                )}
              </Box>

              {/* Comment Section */}
              <Box sx={{ mb: 3 }}>
                <Typography variant="subtitle1" gutterBottom>
                  Share your experience
                </Typography>
                <TextField
                  fullWidth
                  multiline
                  rows={4}
                  placeholder="Tell other students about your experience with this tutor. What did you like? How did they help you learn? (optional)"
                  value={comment}
                  onChange={(e) => {
                    setComment(e.target.value);
                    if (errors.comment) {
                      setErrors(prev => ({ ...prev, comment: '' }));
                    }
                  }}
                  error={!!errors.comment}
                  helperText={errors.comment || `${comment.length}/1000 characters`}
                  sx={{
                    '& .MuiOutlinedInput-root': {
                      borderRadius: 2
                    }
                  }}
                />
              </Box>

              {/* Error Alert */}
              {errors.submit && (
                <Alert severity="error" sx={{ mb: 2 }}>
                  {errors.submit}
                </Alert>
              )}
            </motion.div>
          )}
        </AnimatePresence>
      </DialogContent>

      {!submitSuccess && (
        <DialogActions sx={{ px: 3, pb: 3 }}>
          <Button
            onClick={handleClose}
            disabled={createReviewMutation.isPending}
          >
            Cancel
          </Button>
          <Button
            onClick={handleSubmit}
            variant="contained"
            disabled={createReviewMutation.isPending || rating === 0}
            startIcon={
              createReviewMutation.isPending ? (
                <CircularProgress size={16} />
              ) : (
                <SendIcon />
              )
            }
            sx={{ minWidth: 120 }}
          >
            {createReviewMutation.isPending ? 'Submitting...' : 'Submit Review'}
          </Button>
        </DialogActions>
      )}
    </Dialog>
  );
};

export default TutorReviewForm;
