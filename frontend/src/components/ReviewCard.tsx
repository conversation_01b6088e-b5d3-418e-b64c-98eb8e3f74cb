import React from 'react';
import {
  <PERSON>,
  CardContent,
  Box,
  Typography,
  Avatar,
  Rating,
  Chip,
  IconButton,
  Menu,
  MenuItem,
  useTheme
} from '@mui/material';
import {
  MoreVert as MoreVertIcon,
  ThumbUp as ThumbUpIcon,
  Flag as FlagIcon,
  Person as PersonIcon
} from '@mui/icons-material';
import { TutorReview } from '../api/tutors';
import { motion } from 'framer-motion';
import { formatDistanceToNow } from 'date-fns';

interface ReviewCardProps {
  review: TutorReview;
  showActions?: boolean;
  compact?: boolean;
}

const ReviewCard: React.FC<ReviewCardProps> = ({
  review,
  showActions = false,
  compact = false
}) => {
  const theme = useTheme();
  const [anchorEl, setAnchorEl] = React.useState<null | HTMLElement>(null);

  // Safety check for review data
  if (!review) {
    return null;
  }

  const handleMenuOpen = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleMenuClose = () => {
    setAnchorEl(null);
  };

  const getRatingColor = (rating: number) => {
    const safeRating = typeof rating === 'number' ? rating : 0;
    if (safeRating >= 4.5) return theme.palette.success.main;
    if (safeRating >= 3.5) return theme.palette.warning.main;
    return theme.palette.error.main;
  };

  const formatDate = (dateString: string) => {
    try {
      return formatDistanceToNow(new Date(dateString), { addSuffix: true });
    } catch {
      return 'Recently';
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      <Card
        sx={{
          mb: compact ? 1 : 2,
          borderRadius: 2,
          border: `1px solid ${theme.palette.divider}`,
          '&:hover': {
            boxShadow: theme.shadows[4],
            transform: 'translateY(-2px)',
            transition: 'all 0.2s ease-in-out'
          }
        }}
      >
        <CardContent sx={{ p: compact ? 2 : 3, '&:last-child': { pb: compact ? 2 : 3 } }}>
          {/* Header */}
          <Box sx={{ display: 'flex', alignItems: 'flex-start', mb: 2 }}>
            <Avatar
              src={review.student?.profile_picture_url}
              sx={{ 
                mr: 2, 
                width: compact ? 36 : 44, 
                height: compact ? 36 : 44,
                bgcolor: theme.palette.primary.light
              }}
            >
              {review.student?.full_name?.charAt(0) || <PersonIcon />}
            </Avatar>
            
            <Box sx={{ flex: 1, minWidth: 0 }}>
              <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 0.5 }}>
                <Typography 
                  variant={compact ? "subtitle2" : "subtitle1"} 
                  sx={{ fontWeight: 600 }}
                >
                  {review.student?.full_name || 'Anonymous Student'}
                </Typography>
                
                {showActions && (
                  <IconButton
                    size="small"
                    onClick={handleMenuOpen}
                    sx={{ ml: 1 }}
                  >
                    <MoreVertIcon fontSize="small" />
                  </IconButton>
                )}
              </Box>
              
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                <Rating
                  value={review.rating || 0}
                  readOnly
                  size={compact ? "small" : "medium"}
                  sx={{
                    '& .MuiRating-iconFilled': {
                      color: getRatingColor(review.rating || 0)
                    }
                  }}
                />
                <Chip
                  label={`${review.rating || 0}/5`}
                  size="small"
                  variant="outlined"
                  sx={{
                    height: compact ? 20 : 24,
                    fontSize: compact ? '0.7rem' : '0.75rem',
                    borderColor: getRatingColor(review.rating || 0),
                    color: getRatingColor(review.rating || 0)
                  }}
                />
              </Box>
              
              <Typography 
                variant="caption" 
                color="text.secondary"
                sx={{ fontSize: compact ? '0.7rem' : '0.75rem' }}
              >
                {formatDate(review.created_at)}
                {review.session_id && (
                  <span> • After a tutoring session</span>
                )}
              </Typography>
            </Box>
          </Box>

          {/* Review Content */}
          {review.comment && review.comment.trim() && (
            <Box sx={{ mt: 2 }}>
              <Typography
                variant="body2"
                sx={{
                  lineHeight: 1.6,
                  fontSize: compact ? '0.875rem' : '1rem',
                  color: theme.palette.text.primary
                }}
              >
                "{review.comment}"
              </Typography>
            </Box>
          )}

          {/* Actions Menu */}
          <Menu
            anchorEl={anchorEl}
            open={Boolean(anchorEl)}
            onClose={handleMenuClose}
            PaperProps={{
              sx: { minWidth: 150 }
            }}
          >
            <MenuItem onClick={handleMenuClose}>
              <ThumbUpIcon sx={{ mr: 1, fontSize: 18 }} />
              Helpful
            </MenuItem>
            <MenuItem onClick={handleMenuClose}>
              <FlagIcon sx={{ mr: 1, fontSize: 18 }} />
              Report
            </MenuItem>
          </Menu>
        </CardContent>
      </Card>
    </motion.div>
  );
};

export default ReviewCard;
