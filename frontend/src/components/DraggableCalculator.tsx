import React, { useState, useEffect, useRef } from 'react';
import {
  Box,
  Button,
  Paper,
  Typography,
  IconButton,
  useTheme,
  Drawer,
  <PERSON><PERSON><PERSON>,
  ToggleButton,
  ToggleButtonGroup
} from '@mui/material';
import {
  Calculate as CalculateIcon,
  Backspace as BackspaceIcon,
  Close as CloseIcon,
  Science as ScienceIcon,
  Calculate as BasicIcon,
  DragIndicator as DragIcon
} from '@mui/icons-material';

interface Position {
  x: number;
  y: number;
}

interface CalculatorProps {
  initialPosition?: Position;
}

const DraggableCalculator: React.FC<CalculatorProps> = ({ initialPosition = { x: 20, y: 20 } }) => {
  const theme = useTheme();
  const [open, setOpen] = useState(false);
  const [display, setDisplay] = useState('0');
  const [memory, setMemory] = useState<number | null>(null);
  const [operation, setOperation] = useState<string | null>(null);
  const [waitingForOperand, setWaitingForOperand] = useState(false);
  const [calculatorMode, setCalculatorMode] = useState<'basic' | 'scientific'>('basic');

  // Position state for dragging
  const [position, setPosition] = useState<Position>(initialPosition);
  const [isDragging, setIsDragging] = useState(false);
  const [dragOffset, setDragOffset] = useState<Position>({ x: 0, y: 0 });

  // Ref for the calculator button
  const calculatorRef = useRef<HTMLDivElement>(null);

  const toggleCalculator = () => {
    setOpen(!open);
  };

  // Handle mouse down for dragging
  const handleMouseDown = (e: React.MouseEvent) => {
    if (calculatorRef.current) {
      const rect = calculatorRef.current.getBoundingClientRect();
      setDragOffset({
        x: e.clientX - rect.left,
        y: e.clientY - rect.top
      });
      setIsDragging(true);
    }
  };

  // Handle mouse move for dragging
  const handleMouseMove = (e: MouseEvent) => {
    if (isDragging) {
      setPosition({
        x: e.clientX - dragOffset.x,
        y: e.clientY - dragOffset.y
      });
    }
  };

  // Handle mouse up to stop dragging
  const handleMouseUp = () => {
    setIsDragging(false);
  };

  // Add and remove event listeners for dragging
  useEffect(() => {
    if (isDragging) {
      window.addEventListener('mousemove', handleMouseMove);
      window.addEventListener('mouseup', handleMouseUp);
    } else {
      window.removeEventListener('mousemove', handleMouseMove);
      window.removeEventListener('mouseup', handleMouseUp);
    }

    return () => {
      window.removeEventListener('mousemove', handleMouseMove);
      window.removeEventListener('mouseup', handleMouseUp);
    };
  }, [isDragging]);

  const clearDisplay = () => {
    setDisplay('0');
    setOperation(null);
    setMemory(null);
    setWaitingForOperand(false);
  };

  const handleDigitClick = (digit: string) => {
    if (waitingForOperand) {
      setDisplay(digit);
      setWaitingForOperand(false);
    } else {
      setDisplay(display === '0' ? digit : display + digit);
    }
  };

  const handleDecimalClick = () => {
    if (waitingForOperand) {
      setDisplay('0.');
      setWaitingForOperand(false);
    } else if (display.indexOf('.') === -1) {
      setDisplay(display + '.');
    }
  };

  const handleOperationClick = (op: string) => {
    const inputValue = parseFloat(display);

    if (memory === null) {
      setMemory(inputValue);
    } else if (operation) {
      const result = performCalculation(memory, inputValue, operation);
      setMemory(result);
      setDisplay(String(result));
    }

    setWaitingForOperand(true);
    setOperation(op);
  };

  const handleEqualsClick = () => {
    const inputValue = parseFloat(display);

    if (operation && memory !== null) {
      const result = performCalculation(memory, inputValue, operation);
      setDisplay(String(result));
      setMemory(null);
      setOperation(null);
      setWaitingForOperand(true);
    }
  };

  const handleBackspaceClick = () => {
    if (display.length > 1) {
      setDisplay(display.substring(0, display.length - 1));
    } else {
      setDisplay('0');
    }
  };

  const performCalculation = (a: number, b: number, op: string): number => {
    switch (op) {
      case '+':
        return a + b;
      case '-':
        return a - b;
      case '×':
        return a * b;
      case '÷':
        return b === 0 ? NaN : a / b;
      case 'x^y':
        return Math.pow(a, b);
      case 'log':
        return Math.log10(a);
      case 'ln':
        return Math.log(a);
      case 'sin':
        return Math.sin(a);
      case 'cos':
        return Math.cos(a);
      case 'tan':
        return Math.tan(a);
      default:
        return b;
    }
  };

  const handleScientificFunction = (func: string) => {
    const inputValue = parseFloat(display);
    let result: number;

    switch (func) {
      case 'sqrt':
        result = Math.sqrt(inputValue);
        break;
      case 'square':
        result = Math.pow(inputValue, 2);
        break;
      case 'cube':
        result = Math.pow(inputValue, 3);
        break;
      case '1/x':
        result = 1 / inputValue;
        break;
      case 'sin':
        result = Math.sin(inputValue);
        break;
      case 'cos':
        result = Math.cos(inputValue);
        break;
      case 'tan':
        result = Math.tan(inputValue);
        break;
      case 'log':
        result = Math.log10(inputValue);
        break;
      case 'ln':
        result = Math.log(inputValue);
        break;
      case 'pi':
        result = Math.PI;
        break;
      case 'e':
        result = Math.E;
        break;
      default:
        result = inputValue;
    }

    setDisplay(String(result));
    setWaitingForOperand(true);
  };

  const handleModeChange = (event: React.MouseEvent<HTMLElement>, newMode: 'basic' | 'scientific' | null) => {
    if (newMode !== null) {
      setCalculatorMode(newMode);
    }
  };

  return (
    <>
      <Box
        ref={calculatorRef}
        sx={{
          position: 'fixed',
          top: position.y,
          left: position.x,
          zIndex: 1050,
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          cursor: isDragging ? 'grabbing' : 'grab',
        }}
      >
        <Box
          onMouseDown={handleMouseDown}
          sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            width: '100%',
            mb: 0.5,
            cursor: 'grab',
          }}
        >
          <DragIcon
            fontSize="small"
            sx={{
              color: theme.palette.mode === 'dark' ? 'rgba(255,255,255,0.5)' : 'rgba(0,0,0,0.3)',
              fontSize: '16px',
            }}
          />
        </Box>

        <Tooltip title="Calculator">
          <IconButton
            onClick={toggleCalculator}
            color="primary"
            size="small"
            sx={{
              bgcolor: theme.palette.primary.main,
              color: theme.palette.primary.contrastText,
              width: 40,
              height: 40,
              boxShadow: theme.shadows[4],
              '&:hover': {
                bgcolor: theme.palette.primary.dark,
              },
            }}
          >
            <CalculateIcon fontSize="small" />
          </IconButton>
        </Tooltip>
      </Box>

      <Drawer
        anchor="right"
        open={open}
        onClose={() => setOpen(false)}
        variant="temporary"
        elevation={24}
        sx={{
          zIndex: 1100,
          '& .MuiBackdrop-root': {
            backgroundColor: 'rgba(0, 0, 0, 0.5)',
          },
          '& .MuiPaper-root': {
            overflow: 'visible',
          },
        }}
        PaperProps={{
          sx: {
            width: calculatorMode === 'scientific' ? 320 : 260,
            p: 2,
            borderRadius: '8px 0 0 8px',
            transition: 'width 0.3s ease-in-out',
            boxShadow: theme.shadows[24],
          },
        }}
      >
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Typography variant="subtitle1" fontWeight="bold">
            Calculator
          </Typography>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <ToggleButtonGroup
              value={calculatorMode}
              exclusive
              onChange={handleModeChange}
              size="small"
              sx={{ mr: 1 }}
            >
              <ToggleButton value="basic" size="small">
                <Tooltip title="Basic">
                  <BasicIcon fontSize="small" />
                </Tooltip>
              </ToggleButton>
              <ToggleButton value="scientific" size="small">
                <Tooltip title="Scientific">
                  <ScienceIcon fontSize="small" />
                </Tooltip>
              </ToggleButton>
            </ToggleButtonGroup>
            <IconButton onClick={() => setOpen(false)} size="small">
              <CloseIcon fontSize="small" />
            </IconButton>
          </Box>
        </Box>

        <Paper
          elevation={0}
          sx={{
            p: 1.5,
            mb: 1.5,
            bgcolor: theme.palette.mode === 'light' ? theme.palette.grey[100] : theme.palette.grey[900],
            borderRadius: 2,
            textAlign: 'right',
            border: `1px solid ${theme.palette.divider}`,
          }}
        >
          <Typography variant="h5" component="div" sx={{ fontFamily: 'monospace' }}>
            {display}
          </Typography>
        </Paper>

        {calculatorMode === 'scientific' && (
          <Box sx={{ display: 'grid', gridTemplateColumns: 'repeat(5, 1fr)', gap: 0.5, mb: 1.5 }}>
            <Button
              variant="outlined"
              color="info"
              onClick={() => handleScientificFunction('sqrt')}
              size="small"
            >
              √x
            </Button>
            <Button
              variant="outlined"
              color="info"
              onClick={() => handleScientificFunction('square')}
              size="small"
            >
              x²
            </Button>
            <Button
              variant="outlined"
              color="info"
              onClick={() => handleScientificFunction('cube')}
              size="small"
            >
              x³
            </Button>
            <Button
              variant="outlined"
              color="info"
              onClick={() => handleOperationClick('x^y')}
              size="small"
            >
              x^y
            </Button>
            <Button
              variant="outlined"
              color="info"
              onClick={() => handleScientificFunction('1/x')}
              size="small"
            >
              1/x
            </Button>

            <Button
              variant="outlined"
              color="info"
              onClick={() => handleScientificFunction('sin')}
              size="small"
            >
              sin
            </Button>
            <Button
              variant="outlined"
              color="info"
              onClick={() => handleScientificFunction('cos')}
              size="small"
            >
              cos
            </Button>
            <Button
              variant="outlined"
              color="info"
              onClick={() => handleScientificFunction('tan')}
              size="small"
            >
              tan
            </Button>
            <Button
              variant="outlined"
              color="info"
              onClick={() => handleScientificFunction('log')}
              size="small"
            >
              log
            </Button>
            <Button
              variant="outlined"
              color="info"
              onClick={() => handleScientificFunction('ln')}
              size="small"
            >
              ln
            </Button>
          </Box>
        )}

        <Box sx={{ display: 'grid', gridTemplateColumns: 'repeat(4, 1fr)', gap: 0.5 }}>
          {/* First row */}
          <Button
            variant="outlined"
            color="error"
            onClick={clearDisplay}
            size="small"
          >
            C
          </Button>
          <Button
            variant="outlined"
            onClick={() => setDisplay(String(parseFloat(display) * -1))}
            size="small"
          >
            +/-
          </Button>
          <Button
            variant="outlined"
            onClick={() => setDisplay(String(parseFloat(display) / 100))}
            size="small"
          >
            %
          </Button>
          <Button
            variant="contained"
            color="primary"
            onClick={() => handleOperationClick('÷')}
            size="small"
          >
            ÷
          </Button>

          {/* Second row */}
          <Button
            variant="outlined"
            onClick={() => handleDigitClick('7')}
            size="small"
          >
            7
          </Button>
          <Button
            variant="outlined"
            onClick={() => handleDigitClick('8')}
            size="small"
          >
            8
          </Button>
          <Button
            variant="outlined"
            onClick={() => handleDigitClick('9')}
            size="small"
          >
            9
          </Button>
          <Button
            variant="contained"
            color="primary"
            onClick={() => handleOperationClick('×')}
            size="small"
          >
            ×
          </Button>

          {/* Third row */}
          <Button
            variant="outlined"
            onClick={() => handleDigitClick('4')}
            size="small"
          >
            4
          </Button>
          <Button
            variant="outlined"
            onClick={() => handleDigitClick('5')}
            size="small"
          >
            5
          </Button>
          <Button
            variant="outlined"
            onClick={() => handleDigitClick('6')}
            size="small"
          >
            6
          </Button>
          <Button
            variant="contained"
            color="primary"
            onClick={() => handleOperationClick('-')}
            size="small"
          >
            -
          </Button>

          {/* Fourth row */}
          <Button
            variant="outlined"
            onClick={() => handleDigitClick('1')}
            size="small"
          >
            1
          </Button>
          <Button
            variant="outlined"
            onClick={() => handleDigitClick('2')}
            size="small"
          >
            2
          </Button>
          <Button
            variant="outlined"
            onClick={() => handleDigitClick('3')}
            size="small"
          >
            3
          </Button>
          <Button
            variant="contained"
            color="primary"
            onClick={() => handleOperationClick('+')}
            size="small"
          >
            +
          </Button>

          {/* Fifth row */}
          <Button
            variant="outlined"
            onClick={() => handleDigitClick('0')}
            sx={{ gridColumn: 'span 2' }}
            size="small"
          >
            0
          </Button>
          <Button
            variant="outlined"
            onClick={handleDecimalClick}
            size="small"
          >
            .
          </Button>
          <Button
            variant="contained"
            color="secondary"
            onClick={handleEqualsClick}
            size="small"
          >
            =
          </Button>
        </Box>
      </Drawer>
    </>
  );
};

export default DraggableCalculator;
