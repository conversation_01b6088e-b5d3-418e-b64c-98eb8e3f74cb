import React from 'react';
import { IconButton, Tooltip, useTheme } from '@mui/material';
import { Brightness4 as DarkModeIcon, Brightness7 as LightModeIcon } from '@mui/icons-material';
import { useThemeMode } from '../contexts/ThemeContext';
import { motion } from 'framer-motion';

const ThemeToggle: React.FC = () => {
  const { mode, toggleColorMode } = useThemeMode();
  const theme = useTheme();

  return (
    <Tooltip title={mode === 'light' ? 'Switch to dark mode' : 'Switch to light mode'}>
      <IconButton
        onClick={toggleColorMode}
        color="inherit"
        aria-label="toggle light/dark mode"
        sx={{
          position: 'relative',
          overflow: 'hidden',
        }}
      >
        <motion.div
          initial={{ rotate: 0 }}
          animate={{ rotate: mode === 'light' ? 0 : 180 }}
          transition={{ duration: 0.5, type: 'spring', stiffness: 100 }}
        >
          {mode === 'light' ? (
            <DarkModeIcon sx={{ color: theme.palette.primary.main }} />
          ) : (
            <LightModeIcon sx={{ color: theme.palette.primary.light }} />
          )}
        </motion.div>
      </IconButton>
    </Tooltip>
  );
};

export default ThemeToggle;
