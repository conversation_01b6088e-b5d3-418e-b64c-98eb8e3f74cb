import React, { useState, useEffect } from 'react';
import {
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON>,
  <PERSON>u,
  <PERSON>uItem,
  Typo<PERSON>,
  Box,
  Divider,
  Button,
  ListItemIcon,
  ListItemText,
  Chip,
  useTheme,
  useMediaQuery,
  CircularProgress,
  Alert
} from '@mui/material';
import {
  Notifications as NotificationsIcon,
  NotificationsNone as NotificationsNoneIcon,
  Circle as CircleIcon,
  CheckCircle as CheckCircleIcon,
  Delete as DeleteIcon,
  DoneAll as DoneAllIcon,
  BookOnline as BookingIcon,
  CheckCircleOutline as AcceptedIcon,
  Cancel as RejectedIcon,
  Info as InfoIcon,
  EmojiEvents as AchievementIcon,
  Alarm as ReminderIcon
} from '@mui/icons-material';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { format, formatDistanceToNow } from 'date-fns';
import { useNavigate } from 'react-router-dom';
import {
  getNotifications,
  getUnreadNotificationCount,
  markNotificationAsRead,
  markAllNotificationsAsRead,
  deleteNotification,
  Notification,
  NotificationType
} from '../../api/notifications';
import { useWebSocketChat, RealtimeNotification } from '../../hooks/useWebSocketChat';

const NotificationBell: React.FC = () => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const queryClient = useQueryClient();
  const navigate = useNavigate();

  const [anchorEl, setAnchorEl] = useState<null | HTMLElement>(null);
  const open = Boolean(anchorEl);

  // WebSocket for real-time notifications
  const {
    realtimeNotifications,
    unreadNotificationCount: realtimeUnreadCount,
    markNotificationAsRead: markRealtimeAsRead,
    clearNotification,
    addNotificationHandler
  } = useWebSocketChat();

  // Fetch unread count (fallback for when WebSocket is not connected)
  const { data: apiUnreadCount = 0, refetch: refetchUnreadCount } = useQuery({
    queryKey: ['notifications', 'unread-count'],
    queryFn: getUnreadNotificationCount,
    refetchInterval: 30000, // Refetch every 30 seconds
    staleTime: 10000, // 10 seconds
  });

  // Fetch notifications when menu is opened (fallback for when WebSocket is not connected)
  const {
    data: apiNotifications = [],
    isLoading,
    error,
    refetch: refetchNotifications
  } = useQuery({
    queryKey: ['notifications'],
    queryFn: () => getNotifications(0, 20), // Get latest 20 notifications
    enabled: open, // Only fetch when menu is open
    staleTime: 5000, // 5 seconds
  });

  // Combine real-time and API notifications, prioritizing real-time
  const notifications = realtimeNotifications.length > 0 ?
    realtimeNotifications.map(notif => ({
      ...notif,
      type: notif.type as NotificationType
    })) :
    apiNotifications;

  // Use real-time unread count if available, otherwise fall back to API
  const unreadCount = realtimeUnreadCount > 0 ? realtimeUnreadCount : apiUnreadCount;

  // Mark notification as read mutation
  const markAsReadMutation = useMutation({
    mutationFn: markNotificationAsRead,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['notifications'] });
      refetchUnreadCount();
    },
  });

  // Mark all as read mutation
  const markAllAsReadMutation = useMutation({
    mutationFn: markAllNotificationsAsRead,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['notifications'] });
      refetchUnreadCount();
    },
  });

  // Delete notification mutation
  const deleteNotificationMutation = useMutation({
    mutationFn: deleteNotification,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['notifications'] });
      refetchUnreadCount();
    },
  });

  const handleClick = (event: React.MouseEvent<HTMLElement>) => {
    setAnchorEl(event.currentTarget);
  };

  const handleClose = () => {
    setAnchorEl(null);
  };

  const handleNotificationClick = (notification: Notification | RealtimeNotification) => {
    if (!notification.is_read) {
      // Mark as read in both real-time state and API
      if (realtimeNotifications.some(n => n.id === notification.id)) {
        markRealtimeAsRead(notification.id);
      }
      markAsReadMutation.mutate(notification.id);
    }

    // Handle navigation based on notification type
    if (notification.type === NotificationType.BOOKING_ACCEPTED && 'chat_room_id' in notification && notification.chat_room_id) {
      // Navigate to chat room for booking acceptance
      navigate(`/chat/${notification.chat_room_id}`);
    }

    handleClose();
  };

  const handleMarkAllAsRead = () => {
    markAllAsReadMutation.mutate();
  };

  const handleDeleteNotification = (notificationId: number, event: React.MouseEvent) => {
    event.stopPropagation();

    // Remove from real-time state if it exists there
    if (realtimeNotifications.some(n => n.id === notificationId)) {
      clearNotification(notificationId);
    }

    // Also delete from API
    deleteNotificationMutation.mutate(notificationId);
  };

  const getNotificationIcon = (type: NotificationType) => {
    switch (type) {
      case NotificationType.BOOKING_REQUEST:
        return <BookingIcon fontSize="small" />;
      case NotificationType.BOOKING_ACCEPTED:
        return <AcceptedIcon fontSize="small" color="success" />;
      case NotificationType.BOOKING_REJECTED:
        return <RejectedIcon fontSize="small" color="error" />;
      case NotificationType.ACHIEVEMENT:
        return <AchievementIcon fontSize="small" color="warning" />;
      case NotificationType.REMINDER:
        return <ReminderIcon fontSize="small" color="info" />;
      default:
        return <InfoIcon fontSize="small" />;
    }
  };

  const getNotificationColor = (type: NotificationType) => {
    switch (type) {
      case NotificationType.BOOKING_ACCEPTED:
        return 'success';
      case NotificationType.BOOKING_REJECTED:
        return 'error';
      case NotificationType.ACHIEVEMENT:
        return 'warning';
      case NotificationType.REMINDER:
        return 'info';
      default:
        return 'default';
    }
  };

  return (
    <>
      <IconButton
        color="inherit"
        onClick={handleClick}
        size={isMobile ? 'small' : 'medium'}
        sx={{
          color: theme.palette.mode === 'dark' ? 'white' : 'inherit'
        }}
      >
        <Badge badgeContent={unreadCount} color="error" max={99}>
          {unreadCount > 0 ? <NotificationsIcon /> : <NotificationsNoneIcon />}
        </Badge>
      </IconButton>

      <Menu
        anchorEl={anchorEl}
        open={open}
        onClose={handleClose}
        PaperProps={{
          sx: {
            width: isMobile ? '90vw' : 400,
            maxHeight: 500,
            overflow: 'auto'
          }
        }}
        transformOrigin={{ horizontal: 'right', vertical: 'top' }}
        anchorOrigin={{ horizontal: 'right', vertical: 'bottom' }}
      >
        {/* Header */}
        <Box sx={{ p: 2, borderBottom: `1px solid ${theme.palette.divider}` }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Typography variant="h6" fontWeight="bold">
              Notifications
            </Typography>
            {unreadCount > 0 && (
              <Button
                size="small"
                startIcon={<DoneAllIcon />}
                onClick={handleMarkAllAsRead}
                disabled={markAllAsReadMutation.isPending}
              >
                Mark all read
              </Button>
            )}
          </Box>
          {unreadCount > 0 && (
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <Typography variant="caption" color="text.secondary">
                {unreadCount} unread notification{unreadCount !== 1 ? 's' : ''}
              </Typography>
              {realtimeUnreadCount > 0 && (
                <Chip
                  label="Live"
                  size="small"
                  color="success"
                  variant="outlined"
                  sx={{ fontSize: '0.6rem', height: '16px' }}
                />
              )}
            </Box>
          )}
        </Box>

        {/* Content */}
        {isLoading ? (
          <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
            <CircularProgress size={24} />
          </Box>
        ) : error ? (
          <Box sx={{ p: 2 }}>
            <Alert severity="error" size="small">
              Failed to load notifications
            </Alert>
          </Box>
        ) : notifications.length === 0 ? (
          <Box sx={{ p: 3, textAlign: 'center' }}>
            <NotificationsNoneIcon sx={{ fontSize: 48, color: 'text.secondary', mb: 1 }} />
            <Typography variant="body2" color="text.secondary">
              No notifications yet
            </Typography>
          </Box>
        ) : (
          notifications.map((notification, index) => (
            <MenuItem
              key={notification.id}
              onClick={() => handleNotificationClick(notification)}
              sx={{
                py: 1.5,
                px: 2,
                borderBottom: index < notifications.length - 1 ? `1px solid ${theme.palette.divider}` : 'none',
                backgroundColor: notification.is_read ? 'transparent' : theme.palette.action.hover,
                '&:hover': {
                  backgroundColor: theme.palette.action.selected
                }
              }}
            >
              <ListItemIcon sx={{ minWidth: 40 }}>
                {getNotificationIcon(notification.type)}
              </ListItemIcon>
              <ListItemText
                primary={
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Typography variant="subtitle2" sx={{ flex: 1 }}>
                      {notification.title}
                    </Typography>
                    {!notification.is_read && (
                      <CircleIcon sx={{ fontSize: 8, color: 'primary.main' }} />
                    )}
                  </Box>
                }
                secondary={
                  <Box>
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 0.5 }}>
                      {notification.message}
                    </Typography>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                      <Typography variant="caption" color="text.secondary">
                        {formatDistanceToNow(new Date(notification.created_at), { addSuffix: true })}
                      </Typography>
                      <IconButton
                        size="small"
                        onClick={(e) => handleDeleteNotification(notification.id, e)}
                        disabled={deleteNotificationMutation.isPending}
                        sx={{ opacity: 0.7, '&:hover': { opacity: 1 } }}
                      >
                        <DeleteIcon fontSize="small" />
                      </IconButton>
                    </Box>
                  </Box>
                }
              />
            </MenuItem>
          ))
        )}
      </Menu>
    </>
  );
};

export default NotificationBell;
