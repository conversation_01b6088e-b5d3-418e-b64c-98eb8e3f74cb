import React from 'react';
import { Navigate, useParams, useLocation } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';

interface TutorRedirectProps {
  children: React.ReactNode;
  tutorPath: string;
  preserveParams?: boolean;
}

/**
 * Component that redirects tutors to their specific routes
 * while allowing students to access the original route
 */
const TutorRedirect: React.FC<TutorRedirectProps> = ({ children, tutorPath, preserveParams = false }) => {
  const { user } = useAuth();
  const params = useParams();
  const location = useLocation();

  // If user is a tutor, redirect to the tutor-specific path
  if (user?.role === 'tutor') {
    let redirectPath = tutorPath;

    // Handle special paths like /new and /edit
    if (location.pathname.endsWith('/new')) {
      redirectPath = `${tutorPath}/new`;
    } else if (location.pathname.includes('/edit') && params.id) {
      redirectPath = `${tutorPath}/${params.id}/edit`;
    } else if (preserveParams && params.id) {
      // If preserveParams is true and we have an id param, append it to the path
      redirectPath = `${tutorPath}/${params.id}`;
    }

    return <Navigate to={redirectPath} replace />;
  }

  // Otherwise, render the children (for students and admins)
  return <>{children}</>;
};

export default TutorRedirect;
