import React from 'react';
import { Box, Container, CssBaseline, useTheme, useMediaQuery } from '@mui/material';
import Header from './Header';
import BottomNavigation from './BottomNavigation';
import DesktopNavigation from './DesktopNavigation';
import PageTransition from '../PageTransition';
import { useNotificationHandler } from '../../hooks/useNotificationHandler';

interface MainLayoutProps {
  children: React.ReactNode;
}

const MainLayout: React.FC<MainLayoutProps> = ({ children }) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  // Initialize notification handler for real-time notifications
  useNotificationHandler({
    autoNavigateToChat: true,
    showToast: true
  });

  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        minHeight: '100vh',
        bgcolor: theme.palette.background.default,
      }}
    >
      <CssBaseline />
      <Header />

      <Container
        component="main"
        sx={{
          mt: 4,
          mb: isMobile ? '80px' : 4, // Add bottom margin for mobile bottom navigation
          flex: 1,
          position: 'relative',
          zIndex: 1,
        }}
      >
        {/* Desktop Navigation - shown only on desktop */}
        {!isMobile && <DesktopNavigation />}

        {children}
      </Container>

      {/* Bottom Navigation for mobile */}
      {isMobile && <BottomNavigation />}
    </Box>
  );
};

export default MainLayout;
