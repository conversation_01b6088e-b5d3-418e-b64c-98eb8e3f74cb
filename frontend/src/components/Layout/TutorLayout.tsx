import React from 'react';
import { Box, Container, CssBaseline, useTheme, useMediaQuery } from '@mui/material';
import { Outlet } from 'react-router-dom';
import Header from './Header';
import TutorSidebar from './TutorSidebar';
import BottomNavigation from './BottomNavigation';
import PageTransition from '../PageTransition';

const TutorLayout: React.FC = () => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        minHeight: '100vh',
        bgcolor: theme.palette.background.default,
      }}
    >
      <CssBaseline />
      <Header />
      <Container
        maxWidth="xl"
        sx={{
          flex: 1,
          py: { xs: 2, sm: 3 },
          px: { xs: 1.5, sm: 3 },
          pb: isMobile ? '80px' : 3, // Add bottom padding on mobile for the bottom navigation
        }}
      >
        <Box
          sx={{
            display: 'grid',
            gridTemplateColumns: isMobile ? '1fr' : '250px 1fr',
            gap: isMobile ? 2 : 3
          }}
        >
          {/* Tutor Sidebar - hidden on mobile */}
          {!isMobile && (
            <Box>
              <TutorSidebar />
            </Box>
          )}

          {/* Main content */}
          <Box>
            <Box
              sx={{
                maxWidth: '1200px',
                mx: 'auto',
                // Add some breathing room at the bottom on mobile
                mb: isMobile ? 2 : 0
              }}
            >
              <PageTransition>
                <Outlet />
              </PageTransition>
            </Box>
          </Box>
        </Box>
      </Container>

      {/* Bottom Navigation for mobile */}
      {isMobile && <BottomNavigation />}
    </Box>
  );
};

export default TutorLayout;
