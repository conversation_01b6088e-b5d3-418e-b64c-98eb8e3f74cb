import React from 'react';
import { Box, Container, Grid, useTheme, useMediaQuery } from '@mui/material';
import Header from './Header';
import Footer from './Footer';
import Sidebar from './Sidebar';
import BottomNavigation from './BottomNavigation';
import PageTransition from '../PageTransition';
import FloatingActionButton from '../FloatingActionButton';

interface DashboardLayoutProps {
  children: React.ReactNode;
}

const DashboardLayout: React.FC<DashboardLayoutProps> = ({ children }) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        minHeight: '100vh',
        bgcolor: theme.palette.background.default,
      }}
    >
      <Header />
      <Container
        maxWidth="xl"
        sx={{
          flex: 1,
          py: { xs: 2, sm: 3 },
          px: { xs: 1.5, sm: 3 },
          pb: isMobile ? '80px' : 3, // Add bottom padding on mobile for the bottom navigation
        }}
      >
        <Box
          sx={{
            display: 'grid',
            gridTemplateColumns: isMobile ? '1fr' : '250px 1fr',
            gap: isMobile ? 2 : 3
          }}
        >
          {/* Sidebar - hidden on mobile */}
          {!isMobile && (
            <Box>
              <Sidebar />
            </Box>
          )}

          {/* Main content */}
          <Box>
            <Box
              sx={{
                maxWidth: '1200px',
                mx: 'auto',
                // Add some breathing room at the bottom on mobile
                mb: isMobile ? 2 : 0
              }}
            >
              <PageTransition>
                {children}
              </PageTransition>
            </Box>
          </Box>
        </Box>
      </Container>

      {/* Show Footer only on desktop */}
      {!isMobile && <Footer />}

      {/* Mobile-specific components */}
      {isMobile && (
        <>
          <BottomNavigation />
          <FloatingActionButton />
        </>
      )}
    </Box>
  );
};

export default DashboardLayout;
