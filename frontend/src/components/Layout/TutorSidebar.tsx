import React from 'react';
import {
  Box,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Divider,
  Typography,
  useTheme,
  Paper,
  Avatar,
  Tooltip
} from '@mui/material';
import {
  Dashboard,
  Person,
  Event,
  AttachMoney,
  Star,
  Analytics,
  School,
  Settings,
  AccountCircle,
  Notifications,
  Chat,
} from '@mui/icons-material';
import { Link as RouterLink, useLocation } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { motion } from 'framer-motion';
import ThemeToggle from '../ThemeToggle';

const TutorSidebar: React.FC = () => {
  const { user } = useAuth();
  const theme = useTheme();
  const location = useLocation();

  // Define tutor-specific menu items
  const menuItems = [
    { text: 'Dashboard', icon: <Dashboard />, path: '/tutor/dashboard' },
    { text: 'Profile', icon: <Person />, path: '/tutor/profile' },
    { text: 'Booking Requests', icon: <Notifications />, path: '/tutor/booking-requests' },
    { text: 'Chat', icon: <Chat />, path: '/chat' },
    { text: 'Tutorials', icon: <Event />, path: '/tutor/tutorials' },
    { text: 'Earnings', icon: <AttachMoney />, path: '/tutor/earnings' },
    { text: 'Reviews', icon: <Star />, path: '/tutor/reviews' },
    { text: 'Analytics', icon: <Analytics />, path: '/tutor/analytics' },
  ];

  const MotionListItem = motion.create(ListItem);
  const MotionList = motion.create(List);

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { x: -20, opacity: 0 },
    visible: { x: 0, opacity: 1 }
  };

  return (
    <Paper
      elevation={0}
      sx={{
        height: 'fit-content',
        borderRadius: 2,
        border: `1px solid ${theme.palette.divider}`,
        position: 'sticky',
        top: 20,
      }}
    >
      {/* User profile section */}
      <Box sx={{ p: 2, borderBottom: `1px solid ${theme.palette.divider}` }}>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Avatar
            src={user?.profile_picture_url}
            sx={{ width: 40, height: 40, mr: 2 }}
          >
            {user?.full_name?.charAt(0)}
          </Avatar>
          <Box sx={{ flex: 1, minWidth: 0 }}>
            <Typography variant="subtitle2" noWrap>
              {user?.full_name}
            </Typography>
            <Typography variant="caption" color="text.secondary" noWrap>
              Tutor
            </Typography>
          </Box>
        </Box>

        <Box sx={{ mt: 1 }}>
          <ThemeToggle />
        </Box>
      </Box>

      {/* Navigation menu */}
      <MotionList
        variants={containerVariants}
        initial="hidden"
        animate="visible"
        sx={{ px: 1 }}
      >
        {menuItems.map((item) => (
          <MotionListItem
            key={item.text}
            component={RouterLink}
            to={item.path}
            variants={itemVariants}
            sx={{
              borderRadius: 2,
              mb: 0.5,
              color: location.pathname === item.path ? 'primary.main' : 'text.primary',
              bgcolor: location.pathname === item.path
                ? theme.palette.mode === 'light'
                  ? 'rgba(0, 0, 0, 0.04)'
                  : 'rgba(255, 255, 255, 0.05)'
                : 'transparent',
              '&:hover': {
                bgcolor: theme.palette.mode === 'light'
                  ? 'rgba(0, 0, 0, 0.08)'
                  : 'rgba(255, 255, 255, 0.08)',
              },
            }}
          >
            <ListItemIcon
              sx={{
                color: location.pathname === item.path ? 'primary.main' : 'inherit',
                minWidth: 40,
              }}
            >
              {item.icon}
            </ListItemIcon>
            <ListItemText
              primary={item.text}
              primaryTypographyProps={{
                fontSize: '0.875rem',
                fontWeight: location.pathname === item.path ? 600 : 400,
              }}
            />
          </MotionListItem>
        ))}
      </MotionList>

      <Divider sx={{ mx: 2 }} />

      {/* Settings section */}
      <List sx={{ px: 1, pb: 1 }}>
        <ListItem
          component={RouterLink}
          to="/profile"
          sx={{
            borderRadius: 2,
            '&:hover': {
              bgcolor: theme.palette.mode === 'light'
                ? 'rgba(0, 0, 0, 0.08)'
                : 'rgba(255, 255, 255, 0.08)',
            },
          }}
        >
          <ListItemIcon sx={{ minWidth: 40 }}>
            <Settings />
          </ListItemIcon>
          <ListItemText
            primary="Settings"
            primaryTypographyProps={{
              fontSize: '0.875rem',
            }}
          />
        </ListItem>
      </List>
    </Paper>
  );
};

export default TutorSidebar;
