import React, { useEffect } from 'react';
import { Box, CssBaseline, useTheme, GlobalStyles } from '@mui/material';

interface ExamLayoutProps {
  children: React.ReactNode;
}

const ExamLayout: React.FC<ExamLayoutProps> = ({ children }) => {
  const theme = useTheme();

  // Add effect to hide any global elements that might be rendered outside our control
  useEffect(() => {
    // Save the original body style
    const originalBodyStyle = document.body.style.cssText;
    const originalHtmlStyle = document.documentElement.style.cssText;

    // Apply styles to ensure full-screen mode
    document.body.style.overflow = 'auto';
    document.body.style.padding = '0';
    document.body.style.margin = '0';
    document.body.style.height = 'auto';
    document.body.style.position = 'static';

    document.documentElement.style.overflow = 'auto';
    document.documentElement.style.height = 'auto';

    // Clean up when component unmounts
    return () => {
      document.body.style.cssText = originalBodyStyle;
      document.documentElement.style.cssText = originalHtmlStyle;
    };
  }, []);

  return (
    <Box
      sx={{
        display: 'flex',
        flexDirection: 'column',
        minHeight: '100vh',
        width: '100%',
        bgcolor: theme.palette.background.default,
        position: 'static',
        zIndex: 'auto',
      }}
    >
      <CssBaseline />
      <GlobalStyles
        styles={{
          'html': {
            overflow: 'auto',
            height: 'auto',
          },
          'body': {
            overflow: 'auto',
            margin: 0,
            padding: 0,
            height: 'auto',
            position: 'static',
          },
          '#root': {
            minHeight: '100vh',
            overflow: 'auto',
            position: 'static',
          },
          // Hide any navigation elements that might be rendered outside our control
          'header, footer, nav': {
            display: 'none !important',
          },
          // Ensure all elements are clickable
          '*': {
            pointerEvents: 'auto',
          }
        }}
      />
      <Box
        component="main"
        sx={{
          flex: 1,
          position: 'static',
          width: '100%',
          maxWidth: '1200px',
          margin: '0 auto',
          p: 3,
          overflow: 'visible',
          pointerEvents: 'auto'
        }}
      >
        {children}
      </Box>
    </Box>
  );
};

export default ExamLayout;
