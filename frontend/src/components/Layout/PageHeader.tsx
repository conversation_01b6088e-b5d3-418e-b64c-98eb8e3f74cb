import React from 'react';
import { Box, Typography, useTheme, useMediaQuery } from '@mui/material';
import { motion } from 'framer-motion';

interface PageHeaderProps {
  title: string;
  description?: string;
  subtitle?: string;
  icon?: React.ReactNode;
  action?: React.ReactNode;
}

const PageHeader: React.FC<PageHeaderProps> = ({ title, description, subtitle, icon, action }) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  return (
    <motion.div
      initial={{ opacity: 0, y: -20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
    >
      <Box
        sx={{
          display: 'flex',
          alignItems: { xs: 'stretch', sm: 'center' },
          justifyContent: 'space-between',
          mb: 3,
          flexDirection: { xs: 'column', sm: 'row' },
          gap: { xs: 2, sm: 3 },
        }}
      >
        <Box sx={{
          display: 'flex',
          alignItems: 'center',
          flexDirection: isMobile ? 'column' : 'row',
          textAlign: isMobile ? 'center' : 'left',
          flex: 1
        }}>
          {icon && (
            <Box
              sx={{
                mr: isMobile ? 0 : 2,
                mb: isMobile ? 1 : 0,
                color: theme.palette.primary.main,
              }}
            >
              {icon}
            </Box>
          )}
          <Box>
            <Typography
              variant={isMobile ? 'h5' : 'h4'}
              component="h1"
              fontWeight="bold"
              gutterBottom={!!(description || subtitle)}
            >
              {title}
            </Typography>
            {(description || subtitle) && (
              <Typography
                variant="body1"
                color="text.secondary"
                sx={{ maxWidth: '800px' }}
              >
                {description || subtitle}
              </Typography>
            )}
          </Box>
        </Box>

        {action && (
          <Box sx={{
            display: 'flex',
            alignItems: 'center',
            flexShrink: 0,
            width: { xs: '100%', sm: 'auto' }
          }}>
            {action}
          </Box>
        )}
      </Box>
    </motion.div>
  );
};

export default PageHeader;
