import React from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  LinearProgress,
  Rating,
  useTheme,
  Card,
  CardContent
} from '@mui/material';
import {
  Star as StarIcon,
  TrendingUp as TrendingUpIcon,
  People as PeopleIcon
} from '@mui/icons-material';
import { TutorReview } from '../api/tutors';
import { motion } from 'framer-motion';

interface ReviewStatsProps {
  reviews: TutorReview[];
  averageRating?: number;
  totalReviews?: number;
  compact?: boolean;
}

interface RatingDistribution {
  [key: number]: number;
}

const ReviewStats: React.FC<ReviewStatsProps> = ({ 
  reviews, 
  averageRating, 
  totalReviews,
  compact = false 
}) => {
  const theme = useTheme();

  // Calculate rating distribution
  const ratingDistribution: RatingDistribution = (reviews || []).reduce((acc, review) => {
    if (review && typeof review.rating === 'number') {
      acc[review.rating] = (acc[review.rating] || 0) + 1;
    }
    return acc;
  }, {} as RatingDistribution);

  // Calculate average if not provided
  const safeReviews = reviews || [];
  const calculatedAverage = averageRating ?? (
    safeReviews.length > 0
      ? safeReviews.reduce((sum, review) => sum + (review?.rating || 0), 0) / safeReviews.length
      : 0
  );

  // Ensure calculatedAverage is a valid number
  const safeAverage = typeof calculatedAverage === 'number' && !isNaN(calculatedAverage)
    ? calculatedAverage
    : 0;

  const totalCount = totalReviews || reviews.length;

  const getRatingPercentage = (rating: number) => {
    return totalCount > 0 ? ((ratingDistribution[rating] || 0) / totalCount) * 100 : 0;
  };

  const getRecentTrend = () => {
    if (safeReviews.length < 2) return null;
    
    // Get reviews from last 30 days
    const thirtyDaysAgo = new Date();
    thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30);
    
    const recentReviews = safeReviews.filter(review =>
      review?.created_at && new Date(review.created_at) > thirtyDaysAgo
    );
    
    if (recentReviews.length === 0) return null;
    
    const recentAverage = recentReviews.reduce((sum, review) => sum + (review?.rating || 0), 0) / recentReviews.length;
    const difference = recentAverage - safeAverage;
    
    return {
      value: difference,
      count: recentReviews.length
    };
  };

  const trend = getRecentTrend();

  if (compact) {
    return (
      <Card sx={{ mb: 2 }}>
        <CardContent sx={{ p: 2 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <Typography variant="h6" sx={{ mr: 1 }}>
                {safeAverage.toFixed(1)}
              </Typography>
              <Rating value={safeAverage} readOnly size="small" />
            </Box>
            <Typography variant="body2" color="text.secondary">
              {totalCount} reviews
            </Typography>
          </Box>
        </CardContent>
      </Card>
    );
  }

  return (
    <Paper sx={{ p: 3, mb: 3, borderRadius: 2 }}>
      <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
        <StarIcon sx={{ mr: 1, color: 'warning.main' }} />
        Rating Overview
      </Typography>
      
      <Grid container spacing={3}>
        {/* Overall Rating */}
        <Grid item xs={12} md={6}>
          <Box sx={{ textAlign: 'center', mb: 2 }}>
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ type: 'spring', stiffness: 200, delay: 0.1 }}
            >
              <Typography
                variant="h2"
                sx={{
                  fontWeight: 'bold',
                  color: 'warning.main',
                  fontSize: { xs: '2.5rem', sm: '3rem' }
                }}
              >
                {safeAverage.toFixed(1)}
              </Typography>
            </motion.div>
            <Rating value={safeAverage} readOnly size="large" sx={{ mb: 1 }} />
            <Typography variant="body2" color="text.secondary">
              Based on {totalCount} review{totalCount !== 1 ? 's' : ''}
            </Typography>
            
            {trend && (
              <Box sx={{ mt: 2, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                <TrendingUpIcon 
                  sx={{ 
                    mr: 0.5, 
                    fontSize: 16,
                    color: trend.value >= 0 ? 'success.main' : 'error.main'
                  }} 
                />
                <Typography 
                  variant="caption" 
                  sx={{ 
                    color: trend.value >= 0 ? 'success.main' : 'error.main',
                    fontWeight: 600
                  }}
                >
                  {trend.value >= 0 ? '+' : ''}{trend.value.toFixed(1)} this month
                </Typography>
                <Typography variant="caption" color="text.secondary" sx={{ ml: 1 }}>
                  ({trend.count} reviews)
                </Typography>
              </Box>
            )}
          </Box>
        </Grid>
        
        {/* Rating Distribution */}
        <Grid item xs={12} md={6}>
          <Typography variant="subtitle2" gutterBottom>
            Rating Distribution
          </Typography>
          {[5, 4, 3, 2, 1].map((rating) => (
            <motion.div
              key={rating}
              initial={{ opacity: 0, x: -20 }}
              animate={{ opacity: 1, x: 0 }}
              transition={{ delay: rating * 0.1 }}
            >
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                <Typography variant="body2" sx={{ minWidth: 20 }}>
                  {rating}
                </Typography>
                <StarIcon sx={{ fontSize: 16, color: 'warning.main', mx: 1 }} />
                <LinearProgress
                  variant="determinate"
                  value={getRatingPercentage(rating)}
                  sx={{ 
                    flex: 1, 
                    mx: 1, 
                    height: 8, 
                    borderRadius: 4,
                    bgcolor: theme.palette.grey[200],
                    '& .MuiLinearProgress-bar': {
                      borderRadius: 4,
                      bgcolor: theme.palette.warning.main
                    }
                  }}
                />
                <Typography variant="body2" sx={{ minWidth: 30, textAlign: 'right' }}>
                  {ratingDistribution[rating] || 0}
                </Typography>
              </Box>
            </motion.div>
          ))}
        </Grid>
      </Grid>

      {/* Additional Stats */}
      <Box sx={{ mt: 3, pt: 2, borderTop: `1px solid ${theme.palette.divider}` }}>
        <Grid container spacing={2}>
          <Grid item xs={6} sm={4}>
            <Box sx={{ textAlign: 'center' }}>
              <Typography variant="h6" color="success.main">
                {Math.round(getRatingPercentage(5) + getRatingPercentage(4))}%
              </Typography>
              <Typography variant="caption" color="text.secondary">
                Positive Reviews
              </Typography>
            </Box>
          </Grid>
          <Grid item xs={6} sm={4}>
            <Box sx={{ textAlign: 'center' }}>
              <Typography variant="h6" color="primary.main">
                {safeReviews.filter(r => r?.comment && r.comment.trim().length > 0).length}
              </Typography>
              <Typography variant="caption" color="text.secondary">
                With Comments
              </Typography>
            </Box>
          </Grid>
          <Grid item xs={12} sm={4}>
            <Box sx={{ textAlign: 'center' }}>
              <Typography variant="h6" color="info.main">
                {safeReviews.filter(r => r?.session_id).length}
              </Typography>
              <Typography variant="caption" color="text.secondary">
                After Sessions
              </Typography>
            </Box>
          </Grid>
        </Grid>
      </Box>
    </Paper>
  );
};

export default ReviewStats;
