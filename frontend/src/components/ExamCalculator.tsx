import React, { useState, useEffect } from 'react';
import {
  Box,
  Button,
  Paper,
  Typography,
  IconButton,
  useTheme,
  <PERSON>er,
  <PERSON>lt<PERSON>,
  ToggleButton,
  ToggleButtonGroup
} from '@mui/material';
import {
  Calculate as CalculateIcon,
  Backspace as BackspaceIcon,
  Close as CloseIcon,
  Science as ScienceIcon,
  Calculate as BasicIcon
} from '@mui/icons-material';

interface CalculatorProps {
  position?: 'left' | 'right';
}

const ExamCalculator: React.FC<CalculatorProps> = ({ position = 'right' }) => {
  const theme = useTheme();
  const [open, setOpen] = useState(false);
  const [display, setDisplay] = useState('0');
  const [memory, setMemory] = useState<number | null>(null);
  const [operation, setOperation] = useState<string | null>(null);
  const [waitingForOperand, setWaitingForOperand] = useState(false);
  const [calculatorMode, setCalculatorMode] = useState<'basic' | 'scientific'>('basic');

  const toggleCalculator = () => {
    console.log("Calculator toggle button clicked, current state:", open);
    setOpen(!open);
  };

  // Log when calculator is mounted
  useEffect(() => {
    console.log("ExamCalculator component mounted");
    return () => {
      console.log("ExamCalculator component unmounted");
    };
  }, []);

  // Log when open state changes
  useEffect(() => {
    console.log("Calculator open state changed to:", open);
  }, [open]);

  const clearDisplay = () => {
    setDisplay('0');
    setOperation(null);
    setMemory(null);
    setWaitingForOperand(false);
  };

  const handleDigitClick = (digit: string) => {
    if (waitingForOperand) {
      setDisplay(digit);
      setWaitingForOperand(false);
    } else {
      setDisplay(display === '0' ? digit : display + digit);
    }
  };

  const handleDecimalClick = () => {
    if (waitingForOperand) {
      setDisplay('0.');
      setWaitingForOperand(false);
    } else if (display.indexOf('.') === -1) {
      setDisplay(display + '.');
    }
  };

  const handleOperationClick = (op: string) => {
    const inputValue = parseFloat(display);

    if (memory === null) {
      setMemory(inputValue);
    } else if (operation) {
      const result = performCalculation(memory, inputValue, operation);
      setMemory(result);
      setDisplay(String(result));
    }

    setWaitingForOperand(true);
    setOperation(op);
  };

  const handleEqualsClick = () => {
    const inputValue = parseFloat(display);

    if (operation && memory !== null) {
      const result = performCalculation(memory, inputValue, operation);
      setDisplay(String(result));
      setMemory(null);
      setOperation(null);
      setWaitingForOperand(true);
    }
  };

  const handleBackspaceClick = () => {
    if (display.length > 1) {
      setDisplay(display.substring(0, display.length - 1));
    } else {
      setDisplay('0');
    }
  };

  const performCalculation = (a: number, b: number, op: string): number => {
    switch (op) {
      case '+':
        return a + b;
      case '-':
        return a - b;
      case '×':
        return a * b;
      case '÷':
        return b === 0 ? NaN : a / b;
      case 'x^y':
        return Math.pow(a, b);
      case 'log':
        return Math.log10(a);
      case 'ln':
        return Math.log(a);
      case 'sin':
        return Math.sin(a);
      case 'cos':
        return Math.cos(a);
      case 'tan':
        return Math.tan(a);
      default:
        return b;
    }
  };

  const handleScientificFunction = (func: string) => {
    const inputValue = parseFloat(display);
    let result: number;

    switch (func) {
      case 'sqrt':
        result = Math.sqrt(inputValue);
        break;
      case 'square':
        result = Math.pow(inputValue, 2);
        break;
      case 'cube':
        result = Math.pow(inputValue, 3);
        break;
      case '1/x':
        result = 1 / inputValue;
        break;
      case 'sin':
        result = Math.sin(inputValue);
        break;
      case 'cos':
        result = Math.cos(inputValue);
        break;
      case 'tan':
        result = Math.tan(inputValue);
        break;
      case 'log':
        result = Math.log10(inputValue);
        break;
      case 'ln':
        result = Math.log(inputValue);
        break;
      case 'pi':
        result = Math.PI;
        break;
      case 'e':
        result = Math.E;
        break;
      default:
        result = inputValue;
    }

    setDisplay(String(result));
    setWaitingForOperand(true);
  };

  const handleModeChange = (event: React.MouseEvent<HTMLElement>, newMode: 'basic' | 'scientific' | null) => {
    if (newMode !== null) {
      setCalculatorMode(newMode);
    }
  };

  return (
    <>
      <Tooltip title="Calculator">
        <IconButton
          onClick={toggleCalculator}
          color="primary"
          size="large"
          sx={{
            position: 'fixed',
            bottom: 80, // Higher from bottom to avoid blocking content
            [position]: 20,
            zIndex: 1000, // Lower z-index to not interfere with scrolling
            bgcolor: theme.palette.primary.main,
            color: theme.palette.primary.contrastText,
            width: 48, // Smaller size to be less intrusive
            height: 48,
            boxShadow: theme.shadows[6],
            '&:hover': {
              bgcolor: theme.palette.primary.dark,
              transform: 'scale(1.05)', // Less dramatic scale
            },
            transition: 'all 0.2s ease-in-out',
            // Ensure it doesn't block important content
            '@media (max-height: 600px)': {
              bottom: 60,
              width: 40,
              height: 40,
            },
          }}
        >
          <CalculateIcon fontSize="large" />
        </IconButton>
      </Tooltip>

      <Drawer
        anchor={position}
        open={open}
        onClose={() => setOpen(false)}
        variant="temporary"
        elevation={24}
        sx={{
          zIndex: 1100, // Lower z-index but still above the calculator button
          '& .MuiBackdrop-root': {
            backgroundColor: 'rgba(0, 0, 0, 0.5)',
          },
          '& .MuiPaper-root': {
            overflow: 'visible',
          },
        }}
        PaperProps={{
          sx: {
            width: calculatorMode === 'scientific' ? 360 : 280,
            p: 2,
            borderRadius: position === 'left' ? '0 8px 8px 0' : '8px 0 0 8px',
            transition: 'width 0.3s ease-in-out',
            boxShadow: theme.shadows[24],
          },
        }}
      >
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Typography variant="h6" fontWeight="bold">
            Calculator
          </Typography>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <ToggleButtonGroup
              value={calculatorMode}
              exclusive
              onChange={handleModeChange}
              size="small"
              sx={{ mr: 1 }}
            >
              <ToggleButton value="basic">
                <Tooltip title="Basic">
                  <BasicIcon fontSize="small" />
                </Tooltip>
              </ToggleButton>
              <ToggleButton value="scientific">
                <Tooltip title="Scientific">
                  <ScienceIcon fontSize="small" />
                </Tooltip>
              </ToggleButton>
            </ToggleButtonGroup>
            <IconButton onClick={() => setOpen(false)} size="small">
              <CloseIcon />
            </IconButton>
          </Box>
        </Box>

        <Paper
          elevation={0}
          sx={{
            p: 2,
            mb: 2,
            bgcolor: theme.palette.mode === 'light' ? theme.palette.grey[100] : theme.palette.grey[900],
            borderRadius: 2,
            textAlign: 'right',
            border: `1px solid ${theme.palette.divider}`,
          }}
        >
          <Typography variant="h4" component="div" sx={{ fontFamily: 'monospace' }}>
            {display}
          </Typography>
        </Paper>

        {calculatorMode === 'scientific' && (
          <Box sx={{ display: 'grid', gridTemplateColumns: 'repeat(5, 1fr)', gap: 1, mb: 2 }}>
            <Button
              variant="outlined"
              color="info"
              onClick={() => handleScientificFunction('sqrt')}
            >
              √x
            </Button>
            <Button
              variant="outlined"
              color="info"
              onClick={() => handleScientificFunction('square')}
            >
              x²
            </Button>
            <Button
              variant="outlined"
              color="info"
              onClick={() => handleScientificFunction('cube')}
            >
              x³
            </Button>
            <Button
              variant="outlined"
              color="info"
              onClick={() => handleOperationClick('x^y')}
            >
              x^y
            </Button>
            <Button
              variant="outlined"
              color="info"
              onClick={() => handleScientificFunction('1/x')}
            >
              1/x
            </Button>

            <Button
              variant="outlined"
              color="info"
              onClick={() => handleScientificFunction('sin')}
            >
              sin
            </Button>
            <Button
              variant="outlined"
              color="info"
              onClick={() => handleScientificFunction('cos')}
            >
              cos
            </Button>
            <Button
              variant="outlined"
              color="info"
              onClick={() => handleScientificFunction('tan')}
            >
              tan
            </Button>
            <Button
              variant="outlined"
              color="info"
              onClick={() => handleScientificFunction('log')}
            >
              log
            </Button>
            <Button
              variant="outlined"
              color="info"
              onClick={() => handleScientificFunction('ln')}
            >
              ln
            </Button>

            <Button
              variant="outlined"
              color="info"
              onClick={() => handleScientificFunction('pi')}
            >
              π
            </Button>
            <Button
              variant="outlined"
              color="info"
              onClick={() => handleScientificFunction('e')}
            >
              e
            </Button>
            <Button
              variant="outlined"
              color="info"
              onClick={() => setDisplay(String(Math.abs(parseFloat(display))))}
            >
              |x|
            </Button>
            <Button
              variant="outlined"
              color="info"
              onClick={() => setDisplay(String(Math.floor(parseFloat(display))))}
            >
              ⌊x⌋
            </Button>
            <Button
              variant="outlined"
              color="info"
              onClick={() => setDisplay(String(Math.ceil(parseFloat(display))))}
            >
              ⌈x⌉
            </Button>
          </Box>
        )}

        <Box sx={{ display: 'grid', gridTemplateColumns: 'repeat(4, 1fr)', gap: 1 }}>
          {/* First row */}
          <Button
            variant="outlined"
            color="error"
            onClick={clearDisplay}
          >
            C
          </Button>
          <Button
            variant="outlined"
            onClick={() => setDisplay(String(parseFloat(display) * -1))}
          >
            +/-
          </Button>
          <Button
            variant="outlined"
            onClick={() => setDisplay(String(parseFloat(display) / 100))}
          >
            %
          </Button>
          <Button
            variant="contained"
            color="primary"
            onClick={() => handleOperationClick('÷')}
          >
            ÷
          </Button>

          {/* Second row */}
          <Button
            variant="outlined"
            onClick={() => handleDigitClick('7')}
          >
            7
          </Button>
          <Button
            variant="outlined"
            onClick={() => handleDigitClick('8')}
          >
            8
          </Button>
          <Button
            variant="outlined"
            onClick={() => handleDigitClick('9')}
          >
            9
          </Button>
          <Button
            variant="contained"
            color="primary"
            onClick={() => handleOperationClick('×')}
          >
            ×
          </Button>

          {/* Third row */}
          <Button
            variant="outlined"
            onClick={() => handleDigitClick('4')}
          >
            4
          </Button>
          <Button
            variant="outlined"
            onClick={() => handleDigitClick('5')}
          >
            5
          </Button>
          <Button
            variant="outlined"
            onClick={() => handleDigitClick('6')}
          >
            6
          </Button>
          <Button
            variant="contained"
            color="primary"
            onClick={() => handleOperationClick('-')}
          >
            -
          </Button>

          {/* Fourth row */}
          <Button
            variant="outlined"
            onClick={() => handleDigitClick('1')}
          >
            1
          </Button>
          <Button
            variant="outlined"
            onClick={() => handleDigitClick('2')}
          >
            2
          </Button>
          <Button
            variant="outlined"
            onClick={() => handleDigitClick('3')}
          >
            3
          </Button>
          <Button
            variant="contained"
            color="primary"
            onClick={() => handleOperationClick('+')}
          >
            +
          </Button>

          {/* Fifth row */}
          <Button
            variant="outlined"
            onClick={() => handleDigitClick('0')}
            sx={{ gridColumn: 'span 2' }}
          >
            0
          </Button>
          <Button
            variant="outlined"
            onClick={handleDecimalClick}
          >
            .
          </Button>
          <Button
            variant="contained"
            color="secondary"
            onClick={handleEqualsClick}
          >
            =
          </Button>
        </Box>
      </Drawer>
    </>
  );
};

export default ExamCalculator;
