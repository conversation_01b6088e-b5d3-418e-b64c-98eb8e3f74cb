import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Chip,
  Alert,
  CircularProgress,
  useTheme,
  useMediaQuery,
  Divider,
  Card,
  CardContent,
  IconButton,
  Tooltip,
  But<PERSON>
} from '@mui/material';
import {
  ExpandMore as ExpandMoreIcon,
  Description as DescriptionIcon,
  School as SchoolIcon,
  AccessTime as TimeIcon,
  Lightbulb as ConceptIcon,
  Topic as TopicIcon,
  TextSnippet as LineIcon,
  Visibility as ViewIcon,
  Delete as DeleteIcon
} from '@mui/icons-material';
import { motion, AnimatePresence } from 'framer-motion';

import { getGeneratedNoteExplanations } from '../../api/studentTools';
import { getSummariesWithSharing } from '../../api/contentWithSharing';
import { GeneratedNoteExplanation } from '../../types/studentTools';
import ModernDocumentReader from './ModernDocumentReader';
import ShareButton from '../sharing/ShareButton';
import ContentTag from '../sharing/ContentTag';
import { ContentType, SharedContentItem } from '../../types/contentSharing';

// Component to format the enhanced notes content with markdown support
const FormattedContent: React.FC<{ content: string }> = ({ content }) => {

  // Function to parse inline markdown formatting
  const parseInlineFormatting = (text: string): React.ReactNode[] => {
    const parts: React.ReactNode[] = [];
    let currentIndex = 0;
    let partIndex = 0;

    // Handle **bold** text
    const boldRegex = /\*\*(.*?)\*\*/g;
    let match;

    while ((match = boldRegex.exec(text)) !== null) {
      // Add text before the match
      if (match.index > currentIndex) {
        parts.push(text.slice(currentIndex, match.index));
      }

      // Add bold text
      parts.push(
        <strong key={`bold-${partIndex++}`} style={{ fontWeight: 600 }}>
          {match[1]}
        </strong>
      );

      currentIndex = match.index + match[0].length;
    }

    // Add remaining text
    if (currentIndex < text.length) {
      parts.push(text.slice(currentIndex));
    }

    // If no bold formatting found, return original text
    if (parts.length === 0) {
      parts.push(text);
    }

    return parts;
  };

  const formatContent = (text: string) => {
    // Split content into sections by double newlines
    const sections = text.split('\n\n').filter(p => p.trim());

    return sections.map((section, index) => {
      const trimmed = section.trim();

      // Handle headers (lines ending with : or **text:**)
      if (trimmed.endsWith(':') || /\*\*(.*?):\*\*/.test(trimmed)) {
        let headerText = trimmed;

        // Remove markdown formatting from headers
        headerText = headerText.replace(/\*\*(.*?):\*\*/g, '$1:');
        headerText = headerText.replace(':', '');

        const level = headerText.split(' ').length <= 4 ? 'h3' : 'h4';
        return React.createElement(level, { key: index }, headerText);
      }

      // Handle bullet points (lines with * at start or containing bullet patterns)
      if (trimmed.includes('* **') || trimmed.startsWith('*') || trimmed.startsWith('-') || trimmed.startsWith('•')) {
        // Split into individual bullet points
        const bulletItems = trimmed.split(/\n?\s*\*\s+/).filter(item => item.trim());

        // If it starts with *, remove the first empty item
        if (trimmed.startsWith('*')) {
          bulletItems.shift();
        }

        if (bulletItems.length > 0) {
          return (
            <ul key={index}>
              {bulletItems.map((item, itemIndex) => {
                const cleanItem = item.trim().replace(/^\*+\s*/, '').replace(/^[-•]\s*/, '');
                return (
                  <li key={itemIndex}>
                    {parseInlineFormatting(cleanItem)}
                  </li>
                );
              })}
            </ul>
          );
        }
      }

      // Handle numbered lists
      if (/^\d+\./.test(trimmed)) {
        const numberedItems = trimmed.split(/\n?\s*\d+\.\s+/).filter(item => item.trim());
        numberedItems.shift(); // Remove first empty item

        if (numberedItems.length > 0) {
          return (
            <ol key={index}>
              {numberedItems.map((item, itemIndex) => (
                <li key={itemIndex}>
                  {parseInlineFormatting(item.trim())}
                </li>
              ))}
            </ol>
          );
        }
      }

      // Regular paragraphs with inline formatting
      return (
        <p key={index}>
          {parseInlineFormatting(trimmed)}
        </p>
      );
    });
  };

  return <>{formatContent(content)}</>;
};

const MyGeneratedNoteExplanations: React.FC = () => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  // State management
  const [summariesWithSharing, setSummariesWithSharing] = useState<SharedContentItem[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  // Extract regular explanation data for processing with error handling
  const explanations = summariesWithSharing
    .filter(item => item && item.content_data && item.content_data.id) // Filter out invalid items
    .map(item => item.content_data);
  const [selectedExplanation, setSelectedExplanation] = useState<GeneratedNoteExplanation | null>(null);
  const [expandedAccordion, setExpandedAccordion] = useState<string | false>(false);

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 }
  };

  // Load explanations on component mount
  useEffect(() => {
    loadExplanations();
  }, []);

  // Listen for refresh events
  useEffect(() => {
    const handleRefresh = () => {
      loadExplanations();
    };

    window.addEventListener('refreshGeneratedNoteExplanations', handleRefresh);
    return () => {
      window.removeEventListener('refreshGeneratedNoteExplanations', handleRefresh);
    };
  }, []);

  const loadExplanations = async () => {
    try {
      setLoading(true);
      setError(null);
      const response = await getSummariesWithSharing();
      setSummariesWithSharing(response);
    } catch (err: any) {
      console.error('Error loading explanations:', err);
      setError('Failed to load note explanations. Please try again.');
    } finally {
      setLoading(false);
    }
  };

  const handleAccordionChange = (panel: string) => (event: React.SyntheticEvent, isExpanded: boolean) => {
    setExpandedAccordion(isExpanded ? panel : false);
  };

  const handleViewExplanation = (explanation: GeneratedNoteExplanation) => {
    setSelectedExplanation(explanation);
  };

  const handleBackToList = () => {
    setSelectedExplanation(null);
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty.toLowerCase()) {
      case 'easy': return 'success';
      case 'medium': return 'warning';
      case 'hard': return 'error';
      default: return 'default';
    }
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: 200 }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ mb: 3 }}>
        {error}
      </Alert>
    );
  }

  if (selectedExplanation) {
    return (
      <motion.div
        initial={{ opacity: 0, scale: 0.95 }}
        animate={{ opacity: 1, scale: 1 }}
        exit={{ opacity: 0, scale: 0.95 }}
        style={{
          position: 'fixed',
          top: 0,
          left: 0,
          right: 0,
          bottom: 0,
          zIndex: 9999,
          backgroundColor: theme.palette.background.default,
          overflow: 'hidden'
        }}
      >
        <ModernDocumentReader
          explanation={selectedExplanation}
          onClose={handleBackToList}
        />
      </motion.div>
    );
  }

  return (
    <motion.div
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      {explanations.length === 0 ? (
        <motion.div variants={itemVariants}>
          <Alert severity="info">
            No note explanations found. Generate your first explanation to get started!
          </Alert>
        </motion.div>
      ) : (
        <Box sx={{ display: 'flex', flexDirection: 'column', gap: { xs: 1.5, sm: 2 } }}>
          <Typography variant="h6" gutterBottom sx={{ fontSize: { xs: '1.1rem', sm: '1.25rem' } }}>
            My Generated Explanations ({explanations.length})
          </Typography>

          <AnimatePresence>
            {explanations.map((explanation, index) => (
              <motion.div
                key={explanation.id}
                variants={itemVariants}
                layout
              >
                <Paper
                  sx={{
                    p: { xs: 2, sm: 2.5 },
                    cursor: 'pointer',
                    transition: 'all 0.2s ease-in-out',
                    '&:hover': {
                      transform: 'translateY(-1px)',
                      boxShadow: theme.shadows[3]
                    },
                    border: `1px solid ${theme.palette.divider}`
                  }}
                  onClick={() => handleViewExplanation(explanation)}
                >
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: { xs: 1.5, sm: 2 } }}>
                    <DescriptionIcon
                      color="primary"
                      sx={{
                        fontSize: { xs: '1.2rem', sm: '1.5rem' },
                        flexShrink: 0
                      }}
                    />

                    <Box sx={{ flex: 1, minWidth: 0 }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 0.5 }}>
                        <Typography
                          variant="subtitle1"
                          sx={{
                            fontWeight: 600,
                            fontSize: { xs: '0.95rem', sm: '1.1rem' },
                            lineHeight: 1.3,
                            flex: 1,
                            minWidth: 0
                          }}
                          noWrap
                        >
                          {explanation.title}
                        </Typography>
                        {/* Show sharing indicators if this summary is shared */}
                        {(() => {
                          const sharedItem = summariesWithSharing.find(item =>
                            item.content_data.id === explanation.id && item.is_shared
                          );
                          if (sharedItem) {
                            return (
                              <ContentTag
                                is_shared={true}
                                shared_by_name={sharedItem.shared_by_name}
                                tags={['shared']}
                                size="small"
                              />
                            );
                          }
                          return null;
                        })()}
                      </Box>

                      <Typography
                        variant="body2"
                        color="text.secondary"
                        sx={{
                          fontSize: { xs: '0.8rem', sm: '0.875rem' },
                          lineHeight: 1.4,
                          mb: 1,
                          display: '-webkit-box',
                          WebkitLineClamp: 2,
                          WebkitBoxOrient: 'vertical',
                          overflow: 'hidden'
                        }}
                      >
                        {explanation.overview}
                      </Typography>

                      <Box sx={{ display: 'flex', gap: 0.5, flexWrap: 'wrap', alignItems: 'center' }}>
                        {explanation.course_name && (
                          <Chip
                            label={explanation.course_name}
                            size="small"
                            color="primary"
                            variant="outlined"
                            sx={{ fontSize: '0.7rem', height: 20 }}
                          />
                        )}
                        <Chip
                          label={`${explanation.word_count} words`}
                          size="small"
                          variant="outlined"
                          sx={{ fontSize: '0.7rem', height: 20 }}
                        />
                        <Chip
                          label={new Date(explanation.created_at).toLocaleDateString()}
                          size="small"
                          variant="outlined"
                          sx={{ fontSize: '0.7rem', height: 20 }}
                        />
                      </Box>
                    </Box>

                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <ShareButton
                        content_type={ContentType.SUMMARY}
                        content_id={explanation.id}
                        content_title={explanation.title}
                        course_name={explanation.course_name}
                        variant="icon"
                        size="small"
                      />
                      <ViewIcon
                        color="action"
                        sx={{
                          fontSize: { xs: '1.1rem', sm: '1.3rem' },
                          flexShrink: 0
                        }}
                      />
                    </Box>
                  </Box>
                </Paper>
              </motion.div>
            ))}
          </AnimatePresence>
        </Box>
      )}
    </motion.div>
  );
};

export default MyGeneratedNoteExplanations;
