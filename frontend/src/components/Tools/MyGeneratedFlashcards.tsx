import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  Card,
  CardContent,
  Button,
  Grid,
  Chip,
  Alert,
  CircularProgress,
  IconButton,
  Divider,
  useTheme,
  useMediaQuery
} from '@mui/material';
import {
  PlayArrow as PlayIcon,
  Delete as DeleteIcon,
  Style as FlashcardIcon,
  Topic as TopicIcon,
  School as CourseIcon
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import { useQuery } from '@tanstack/react-query';
import { useNavigate } from 'react-router-dom';
import { format } from 'date-fns';

import { getGeneratedFlashcards } from '../../api/studentTools';
import { getFlashcardsWithSharing } from '../../api/contentWithSharing';
import ShareButton from '../sharing/ShareButton';
import ContentTag from '../sharing/ContentTag';
import DeleteSharedContentButton from '../sharing/DeleteSharedContentButton';
import { ContentType, SharedContentItem } from '../../types/contentSharing';

interface GeneratedFlashcard {
  id: number;
  front_content: string;
  back_content: string;
  topic: string;
  difficulty: string;
  card_type: string;
  course_name: string;
  created_at: string;
  updated_at: string;
}

const MyGeneratedFlashcards: React.FC = () => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const navigate = useNavigate();

  // Fetch generated flashcards with sharing info
  const {
    data: flashcardsWithSharing = [],
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ['flashcardsWithSharing'],
    queryFn: () => getFlashcardsWithSharing(),
    refetchOnWindowFocus: false,
    staleTime: 30000,
  });

  // Extract regular flashcard data for processing with error handling
  const flashcards = flashcardsWithSharing
    .filter(item => item && item.content_data && item.content_data.id) // Filter out invalid items
    .map(item => item.content_data);

  // Listen for refresh events
  useEffect(() => {
    const handleRefresh = () => {
      refetch();
    };

    window.addEventListener('refreshGeneratedFlashcards', handleRefresh);
    return () => {
      window.removeEventListener('refreshGeneratedFlashcards', handleRefresh);
    };
  }, [refetch]);

  // Group flashcards by course with proper error handling
  const flashcardsByCourse = flashcards.reduce((acc: Record<string, GeneratedFlashcard[]>, flashcard) => {
    // Handle cases where flashcard might be null/undefined or missing course_name
    if (!flashcard) return acc;

    const courseName = flashcard.course_name || 'Uncategorized';
    if (!acc[courseName]) {
      acc[courseName] = [];
    }
    acc[courseName].push(flashcard);
    return acc;
  }, {});

  // Sort course groups by the newest flashcard in each course (since flashcards are already ordered by newest first from backend)
  const sortedFlashcardEntries = Object.entries(flashcardsByCourse).sort((a, b) => {
    // Get the first (newest) flashcard from each course
    const newestFlashcardA = a[1][0];
    const newestFlashcardB = b[1][0];

    // Compare creation dates (newest first)
    const dateA = new Date(newestFlashcardA.created_at || 0);
    const dateB = new Date(newestFlashcardB.created_at || 0);

    return dateB.getTime() - dateA.getTime();
  });

  const handlePracticeFlashcards = (courseFlashcards: GeneratedFlashcard[], courseName: string) => {
    const flashcardIds = courseFlashcards.map(f => f.id);

    // Store in session storage
    sessionStorage.setItem('practiceFlashcardIds', JSON.stringify(flashcardIds));
    sessionStorage.setItem('practiceCourseName', courseName);

    // Navigate to practice page
    navigate('/flashcards/practice/generated');
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty.toLowerCase()) {
      case 'easy':
        return 'success';
      case 'medium':
        return 'warning';
      case 'hard':
        return 'error';
      default:
        return 'primary';
    }
  };

  const formatDate = (dateString: string) => {
    try {
      return format(new Date(dateString), 'MMM dd, yyyy');
    } catch {
      return 'Unknown date';
    }
  };

  if (isLoading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: 200 }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    // Provide more helpful error messages based on the error type
    let errorMessage = "Unable to load your generated flashcards.";

    if (error instanceof Error) {
      const errorStr = error.message.toLowerCase();
      if (errorStr.includes('network') || errorStr.includes('connection')) {
        errorMessage = "Network connection issue. Please check your internet connection.";
      } else if (errorStr.includes('timeout')) {
        errorMessage = "Request timed out. Please try again.";
      } else if (errorStr.includes('500')) {
        errorMessage = "Server temporarily unavailable. Please try again in a few minutes.";
      } else if (errorStr.includes('403') || errorStr.includes('unauthorized')) {
        errorMessage = "Access denied. Please log in again.";
      }
    }

    return (
      <Alert
        severity="error"
        sx={{ mb: 2 }}
        action={
          <Button
            color="inherit"
            size="small"
            onClick={() => refetch()}
            sx={{ ml: 1 }}
          >
            Retry
          </Button>
        }
      >
        {errorMessage}
      </Alert>
    );
  }

  if (flashcards.length === 0) {
    return (
      <Box sx={{ textAlign: 'center', py: 4 }}>
        <FlashcardIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
        <Typography variant="h6" color="text.secondary" gutterBottom>
          No flashcards generated yet
        </Typography>
        <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
          Generate flashcards from your uploaded notes to see them here.
        </Typography>
        <Button
          variant="contained"
          onClick={() => {
            // Switch to the generate tab
            const event = new CustomEvent('switchToGenerateTab');
            window.dispatchEvent(event);
          }}
        >
          Generate Flashcards
        </Button>
      </Box>
    );
  }

  return (
    <Box>
      <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
        <FlashcardIcon />
        My Generated Flashcards ({flashcards.length} total)
      </Typography>

      <Grid container spacing={3}>
        {sortedFlashcardEntries.map(([courseName, courseFlashcards]) => (
          <Grid item xs={12} md={6} lg={4} key={courseName}>
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3 }}
            >
              <Card sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
                <CardContent sx={{ flexGrow: 1 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <CourseIcon sx={{ mr: 1, color: 'primary.main' }} />
                      <Typography variant="h6">
                        {courseName}
                      </Typography>
                    </Box>
                    {/* Show sharing indicators if any flashcards in this course are shared */}
                    {(() => {
                      const courseSharedItems = flashcardsWithSharing.filter(item =>
                        item.content_data.course_name === courseName && item.is_shared
                      );
                      if (courseSharedItems.length > 0) {
                        const sharedBy = courseSharedItems[0].shared_by_name;
                        return (
                          <ContentTag
                            is_shared={true}
                            shared_by_name={sharedBy}
                            tags={['shared']}
                            size="small"
                          />
                        );
                      }
                      return null;
                    })()}
                  </Box>

                  <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                    {courseFlashcards.length} flashcard{courseFlashcards.length !== 1 ? 's' : ''}
                  </Typography>

                  {/* Show difficulty distribution */}
                  <Box sx={{ mb: 2 }}>
                    {['easy', 'medium', 'hard'].map(difficulty => {
                      const count = courseFlashcards.filter(f => f.difficulty.toLowerCase() === difficulty).length;
                      if (count === 0) return null;
                      return (
                        <Chip
                          key={difficulty}
                          label={`${difficulty}: ${count}`}
                          size="small"
                          color={getDifficultyColor(difficulty) as any}
                          sx={{ mr: 1, mb: 1 }}
                        />
                      );
                    })}
                  </Box>

                  {/* Show topics */}
                  <Box sx={{ mb: 2 }}>
                    <Typography variant="caption" color="text.secondary" sx={{ display: 'flex', alignItems: 'center', gap: 0.5, mb: 1 }}>
                      <TopicIcon fontSize="small" />
                      Topics:
                    </Typography>
                    <Box sx={{ maxHeight: 60, overflow: 'auto' }}>
                      {Array.from(new Set(courseFlashcards.map(f => f.topic))).slice(0, 3).map(topic => (
                        <Chip
                          key={topic}
                          label={topic}
                          size="small"
                          variant="outlined"
                          sx={{ mr: 0.5, mb: 0.5, fontSize: '0.7rem' }}
                        />
                      ))}
                      {Array.from(new Set(courseFlashcards.map(f => f.topic))).length > 3 && (
                        <Typography variant="caption" color="text.secondary">
                          +{Array.from(new Set(courseFlashcards.map(f => f.topic))).length - 3} more
                        </Typography>
                      )}
                    </Box>
                  </Box>

                  <Divider sx={{ my: 2 }} />

                  <Typography variant="caption" color="text.secondary">
                    Created: {formatDate(courseFlashcards[0]?.created_at)}
                  </Typography>
                </CardContent>

                <Box sx={{ p: 2, pt: 0, display: 'flex', gap: 1 }}>
                  <Button
                    variant="contained"
                    fullWidth
                    startIcon={<PlayIcon />}
                    onClick={() => handlePracticeFlashcards(courseFlashcards, courseName)}
                  >
                    Practice ({courseFlashcards.length} cards)
                  </Button>
                  {/* Check if any flashcards in this course are shared */}
                  {(() => {
                    const courseSharedItems = flashcardsWithSharing.filter(item =>
                      item.content_data.course_name === courseName && item.is_shared
                    );
                    const hasOwnContent = courseFlashcards.some(f =>
                      flashcardsWithSharing.find(item => item.content_id === f.id && !item.is_shared)
                    );

                    if (hasOwnContent) {
                      // Show share button if user owns content in this course
                      return (
                        <ShareButton
                          content_type={ContentType.FLASHCARD}
                          content_id={courseFlashcards[0].id}
                          content_ids={courseFlashcards.map(f => f.id)}
                          content_title={`${courseName} Flashcards (${courseFlashcards.length} cards)`}
                          course_name={courseName}
                          variant="icon"
                          size="medium"
                        />
                      );
                    } else if (courseSharedItems.length > 0) {
                      // Show delete button if all content is shared
                      return (
                        <DeleteSharedContentButton
                          content_type={ContentType.FLASHCARD}
                          content_id={courseSharedItems[0].content_id}
                          content_title={`${courseName} Flashcards`}
                          variant="icon"
                          size="medium"
                          onDeleted={() => refetch()}
                        />
                      );
                    }
                    return null;
                  })()}
                </Box>
              </Card>
            </motion.div>
          </Grid>
        ))}
      </Grid>
    </Box>
  );
};

export default MyGeneratedFlashcards;
