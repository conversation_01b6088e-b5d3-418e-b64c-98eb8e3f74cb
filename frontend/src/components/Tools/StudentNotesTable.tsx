import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton,
  Chip,
  Alert,
  CircularProgress,
  Button,
  Tooltip,
  useTheme,
  useMedia<PERSON>uery,
  Card,
  CardContent,
  Stack
} from '@mui/material';
import {
  Delete as DeleteIcon,
  PictureAsPdf as PdfIcon,
  Schedule as ScheduleIcon,
  Refresh as RefreshIcon,
  Quiz as QuizIcon,
  Style as FlashcardIcon,
  Summarize as SummaryIcon
} from '@mui/icons-material';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { motion, AnimatePresence } from 'framer-motion';
import { format } from 'date-fns';

import { getUploadedFiles, deleteGeminiFile } from '../../api/studentTools';
import { GeminiFile } from '../../types/studentTools';

const StudentNotesTable: React.FC = () => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const queryClient = useQueryClient();
  const [deletingFiles, setDeletingFiles] = useState<Set<string>>(new Set());
  const [error, setError] = useState<string | null>(null);

  // Fetch uploaded files
  const {
    data: filesResponse,
    isLoading,
    error: fetchError,
    refetch
  } = useQuery({
    queryKey: ['uploadedFiles'],
    queryFn: () => getUploadedFiles(),
    refetchOnWindowFocus: false,
    staleTime: 30000, // 30 seconds
  });

  // Listen for refresh events from upload component
  useEffect(() => {
    const handleRefresh = () => {
      refetch();
    };

    window.addEventListener('refreshNotes', handleRefresh);
    return () => window.removeEventListener('refreshNotes', handleRefresh);
  }, [refetch]);

  const handleDeleteFile = async (file: GeminiFile) => {
    if (deletingFiles.has(file.gemini_file_id)) return;

    setDeletingFiles(prev => new Set(prev).add(file.gemini_file_id));
    setError(null);

    try {
      await deleteGeminiFile(file.gemini_file_id);
      
      // Refresh the files list
      await refetch();
      
      console.log('File deleted successfully:', file.original_filename);
    } catch (err: any) {
      console.error('Error deleting file:', err);
      setError(`Failed to delete ${file.original_filename}: ${err.message || 'Unknown error'}`);
    } finally {
      setDeletingFiles(prev => {
        const newSet = new Set(prev);
        newSet.delete(file.gemini_file_id);
        return newSet;
      });
    }
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatDateTime = (dateString: string): string => {
    try {
      return format(new Date(dateString), 'MMM dd, yyyy HH:mm');
    } catch {
      return 'Invalid date';
    }
  };

  const isExpired = (autoDeleteTime: string): boolean => {
    try {
      return new Date(autoDeleteTime) < new Date();
    } catch {
      return false;
    }
  };

  if (isLoading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
        <CircularProgress />
      </Box>
    );
  }

  if (fetchError) {
    return (
      <Alert severity="error" sx={{ mb: 2 }}>
        Failed to load uploaded files. Please try again.
        <Button onClick={() => refetch()} sx={{ ml: 2 }}>
          Retry
        </Button>
      </Alert>
    );
  }

  // Backend now automatically filters out expired files
  const files = filesResponse?.files || [];

  if (files.length === 0) {
    return (
      <Box sx={{ textAlign: 'center', py: 4 }}>
        <PdfIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
        <Typography variant="h6" color="text.secondary" gutterBottom>
          No uploaded files found
        </Typography>
        <Typography variant="body2" color="text.secondary">
          Upload your first PDF file to get started with AI-powered content generation.
        </Typography>
      </Box>
    );
  }

  return (
    <Box>
      {error && (
        <Alert severity="error" sx={{ mb: 2 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      {/* Header */}
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
        <Typography variant="h6">
          Your Uploaded Files ({files.length})
        </Typography>
        <Button
          startIcon={<RefreshIcon />}
          onClick={() => refetch()}
          size="small"
          variant="outlined"
        >
          Refresh
        </Button>
      </Box>

      {/* Mobile View - Cards */}
      {isMobile ? (
        <Stack spacing={2}>
          <AnimatePresence>
            {files.map((file) => (
              <motion.div
                key={file.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ duration: 0.2 }}
              >
                <Card variant="outlined">
                  <CardContent>
                    <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 2 }}>
                      <PdfIcon color="error" sx={{ mt: 0.5 }} />
                      <Box sx={{ flex: 1, minWidth: 0 }}>
                        <Typography variant="subtitle2" noWrap>
                          {file.original_filename}
                        </Typography>
                        {file.course_name && (
                          <Chip
                            label={file.course_name}
                            size="small"
                            color="primary"
                            variant="outlined"
                            sx={{ mt: 1, mb: 1 }}
                          />
                        )}
                        <Typography variant="caption" color="text.secondary" display="block">
                          {formatFileSize(file.file_size)} • {formatDateTime(file.upload_time)}
                        </Typography>
                        <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                          <ScheduleIcon sx={{ fontSize: 16, mr: 0.5 }} />
                          <Typography variant="caption" color={isExpired(file.auto_delete_time) ? 'error' : 'text.secondary'}>
                            {isExpired(file.auto_delete_time) ? 'Expired' : `Expires ${formatDateTime(file.auto_delete_time)}`}
                          </Typography>
                        </Box>
                      </Box>
                      <IconButton
                        onClick={() => handleDeleteFile(file)}
                        disabled={deletingFiles.has(file.gemini_file_id)}
                        size="small"
                        color="error"
                      >
                        {deletingFiles.has(file.gemini_file_id) ? (
                          <CircularProgress size={20} />
                        ) : (
                          <DeleteIcon />
                        )}
                      </IconButton>
                    </Box>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </AnimatePresence>
        </Stack>
      ) : (
        /* Desktop View - Table */
        <TableContainer component={Paper} variant="outlined">
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>File</TableCell>
                <TableCell>Course</TableCell>
                <TableCell>Size</TableCell>
                <TableCell>Uploaded</TableCell>
                <TableCell>Expires</TableCell>
                <TableCell align="center">Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              <AnimatePresence>
                {files.map((file) => (
                  <motion.tr
                    key={file.id}
                    component={TableRow}
                    initial={{ opacity: 0 }}
                    animate={{ opacity: 1 }}
                    exit={{ opacity: 0 }}
                    transition={{ duration: 0.2 }}
                    hover
                  >
                    <TableCell>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <PdfIcon color="error" />
                        <Typography variant="body2" noWrap>
                          {file.original_filename}
                        </Typography>
                      </Box>
                    </TableCell>
                    <TableCell>
                      {file.course_name ? (
                        <Chip
                          label={file.course_name}
                          size="small"
                          color="primary"
                          variant="outlined"
                        />
                      ) : (
                        <Typography variant="body2" color="text.secondary">
                          No course
                        </Typography>
                      )}
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {formatFileSize(file.file_size)}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Typography variant="body2">
                        {formatDateTime(file.upload_time)}
                      </Typography>
                    </TableCell>
                    <TableCell>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                        <ScheduleIcon sx={{ fontSize: 16 }} />
                        <Typography 
                          variant="body2" 
                          color={isExpired(file.auto_delete_time) ? 'error' : 'text.secondary'}
                        >
                          {isExpired(file.auto_delete_time) ? 'Expired' : formatDateTime(file.auto_delete_time)}
                        </Typography>
                      </Box>
                    </TableCell>
                    <TableCell align="center">
                      <Tooltip title="Delete file">
                        <IconButton
                          onClick={() => handleDeleteFile(file)}
                          disabled={deletingFiles.has(file.gemini_file_id)}
                          size="small"
                          color="error"
                        >
                          {deletingFiles.has(file.gemini_file_id) ? (
                            <CircularProgress size={20} />
                          ) : (
                            <DeleteIcon />
                          )}
                        </IconButton>
                      </Tooltip>
                    </TableCell>
                  </motion.tr>
                ))}
              </AnimatePresence>
            </TableBody>
          </Table>
        </TableContainer>
      )}
    </Box>
  );
};

export default StudentNotesTable;
