import React, { useState, useEffect } from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  LinearProgress,
  Grid,
  Chip,
  Alert,
  IconButton,
  Tooltip,
  Divider,
  Collapse
} from '@mui/material';
import {
  CloudUpload as UploadIcon,
  Quiz as MCQIcon,
  Style as FlashcardIcon,
  Description as SummaryIcon,
  Refresh as RefreshIcon,
  Info as InfoIcon,
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon
} from '@mui/icons-material';
import { getUsageStatistics } from '../../api/studentTools';

interface UsageStats {
  current_usage: number;
  limit: number;
  remaining: number;
  is_limit_reached: boolean;
  month: string;
}

interface UsageData {
  student_id: number;
  month: string;
  usage_stats: {
    pdf_upload: UsageStats;
    mcq_generation: UsageStats;
    flashcard_generation: UsageStats;
    summary_generation: UsageStats;
  };
}

const UsageLimitsDisplay: React.FC = () => {
  const [usageData, setUsageData] = useState<UsageData | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [expanded, setExpanded] = useState(false);

  const fetchUsageStats = async () => {
    try {
      setLoading(true);
      setError(null);
      const data = await getUsageStatistics();
      setUsageData(data);
    } catch (err: any) {
      console.error('Error fetching usage statistics:', err);
      setError('Failed to load usage statistics');
    } finally {
      setLoading(false);
    }
  };

  useEffect(() => {
    fetchUsageStats();
  }, []);

  const getUsageTypeConfig = (type: string) => {
    const configs = {
      pdf_upload: {
        label: 'PDF Uploads',
        icon: <UploadIcon />,
        color: '#2196f3' as const,
        description: 'Upload PDF files for processing'
      },
      mcq_generation: {
        label: 'MCQ Generation',
        icon: <MCQIcon />,
        color: '#4caf50' as const,
        description: 'Generate multiple choice questions'
      },
      flashcard_generation: {
        label: 'Flashcard Generation',
        icon: <FlashcardIcon />,
        color: '#ff9800' as const,
        description: 'Generate study flashcards'
      },
      summary_generation: {
        label: 'Summary Generation',
        icon: <SummaryIcon />,
        color: '#9c27b0' as const,
        description: 'Generate note explanations and summaries'
      }
    };
    return configs[type as keyof typeof configs];
  };

  const getProgressColor = (usage: UsageStats) => {
    const percentage = (usage.current_usage / usage.limit) * 100;
    if (percentage >= 100) return 'error';
    if (percentage >= 80) return 'warning';
    return 'primary';
  };

  const formatMonth = (monthStr: string) => {
    const [year, month] = monthStr.split('-');
    const date = new Date(parseInt(year), parseInt(month) - 1);
    return date.toLocaleDateString('en-US', { month: 'long', year: 'numeric' });
  };

  if (loading) {
    return (
      <Card>
        <CardContent>
          <Box display="flex" alignItems="center" gap={2}>
            <LinearProgress sx={{ flexGrow: 1 }} />
            <Typography variant="body2" color="text.secondary">
              Loading usage statistics...
            </Typography>
          </Box>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Alert 
        severity="error" 
        action={
          <IconButton color="inherit" size="small" onClick={fetchUsageStats}>
            <RefreshIcon />
          </IconButton>
        }
      >
        {error}
      </Alert>
    );
  }

  if (!usageData) {
    return null;
  }

  return (
    <Card>
      <CardContent>
        <Box
          display="flex"
          alignItems="center"
          justifyContent="space-between"
          sx={{ cursor: 'pointer' }}
          onClick={() => setExpanded(!expanded)}
        >
          <Typography variant="h6" component="h2">
            Monthly Usage Limits
          </Typography>
          <Box display="flex" alignItems="center" gap={1}>
            <Chip
              label={formatMonth(usageData.month)}
              size="small"
              variant="outlined"
            />
            <Tooltip title="Refresh usage statistics">
              <IconButton
                size="small"
                onClick={(e) => {
                  e.stopPropagation();
                  fetchUsageStats();
                }}
              >
                <RefreshIcon />
              </IconButton>
            </Tooltip>
            <IconButton size="small">
              {expanded ? <ExpandLessIcon /> : <ExpandMoreIcon />}
            </IconButton>
          </Box>
        </Box>

        <Collapse in={expanded}>
          <Box sx={{ mt: 2 }}>
            <Alert
              severity="info"
              icon={<InfoIcon />}
              sx={{ mb: 3 }}
            >
              You have a monthly limit of 5 uses for each tool. Limits reset at the beginning of each month.
            </Alert>

            <Grid container spacing={2}>
          {Object.entries(usageData.usage_stats).map(([type, stats]) => {
            const config = getUsageTypeConfig(type);
            if (!config) return null;

            const percentage = (stats.current_usage / stats.limit) * 100;

            return (
              <Grid item xs={12} sm={6} key={type}>
                <Card variant="outlined">
                  <CardContent>
                    <Box display="flex" alignItems="center" gap={2} mb={2}>
                      <Box 
                        sx={{ 
                          color: config.color,
                          display: 'flex',
                          alignItems: 'center'
                        }}
                      >
                        {config.icon}
                      </Box>
                      <Box flexGrow={1}>
                        <Typography variant="subtitle1" fontWeight="medium">
                          {config.label}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          {config.description}
                        </Typography>
                      </Box>
                    </Box>

                    <Box mb={1}>
                      <Box display="flex" justifyContent="space-between" alignItems="center" mb={1}>
                        <Typography variant="body2" color="text.secondary">
                          Usage
                        </Typography>
                        <Typography variant="body2" fontWeight="medium">
                          {stats.current_usage} / {stats.limit}
                        </Typography>
                      </Box>
                      <LinearProgress
                        variant="determinate"
                        value={Math.min(percentage, 100)}
                        color={getProgressColor(stats)}
                        sx={{ height: 8, borderRadius: 4 }}
                      />
                    </Box>

                    <Box display="flex" justifyContent="space-between" alignItems="center">
                      <Typography variant="body2" color="text.secondary">
                        Remaining: {stats.remaining}
                      </Typography>
                      {stats.is_limit_reached && (
                        <Chip 
                          label="Limit Reached" 
                          size="small" 
                          color="error" 
                          variant="outlined"
                        />
                      )}
                    </Box>
                  </CardContent>
                </Card>
              </Grid>
            );
          })}
            </Grid>

            <Divider sx={{ my: 2 }} />

            <Typography variant="body2" color="text.secondary" textAlign="center">
              Need more usage? Contact support or upgrade your plan.
            </Typography>
          </Box>
        </Collapse>
      </CardContent>
    </Card>
  );
};

export default UsageLimitsDisplay;
