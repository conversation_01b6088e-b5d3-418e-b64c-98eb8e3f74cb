import React, { useState, useRef, useEffect } from 'react';
import {
  Box,
  Typography,
  IconButton,
  Chip,
  useTheme,
  useMediaQuery,
  Fab,
  Drawer,
  Di<PERSON>r,
  <PERSON><PERSON>r,
  Switch,
  FormControlLabel,
  TextField,
  InputAdornment,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Tooltip,
  Paper,
  Button,
  Stack
} from '@mui/material';
import {
  ArrowBack as ArrowBackIcon,
  Settings as SettingsIcon,
  Search as SearchIcon,
  Bookmark as BookmarkIcon,
  VolumeUp as VolumeUpIcon,
  VolumeOff as VolumeOffIcon,
  Chat as ChatIcon,
  Close as CloseIcon,
  TextFields as TextFieldsIcon,
  Palette as PaletteIcon,
  FindInPage as FindInPageIcon,
  BookmarkBorder as BookmarkBorderIcon,
  Send as SendIcon,
  Stop as StopIcon
} from '@mui/icons-material';
import { motion, AnimatePresence } from 'framer-motion';
import { GeneratedNoteExplanation } from '../../types/studentTools';
import { streamDocumentChat } from '../../api/studentTools';

interface ModernDocumentReaderProps {
  explanation: GeneratedNoteExplanation;
  onClose: () => void;
}

interface ReadingSettings {
  fontSize: number;
  lineHeight: number;
  isDarkMode: boolean;
  fontFamily: string;
}

interface ChatMessage {
  id: string;
  content: string;
  isUser: boolean;
  timestamp: Date;
  isTyping?: boolean;
}

const ModernDocumentReader: React.FC<ModernDocumentReaderProps> = ({
  explanation,
  onClose
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const contentRef = useRef<HTMLDivElement>(null);

  // State management
  const [toolsOpen, setToolsOpen] = useState(false);
  const [chatOpen, setChatOpen] = useState(false);
  const [searchTerm, setSearchTerm] = useState('');
  const [bookmarks, setBookmarks] = useState<number[]>([]);
  const [chatMessages, setChatMessages] = useState<ChatMessage[]>([]);
  const [chatInput, setChatInput] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const [isReading, setIsReading] = useState(false);
  const [speechSynthesis, setSpeechSynthesis] = useState<SpeechSynthesis | null>(null);

  // Reading settings
  const [settings, setSettings] = useState<ReadingSettings>({
    fontSize: 16,
    lineHeight: 1.6,
    isDarkMode: theme.palette.mode === 'dark',
    fontFamily: 'Inter, system-ui, sans-serif'
  });

  // Initialize speech synthesis and debug gemini_file_id
  useEffect(() => {
    console.log('ModernDocumentReader - explanation data:', {
      id: explanation.id,
      title: explanation.title,
      gemini_file_id: explanation.gemini_file_id,
      hasGeminiFileId: !!explanation.gemini_file_id
    });

    if ('speechSynthesis' in window) {
      setSpeechSynthesis(window.speechSynthesis);
      console.log('Speech synthesis initialized:', window.speechSynthesis);

      // Load voices (sometimes needed for proper initialization)
      const loadVoices = () => {
        const voices = window.speechSynthesis.getVoices();
        console.log('Available voices:', voices.length);
      };

      if (window.speechSynthesis.getVoices().length > 0) {
        loadVoices();
      } else {
        window.speechSynthesis.addEventListener('voiceschanged', loadVoices);
      }
    } else {
      console.log('Speech synthesis not supported');
    }
  }, [explanation]);

  // Cleanup speech synthesis on unmount
  useEffect(() => {
    return () => {
      if (speechSynthesis && isReading) {
        speechSynthesis.cancel();
      }
    };
  }, [speechSynthesis, isReading]);

  // Handle settings changes
  const handleSettingChange = (key: keyof ReadingSettings, value: any) => {
    setSettings(prev => ({ ...prev, [key]: value }));
  };

  // Text-to-speech functionality
  const handleTextToSpeech = () => {
    console.log('Text-to-speech clicked', { speechSynthesis, isReading });

    if (!speechSynthesis) {
      console.log('Speech synthesis not available');
      return;
    }

    if (isReading) {
      // Stop reading
      console.log('Stopping speech');
      speechSynthesis.cancel();
      setIsReading(false);
    } else {
      // Start reading
      console.log('Starting speech');
      const text = explanation.detailed_explanation;
      console.log('Text to read:', text.substring(0, 100) + '...');

      // Clean the text for better speech synthesis
      const cleanText = text.replace(/<[^>]*>/g, '').replace(/\*\*/g, '').replace(/\*/g, '');

      const utterance = new SpeechSynthesisUtterance(cleanText);
      utterance.rate = 0.8;
      utterance.pitch = 1;
      utterance.volume = 1;
      utterance.lang = 'en-US';

      // Set voice if available
      const voices = speechSynthesis.getVoices();
      if (voices.length > 0) {
        const englishVoice = voices.find(voice => voice.lang.startsWith('en'));
        if (englishVoice) {
          utterance.voice = englishVoice;
        }
      }

      utterance.onstart = () => {
        console.log('Speech started');
        setIsReading(true);
      };

      utterance.onend = () => {
        console.log('Speech ended');
        setIsReading(false);
      };

      utterance.onerror = (event) => {
        console.log('Speech error:', event);
        setIsReading(false);
      };

      // Cancel any existing speech first
      speechSynthesis.cancel();

      // Small delay to ensure cancellation is processed
      setTimeout(() => {
        speechSynthesis.speak(utterance);
        console.log('Speech utterance queued');
      }, 100);
    }
  };

  // Search functionality
  const handleSearch = (term: string) => {
    setSearchTerm(term);
    if (term && contentRef.current) {
      const walker = document.createTreeWalker(
        contentRef.current,
        NodeFilter.SHOW_TEXT,
        null
      );
      
      // Simple search highlighting (basic implementation)
      // In a real app, you'd want a more sophisticated search
    }
  };

  // Bookmark functionality
  const toggleBookmark = (position: number) => {
    setBookmarks(prev => 
      prev.includes(position) 
        ? prev.filter(b => b !== position)
        : [...prev, position]
    );
  };

  // Chat functionality
  const handleSendMessage = async () => {
    if (!chatInput.trim() || !explanation.gemini_file_id) return;

    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      content: chatInput.trim(),
      isUser: true,
      timestamp: new Date()
    };

    setChatMessages(prev => [...prev, userMessage]);
    const query = chatInput.trim();
    setChatInput('');
    setIsTyping(true);

    // Add typing indicator message
    const assistantMessageId = (Date.now() + 1).toString();
    const typingMessage: ChatMessage = {
      id: assistantMessageId,
      content: '',
      isUser: false,
      timestamp: new Date(),
      isTyping: true
    };
    setChatMessages(prev => [...prev, typingMessage]);

    // Use streaming API
    const abortStream = streamDocumentChat(explanation.gemini_file_id, query, (event) => {
      switch (event.type) {
        case 'start':
          // Stream has started, typing indicator is already shown
          break;

        case 'token':
          // Append the new token to the current message
          if (event.content) {
            setChatMessages(prev =>
              prev.map(msg =>
                msg.id === assistantMessageId
                  ? {
                      ...msg,
                      content: msg.content + event.content,
                      isTyping: false,
                    }
                  : msg
              )
            );
          }
          break;

        case 'error':
          // Handle error
          setChatMessages(prev =>
            prev.map(msg =>
              msg.id === assistantMessageId
                ? {
                    ...msg,
                    content: event.error || 'Sorry, I encountered an error. Please try again.',
                    isTyping: false,
                  }
                : msg
            )
          );
          setIsTyping(false);
          break;

        case 'end':
          // Stream completed
          setIsTyping(false);
          break;
      }
    });

    // Store abort function if needed for cleanup
    // You could store this in a ref if you need to abort streams
  };

  // Format content with proper styling
  const FormattedContent: React.FC<{ content: string }> = ({ content }) => {
    const parseInlineFormatting = (text: string) => {
      // Handle bold text
      text = text.replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>');
      // Handle italic text
      text = text.replace(/\*(.*?)\*/g, '<em>$1</em>');
      return text;
    };

    const formatContent = (content: string) => {
      const sections = content.split(/\n\s*\n/);
      
      return sections.map((section, index) => {
        const trimmed = section.trim();
        if (!trimmed) return null;

        // Handle headers
        if (trimmed.startsWith('# ')) {
          return (
            <Typography key={index} variant="h4" component="h1" sx={{
              mt: 4, mb: 2, fontWeight: 700,
              color: settings.isDarkMode ? '#90caf9' : 'primary.main',
              fontSize: `${settings.fontSize * 1.5}px`,
              lineHeight: settings.lineHeight,
              fontFamily: settings.fontFamily
            }}>
              {trimmed.substring(2)}
            </Typography>
          );
        }

        if (trimmed.startsWith('## ')) {
          return (
            <Typography key={index} variant="h5" component="h2" sx={{
              mt: 3, mb: 2, fontWeight: 600,
              color: settings.isDarkMode ? '#90caf9' : 'primary.main',
              fontSize: `${settings.fontSize * 1.3}px`,
              lineHeight: settings.lineHeight,
              fontFamily: settings.fontFamily
            }}>
              {trimmed.substring(3)}
            </Typography>
          );
        }

        if (trimmed.startsWith('### ')) {
          return (
            <Typography key={index} variant="h6" component="h3" sx={{
              mt: 2, mb: 1.5, fontWeight: 600,
              color: settings.isDarkMode ? '#90caf9' : 'primary.main',
              fontSize: `${settings.fontSize * 1.1}px`,
              lineHeight: settings.lineHeight,
              fontFamily: settings.fontFamily
            }}>
              {trimmed.substring(4)}
            </Typography>
          );
        }

        // Handle bullet lists
        if (trimmed.includes('*') && !trimmed.startsWith('*')) {
          const bulletItems = trimmed.split(/\n?\s*\*\s+/).filter(item => item.trim());
          if (trimmed.startsWith('*')) {
            bulletItems.shift();
          }

          if (bulletItems.length > 0) {
            return (
              <Box component="ul" key={index} sx={{
                pl: 3, mb: 2,
                '& li': {
                  lineHeight: settings.lineHeight,
                  mb: 1,
                  fontSize: `${settings.fontSize}px`,
                  fontFamily: settings.fontFamily,
                  color: settings.isDarkMode ? '#ffffff' : 'text.primary'
                }
              }}>
                {bulletItems.map((item, itemIndex) => {
                  const cleanItem = item.trim().replace(/^\*+\s*/, '').replace(/^[-•]\s*/, '');
                  return (
                    <li key={itemIndex} dangerouslySetInnerHTML={{ 
                      __html: parseInlineFormatting(cleanItem) 
                    }} />
                  );
                })}
              </Box>
            );
          }
        }

        // Regular paragraphs
        return (
          <Typography key={index} component="p" sx={{
            mb: 2,
            fontSize: `${settings.fontSize}px`,
            lineHeight: settings.lineHeight,
            fontFamily: settings.fontFamily,
            textAlign: 'justify',
            color: settings.isDarkMode ? '#ffffff' : 'text.primary'
          }} dangerouslySetInnerHTML={{
            __html: parseInlineFormatting(trimmed)
          }} />
        );
      });
    };

    return <>{formatContent(content)}</>;
  };

  return (
    <Box sx={{
      height: '100vh',
      display: 'flex',
      flexDirection: 'column',
      bgcolor: settings.isDarkMode ? 'grey.900' : 'background.default',
      // Account for the sticky header
      paddingTop: '64px'
    }}>
      {/* Header */}
      <Box sx={{
        p: { xs: 1.5, sm: 3 },
        borderBottom: `1px solid ${theme.palette.divider}`,
        bgcolor: settings.isDarkMode ? 'grey.800' : 'background.paper',
        flexShrink: 0,
        display: 'flex',
        alignItems: 'center',
        gap: { xs: 1, sm: 2 },
        minHeight: { xs: 56, sm: 64 }
      }}>
        <IconButton
          onClick={onClose}
          size={isMobile ? "medium" : "small"}
          sx={{
            minWidth: isMobile ? 44 : 'auto',
            minHeight: isMobile ? 44 : 'auto'
          }}
        >
          <ArrowBackIcon />
        </IconButton>
        
        <Typography
          variant="h6"
          sx={{
            flex: 1,
            fontSize: { xs: '1rem', sm: '1.3rem' },
            fontWeight: 600,
            noWrap: true,
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            // Ensure text doesn't take up too much space on mobile
            maxWidth: { xs: 'calc(100% - 120px)', sm: 'none' }
          }}
        >
          {explanation.title}
        </Typography>

        <Stack direction="row" spacing={{ xs: 0.5, sm: 1 }} sx={{ flexShrink: 0 }}>
          <Tooltip title="Reading Tools">
            <IconButton
              onClick={() => setToolsOpen(true)}
              size={isMobile ? "medium" : "small"}
              sx={{
                minWidth: { xs: 40, sm: 44 },
                minHeight: { xs: 40, sm: 44 },
                p: { xs: 1, sm: 1.5 }
              }}
            >
              <SettingsIcon fontSize={isMobile ? "medium" : "small"} />
            </IconButton>
          </Tooltip>

          <Tooltip title="Chat with Document">
            <IconButton
              onClick={() => setChatOpen(true)}
              size={isMobile ? "medium" : "small"}
              sx={{
                minWidth: { xs: 40, sm: 44 },
                minHeight: { xs: 40, sm: 44 },
                p: { xs: 1, sm: 1.5 },
                opacity: explanation.gemini_file_id ? 1 : 0.6
              }}
            >
              <ChatIcon fontSize={isMobile ? "medium" : "small"} />
            </IconButton>
          </Tooltip>
        </Stack>
      </Box>

      {/* Main Content Area */}
      <Box sx={{ 
        flex: 1, 
        display: 'flex',
        overflow: 'hidden'
      }}>
        {/* Document Content */}
        <Box sx={{
          flex: 1,
          overflow: 'auto',
          p: { xs: 2, sm: 4, md: 6 },
          bgcolor: settings.isDarkMode ? 'grey.900' : 'background.default',
          // Add smooth scrolling for better mobile experience
          scrollBehavior: 'smooth',
          // Improve touch scrolling on iOS
          WebkitOverflowScrolling: 'touch'
        }}>
          <Box sx={{
            maxWidth: '800px',
            mx: 'auto',
            // Add extra padding on mobile for better readability
            px: { xs: 1, sm: 0 }
          }}>
            {/* Document Metadata */}
            <Box sx={{ mb: { xs: 3, sm: 4 } }}>
              <Typography variant="body2" color="text.secondary" sx={{
                lineHeight: 1.6,
                fontSize: { xs: '0.9rem', sm: '0.95rem' },
                mb: 2
              }}>
                {explanation.overview}
              </Typography>
              <Stack direction="row" spacing={1} flexWrap="wrap">
                <Chip
                  label={`${explanation.word_count} words`}
                  size={isMobile ? "medium" : "small"}
                  variant="outlined"
                />
                <Chip
                  label={`${explanation.key_concepts.length} concepts`}
                  size={isMobile ? "medium" : "small"}
                  variant="outlined"
                />
                <Chip
                  label={`${explanation.main_topics.length} topics`}
                  size={isMobile ? "medium" : "small"}
                  variant="outlined"
                />
              </Stack>
            </Box>

            {/* Document Content */}
            <Box ref={contentRef} sx={{
              '& img': { maxWidth: '100%', height: 'auto' },
              '& blockquote': {
                borderLeft: `4px solid ${theme.palette.primary.main}`,
                pl: 2,
                ml: 0,
                fontStyle: 'italic',
                color: 'text.secondary'
              }
            }}>
              <FormattedContent content={explanation.detailed_explanation} />
            </Box>

            {/* Summary Section */}
            <Box sx={{ mt: 6, pt: 4, borderTop: `1px solid ${theme.palette.divider}` }}>
              <Typography variant="h5" gutterBottom color="primary.main" sx={{
                fontSize: `${settings.fontSize * 1.3}px`,
                fontWeight: 700,
                mb: 3,
                fontFamily: settings.fontFamily
              }}>
                Summary
              </Typography>
              <FormattedContent content={explanation.comprehensive_summary} />
            </Box>

            {/* Key Concepts */}
            <Box sx={{ mt: 4 }}>
              <Typography variant="h6" gutterBottom color="primary.main" sx={{
                fontSize: `${settings.fontSize * 1.1}px`,
                fontWeight: 600,
                mb: 2,
                fontFamily: settings.fontFamily
              }}>
                Key Concepts
              </Typography>
              <Stack direction="row" spacing={1} flexWrap="wrap">
                {explanation.key_concepts.map((concept, index) => (
                  <Chip 
                    key={index} 
                    label={concept} 
                    variant="outlined" 
                    color="primary"
                    sx={{ mb: 1 }}
                  />
                ))}
              </Stack>
            </Box>

            {/* Main Topics */}
            <Box sx={{ mt: 4, mb: 6 }}>
              <Typography variant="h6" gutterBottom color="primary.main" sx={{
                fontSize: `${settings.fontSize * 1.1}px`,
                fontWeight: 600,
                mb: 2,
                fontFamily: settings.fontFamily
              }}>
                Main Topics
              </Typography>
              <Stack direction="row" spacing={1} flexWrap="wrap">
                {explanation.main_topics.map((topic, index) => (
                  <Chip 
                    key={index} 
                    label={topic} 
                    variant="filled" 
                    color="secondary"
                    sx={{ mb: 1 }}
                  />
                ))}
              </Stack>
            </Box>
          </Box>
        </Box>
      </Box>

      {/* Mobile Floating Action Buttons - Backup for very small screens */}
      {isMobile && (
        <>
          <Fab
            color="primary"
            size="small"
            onClick={() => setToolsOpen(true)}
            sx={{
              position: 'fixed',
              bottom: 'calc(env(safe-area-inset-bottom, 0px) + 100px)',
              right: 16,
              zIndex: 10000,
              boxShadow: 4,
              '&:hover': {
                transform: 'scale(1.1)'
              }
            }}
          >
            <SettingsIcon fontSize="small" />
          </Fab>

          <Fab
            color="secondary"
            size="small"
            onClick={() => setChatOpen(true)}
            sx={{
              position: 'fixed',
              bottom: 'calc(env(safe-area-inset-bottom, 0px) + 160px)',
              right: 16,
              zIndex: 10000,
              boxShadow: 4,
              opacity: explanation.gemini_file_id ? 1 : 0.6,
              '&:hover': {
                transform: 'scale(1.1)'
              }
            }}
          >
            <ChatIcon fontSize="small" />
          </Fab>
        </>
      )}

      {/* Reading Tools Drawer */}
      <Drawer
        anchor={isMobile ? "bottom" : "right"}
        open={toolsOpen}
        onClose={() => setToolsOpen(false)}
        sx={{
          '& .MuiDrawer-paper': {
            width: { xs: '100%', sm: 350 },
            maxWidth: '90vw',
            height: isMobile ? '70vh' : 'auto',
            borderTopLeftRadius: isMobile ? 16 : 0,
            borderTopRightRadius: isMobile ? 16 : 0
          }
        }}
      >
        <Box sx={{ p: 3 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 3 }}>
            <Typography variant="h6" fontWeight={600}>
              Reading Tools
            </Typography>
            <IconButton onClick={() => setToolsOpen(false)} size="small">
              <CloseIcon />
            </IconButton>
          </Box>

          <Divider sx={{ mb: 3 }} />

          {/* Font Size */}
          <Box sx={{ mb: 3 }}>
            <Typography variant="subtitle2" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <TextFieldsIcon fontSize="small" />
              Font Size
            </Typography>
            <Slider
              value={settings.fontSize}
              onChange={(_, value) => handleSettingChange('fontSize', value)}
              min={12}
              max={24}
              step={1}
              marks
              valueLabelDisplay="auto"
              sx={{ mt: 1 }}
            />
          </Box>

          {/* Line Height */}
          <Box sx={{ mb: 3 }}>
            <Typography variant="subtitle2" gutterBottom>
              Line Height
            </Typography>
            <Slider
              value={settings.lineHeight}
              onChange={(_, value) => handleSettingChange('lineHeight', value)}
              min={1.2}
              max={2.0}
              step={0.1}
              marks
              valueLabelDisplay="auto"
              sx={{ mt: 1 }}
            />
          </Box>

          {/* Theme Toggle */}
          <Box sx={{ mb: 3 }}>
            <FormControlLabel
              control={
                <Switch
                  checked={settings.isDarkMode}
                  onChange={(e) => handleSettingChange('isDarkMode', e.target.checked)}
                />
              }
              label={
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <PaletteIcon fontSize="small" />
                  Dark Mode
                </Box>
              }
            />
          </Box>

          <Divider sx={{ my: 3 }} />

          {/* Search */}
          <Box sx={{ mb: 3 }}>
            <TextField
              fullWidth
              placeholder="Search in document..."
              value={searchTerm}
              onChange={(e) => handleSearch(e.target.value)}
              InputProps={{
                startAdornment: (
                  <InputAdornment position="start">
                    <SearchIcon />
                  </InputAdornment>
                )
              }}
              size="small"
            />
          </Box>

          {/* Text to Speech */}
          <Button
            fullWidth
            variant={isReading ? "contained" : "outlined"}
            color={isReading ? "error" : "primary"}
            startIcon={
              <motion.div
                animate={isReading ? { scale: [1, 1.2, 1] } : {}}
                transition={{ repeat: isReading ? Infinity : 0, duration: 1 }}
              >
                {isReading ? <StopIcon /> : <VolumeUpIcon />}
              </motion.div>
            }
            onClick={handleTextToSpeech}
            sx={{
              mb: 2,
              fontWeight: isReading ? 600 : 400,
              boxShadow: isReading ? 2 : 0,
              '&:hover': {
                boxShadow: isReading ? 4 : 1
              },
              transition: 'all 0.3s ease'
            }}
            disabled={!speechSynthesis}
          >
            {isReading ? 'STOP READING' : 'Start Reading'}
          </Button>

          {/* Bookmarks */}
          <Box sx={{ mb: 3 }}>
            <Typography variant="subtitle2" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <BookmarkIcon fontSize="small" />
              Bookmarks ({bookmarks.length})
            </Typography>
            {bookmarks.length === 0 ? (
              <Typography variant="body2" color="text.secondary">
                No bookmarks yet
              </Typography>
            ) : (
              <List dense>
                {bookmarks.map((bookmark, index) => (
                  <ListItem key={index} dense>
                    <ListItemIcon>
                      <BookmarkBorderIcon fontSize="small" />
                    </ListItemIcon>
                    <ListItemText primary={`Bookmark ${index + 1}`} />
                  </ListItem>
                ))}
              </List>
            )}
          </Box>
        </Box>
      </Drawer>

      {/* Chat Drawer */}
      <Drawer
        anchor={isMobile ? "bottom" : "right"}
        open={chatOpen}
        onClose={() => setChatOpen(false)}
        sx={{
          '& .MuiDrawer-paper': {
            width: { xs: '100%', sm: 400 },
            maxWidth: '90vw',
            height: isMobile ? '80vh' : 'auto',
            borderTopLeftRadius: isMobile ? 16 : 0,
            borderTopRightRadius: isMobile ? 16 : 0
          }
        }}
      >
        <Box sx={{ height: '100%', display: 'flex', flexDirection: 'column' }}>
          {/* Chat Header */}
          <Box sx={{ p: 3, borderBottom: `1px solid ${theme.palette.divider}` }}>
            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
              <Typography variant="h6" fontWeight={600}>
                Chat with Document
              </Typography>
              <IconButton onClick={() => setChatOpen(false)} size="small">
                <CloseIcon />
              </IconButton>
            </Box>
            <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
              Ask questions about your notes and get instant answers
            </Typography>
          </Box>

          {/* Chat Messages */}
          <Box sx={{ flex: 1, overflow: 'auto', p: 2 }}>
            {!explanation.gemini_file_id ? (
              <Box sx={{ textAlign: 'center', mt: 4, p: 3 }}>
                <ChatIcon sx={{ fontSize: 48, color: 'text.secondary', mb: 2 }} />
                <Typography variant="h6" color="text.primary" gutterBottom>
                  Chat Not Available
                </Typography>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                  This document was generated before our chat feature was available.
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  To use chat with documents, please upload a new PDF and generate a fresh explanation.
                </Typography>
              </Box>
            ) : chatMessages.length === 0 ? (
              <Box sx={{ textAlign: 'center', mt: 4 }}>
                <ChatIcon sx={{ fontSize: 48, color: 'text.secondary', mb: 2 }} />
                <Typography variant="body2" color="text.secondary">
                  Start a conversation about your document
                </Typography>
              </Box>
            ) : (
              <Stack spacing={2}>
                {chatMessages.map((message) => (
                  <Box
                    key={message.id}
                    sx={{
                      display: 'flex',
                      justifyContent: message.isUser ? 'flex-end' : 'flex-start'
                    }}
                  >
                    <Paper
                      sx={{
                        p: 2,
                        maxWidth: '80%',
                        bgcolor: message.isUser ? 'primary.main' : 'background.paper',
                        color: message.isUser ? 'primary.contrastText' : 'text.primary'
                      }}
                    >
                      <Typography variant="body2">
                        {message.content}
                      </Typography>
                    </Paper>
                  </Box>
                ))}
                {isTyping && (
                  <Box sx={{ display: 'flex', justifyContent: 'flex-start' }}>
                    <Paper sx={{ p: 2, bgcolor: 'background.paper' }}>
                      <Typography variant="body2" color="text.secondary">
                        AI is typing...
                      </Typography>
                    </Paper>
                  </Box>
                )}
              </Stack>
            )}
          </Box>

          {/* Chat Input */}
          <Box sx={{
            p: { xs: 2, sm: 2 },
            borderTop: `1px solid ${theme.palette.divider}`,
            // Add safe area padding for mobile devices with notches
            paddingBottom: isMobile ? 'env(safe-area-inset-bottom, 16px)' : 2
          }}>
            <Box sx={{ display: 'flex', gap: 1, alignItems: 'flex-end' }}>
              <TextField
                fullWidth
                placeholder={explanation.gemini_file_id ? "Ask about your document..." : "Chat not available for this document"}
                value={chatInput}
                onChange={(e) => setChatInput(e.target.value)}
                onKeyPress={(e) => e.key === 'Enter' && !e.shiftKey && handleSendMessage()}
                size={isMobile ? "medium" : "small"}
                multiline
                maxRows={isMobile ? 4 : 3}
                disabled={!explanation.gemini_file_id}
                sx={{
                  '& .MuiInputBase-root': {
                    minHeight: isMobile ? 48 : 'auto'
                  }
                }}
              />
              <IconButton
                onClick={handleSendMessage}
                disabled={!chatInput.trim() || !explanation.gemini_file_id}
                color="primary"
                size={isMobile ? "large" : "medium"}
                sx={{
                  minWidth: isMobile ? 48 : 'auto',
                  minHeight: isMobile ? 48 : 'auto'
                }}
              >
                <SendIcon />
              </IconButton>
            </Box>
          </Box>
        </Box>
      </Drawer>
    </Box>
  );
};

export default ModernDocumentReader;
