import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TextField,
  Button,
  Alert,
  CircularProgress,
  Card,
  CardContent,
  Chip,
  Divider,
  useTheme,
  useMediaQuery,
  Slider
} from '@mui/material';
import {
  Quiz as QuizIcon,
  PictureAsPdf as PdfIcon,
  AutoAwesome as AutoAwesomeIcon,
  Schedule as ScheduleIcon
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import { useQuery } from '@tanstack/react-query';
import { format } from 'date-fns';
import { useNavigate } from 'react-router-dom';

import { getUploadedFiles, generateMCQsFromGemini } from '../../api/studentTools';
import { GeminiFile, GeminiMCQGenerationResponse } from '../../types/studentTools';
import ProcessingModal, { MCQ_GENERATION_STAGES } from '../Upload/ProcessingModal';
import { generateUserFriendlyErrorMessage } from '../../utils/errorUtils';

const GeminiMCQGenerator: React.FC = () => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const navigate = useNavigate();

  // Form state
  const [selectedFileId, setSelectedFileId] = useState<string>('');
  const [questionCount, setQuestionCount] = useState<number>(10);
  const [isGenerating, setIsGenerating] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [generationResult, setGenerationResult] = useState<GeminiMCQGenerationResponse | null>(null);

  // Processing modal state
  const [showProcessingModal, setShowProcessingModal] = useState(false);
  const [currentStage, setCurrentStage] = useState<string>('analyzing');

  // Fetch uploaded files
  const {
    data: filesResponse,
    isLoading: filesLoading,
    error: filesError,
    refetch: refetchFiles
  } = useQuery({
    queryKey: ['uploadedFiles'],
    queryFn: () => getUploadedFiles(),
    refetchOnWindowFocus: false,
    staleTime: 30000,
  });

  const isExpired = (autoDeleteTime: string): boolean => {
    try {
      return new Date(autoDeleteTime) < new Date();
    } catch {
      return false;
    }
  };

  const files = filesResponse?.files || [];
  const activeFiles = files.filter(file => !isExpired(file.auto_delete_time));

  const formatDateTime = (dateString: string): string => {
    try {
      return format(new Date(dateString), 'MMM dd, yyyy HH:mm');
    } catch {
      return 'Invalid date';
    }
  };

  const handleGenerate = async () => {
    if (!selectedFileId) {
      setError('Please select a PDF file to generate MCQs from.');
      return;
    }

    if (questionCount < 1 || questionCount > 30) {
      setError('Number of questions must be between 1 and 30.');
      return;
    }

    setIsGenerating(true);
    setError(null);
    setShowProcessingModal(true);
    setCurrentStage('analyzing');

    try {
      console.log('Generating MCQs:', { selectedFileId, questionCount });

      // Stage 1: Analyzing
      setCurrentStage('analyzing');
      await new Promise(resolve => setTimeout(resolve, 1500));

      // Stage 2: Generating
      setCurrentStage('generating');

      const result = await generateMCQsFromGemini({
        gemini_file_id: selectedFileId,
        question_count: questionCount
      });

      console.log('MCQ generation result:', result);

      // Stage 3: Validating
      setCurrentStage('validating');
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Stage 4: Complete
      setCurrentStage('complete');
      setGenerationResult(result);

      // Trigger refresh of generated MCQs list
      window.dispatchEvent(new CustomEvent('refreshGeneratedMCQs'));

    } catch (err: any) {
      console.error('MCQ generation error:', err);
      setShowProcessingModal(false);

      // Use the new error handling utility for consistent user-friendly messages
      const errorMessage = generateUserFriendlyErrorMessage(
        err,
        'Unable to generate MCQs at this time. Please try again later.'
      );
      setError(errorMessage);
    } finally {
      setIsGenerating(false);
    }
  };

  const handleProcessingComplete = () => {
    setShowProcessingModal(false);
    // Auto-navigate to the "My Generated MCQs" tab
    const event = new CustomEvent('switchToGeneratedMCQsTab');
    window.dispatchEvent(event);
  };

  const handleReset = () => {
    setSelectedFileId('');
    setQuestionCount(10);
    setGenerationResult(null);
    setError(null);
  };

  const selectedFile = files.find(file => file.gemini_file_id === selectedFileId);

  if (filesLoading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
        <CircularProgress />
      </Box>
    );
  }

  if (filesError) {
    return (
      <Alert severity="error" sx={{ mb: 2 }}>
        Failed to load uploaded files. Please try again.
        <Button onClick={() => refetchFiles()} sx={{ ml: 2 }}>
          Retry
        </Button>
      </Alert>
    );
  }

  if (activeFiles.length === 0) {
    return (
      <Paper sx={{ p: 4, textAlign: 'center' }}>
        <PdfIcon sx={{ fontSize: 64, color: 'text.secondary', mb: 2 }} />
        <Typography variant="h6" color="text.secondary" gutterBottom>
          No uploaded files available
        </Typography>
        <Typography variant="body2" color="text.secondary" paragraph>
          You need to upload PDF files first before generating MCQs.
        </Typography>
        <Button
          variant="contained"
          href="/tools/upload"
          sx={{ mt: 2 }}
        >
          Upload PDF Files
        </Button>
      </Paper>
    );
  }

  return (
    <Box>
      {error && (
        <Alert severity="error" sx={{ mb: 3 }} onClose={() => setError(null)}>
          {error}
        </Alert>
      )}

      {!generationResult || showProcessingModal ? (
        <Paper sx={{ p: 3 }}>

          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 3, mt: 3 }}>
            {/* File Selection */}
            <FormControl fullWidth>
              <InputLabel id="file-select-label">Select PDF File</InputLabel>
              <Select
                labelId="file-select-label"
                value={selectedFileId}
                label="Select PDF File"
                onChange={(e) => setSelectedFileId(e.target.value)}
                disabled={isGenerating}
              >
                {activeFiles.map((file) => (
                  <MenuItem key={file.id} value={file.gemini_file_id}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, width: '100%' }}>
                      <PdfIcon color="error" sx={{ fontSize: 20 }} />
                      <Box sx={{ flex: 1, minWidth: 0 }}>
                        <Typography variant="body2" noWrap>
                          {file.original_filename}
                        </Typography>
                        {file.course_name && (
                          <Chip 
                            label={file.course_name} 
                            size="small" 
                            color="primary" 
                            variant="outlined"
                            sx={{ mt: 0.5 }}
                          />
                        )}
                      </Box>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                        <ScheduleIcon sx={{ fontSize: 14 }} />
                        <Typography variant="caption" color="text.secondary">
                          {formatDateTime(file.upload_time)}
                        </Typography>
                      </Box>
                    </Box>
                  </MenuItem>
                ))}
              </Select>
            </FormControl>

            {/* Question Count */}
            <Box sx={{ mb: 2 }}>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                Number of Questions: {questionCount}
              </Typography>
              <Slider
                value={questionCount}
                onChange={(_, newValue) => setQuestionCount(newValue as number)}
                disabled={isGenerating}
                min={1}
                max={30}
                step={1}
                marks={[
                  { value: 1, label: '1' },
                  { value: 5, label: '5' },
                  { value: 10, label: '10' },
                  { value: 15, label: '15' },
                  { value: 20, label: '20' },
                  { value: 25, label: '25' },
                  { value: 30, label: '30' }
                ]}
                valueLabelDisplay="auto"
                sx={{
                  '& .MuiSlider-markLabel': {
                    fontSize: '0.75rem'
                  }
                }}
              />
              <Typography variant="caption" color="text.secondary">
                Select between 1 and 30 questions
              </Typography>
            </Box>



            {/* Action Buttons */}
            <Box sx={{ display: 'flex', gap: 2, flexDirection: isMobile ? 'column' : 'row' }}>
              <Button
                variant="contained"
                color="primary"
                startIcon={isGenerating ? <CircularProgress size={20} /> : <AutoAwesomeIcon />}
                onClick={handleGenerate}
                disabled={!selectedFileId || isGenerating}
                fullWidth={isMobile}
                sx={{ minWidth: 200 }}
              >
                {isGenerating ? 'Generating MCQs...' : `Generate ${questionCount} MCQs`}
              </Button>
              
              <Button
                variant="outlined"
                onClick={handleReset}
                disabled={isGenerating}
                fullWidth={isMobile}
              >
                Reset
              </Button>
            </Box>
          </Box>
        </Paper>
      ) : (
        /* Generation Success */
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
        >
          <Card sx={{ bgcolor: 'success.light', color: 'success.contrastText', mb: 3 }}>
            <CardContent>
              <Typography variant="h6" gutterBottom>
                ✅ MCQs Generated Successfully!
              </Typography>
              <Typography variant="body2">
                Generated {generationResult.total_count} questions from {selectedFile?.original_filename}
              </Typography>
              {generationResult.source_topics && generationResult.source_topics.length > 0 && (
                <Box sx={{ mt: 2 }}>
                  <Typography variant="caption" display="block" gutterBottom>
                    Topics covered:
                  </Typography>
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                    {generationResult.source_topics.map((topic, index) => (
                      <Chip 
                        key={index} 
                        label={topic} 
                        size="small" 
                        variant="outlined"
                        sx={{ color: 'inherit', borderColor: 'currentColor' }}
                      />
                    ))}
                  </Box>
                </Box>
              )}
            </CardContent>
          </Card>

          <Box sx={{ display: 'flex', gap: 2, flexDirection: isMobile ? 'column' : 'row' }}>
            <Button
              variant="contained"
              href="/tools/mcq#generated"
              sx={{ minWidth: 200 }}
            >
              View Generated MCQs
            </Button>
            
            <Button
              variant="outlined"
              onClick={handleReset}
            >
              Generate More MCQs
            </Button>
          </Box>
        </motion.div>
      )}

      {/* Processing Modal */}
      <ProcessingModal
        open={showProcessingModal}
        stages={MCQ_GENERATION_STAGES}
        currentStage={currentStage}
        onComplete={handleProcessingComplete}
        title="Generating MCQs"
      />
    </Box>
  );
};

export default GeminiMCQGenerator;
