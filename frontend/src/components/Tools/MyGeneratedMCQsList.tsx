import React, { useEffect } from 'react';
import {
  Box,
  Paper,
  Typography,
  CircularProgress,
  Alert,
  Button,
  Card,
  CardContent,
  IconButton,
  Tooltip,
  Grid,
  useTheme,
  useMediaQuery,
  Stack
} from '@mui/material';
import {
  Refresh as RefreshIcon,
  PlayArrow as PlayArrowIcon,
  Assessment as AssessmentIcon,
  Quiz as QuizIcon
} from '@mui/icons-material';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { useNavigate } from 'react-router-dom';
import { getMyGeneratedMCQs } from '../../api/studentTools';
import { getMCQsWithSharing } from '../../api/contentWithSharing';
import ShareButton from '../sharing/ShareButton';
import ContentTag from '../sharing/ContentTag';
import DeleteSharedContentButton from '../sharing/DeleteSharedContentButton';
import { ContentType, SharedContentItem } from '../../types/contentSharing';

const MyGeneratedMCQsList: React.FC = () => {
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  // Fetch student's generated MCQs with sharing info (own + shared)
  const {
    data: mcqsWithSharing = [],
    isLoading,
    error,
    refetch
  } = useQuery({
    queryKey: ['mcqsWithSharing'],
    queryFn: () => getMCQsWithSharing(),
    staleTime: 5000, // Reduced to 5 seconds for faster updates
    refetchOnWindowFocus: true // Refetch when window gains focus
  });

  // Debug logging
  console.log('MyGeneratedMCQsList - MCQs with sharing data:', mcqsWithSharing);

  // Group MCQs by course and extract regular MCQ data for processing with error handling
  const myMCQs = mcqsWithSharing
    .filter(item => item && item.content_data && item.content_data.id) // Filter out invalid items
    .map(item => item.content_data);
  console.log('MyGeneratedMCQsList - isLoading:', isLoading);
  console.log('MyGeneratedMCQsList - error:', error);

  const handleRefresh = () => {
    console.log('Manual refresh triggered');
    // Invalidate and refetch the query
    queryClient.invalidateQueries({ queryKey: ['myGeneratedMCQs'] });
    refetch();
  };

  // Listen for custom event to refresh MCQs when new ones are generated
  useEffect(() => {
    const handleRefreshEvent = () => {
      console.log('Received refreshGeneratedMCQs event, invalidating cache and refetching...');
      // Invalidate cache and refetch
      queryClient.invalidateQueries({ queryKey: ['myGeneratedMCQs'] });
      refetch();
    };

    window.addEventListener('refreshGeneratedMCQs', handleRefreshEvent);

    return () => {
      window.removeEventListener('refreshGeneratedMCQs', handleRefreshEvent);
    };
  }, [refetch, queryClient]);

  const handlePractice = (courseName: string) => {
    const courseQuestions = myMCQs.filter(q => q.course_name === courseName);
    if (courseQuestions.length > 0) {
      const questionIds = courseQuestions.map(q => q.id);
      // Store question IDs in session storage
      sessionStorage.setItem('practiceQuestionIds', JSON.stringify(questionIds));
      sessionStorage.setItem('practiceCourseName', courseName);
      navigate('/mcq/practice/generated');
    }
  };

  const handleExam = (courseName: string) => {
    const courseQuestions = myMCQs.filter(q => q.course_name === courseName);
    if (courseQuestions.length > 0) {
      const questionIds = courseQuestions.map(q => q.id);
      // Store question IDs in session storage
      sessionStorage.setItem('examQuestionIds', JSON.stringify(questionIds));
      sessionStorage.setItem('examCourseName', courseName);
      navigate('/mcq/exam/generated');
    }
  };

  // Group MCQs by course name
  const courseGroups = myMCQs.reduce((groups, mcq) => {
    const courseName = mcq.course_name || 'Untitled Course';
    if (!groups[courseName]) {
      groups[courseName] = [];
    }
    groups[courseName].push(mcq);
    return groups;
  }, {} as Record<string, typeof myMCQs>);

  // Sort course groups by the newest MCQ in each course (since MCQs are already ordered by newest first from backend)
  const sortedCourseEntries = Object.entries(courseGroups).sort((a, b) => {
    // Get the first (newest) MCQ from each course
    const newestMCQA = a[1][0];
    const newestMCQB = b[1][0];

    // Compare creation dates (newest first)
    const dateA = new Date(newestMCQA.created_at || 0);
    const dateB = new Date(newestMCQB.created_at || 0);

    return dateB.getTime() - dateA.getTime();
  });

  if (isLoading) {
    return (
      <Box sx={{
        display: 'flex',
        justifyContent: 'center',
        p: isMobile ? 2 : 3
      }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    // Provide more helpful error messages based on the error type
    let errorMessage = "Unable to load your generated MCQs.";

    if (error instanceof Error) {
      const errorStr = error.message.toLowerCase();
      if (errorStr.includes('network') || errorStr.includes('connection')) {
        errorMessage = "Network connection issue. Please check your internet connection.";
      } else if (errorStr.includes('timeout')) {
        errorMessage = "Request timed out. Please try again.";
      } else if (errorStr.includes('500')) {
        errorMessage = "Server temporarily unavailable. Please try again in a few minutes.";
      } else if (errorStr.includes('403') || errorStr.includes('unauthorized')) {
        errorMessage = "Access denied. Please log in again.";
      }
    }

    return (
      <Alert
        severity="error"
        sx={{
          mb: 2,
          mx: isMobile ? 2 : 0,
          borderRadius: 2
        }}
        action={
          <Button
            color="inherit"
            size="small"
            onClick={handleRefresh}
            sx={{ ml: 1 }}
          >
            Retry
          </Button>
        }
      >
        {errorMessage}
      </Alert>
    );
  }

  if (myMCQs.length === 0) {
    return (
      <Alert
        severity="info"
        sx={{
          mb: 2,
          mx: isMobile ? 2 : 0,
          borderRadius: 2
        }}
      >
        You don't have any generated MCQs yet. Upload a PDF and generate some MCQs first.
      </Alert>
    );
  }

  return (
    <Box sx={{ p: isMobile ? 1 : 0 }}>
      {/* Header with refresh button */}
      <Box sx={{
        display: 'flex',
        justifyContent: 'space-between',
        alignItems: 'center',
        mb: isMobile ? 2 : 3,
        px: isMobile ? 1 : 0
      }}>
        <Typography
          variant={isMobile ? "h6" : "h5"}
          gutterBottom
          sx={{
            fontWeight: 600,
            color: theme.palette.text.primary
          }}
        >
          Your Generated MCQs
        </Typography>
        <Tooltip title="Refresh MCQs">
          <IconButton
            onClick={handleRefresh}
            sx={{
              bgcolor: theme.palette.action.hover,
              '&:hover': {
                bgcolor: theme.palette.action.selected
              }
            }}
          >
            <RefreshIcon />
          </IconButton>
        </Tooltip>
      </Box>

      {/* Course List */}
      <Grid container spacing={isMobile ? 1.5 : 2} sx={{ px: isMobile ? 1 : 0 }}>
        {sortedCourseEntries.map(([courseName, questions]) => (
          <Grid item xs={12} key={courseName}>
            <Card
              variant="outlined"
              sx={{
                borderRadius: 2,
                transition: 'all 0.2s ease-in-out',
                '&:hover': {
                  boxShadow: theme.shadows[4],
                  transform: 'translateY(-2px)'
                }
              }}
            >
              <CardContent sx={{ p: isMobile ? 2 : 3 }}>
                {isMobile ? (
                  // Mobile Layout - Stacked
                  <Stack spacing={2}>
                    <Box>
                      <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 1 }}>
                        <Typography
                          variant={isMobile ? "subtitle1" : "h6"}
                          sx={{
                            display: 'flex',
                            alignItems: 'center',
                            gap: 1,
                            fontWeight: 600
                          }}
                        >
                          <QuizIcon color="primary" />
                          {courseName}
                        </Typography>
                        {/* Show sharing indicators if any MCQs in this course are shared */}
                        {(() => {
                          const courseSharedItems = mcqsWithSharing.filter(item =>
                            item.content_data.course_name === courseName && item.is_shared
                          );
                          if (courseSharedItems.length > 0) {
                            const sharedBy = courseSharedItems[0].shared_by_name;
                            return (
                              <ContentTag
                                is_shared={true}
                                shared_by_name={sharedBy}
                                tags={['shared']}
                                size="small"
                              />
                            );
                          }
                          return null;
                        })()}
                      </Box>
                      <Typography variant="body2" color="text.secondary">
                        {questions.length} question{questions.length !== 1 ? 's' : ''} available
                      </Typography>
                    </Box>
                    <Stack direction="row" spacing={1} sx={{ width: '100%' }}>
                      <Button
                        variant="contained"
                        startIcon={<PlayArrowIcon />}
                        onClick={() => handlePractice(courseName)}
                        size="small"
                        fullWidth
                        sx={{ flex: 1 }}
                      >
                        Practice
                      </Button>
                      <Button
                        variant="outlined"
                        startIcon={<AssessmentIcon />}
                        onClick={() => handleExam(courseName)}
                        size="small"
                        fullWidth
                        sx={{ flex: 1 }}
                      >
                        Exam
                      </Button>
                      {/* Check if any questions in this course are shared */}
                      {(() => {
                        const courseSharedItems = mcqsWithSharing.filter(item =>
                          item.content_data.course_name === courseName && item.is_shared
                        );
                        const hasOwnContent = questions.some(q =>
                          mcqsWithSharing.find(item => item.content_id === q.id && !item.is_shared)
                        );

                        if (hasOwnContent) {
                          // Show share button if user owns content in this course
                          return (
                            <ShareButton
                              content_type={ContentType.MCQ}
                              content_id={questions[0].id}
                              content_ids={questions.map(q => q.id)}
                              content_title={`${courseName} MCQs`}
                              course_name={courseName}
                              variant="icon"
                              size="small"
                            />
                          );
                        } else if (courseSharedItems.length > 0) {
                          // Show delete button if all content is shared
                          return (
                            <DeleteSharedContentButton
                              content_type={ContentType.MCQ}
                              content_id={courseSharedItems[0].content_id}
                              content_title={`${courseName} MCQs`}
                              variant="icon"
                              size="small"
                              onDeleted={() => refetch()}
                            />
                          );
                        }
                        return null;
                      })()}
                    </Stack>
                  </Stack>
                ) : (
                  // Desktop Layout - Side by side
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
                    <Box>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 1 }}>
                        <Typography variant="h6" sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <QuizIcon color="primary" />
                          {courseName}
                        </Typography>
                        {/* Show sharing indicators if any MCQs in this course are shared */}
                        {(() => {
                          const courseSharedItems = mcqsWithSharing.filter(item =>
                            item.content_data.course_name === courseName && item.is_shared
                          );
                          if (courseSharedItems.length > 0) {
                            const sharedBy = courseSharedItems[0].shared_by_name;
                            return (
                              <ContentTag
                                is_shared={true}
                                shared_by_name={sharedBy}
                                tags={['shared']}
                                size="small"
                              />
                            );
                          }
                          return null;
                        })()}
                      </Box>
                      <Typography variant="body2" color="text.secondary">
                        {questions.length} question{questions.length !== 1 ? 's' : ''} available
                      </Typography>
                    </Box>
                    <Box sx={{ display: 'flex', gap: 1 }}>
                      <Button
                        variant="contained"
                        startIcon={<PlayArrowIcon />}
                        onClick={() => handlePractice(courseName)}
                        size="small"
                      >
                        Practice
                      </Button>
                      <Button
                        variant="outlined"
                        startIcon={<AssessmentIcon />}
                        onClick={() => handleExam(courseName)}
                        size="small"
                      >
                        Take Exam
                      </Button>
                      {/* Check if any questions in this course are shared */}
                      {(() => {
                        const courseSharedItems = mcqsWithSharing.filter(item =>
                          item.content_data.course_name === courseName && item.is_shared
                        );
                        const hasOwnContent = questions.some(q =>
                          mcqsWithSharing.find(item => item.content_id === q.id && !item.is_shared)
                        );

                        if (hasOwnContent) {
                          // Show share button if user owns content in this course
                          return (
                            <ShareButton
                              content_type={ContentType.MCQ}
                              content_id={questions[0].id}
                              content_ids={questions.map(q => q.id)}
                              content_title={`${courseName} MCQs`}
                              course_name={courseName}
                              variant="icon"
                              size="small"
                            />
                          );
                        } else if (courseSharedItems.length > 0) {
                          // Show delete button if all content is shared
                          return (
                            <DeleteSharedContentButton
                              content_type={ContentType.MCQ}
                              content_id={courseSharedItems[0].content_id}
                              content_title={`${courseName} MCQs`}
                              variant="icon"
                              size="small"
                              onDeleted={() => refetch()}
                            />
                          );
                        }
                        return null;
                      })()}
                    </Box>
                  </Box>
                )}
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>
    </Box>
  );
};

export default MyGeneratedMCQsList;
