import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TextField,
  Button,
  Alert,
  CircularProgress,
  Card,
  CardContent,
  Chip,
  Divider,
  useTheme,
  useMediaQuery,
  Slider
} from '@mui/material';
import {
  Style as FlashcardIcon,
  PictureAsPdf as PdfIcon,
  AutoAwesome as AutoAwesomeIcon,
  Schedule as ScheduleIcon
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import { useQuery } from '@tanstack/react-query';
import { format } from 'date-fns';
import { useNavigate } from 'react-router-dom';

import { getUploadedFiles, generateFlashcardsFromGemini } from '../../api/studentTools';
import { GeminiFile, GeminiFlashcardGenerationResponse } from '../../types/studentTools';
import ProcessingModal, { MCQ_GENERATION_STAGES } from '../Upload/ProcessingModal';
import { generateUserFriendlyErrorMessage } from '../../utils/errorUtils';

const GeminiFlashcardGenerator: React.FC = () => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const navigate = useNavigate();

  // Form state
  const [selectedFileId, setSelectedFileId] = useState<string>('');
  const [cardCount, setCardCount] = useState<number>(10);
  const [isGenerating, setIsGenerating] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [generationResult, setGenerationResult] = useState<GeminiFlashcardGenerationResponse | null>(null);

  // Processing modal state
  const [showProcessingModal, setShowProcessingModal] = useState(false);
  const [currentStage, setCurrentStage] = useState<string>('analyzing');

  // Fetch uploaded files
  const {
    data: filesResponse,
    isLoading: filesLoading,
    error: filesError,
    refetch: refetchFiles
  } = useQuery({
    queryKey: ['uploadedFiles'],
    queryFn: () => getUploadedFiles(),
    refetchOnWindowFocus: false,
    staleTime: 30000,
  });

  const isExpired = (autoDeleteTime: string): boolean => {
    try {
      return new Date(autoDeleteTime) < new Date();
    } catch {
      return false;
    }
  };

  const files = filesResponse?.files || [];
  const activeFiles = files.filter(file => !isExpired(file.auto_delete_time));

  const formatDateTime = (dateString: string): string => {
    try {
      return format(new Date(dateString), 'MMM dd, yyyy HH:mm');
    } catch {
      return 'Invalid date';
    }
  };

  const handleGenerate = async () => {
    if (!selectedFileId) {
      setError('Please select a PDF file to generate flashcards from.');
      return;
    }

    if (cardCount < 1 || cardCount > 30) {
      setError('Number of flashcards must be between 1 and 30.');
      return;
    }

    setIsGenerating(true);
    setError(null);
    setShowProcessingModal(true);
    setCurrentStage('analyzing');

    try {
      console.log('Generating flashcards:', { selectedFileId, cardCount });

      // Stage 1: Analyzing
      setCurrentStage('analyzing');
      await new Promise(resolve => setTimeout(resolve, 1500));

      // Stage 2: Generating
      setCurrentStage('generating');

      const result = await generateFlashcardsFromGemini({
        gemini_file_id: selectedFileId,
        card_count: cardCount
      });

      console.log('Flashcard generation result:', result);

      // Stage 3: Validating
      setCurrentStage('validating');
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Stage 4: Complete
      setCurrentStage('complete');
      setGenerationResult(result);

      // Trigger refresh of generated flashcards list
      window.dispatchEvent(new CustomEvent('refreshGeneratedFlashcards'));

    } catch (err: any) {
      console.error('Flashcard generation error:', err);
      setShowProcessingModal(false);

      // Use the new error handling utility for consistent user-friendly messages
      const errorMessage = generateUserFriendlyErrorMessage(
        err,
        'Unable to generate flashcards at this time. Please try again later.'
      );
      setError(errorMessage);
    } finally {
      setIsGenerating(false);
    }
  };

  const handleProcessingComplete = () => {
    setShowProcessingModal(false);
    // Auto-navigate to the "My Generated Flashcards" tab
    const event = new CustomEvent('switchToGeneratedFlashcardsTab');
    window.dispatchEvent(event);
  };

  const handleReset = () => {
    setSelectedFileId('');
    setCardCount(10);
    setError(null);
    setGenerationResult(null);
  };

  const handlePracticeFlashcards = () => {
    if (generationResult && generationResult.saved_flashcard_ids.length > 0) {
      // Store flashcard IDs in session storage and navigate to practice
      sessionStorage.setItem('practiceFlashcardIds', JSON.stringify(generationResult.saved_flashcard_ids));
      sessionStorage.setItem('practiceCourseName', 'Generated Flashcards');
      navigate('/flashcards/practice/generated');
    }
  };

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 }
  };

  if (filesLoading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: 200 }}>
        <CircularProgress />
      </Box>
    );
  }

  if (filesError) {
    return (
      <Alert severity="error" sx={{ mb: 2 }}>
        Failed to load uploaded files. Please try again.
      </Alert>
    );
  }

  return (
    <motion.div
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      <Paper
        elevation={3}
        sx={{
          p: 3,
          borderRadius: 2,
          background: `linear-gradient(135deg, ${theme.palette.primary.main}15 0%, ${theme.palette.secondary.main}15 100%)`,
        }}
      >


        {error && (
          <motion.div variants={itemVariants}>
            <Alert severity="error" sx={{ mb: 3 }}>
              {error}
            </Alert>
          </motion.div>
        )}

        {generationResult && (
          <motion.div variants={itemVariants}>
            <Alert severity="success" sx={{ mb: 3 }}>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', width: '100%' }}>
                <Typography>
                  Successfully generated {generationResult.total_count} flashcards!
                </Typography>
                <Button
                  variant="contained"
                  color="primary"
                  size="small"
                  onClick={handlePracticeFlashcards}
                  sx={{ ml: 2 }}
                >
                  Practice Now
                </Button>
              </Box>
            </Alert>
          </motion.div>
        )}

        <motion.div variants={itemVariants}>
          <FormControl fullWidth sx={{ mb: 3 }}>
            <InputLabel>Select PDF File</InputLabel>
            <Select
              value={selectedFileId}
              onChange={(e) => setSelectedFileId(e.target.value)}
              label="Select PDF File"
              disabled={isGenerating}
            >
              {activeFiles.length === 0 ? (
                <MenuItem disabled>
                  <Typography color="text.secondary">No files available</Typography>
                </MenuItem>
              ) : (
                activeFiles.map((file) => (
                  <MenuItem key={file.gemini_file_id} value={file.gemini_file_id}>
                    <Box sx={{ display: 'flex', alignItems: 'center', width: '100%' }}>
                      <PdfIcon sx={{ mr: 1, color: 'error.main' }} />
                      <Box sx={{ flexGrow: 1 }}>
                        <Typography variant="body2" fontWeight="medium">
                          {file.course_name || file.original_filename}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {formatDateTime(file.upload_time)} • {(file.file_size / (1024 * 1024)).toFixed(2)} MB
                        </Typography>
                      </Box>
                    </Box>
                  </MenuItem>
                ))
              )}
            </Select>
          </FormControl>
        </motion.div>

        <motion.div variants={itemVariants}>
          <Typography variant="body2" fontWeight="medium" sx={{ mb: 2 }}>
            Number of Flashcards: {cardCount}
          </Typography>
          <Slider
            value={cardCount}
            onChange={(_, newValue) => setCardCount(newValue as number)}
            min={1}
            max={30}
            step={1}
            marks={[
              { value: 1, label: '1' },
              { value: 10, label: '10' },
              { value: 20, label: '20' },
              { value: 30, label: '30' }
            ]}
            disabled={isGenerating}
            sx={{ mb: 3 }}
          />
        </motion.div>

        <motion.div variants={itemVariants}>
          <Box sx={{ display: 'flex', gap: 2, flexDirection: isMobile ? 'column' : 'row' }}>
            <Button
              variant="contained"
              color="primary"
              onClick={handleGenerate}
              disabled={isGenerating || !selectedFileId || activeFiles.length === 0}
              startIcon={isGenerating ? <CircularProgress size={20} /> : <AutoAwesomeIcon />}
              fullWidth={isMobile}
              sx={{ minWidth: 200 }}
            >
              {isGenerating ? 'Generating...' : 'Generate Flashcards'}
            </Button>

            <Button
              variant="outlined"
              onClick={handleReset}
              disabled={isGenerating}
              fullWidth={isMobile}
            >
              Reset
            </Button>
          </Box>
        </motion.div>

        {activeFiles.length === 0 && (
          <motion.div variants={itemVariants}>
            <Alert severity="info" sx={{ mt: 3 }}>
              No PDF files available. Please upload a PDF file first.
            </Alert>
          </motion.div>
        )}
      </Paper>

      {/* Processing Modal */}
      <ProcessingModal
        open={showProcessingModal}
        stages={MCQ_GENERATION_STAGES}
        currentStage={currentStage}
        onComplete={handleProcessingComplete}
        title="Generating Flashcards"
        description="Creating flashcards from your uploaded notes..."
      />
    </motion.div>
  );
};

export default GeminiFlashcardGenerator;
