import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  CircularProgress,
  Paper,
  useTheme,
  useMediaQuery,
  Chip,
  Divider
} from '@mui/material';
import {
  AutoAwesome as AutoAwesomeIcon,
  Description as DescriptionIcon,
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon
} from '@mui/icons-material';
import { motion } from 'framer-motion';

import { getUploadedFiles, generateNoteExplanationFromGemini } from '../../api/studentTools';
import { GeminiFile, GeminiNoteExplanationGenerationResponse } from '../../types/studentTools';
import ProcessingModal, { NOTE_EXPLANATION_GENERATION_STAGES } from '../Upload/ProcessingModal';
import { generateUserFriendlyErrorMessage } from '../../utils/errorUtils';

const GeminiNoteExplanationGenerator: React.FC = () => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));

  // State management
  const [files, setFiles] = useState<GeminiFile[]>([]);
  const [activeFiles, setActiveFiles] = useState<GeminiFile[]>([]);
  const [selectedFileId, setSelectedFileId] = useState<string>('');
  const [isGenerating, setIsGenerating] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [generationResult, setGenerationResult] = useState<GeminiNoteExplanationGenerationResponse | null>(null);
  const [showProcessingModal, setShowProcessingModal] = useState(false);
  const [currentStage, setCurrentStage] = useState<keyof typeof NOTE_EXPLANATION_GENERATION_STAGES>('analyzing');

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 }
  };

  // Load uploaded files on component mount
  useEffect(() => {
    loadUploadedFiles();
  }, []);

  const loadUploadedFiles = async () => {
    try {
      const response = await getUploadedFiles();
      setFiles(response.files);
      
      // Filter active files (not deleted and not expired)
      const now = new Date();
      const active = response.files.filter(file => 
        !file.is_deleted && new Date(file.auto_delete_time) > now
      );
      setActiveFiles(active);
    } catch (err: any) {
      console.error('Error loading files:', err);
      setError('Failed to load uploaded files. Please try again.');
    }
  };

  const handleGenerate = async () => {
    if (!selectedFileId) {
      setError('Please select a PDF file to generate explanation from.');
      return;
    }

    setIsGenerating(true);
    setError(null);
    setShowProcessingModal(true);
    setCurrentStage('analyzing');

    try {
      console.log('Generating note explanation:', { selectedFileId });

      // Stage 1: Analyzing
      setCurrentStage('analyzing');
      await new Promise(resolve => setTimeout(resolve, 2000));

      // Stage 2: Processing
      setCurrentStage('processing');
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Stage 3: Generating
      setCurrentStage('generating');

      const result = await generateNoteExplanationFromGemini({
        gemini_file_id: selectedFileId
      });

      console.log('Note explanation generation result:', result);

      // Stage 4: Finalizing
      setCurrentStage('finalizing');
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Stage 5: Complete
      setCurrentStage('complete');
      setGenerationResult(result);

      // Trigger refresh of generated explanations list
      window.dispatchEvent(new CustomEvent('refreshGeneratedNoteExplanations'));

    } catch (err: any) {
      console.error('Note explanation generation error:', err);
      setShowProcessingModal(false);

      // Use the new error handling utility for consistent user-friendly messages
      const errorMessage = generateUserFriendlyErrorMessage(
        err,
        'An error occurred during note explanation generation. Please try again.'
      );
      setError(errorMessage);
    } finally {
      setIsGenerating(false);
    }
  };

  const handleProcessingComplete = () => {
    setShowProcessingModal(false);
    // Auto-navigate to the "My Generated Explanations" tab
    const event = new CustomEvent('switchToGeneratedExplanationsTab');
    window.dispatchEvent(event);
  };

  const handleReset = () => {
    setSelectedFileId('');
    setGenerationResult(null);
    setError(null);
  };

  const selectedFile = activeFiles.find(file => file.gemini_file_id === selectedFileId);

  return (
    <motion.div
      variants={containerVariants}
      initial="hidden"
      animate="visible"
    >
      {/* Processing Modal */}
      <ProcessingModal
        open={showProcessingModal}
        currentStage={currentStage}
        stages={NOTE_EXPLANATION_GENERATION_STAGES}
        onComplete={handleProcessingComplete}
        title="Generating Note Explanation"
        type="note-explanation"
      />

      {!generationResult || showProcessingModal ? (
        <Paper sx={{ p: 3 }}>

          {error && (
            <motion.div variants={itemVariants}>
              <Alert severity="error" sx={{ mb: 3 }}>
                {error}
              </Alert>
            </motion.div>
          )}

          {activeFiles.length === 0 ? (
            <motion.div variants={itemVariants}>
              <Alert severity="info" sx={{ mb: 3 }}>
                No active PDF files found. Please upload a PDF file first.
              </Alert>
            </motion.div>
          ) : (
            <>
              <motion.div variants={itemVariants}>
                <FormControl fullWidth sx={{ mb: 3 }}>
                  <InputLabel>Select PDF File</InputLabel>
                  <Select
                    value={selectedFileId}
                    onChange={(e) => setSelectedFileId(e.target.value)}
                    label="Select PDF File"
                    disabled={isGenerating}
                  >
                    {activeFiles.map((file) => (
                      <MenuItem key={file.gemini_file_id} value={file.gemini_file_id}>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, width: '100%' }}>
                          <DescriptionIcon fontSize="small" />
                          <Box sx={{ flex: 1 }}>
                            <Typography variant="body2" noWrap>
                              {file.original_filename}
                            </Typography>
                            {file.course_name && (
                              <Typography variant="caption" color="text.secondary">
                                {file.course_name}
                              </Typography>
                            )}
                          </Box>
                          <Chip 
                            label={`${(file.file_size / 1024 / 1024).toFixed(1)} MB`} 
                            size="small" 
                            variant="outlined" 
                          />
                        </Box>
                      </MenuItem>
                    ))}
                  </Select>
                </FormControl>
              </motion.div>



              <motion.div variants={itemVariants}>
                <Box sx={{ display: 'flex', gap: 2, flexDirection: isMobile ? 'column' : 'row' }}>
                  <Button
                    variant="contained"
                    color="primary"
                    onClick={handleGenerate}
                    disabled={isGenerating || !selectedFileId || activeFiles.length === 0}
                    startIcon={isGenerating ? <CircularProgress size={20} /> : <AutoAwesomeIcon />}
                    fullWidth={isMobile}
                    sx={{ minWidth: 200 }}
                  >
                    {isGenerating ? 'Generating...' : 'Generate Explanation'}
                  </Button>

                  <Button
                    variant="outlined"
                    onClick={handleReset}
                    disabled={isGenerating}
                    fullWidth={isMobile}
                  >
                    Reset
                  </Button>
                </Box>
              </motion.div>
            </>
          )}
        </Paper>
      ) : (
        <motion.div variants={itemVariants}>
          <Paper sx={{ p: 3 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 2 }}>
              <CheckCircleIcon color="success" />
              <Typography variant="h6" color="success.main">
                Note Explanation Generated Successfully!
              </Typography>
            </Box>
            
            <Typography variant="body2" color="text.secondary" paragraph>
              Your comprehensive note explanation has been generated and saved. You can view it in the "My Generated Explanations" tab.
            </Typography>

            <Divider sx={{ my: 2 }} />

            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
              <Typography variant="subtitle1" fontWeight="bold">
                {generationResult.explanation.title}
              </Typography>
              
              <Typography variant="body2" color="text.secondary">
                {generationResult.explanation.overview}
              </Typography>

              <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                <Chip label={`${generationResult.explanation.word_count} words`} size="small" />
                <Chip label={`${generationResult.explanation.key_concepts.length} key concepts`} size="small" />
                <Chip label={`${generationResult.explanation.main_topics.length} topics`} size="small" />
              </Box>
            </Box>

            <Box sx={{ mt: 3, display: 'flex', gap: 2, flexDirection: isMobile ? 'column' : 'row' }}>
              <Button
                variant="contained"
                onClick={() => {
                  const event = new CustomEvent('switchToGeneratedExplanationsTab');
                  window.dispatchEvent(event);
                }}
                fullWidth={isMobile}
              >
                View Explanation
              </Button>
              
              <Button
                variant="outlined"
                onClick={handleReset}
                fullWidth={isMobile}
              >
                Generate Another
              </Button>
            </Box>
          </Paper>
        </motion.div>
      )}
    </motion.div>
  );
};

export default GeminiNoteExplanationGenerator;
