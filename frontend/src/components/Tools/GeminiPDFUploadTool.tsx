import React, { useState, useRef } from 'react';
import {
  Box,
  Typography,
  Button,
  Paper,
  Alert,
  useTheme,
  TextField,
  LinearProgress,
  Card,
  CardContent,
  Chip,
  Stack
} from '@mui/material';
import {
  CloudUpload as CloudUploadIcon,
  PictureAsPdf as PdfIcon,
  CheckCircle as CheckCircleIcon,
  Schedule as ScheduleIcon,
  AutoAwesome as AutoAwesomeIcon,
  Quiz as QuizIcon,
  Style as FlashcardIcon
} from '@mui/icons-material';
import { motion } from 'framer-motion';

import {
  uploadPDFToGemini,
  generateMCQsFromGeminiFile,
  generateFlashcardsFromGeminiFile
} from '../../api/studentTools';
import {
  UploadStartResponse,
  GeminiMCQGenerationResponse,
  GeminiFlashcardGenerationResponse
} from '../../types/studentTools';
import { useUploadManager } from '../../hooks/useUploadManager';
import ProcessingModal, { UPLOAD_STAGES } from '../Upload/ProcessingModal';
import { generateUserFriendlyErrorMessage } from '../../utils/errorUtils';

const MAX_FILE_SIZE = 30 * 1024 * 1024; // 30MB

const GeminiPDFUploadTool: React.FC = () => {
  const theme = useTheme();
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [courseName, setCourseName] = useState<string>('');
  const [uploadProgress, setUploadProgress] = useState<boolean>(false);
  const [uploadResult, setUploadResult] = useState<UploadStartResponse | null>(null);
  const [error, setError] = useState<string | null>(null);
  const [currentUploadId, setCurrentUploadId] = useState<string | null>(null);
  const [generationProgress, setGenerationProgress] = useState<{
    mcq: boolean;
    flashcards: boolean;
  }>({ mcq: false, flashcards: false });
  const [generationResults, setGenerationResults] = useState<{
    mcq: GeminiMCQGenerationResponse | null;
    flashcards: GeminiFlashcardGenerationResponse | null;
  }>({ mcq: null, flashcards: null });

  // Processing modal state
  const [showProcessingModal, setShowProcessingModal] = useState(false);
  const [currentStage, setCurrentStage] = useState<string>('uploading');

  const { startUpload, isUploading, error: uploadError, clearError } = useUploadManager();

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (!file) return;

    setError(null);

    // Check file type
    if (!file.type.includes('pdf')) {
      setError('Only PDF files are allowed.');
      return;
    }

    // Check file size
    if (file.size > MAX_FILE_SIZE) {
      setError(`File exceeds the maximum size of 30MB.`);
      return;
    }

    setSelectedFile(file);
    setUploadResult(null);
    setGenerationResults({ mcq: null, flashcards: null });

    // Don't reset the file input here - keep it until after upload
  };

  const handleUpload = async () => {
    if (!selectedFile) {
      setError('Please select a PDF file to upload.');
      return;
    }

    // Prevent multiple uploads
    if (isUploading) {
      console.log('Upload already in progress, ignoring duplicate request');
      return;
    }

    setError(null);
    clearError(); // Clear any previous upload errors
    setShowProcessingModal(true);
    setCurrentStage('uploading');

    try {
      console.log('Starting direct upload for PDF:', selectedFile.name, 'Size:', selectedFile.size);

      // Stage 1: Uploading
      setCurrentStage('uploading');

      // Use direct upload to Gemini
      const response = await startUpload(selectedFile, courseName.trim() || undefined);
      console.log('Direct upload completed:', response);

      // Stage 2: Processing
      setCurrentStage('processing');
      await new Promise(resolve => setTimeout(resolve, 1500));

      // Stage 3: Indexing
      setCurrentStage('indexing');
      await new Promise(resolve => setTimeout(resolve, 1000));

      // Stage 4: Complete
      setCurrentStage('complete');
      setUploadResult(response);

      // Reset the file input after successful upload
      if (fileInputRef.current) {
        fileInputRef.current.value = '';
      }

      // Trigger refresh of notes table
      window.dispatchEvent(new CustomEvent('refreshNotes'));
    } catch (err: any) {
      console.error('Upload error:', err);
      setShowProcessingModal(false);

      // Use the new error handling utility for consistent user-friendly messages
      const errorMessage = generateUserFriendlyErrorMessage(
        err,
        'An error occurred during upload. Please try again.'
      );
      setError(errorMessage);
    }
  };

  const handleProcessingComplete = () => {
    setShowProcessingModal(false);
    // Auto-navigate to the "My Notes" tab
    const event = new CustomEvent('switchToNotesTab');
    window.dispatchEvent(event);
  };

  const handleGenerateMCQs = async () => {
    if (!uploadResult) return;

    setGenerationProgress(prev => ({ ...prev, mcq: true }));
    setError(null);

    try {
      const result = await generateMCQsFromGeminiFile(
        uploadResult.gemini_file_id,
        10, // Generate 10 MCQs
        courseName.trim() || undefined
      );
      
      setGenerationResults(prev => ({ ...prev, mcq: result }));
    } catch (err: any) {
      console.error('MCQ generation error:', err);
      setError(`MCQ generation failed: ${err.response?.data?.detail || err.message}`);
    } finally {
      setGenerationProgress(prev => ({ ...prev, mcq: false }));
    }
  };

  const handleGenerateFlashcards = async () => {
    if (!uploadResult) return;

    setGenerationProgress(prev => ({ ...prev, flashcards: true }));
    setError(null);

    try {
      const result = await generateFlashcardsFromGeminiFile(
        uploadResult.gemini_file_id,
        10, // Generate 10 flashcards
        courseName.trim() || undefined
      );

      setGenerationResults(prev => ({ ...prev, flashcards: result }));
    } catch (err: any) {
      console.error('Flashcard generation error:', err);
      setError(`Flashcard generation failed: ${err.response?.data?.detail || err.message}`);
    } finally {
      setGenerationProgress(prev => ({ ...prev, flashcards: false }));
    }
  };

  const formatFileSize = (bytes: number) => {
    return `${(bytes / (1024 * 1024)).toFixed(2)} MB`;
  };

  const formatDateTime = (dateString: string) => {
    return new Date(dateString).toLocaleString();
  };

  return (
    <Box>
      <input
        type="file"
        accept=".pdf"
        onChange={handleFileSelect}
        style={{ display: 'none' }}
        ref={fileInputRef}
      />



      {error && (
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
      )}

      {/* Course Name Input */}
      <Box sx={{ mb: 2 }}>
        <TextField
          fullWidth
          label="Course Name (Optional)"
          variant="outlined"
          value={courseName}
          onChange={(e) => setCourseName(e.target.value)}
          placeholder="e.g. Physics 101"
          size="small"
          sx={{ borderRadius: 2 }}
        />
      </Box>

      {/* File Upload Area */}
      <Paper
        variant="outlined"
        sx={{
          p: { xs: 2, sm: 3 },
          mb: 2,
          borderStyle: 'dashed',
          borderWidth: 2,
          borderColor: selectedFile ? theme.palette.primary.main : theme.palette.divider,
          backgroundColor: theme.palette.mode === 'dark' ? theme.palette.grey[900] : theme.palette.grey[50],
          textAlign: 'center',
          cursor: 'pointer',
          borderRadius: 2,
          transition: 'all 0.2s ease-in-out',
          '&:hover': {
            borderColor: theme.palette.primary.main,
            backgroundColor: theme.palette.action.hover
          }
        }}
        onClick={() => fileInputRef.current?.click()}
      >
        <CloudUploadIcon sx={{
          fontSize: { xs: 32, sm: 40 },
          color: selectedFile ? theme.palette.primary.main : theme.palette.text.secondary,
          mb: 1
        }} />
        <Typography
          variant="subtitle1"
          gutterBottom
          sx={{
            fontSize: { xs: '0.9rem', sm: '1rem' },
            wordBreak: 'break-word',
            px: { xs: 1, sm: 0 }
          }}
        >
          {selectedFile ? selectedFile.name : 'Select PDF file'}
        </Typography>
        <Typography
          variant="caption"
          color="text.secondary"
          sx={{ fontSize: { xs: '0.7rem', sm: '0.75rem' } }}
        >
          {selectedFile ? formatFileSize(selectedFile.size) : 'Max 30MB'}
        </Typography>
      </Paper>



      {/* Upload Button */}
      {selectedFile && !uploadResult && (
        <Button
          variant="contained"
          color="primary"
          startIcon={<CloudUploadIcon />}
          onClick={handleUpload}
          disabled={isUploading}
          fullWidth
          sx={{
            mb: 2,
            borderRadius: 2,
            py: { xs: 1.5, sm: 1 },
            fontSize: { xs: '0.875rem', sm: '1rem' },
            pointerEvents: uploadProgress ? 'none' : 'auto', // Prevent any clicks during upload
            '& .MuiButton-startIcon': {
              marginRight: { xs: 1, sm: 1.5 }
            }
          }}
        >
          {isUploading ? 'Uploading...' : 'Upload PDF'}
        </Button>
      )}

      {isUploading && (
        <Box sx={{ mb: 3 }}>
          <LinearProgress />
          <Typography variant="body2" align="center" sx={{ mt: 1 }}>
            Uploading to Gemini...
          </Typography>
        </Box>
      )}

      {/* Upload Success and Generation Options */}
      {uploadResult && !showProcessingModal && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.3 }}
        >
          <Card sx={{ mb: 3, bgcolor: 'success.light', color: 'success.contrastText' }}>
            <CardContent>
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                <CheckCircleIcon />
                <Typography variant="h6">
                  Upload started successfully
                </Typography>
              </Box>
              <Typography variant="body2" sx={{ mt: 1, opacity: 0.9 }}>
                Your file is uploading in the background. You can navigate away and check progress anytime using the upload indicator.
              </Typography>
            </CardContent>
          </Card>

          {/* Generation Buttons */}
          <Typography variant="h6" gutterBottom>
            Generate Content
          </Typography>
          <Stack spacing={2} sx={{ mb: 3 }}>
            <Button
              variant="outlined"
              startIcon={<QuizIcon />}
              onClick={handleGenerateMCQs}
              disabled={generationProgress.mcq}
              fullWidth
            >
              {generationProgress.mcq ? 'Generating MCQs...' : 'Generate 10 MCQs'}
            </Button>

            <Button
              variant="outlined"
              startIcon={<FlashcardIcon />}
              onClick={handleGenerateFlashcards}
              disabled={generationProgress.flashcards}
              fullWidth
            >
              {generationProgress.flashcards ? 'Generating Flashcards...' : 'Generate 10 Flashcards'}
            </Button>

          </Stack>

          {/* Generation Progress */}
          {generationProgress.mcq && (
            <Box sx={{ mb: 3 }}>
              <LinearProgress />
              <Typography variant="body2" align="center" sx={{ mt: 1 }}>
                Generating MCQs using Gemini AI...
              </Typography>
            </Box>
          )}

          {generationProgress.flashcards && (
            <Box sx={{ mb: 3 }}>
              <LinearProgress />
              <Typography variant="body2" align="center" sx={{ mt: 1 }}>
                Generating Flashcards using Gemini AI...
              </Typography>
            </Box>
          )}

          {/* Generation Results */}
          {(generationResults.mcq || generationResults.flashcards) && (
            <Box sx={{ mt: 3 }}>
              <Typography variant="h6" gutterBottom>
                Generated Content
              </Typography>

              {/* MCQ Results */}
              {generationResults.mcq && (
                <Card sx={{ mb: 2 }}>
                  <CardContent>
                    <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <QuizIcon color="primary" />
                      MCQs Generated ({generationResults.mcq.total_count})
                    </Typography>
                    <Typography variant="body2" color="success.main" sx={{ mb: 1 }}>
                      ✅ {generationResults.mcq.message}
                    </Typography>
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                      Topics covered: {generationResults.mcq.source_topics.join(', ')}
                    </Typography>
                    <Box sx={{ maxHeight: 200, overflow: 'auto' }}>
                      {generationResults.mcq.questions.slice(0, 3).map((question, index) => (
                        <Box key={index} sx={{ mb: 2, p: 2, bgcolor: 'background.default', borderRadius: 1 }}>
                          <Typography variant="body2" sx={{ fontWeight: 'bold', mb: 1 }}>
                            Q{index + 1}: {question.question_text}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            Topic: {question.topic} | Difficulty: {question.difficulty} | Level: {question.bloom_taxonomy_level}
                          </Typography>
                        </Box>
                      ))}
                      {generationResults.mcq.questions.length > 3 && (
                        <Typography variant="body2" color="text.secondary" align="center">
                          ... and {generationResults.mcq.questions.length - 3} more questions
                        </Typography>
                      )}
                    </Box>
                    <Box sx={{ mt: 2, display: 'flex', gap: 1 }}>
                      <Button
                        variant="contained"
                        size="small"
                        onClick={() => {
                          // Store question IDs in session storage and navigate to practice mode
                          const questionIds = generationResults.mcq?.saved_question_ids;
                          if (questionIds) {
                            sessionStorage.setItem('practiceQuestionIds', JSON.stringify(questionIds));
                            sessionStorage.setItem('practiceCourseName', 'Generated Questions');
                            window.open('/mcq/practice/generated', '_blank');
                          }
                        }}
                      >
                        Practice Now
                      </Button>
                      <Button
                        variant="outlined"
                        size="small"
                        onClick={() => {
                          // Store question IDs in session storage and navigate to exam mode
                          const questionIds = generationResults.mcq?.saved_question_ids;
                          if (questionIds) {
                            sessionStorage.setItem('examQuestionIds', JSON.stringify(questionIds));
                            sessionStorage.setItem('examCourseName', 'Generated Questions');
                            window.open('/mcq/exam/generated', '_blank');
                          }
                        }}
                      >
                        Take Exam
                      </Button>
                    </Box>
                  </CardContent>
                </Card>
              )}

              {/* Flashcard Results */}
              {generationResults.flashcards && (
                <Card sx={{ mb: 2 }}>
                  <CardContent>
                    <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <FlashcardIcon color="primary" />
                      Flashcards Generated ({generationResults.flashcards.total_count})
                    </Typography>
                    <Typography variant="body2" color="success.main" sx={{ mb: 1 }}>
                      ✅ {generationResults.flashcards.message}
                    </Typography>
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                      Topics covered: {generationResults.flashcards.source_topics.join(', ')}
                    </Typography>
                    <Box sx={{ maxHeight: 200, overflow: 'auto' }}>
                      {generationResults.flashcards.flashcards.slice(0, 3).map((flashcard, index) => (
                        <Box key={index} sx={{ mb: 2, p: 2, bgcolor: 'background.default', borderRadius: 1 }}>
                          <Typography variant="body2" sx={{ fontWeight: 'bold', mb: 1 }}>
                            Card {index + 1}: {flashcard.front_content}
                          </Typography>
                          <Typography variant="body2" sx={{ mb: 1, color: 'text.secondary' }}>
                            Answer: {flashcard.back_content}
                          </Typography>
                          <Typography variant="caption" color="text.secondary">
                            Topic: {flashcard.topic} | Difficulty: {flashcard.difficulty} | Type: {flashcard.card_type}
                          </Typography>
                        </Box>
                      ))}
                      {generationResults.flashcards.flashcards.length > 3 && (
                        <Typography variant="body2" color="text.secondary" align="center">
                          ... and {generationResults.flashcards.flashcards.length - 3} more flashcards
                        </Typography>
                      )}
                    </Box>
                    <Box sx={{ mt: 2, display: 'flex', gap: 1 }}>
                      <Button
                        variant="contained"
                        size="small"
                        onClick={() => {
                          // Store flashcard IDs in session storage and navigate to practice mode
                          const flashcardIds = generationResults.flashcards?.saved_flashcard_ids;
                          if (flashcardIds) {
                            sessionStorage.setItem('practiceFlashcardIds', JSON.stringify(flashcardIds));
                            sessionStorage.setItem('practiceCourseName', 'Generated Flashcards');
                            window.open('/flashcards/practice/generated', '_blank');
                          }
                        }}
                      >
                        Practice Now
                      </Button>
                    </Box>
                  </CardContent>
                </Card>
              )}

            </Box>
          )}
        </motion.div>
      )}

      {/* Processing Modal */}
      <ProcessingModal
        open={showProcessingModal}
        stages={UPLOAD_STAGES}
        currentStage={currentStage}
        onComplete={handleProcessingComplete}
        title="Uploading PDF"
      />
    </Box>
  );
};

export default GeminiPDFUploadTool;
