import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  CircularProgress,
  useTheme,
  Card,
  CardContent,
  CardHeader,
  Chip,
  Divider,
  Alert,
  Grid,
  useMediaQuery,
  IconButton,
  Collapse,
  Tabs,
  Tab,
} from '@mui/material';
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  Legend,
  ResponsiveContainer,
  PieChart,
  Pie,
  Cell,
  RadarChart,
  PolarGrid,
  PolarAngleAxis,
  PolarRadiusAxis,
  Radar,
} from 'recharts';
import {
  ExpandMore as ExpandMoreIcon,
  ExpandLess as ExpandLessIcon,
  BarChart as BarChartIcon,
  <PERSON><PERSON>hart as PieChartIcon,
  Timeline as TimelineIcon,
  List as ListIcon,
} from '@mui/icons-material';
import { useQuery } from '@tanstack/react-query';
import { getExamTopicAnalysis, getCourseTopicAnalysis, TopicAnalysis as TopicAnalysisType } from '../../api/studentProgress';
import { motion } from 'framer-motion';

interface TopicAnalysisProps {
  examId?: number;
  courseId?: number;
  studentId: number;
  showTimeData?: boolean;
}

const TopicAnalysis: React.FC<TopicAnalysisProps> = ({
  examId,
  courseId,
  studentId,
  showTimeData = true,
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const isTablet = useMediaQuery(theme.breakpoints.between('sm', 'md'));
  const [chartData, setChartData] = useState<any[]>([]);
  const [tableExpanded, setTableExpanded] = useState(!isMobile);
  const [mobileChartType, setMobileChartType] = useState(0);

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.2
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        type: 'spring',
        stiffness: 300,
        damping: 24
      }
    }
  };

  // Fetch topic analysis data
  const {
    data: topicAnalysis,
    isLoading,
    error,
  } = useQuery({
    queryKey: examId ? ['examTopicAnalysis', examId] : ['courseTopicAnalysis', courseId, studentId],
    queryFn: () => examId
      ? getExamTopicAnalysis(examId)
      : getCourseTopicAnalysis(studentId, courseId!),
    enabled: !!(examId || (courseId && studentId)),
    retry: 1,
    retryDelay: 1000,
  });

  // Process data for charts
  useEffect(() => {
    if (topicAnalysis) {
      const data = Object.entries(topicAnalysis).map(([topic, stats]) => ({
        topic: topic === 'null' || !topic ? 'Uncategorized' : topic,
        score: Math.round(stats.score),
        correctCount: stats.correct_count,
        totalCount: stats.total_count,
        avgTimeSpent: Math.round(stats.avg_time_spent),
        examsCount: stats.exams_count || 1,
      }));
      setChartData(data);
    }
  }, [topicAnalysis]);

  // Colors for charts
  const COLORS = [
    theme.palette.primary.main,
    theme.palette.secondary.main,
    theme.palette.success.main,
    theme.palette.error.main,
    theme.palette.warning.main,
    theme.palette.info.main,
  ];

  // Format time for display
  const formatTime = (seconds: number) => {
    if (seconds < 60) return `${seconds}s`;
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = seconds % 60;
    return `${minutes}m ${remainingSeconds}s`;
  };

  // Get color based on score
  const getScoreColor = (score: number) => {
    if (score >= 80) return theme.palette.success.main;
    if (score >= 60) return theme.palette.warning.main;
    return theme.palette.error.main;
  };

  // Handle mobile chart type change
  const handleMobileChartChange = (event: React.SyntheticEvent, newValue: number) => {
    setMobileChartType(newValue);
  };

  // Toggle table expanded state
  const toggleTableExpanded = () => {
    setTableExpanded(!tableExpanded);
  };

  if (isLoading) {
    return (
      <Box sx={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        flexDirection: 'column',
        p: { xs: 2, sm: 3 },
        minHeight: '200px'
      }}>
        <motion.div
          initial={{ scale: 0.8, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          transition={{ duration: 0.5 }}
        >
          <CircularProgress size={isMobile ? 40 : 50} />
        </motion.div>
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.3 }}
        >
          <Typography variant="body2" color="text.secondary" sx={{ mt: 2 }}>
            Loading topic analysis...
          </Typography>
        </motion.div>
      </Box>
    );
  }

  if (error) {
    console.error("Topic analysis error:", error);
    return (
      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Alert
          severity="warning"
          sx={{
            mt: 2,
            borderRadius: 2,
            boxShadow: '0 2px 8px rgba(0,0,0,0.05)'
          }}
        >
          <Typography variant="subtitle2" fontWeight="medium">
            Topic analysis is not available
          </Typography>
          <Typography variant="body2">
            This may be because the questions don't have topics assigned or there was an error retrieving the data.
          </Typography>
        </Alert>
      </motion.div>
    );
  }

  if (!topicAnalysis || Object.keys(topicAnalysis).length === 0) {
    return (
      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        <Alert
          severity="info"
          sx={{
            mt: 2,
            borderRadius: 2,
            boxShadow: '0 2px 8px rgba(0,0,0,0.05)'
          }}
        >
          <Typography variant="subtitle2" fontWeight="medium">
            No topic data available
          </Typography>
          <Typography variant="body2">
            Try taking more exams or practice sessions to generate topic-based analysis. This will help you track your progress by subject area.
          </Typography>
        </Alert>
      </motion.div>
    );
  }

  return (
    <motion.div
      initial="hidden"
      animate="visible"
      variants={containerVariants}
    >
      <Box sx={{ mt: { xs: 2, sm: 3 } }}>
        <motion.div variants={itemVariants}>
          <Typography
            variant={isMobile ? "h6" : "h5"}
            gutterBottom
            sx={{
              display: 'flex',
              alignItems: 'center',
              gap: 1
            }}
          >
            <BarChartIcon color="primary" />
            Topic Analysis
          </Typography>
        </motion.div>

        {/* Mobile Chart Selector */}
        {isMobile && (
          <motion.div variants={itemVariants}>
            <Box
              sx={{
                mb: 2,
                borderRadius: '8px',
                overflow: 'hidden',
                bgcolor: 'rgba(0,0,0,0.02)',
                border: '1px solid',
                borderColor: 'divider'
              }}
            >
              <Tabs
                value={mobileChartType}
                onChange={handleMobileChartChange}
                variant="fullWidth"
                aria-label="Chart type tabs"
                sx={{
                  '& .MuiTab-root': {
                    minHeight: '48px',
                    py: 1.2,
                    fontSize: '0.8rem'
                  }
                }}
              >
                <Tab
                  icon={<BarChartIcon fontSize="small" />}
                  label="Scores"
                  iconPosition="start"
                />
                <Tab
                  icon={<PieChartIcon fontSize="small" />}
                  label="Distribution"
                  iconPosition="start"
                />
                {showTimeData && (
                  <Tab
                    icon={<TimelineIcon fontSize="small" />}
                    label="Time"
                    iconPosition="start"
                  />
                )}
              </Tabs>
            </Box>
          </motion.div>
        )}

        {/* Desktop Grid Layout or Mobile Chart Based on Selection */}
        {isMobile ? (
          <motion.div variants={itemVariants}>
            <Card
              sx={{
                mb: 2,
                boxShadow: '0 2px 12px rgba(0,0,0,0.08)',
                borderRadius: 2
              }}
            >
              <CardHeader
                title={
                  mobileChartType === 0 ? "Score by Topic" :
                  mobileChartType === 1 ? "Questions by Topic" :
                  "Time Spent by Topic"
                }
                titleTypographyProps={{ variant: 'subtitle1', fontWeight: 'medium' }}
                sx={{ pb: 0 }}
              />
              <CardContent>
                <Box sx={{ height: 300 }}>
                  <ResponsiveContainer width="100%" height="100%">
                    {mobileChartType === 0 ? (
                      // Score Chart
                      <BarChart
                        data={chartData}
                        margin={{ top: 5, right: 10, left: 0, bottom: 5 }}
                      >
                        <CartesianGrid strokeDasharray="3 3" stroke="rgba(0,0,0,0.1)" />
                        <XAxis
                          dataKey="topic"
                          tick={{ fontSize: 10 }}
                          tickLine={{ stroke: 'rgba(0,0,0,0.2)' }}
                          axisLine={{ stroke: 'rgba(0,0,0,0.2)' }}
                        />
                        <YAxis
                          domain={[0, 100]}
                          tick={{ fontSize: 10 }}
                          tickLine={{ stroke: 'rgba(0,0,0,0.2)' }}
                          axisLine={{ stroke: 'rgba(0,0,0,0.2)' }}
                        />
                        <Tooltip
                          formatter={(value) => [`${value}%`, 'Score']}
                          contentStyle={{ fontSize: '12px', borderRadius: '4px' }}
                        />
                        <Legend wrapperStyle={{ fontSize: '10px' }} />
                        <Bar
                          dataKey="score"
                          name="Score (%)"
                          fill={theme.palette.primary.main}
                          radius={[4, 4, 0, 0]}
                          animationDuration={1000}
                        />
                      </BarChart>
                    ) : mobileChartType === 1 ? (
                      // Distribution Chart
                      <PieChart>
                        <Pie
                          data={chartData}
                          cx="50%"
                          cy="50%"
                          labelLine={false}
                          outerRadius={80}
                          fill="#8884d8"
                          dataKey="totalCount"
                          nameKey="topic"
                          label={({ name, percent }) =>
                            isMobile
                              ? `${(percent * 100).toFixed(0)}%`
                              : `${name}: ${(percent * 100).toFixed(0)}%`
                          }
                          animationDuration={1000}
                        >
                          {chartData.map((entry, index) => (
                            <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                          ))}
                        </Pie>
                        <Tooltip
                          formatter={(value, name, props) => [value, props.payload.topic]}
                          contentStyle={{ fontSize: '12px', borderRadius: '4px' }}
                        />
                        <Legend
                          wrapperStyle={{ fontSize: '10px' }}
                          layout="horizontal"
                          verticalAlign="bottom"
                        />
                      </PieChart>
                    ) : (
                      // Time Chart
                      <BarChart
                        data={chartData}
                        margin={{ top: 5, right: 10, left: 0, bottom: 5 }}
                      >
                        <CartesianGrid strokeDasharray="3 3" stroke="rgba(0,0,0,0.1)" />
                        <XAxis
                          dataKey="topic"
                          tick={{ fontSize: 10 }}
                          tickLine={{ stroke: 'rgba(0,0,0,0.2)' }}
                          axisLine={{ stroke: 'rgba(0,0,0,0.2)' }}
                        />
                        <YAxis
                          tick={{ fontSize: 10 }}
                          tickLine={{ stroke: 'rgba(0,0,0,0.2)' }}
                          axisLine={{ stroke: 'rgba(0,0,0,0.2)' }}
                        />
                        <Tooltip
                          formatter={(value) => [formatTime(value as number), 'Avg Time']}
                          contentStyle={{ fontSize: '12px', borderRadius: '4px' }}
                        />
                        <Legend wrapperStyle={{ fontSize: '10px' }} />
                        <Bar
                          dataKey="avgTimeSpent"
                          name="Average Time"
                          fill={theme.palette.secondary.main}
                          radius={[4, 4, 0, 0]}
                          animationDuration={1000}
                        />
                      </BarChart>
                    )}
                  </ResponsiveContainer>
                </Box>
              </CardContent>
            </Card>
          </motion.div>
        ) : (
          // Desktop Layout with Grid
          <motion.div variants={containerVariants}>
            <Grid container spacing={2}>
              {/* Score by Topic Chart */}
              <Grid item xs={12} md={6} component={motion.div} variants={itemVariants}>
                <Card
                  sx={{
                    boxShadow: '0 2px 12px rgba(0,0,0,0.08)',
                    borderRadius: 2,
                    transition: 'transform 0.2s ease',
                    '&:hover': {
                      transform: 'translateY(-4px)'
                    }
                  }}
                >
                  <CardHeader
                    title="Score by Topic"
                    titleTypographyProps={{ variant: 'subtitle1', fontWeight: 'medium' }}
                    avatar={<BarChartIcon color="primary" />}
                    sx={{ pb: 0 }}
                  />
                  <CardContent>
                    <Box sx={{ height: 300 }}>
                      <ResponsiveContainer width="100%" height="100%">
                        <BarChart
                          data={chartData}
                          margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                        >
                          <CartesianGrid strokeDasharray="3 3" stroke="rgba(0,0,0,0.1)" />
                          <XAxis dataKey="topic" />
                          <YAxis domain={[0, 100]} />
                          <Tooltip formatter={(value) => [`${value}%`, 'Score']} />
                          <Legend />
                          <Bar
                            dataKey="score"
                            name="Score (%)"
                            fill={theme.palette.primary.main}
                            radius={[4, 4, 0, 0]}
                            animationDuration={1000}
                          />
                        </BarChart>
                      </ResponsiveContainer>
                    </Box>
                  </CardContent>
                </Card>
              </Grid>

              {/* Questions Distribution Chart */}
              <Grid item xs={12} md={6} component={motion.div} variants={itemVariants}>
                <Card
                  sx={{
                    boxShadow: '0 2px 12px rgba(0,0,0,0.08)',
                    borderRadius: 2,
                    transition: 'transform 0.2s ease',
                    '&:hover': {
                      transform: 'translateY(-4px)'
                    }
                  }}
                >
                  <CardHeader
                    title="Questions by Topic"
                    titleTypographyProps={{ variant: 'subtitle1', fontWeight: 'medium' }}
                    avatar={<PieChartIcon color="primary" />}
                    sx={{ pb: 0 }}
                  />
                  <CardContent>
                    <Box sx={{ height: 300, display: 'flex', justifyContent: 'center' }}>
                      <ResponsiveContainer width="100%" height="100%">
                        <PieChart>
                          <Pie
                            data={chartData}
                            cx="50%"
                            cy="50%"
                            labelLine={false}
                            outerRadius={80}
                            fill="#8884d8"
                            dataKey="totalCount"
                            nameKey="topic"
                            label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                            animationDuration={1000}
                          >
                            {chartData.map((entry, index) => (
                              <Cell key={`cell-${index}`} fill={COLORS[index % COLORS.length]} />
                            ))}
                          </Pie>
                          <Tooltip formatter={(value, name, props) => [value, props.payload.topic]} />
                          <Legend />
                        </PieChart>
                      </ResponsiveContainer>
                    </Box>
                  </CardContent>
                </Card>
              </Grid>

              {/* Time Spent Chart (conditional) */}
              {showTimeData && (
                <Grid item xs={12} component={motion.div} variants={itemVariants}>
                  <Card
                    sx={{
                      boxShadow: '0 2px 12px rgba(0,0,0,0.08)',
                      borderRadius: 2,
                      transition: 'transform 0.2s ease',
                      '&:hover': {
                        transform: 'translateY(-4px)'
                      }
                    }}
                  >
                    <CardHeader
                      title="Average Time Spent by Topic"
                      titleTypographyProps={{ variant: 'subtitle1', fontWeight: 'medium' }}
                      avatar={<TimelineIcon color="primary" />}
                      sx={{ pb: 0 }}
                    />
                    <CardContent>
                      <Box sx={{ height: 300 }}>
                        <ResponsiveContainer width="100%" height="100%">
                          <BarChart
                            data={chartData}
                            margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                          >
                            <CartesianGrid strokeDasharray="3 3" stroke="rgba(0,0,0,0.1)" />
                            <XAxis dataKey="topic" />
                            <YAxis />
                            <Tooltip formatter={(value) => [formatTime(value as number), 'Avg Time']} />
                            <Legend />
                            <Bar
                              dataKey="avgTimeSpent"
                              name="Average Time (seconds)"
                              fill={theme.palette.secondary.main}
                              radius={[4, 4, 0, 0]}
                              animationDuration={1000}
                            />
                          </BarChart>
                        </ResponsiveContainer>
                      </Box>
                    </CardContent>
                  </Card>
                </Grid>
              )}
            </Grid>
          </motion.div>
        )}

        {/* Topic Performance Table with Toggle on Mobile */}
        <motion.div variants={itemVariants}>
          <Box sx={{ mt: 3 }}>
            <Box
              sx={{
                display: 'flex',
                justifyContent: 'space-between',
                alignItems: 'center',
                mb: 1
              }}
            >
              <Typography
                variant={isMobile ? "subtitle1" : "h6"}
                fontWeight="medium"
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: 1
                }}
              >
                <ListIcon color="primary" fontSize="small" />
                Topic Performance Details
              </Typography>

              {isMobile && (
                <IconButton
                  onClick={toggleTableExpanded}
                  size="small"
                  sx={{
                    bgcolor: 'rgba(0,0,0,0.03)',
                    '&:hover': { bgcolor: 'rgba(0,0,0,0.08)' }
                  }}
                >
                  {tableExpanded ? <ExpandLessIcon /> : <ExpandMoreIcon />}
                </IconButton>
              )}
            </Box>

            <Collapse in={tableExpanded}>
              <Card
                variant="outlined"
                sx={{
                  borderRadius: 2,
                  overflow: 'hidden'
                }}
              >
                <TableContainer
                  sx={{
                    maxHeight: isMobile ? 300 : 400,
                    overflowY: 'auto',
                    '&::-webkit-scrollbar': {
                      width: '8px',
                      height: '8px',
                    },
                    '&::-webkit-scrollbar-thumb': {
                      backgroundColor: 'rgba(0,0,0,0.1)',
                      borderRadius: '4px',
                    }
                  }}
                >
                  <Table size={isMobile ? "small" : "medium"} stickyHeader>
                    <TableHead>
                      <TableRow>
                        <TableCell
                          sx={{
                            fontWeight: 'bold',
                            bgcolor: theme.palette.background.paper
                          }}
                        >
                          Topic
                        </TableCell>
                        <TableCell
                          align="center"
                          sx={{
                            fontWeight: 'bold',
                            bgcolor: theme.palette.background.paper
                          }}
                        >
                          Score
                        </TableCell>
                        <TableCell
                          align="center"
                          sx={{
                            fontWeight: 'bold',
                            bgcolor: theme.palette.background.paper
                          }}
                        >
                          Correct/Total
                        </TableCell>
                        {showTimeData && !isMobile && (
                          <TableCell
                            align="center"
                            sx={{
                              fontWeight: 'bold',
                              bgcolor: theme.palette.background.paper
                            }}
                          >
                            Avg Time
                          </TableCell>
                        )}
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {chartData.map((topic) => (
                        <TableRow
                          key={topic.topic}
                          sx={{
                            '&:hover': {
                              bgcolor: 'rgba(0,0,0,0.02)'
                            }
                          }}
                        >
                          <TableCell sx={{ fontSize: isMobile ? '0.8rem' : '0.875rem' }}>
                            {topic.topic}
                          </TableCell>
                          <TableCell align="center">
                            <Chip
                              label={`${topic.score}%`}
                              size="small"
                              sx={{
                                bgcolor: getScoreColor(topic.score),
                                color: 'white',
                                fontSize: isMobile ? '0.7rem' : '0.75rem',
                                height: isMobile ? '22px' : '24px'
                              }}
                            />
                          </TableCell>
                          <TableCell
                            align="center"
                            sx={{ fontSize: isMobile ? '0.8rem' : '0.875rem' }}
                          >
                            {topic.correctCount}/{topic.totalCount}
                          </TableCell>
                          {showTimeData && !isMobile && (
                            <TableCell
                              align="center"
                              sx={{ fontSize: '0.875rem' }}
                            >
                              {formatTime(topic.avgTimeSpent)}
                            </TableCell>
                          )}
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              </Card>
            </Collapse>
          </Box>
        </motion.div>
      </Box>
    </motion.div>
  );
};

export default TopicAnalysis;
