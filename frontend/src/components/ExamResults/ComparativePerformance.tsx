import React, { useState, useEffect } from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  <PERSON>,
  Card<PERSON>ontent,
  CardHeader,
  useTheme,
  useMediaQuery,
  CircularProgress,
  Alert,
  Tooltip,
  IconButton,
  Chip,
} from '@mui/material';
import {
  <PERSON><PERSON>hart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  ResponsiveContainer,
  Legend,
  ReferenceLine,
  AreaChart,
  Area,
  LineChart,
  Line,
  Scatter,
  ScatterChart,
  Cell,
  Label,
} from 'recharts';
import {
  InfoOutlined as InfoIcon,
  EmojiEvents as TrophyIcon,
  Groups as GroupsIcon,
  Person as PersonIcon,
} from '@mui/icons-material';
import { motion } from 'framer-motion';

// Define the props interface
interface ComparativePerformanceProps {
  examId: number;
  courseId: number;
  studentScore: number;
  isLoading?: boolean;
}

// Mock data generator function
const generateMockPerformanceData = (studentScore: number) => {
  // Generate a normal distribution of scores around a mean
  const mean = Math.min(Math.max(studentScore - 5 + Math.random() * 10, 50), 85);
  const stdDev = 10 + Math.random() * 5;
  
  // Generate score distribution
  const distribution: { score: number; count: number; isYou?: boolean }[] = [];
  const scoreRanges = ['0-10', '11-20', '21-30', '31-40', '41-50', '51-60', '61-70', '71-80', '81-90', '91-100'];
  const counts = [0, 0, 0, 0, 0, 0, 0, 0, 0, 0];
  
  // Generate 50 random scores
  for (let i = 0; i < 50; i++) {
    // Box-Muller transform for normal distribution
    const u1 = Math.random();
    const u2 = Math.random();
    const z0 = Math.sqrt(-2.0 * Math.log(u1)) * Math.cos(2.0 * Math.PI * u2);
    let score = Math.round(z0 * stdDev + mean);
    
    // Clamp score between 0 and 100
    score = Math.min(Math.max(score, 0), 100);
    
    // Increment the appropriate bin
    const bin = Math.floor(score / 10);
    if (bin >= 0 && bin < 10) {
      counts[bin]++;
    }
  }
  
  // Create the distribution data
  for (let i = 0; i < 10; i++) {
    const scoreRange = scoreRanges[i];
    const count = counts[i];
    const studentBin = Math.floor(studentScore / 10);
    
    distribution.push({
      score: scoreRange,
      count,
      isYou: i === studentBin
    });
  }
  
  // Calculate percentile
  let belowCount = 0;
  for (let i = 0; i < Math.floor(studentScore / 10); i++) {
    belowCount += counts[i];
  }
  
  // Add partial count from the student's bin
  const studentBin = Math.floor(studentScore / 10);
  const positionInBin = (studentScore % 10) / 10;
  belowCount += counts[studentBin] * positionInBin;
  
  const totalStudents = counts.reduce((sum, count) => sum + count, 0);
  const percentile = Math.round((belowCount / totalStudents) * 100);
  
  // Calculate class average
  let totalScore = 0;
  let totalCount = 0;
  
  for (let i = 0; i < 10; i++) {
    const midpoint = i * 10 + 5; // Midpoint of the range
    totalScore += midpoint * counts[i];
    totalCount += counts[i];
  }
  
  const classAverage = Math.round(totalScore / totalCount);
  
  return {
    distribution,
    percentile,
    classAverage,
    totalStudents
  };
};

const ComparativePerformance: React.FC<ComparativePerformanceProps> = ({
  examId,
  courseId,
  studentScore,
  isLoading = false,
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const [performanceData, setPerformanceData] = useState<any>(null);
  
  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        duration: 0.5
      }
    }
  };
  
  // Generate mock data on component mount or when studentScore changes
  useEffect(() => {
    if (!isLoading && studentScore !== undefined) {
      // In a real implementation, this would be an API call
      // For now, we'll use mock data
      const data = generateMockPerformanceData(studentScore);
      setPerformanceData(data);
    }
  }, [isLoading, studentScore, examId, courseId]);
  
  if (isLoading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', p: 3 }}>
        <CircularProgress />
      </Box>
    );
  }
  
  if (!performanceData) {
    return (
      <Alert severity="info" sx={{ mt: 2 }}>
        Comparative performance data is not available for this exam.
      </Alert>
    );
  }
  
  const { distribution, percentile, classAverage, totalStudents } = performanceData;
  
  // Determine performance level
  const getPerformanceLevel = (score: number, average: number) => {
    if (score >= average + 15) return { text: 'Excellent', color: theme.palette.success.main };
    if (score >= average + 5) return { text: 'Above Average', color: theme.palette.success.light };
    if (score >= average - 5) return { text: 'Average', color: theme.palette.info.main };
    if (score >= average - 15) return { text: 'Below Average', color: theme.palette.warning.main };
    return { text: 'Needs Improvement', color: theme.palette.error.main };
  };
  
  const performanceLevel = getPerformanceLevel(studentScore, classAverage);
  
  return (
    <motion.div
      initial="hidden"
      animate="visible"
      variants={containerVariants}
    >
      <Card 
        sx={{ 
          mt: 3, 
          borderRadius: 2,
          boxShadow: '0 2px 12px rgba(0,0,0,0.08)',
          overflow: 'hidden'
        }}
      >
        <CardHeader
          title={
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <GroupsIcon color="primary" />
              <Typography variant={isMobile ? "subtitle1" : "h6"} fontWeight="medium">
                Class Performance Comparison
              </Typography>
              <Tooltip title="Compare your performance with other students who took this exam">
                <IconButton size="small">
                  <InfoIcon fontSize="small" />
                </IconButton>
              </Tooltip>
            </Box>
          }
          sx={{ pb: 0 }}
        />
        <CardContent>
          <Box sx={{ mb: 2, display: 'flex', flexDirection: isMobile ? 'column' : 'row', gap: 2 }}>
            <Card 
              variant="outlined" 
              sx={{ 
                p: 1.5, 
                flex: 1, 
                display: 'flex', 
                flexDirection: 'column', 
                alignItems: 'center',
                justifyContent: 'center',
                borderRadius: 2,
                bgcolor: theme.palette.background.default
              }}
            >
              <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                Your Score
              </Typography>
              <Typography variant="h4" fontWeight="bold" color="primary.main">
                {studentScore}%
              </Typography>
              <Chip 
                label={performanceLevel.text} 
                size="small" 
                sx={{ 
                  mt: 1, 
                  bgcolor: performanceLevel.color,
                  color: 'white',
                  fontWeight: 'medium'
                }} 
              />
            </Card>
            
            <Card 
              variant="outlined" 
              sx={{ 
                p: 1.5, 
                flex: 1, 
                display: 'flex', 
                flexDirection: 'column', 
                alignItems: 'center',
                justifyContent: 'center',
                borderRadius: 2,
                bgcolor: theme.palette.background.default
              }}
            >
              <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                Class Average
              </Typography>
              <Typography variant="h4" fontWeight="bold" color="text.primary">
                {classAverage}%
              </Typography>
              <Typography variant="caption" color="text.secondary" sx={{ mt: 1 }}>
                {totalStudents} students
              </Typography>
            </Card>
            
            <Card 
              variant="outlined" 
              sx={{ 
                p: 1.5, 
                flex: 1, 
                display: 'flex', 
                flexDirection: 'column', 
                alignItems: 'center',
                justifyContent: 'center',
                borderRadius: 2,
                bgcolor: theme.palette.background.default
              }}
            >
              <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                Your Percentile
              </Typography>
              <Typography variant="h4" fontWeight="bold" color="secondary.main">
                {percentile}%
              </Typography>
              <Typography variant="caption" color="text.secondary" sx={{ mt: 1 }}>
                Better than {percentile}% of students
              </Typography>
            </Card>
          </Box>
          
          <Box sx={{ height: isMobile ? 250 : 300, mt: 3 }}>
            <Typography variant="subtitle2" gutterBottom>
              Score Distribution
            </Typography>
            <ResponsiveContainer width="100%" height="100%">
              <BarChart
                data={distribution}
                margin={{
                  top: 5,
                  right: 10,
                  left: isMobile ? 0 : 10,
                  bottom: 5,
                }}
              >
                <CartesianGrid strokeDasharray="3 3" vertical={!isMobile} />
                <XAxis 
                  dataKey="score" 
                  tick={{ fontSize: isMobile ? 10 : 12 }}
                  interval={isMobile ? 1 : 0}
                />
                <YAxis 
                  tick={{ fontSize: isMobile ? 10 : 12 }}
                  width={isMobile ? 25 : 35}
                  label={
                    isMobile ? undefined : {
                      value: 'Number of Students',
                      angle: -90,
                      position: 'insideLeft',
                      style: { fontSize: '12px' }
                    }
                  }
                />
                <Legend wrapperStyle={{ fontSize: isMobile ? 10 : 12 }} />
                <ReferenceLine
                  x={Math.floor(studentScore / 10)}
                  stroke={theme.palette.primary.main}
                  strokeWidth={2}
                  strokeDasharray="3 3"
                  label={
                    isMobile ? undefined : {
                      value: 'Your Score',
                      position: 'top',
                      fill: theme.palette.primary.main,
                      fontSize: 12
                    }
                  }
                />
                <Bar 
                  dataKey="count" 
                  name="Students" 
                  fill={theme.palette.info.light}
                  radius={[4, 4, 0, 0]}
                >
                  {distribution.map((entry, index) => (
                    <Cell 
                      key={`cell-${index}`} 
                      fill={entry.isYou ? theme.palette.primary.main : theme.palette.info.light} 
                    />
                  ))}
                </Bar>
              </BarChart>
            </ResponsiveContainer>
          </Box>
        </CardContent>
      </Card>
    </motion.div>
  );
};

export default ComparativePerformance;
