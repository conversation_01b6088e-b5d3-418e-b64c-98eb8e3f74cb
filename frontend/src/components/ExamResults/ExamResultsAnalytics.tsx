import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Divider,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  CircularProgress,
  useTheme,
  Card,
  CardContent,
  CardHeader,
  Button,
  Tabs,
  Tab,
} from '@mui/material';
import {
  CheckCircle as CorrectIcon,
  Cancel as IncorrectIcon,
  TrendingUp as TrendingUpIcon,
  Timeline as TimelineIcon,
  EmojiEvents as TrophyIcon,
  School as SchoolIcon,
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { useAuth } from '../../contexts/AuthContext';
import { getExams, getExamWithAttempts, ExamWithAttempts } from '../../api/studentProgress';
import { useQuery } from '@tanstack/react-query';

// Import chart components
import {
  <PERSON><PERSON>hart,
  Bar,
  LineChart,
  Line,
  XAxis,
  YA<PERSON>s,
  CartesianGrid,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON><PERSON>ons<PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>,
  <PERSON>,
  <PERSON>,
} from 'recharts';

interface ExamResultsAnalyticsProps {
  examId: number;
  score: number;
  correctAnswers: number;
  totalQuestions: number;
  courseId?: number;
}

const ExamResultsAnalytics: React.FC<ExamResultsAnalyticsProps> = ({
  examId,
  score,
  correctAnswers,
  totalQuestions,
  courseId,
}) => {
  const theme = useTheme();
  const navigate = useNavigate();
  const { user } = useAuth();
  const [tabValue, setTabValue] = useState(0);
  const [historicalData, setHistoricalData] = useState<any[]>([]);

  // Fetch exam details with attempts
  const { data: examWithAttempts, isLoading: isLoadingExam } = useQuery(
    ['exam', examId],
    () => getExamWithAttempts(examId),
    {
      enabled: !!examId,
      retry: 3,
      retryDelay: 1000,
      onError: (error) => {
        console.error('Error fetching exam details:', error);
      },
    }
  );

  // Fetch historical exams for this course
  const { data: exams, isLoading: isLoadingExams } = useQuery(
    ['exams', user?.id, courseId],
    () => getExams(user?.id || 0, courseId),
    {
      enabled: !!user?.id && !!courseId,
      onError: (error) => {
        console.error('Error fetching historical exams:', error);
      },
    }
  );

  // Process historical data for charts
  useEffect(() => {
    if (exams) {
      // Sort exams by date
      const sortedExams = [...exams].sort((a, b) =>
        new Date(a.start_time).getTime() - new Date(b.start_time).getTime()
      );

      // Format data for charts
      const chartData = sortedExams.map((exam, index) => ({
        id: exam.id,
        name: `Exam ${index + 1}`,
        score: exam.score || 0,
        date: new Date(exam.start_time).toLocaleDateString(),
        correctAnswers: exam.correct_answers || 0,
        totalQuestions: exam.total_questions,
        timestamp: new Date(exam.start_time).getTime(),
      }));

      setHistoricalData(chartData);
    }
  }, [exams]);

  // Calculate performance metrics
  const incorrectAnswers = totalQuestions - correctAnswers;
  const percentageCorrect = (correctAnswers / totalQuestions) * 100;
  const percentageIncorrect = (incorrectAnswers / totalQuestions) * 100;

  // Data for pie chart
  const pieData = [
    { name: 'Correct', value: correctAnswers, color: theme.palette.success.main },
    { name: 'Incorrect', value: incorrectAnswers, color: theme.palette.error.main },
  ];

  // Handle tab change
  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  // Get performance grade
  const getGrade = (score: number) => {
    if (score >= 90) return { grade: 'A', color: theme.palette.success.dark };
    if (score >= 80) return { grade: 'B', color: theme.palette.success.main };
    if (score >= 70) return { grade: 'C', color: theme.palette.warning.main };
    if (score >= 60) return { grade: 'D', color: theme.palette.warning.dark };
    return { grade: 'F', color: theme.palette.error.main };
  };

  const grade = getGrade(score);

  // Get performance feedback
  const getFeedback = (score: number) => {
    if (score >= 90) return 'Excellent! You have mastered this material.';
    if (score >= 80) return 'Great job! You have a strong understanding of the material.';
    if (score >= 70) return 'Good work! You understand most of the material, but there\'s room for improvement.';
    if (score >= 60) return 'You passed, but should review the material more thoroughly.';
    return 'You need to study more and retake this exam.';
  };

  // Get historical performance trend
  const getPerformanceTrend = () => {
    if (historicalData.length < 2) return null;

    const latestScores = historicalData.slice(-3);
    const trend = latestScores[latestScores.length - 1].score - latestScores[0].score;

    if (trend > 5) return { text: 'Improving', color: theme.palette.success.main };
    if (trend < -5) return { text: 'Declining', color: theme.palette.error.main };
    return { text: 'Stable', color: theme.palette.info.main };
  };

  const performanceTrend = getPerformanceTrend();

  return (
    <Box sx={{ mt: 4 }}>
      {isLoadingExam || isLoadingExams ? (
        <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
          <CircularProgress />
        </Box>
      ) : (
        <>
          {/* Performance Summary Card */}
          <Card sx={{ mb: 4, boxShadow: theme.shadows[3] }}>
            <CardHeader
              title="Exam Performance Summary"
              titleTypographyProps={{ variant: 'h5', fontWeight: 'bold' }}
              avatar={<TrophyIcon color="primary" fontSize="large" />}
            />
            <CardContent>
              <Grid container spacing={3}>
                <Grid item xs={12} md={4}>
                  <Box sx={{ textAlign: 'center' }}>
                    <Box sx={{ position: 'relative', display: 'inline-flex' }}>
                      <CircularProgress
                        variant="determinate"
                        value={score}
                        size={120}
                        thickness={5}
                        sx={{
                          color: grade.color,
                          circle: {
                            strokeLinecap: 'round',
                          }
                        }}
                      />
                      <Box
                        sx={{
                          top: 0,
                          left: 0,
                          bottom: 0,
                          right: 0,
                          position: 'absolute',
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                        }}
                      >
                        <Typography variant="h4" fontWeight="bold" color={grade.color}>
                          {Math.round(score)}%
                        </Typography>
                      </Box>
                    </Box>
                    <Typography variant="h5" sx={{ mt: 2 }}>
                      Grade: <span style={{ color: grade.color, fontWeight: 'bold' }}>{grade.grade}</span>
                    </Typography>
                    <Typography variant="body1" sx={{ mt: 1 }}>
                      {getFeedback(score)}
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={12} md={4}>
                  <Box>
                    <Typography variant="h6" gutterBottom>
                      Performance Details
                    </Typography>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                      <Typography variant="body1">Correct Answers:</Typography>
                      <Typography variant="body1" fontWeight="bold" color="success.main">
                        {correctAnswers} / {totalQuestions} ({Math.round(percentageCorrect)}%)
                      </Typography>
                    </Box>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                      <Typography variant="body1">Incorrect Answers:</Typography>
                      <Typography variant="body1" fontWeight="bold" color="error.main">
                        {incorrectAnswers} / {totalQuestions} ({Math.round(percentageIncorrect)}%)
                      </Typography>
                    </Box>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                      <Typography variant="body1">Exam ID:</Typography>
                      <Typography variant="body1" fontWeight="bold">
                        {examId}
                      </Typography>
                    </Box>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                      <Typography variant="body1">Date:</Typography>
                      <Typography variant="body1" fontWeight="bold">
                        {examWithAttempts?.start_time ? new Date(examWithAttempts.start_time).toLocaleString() : 'N/A'}
                      </Typography>
                    </Box>
                  </Box>
                </Grid>
                <Grid item xs={12} md={4}>
                  <Box>
                    <Typography variant="h6" gutterBottom>
                      Performance Trend
                    </Typography>
                    {performanceTrend ? (
                      <>
                        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                          <TrendingUpIcon sx={{ mr: 1, color: performanceTrend.color }} />
                          <Typography variant="body1" fontWeight="bold" sx={{ color: performanceTrend.color }}>
                            {performanceTrend.text}
                          </Typography>
                        </Box>
                        <Typography variant="body2">
                          Based on your last {Math.min(historicalData.length, 3)} exams, your performance is {performanceTrend.text.toLowerCase()}.
                        </Typography>
                      </>
                    ) : (
                      <Typography variant="body2">
                        Not enough data to determine performance trend. Take more exams to see your progress.
                      </Typography>
                    )}
                    <Box sx={{ mt: 2 }}>
                      <Button
                        variant="outlined"
                        color="primary"
                        fullWidth
                        onClick={() => navigate('/mcq/history')}
                      >
                        View Full History
                      </Button>
                    </Box>
                  </Box>
                </Grid>
              </Grid>
            </CardContent>
          </Card>

          {/* Tabs for different analytics views */}
          <Box sx={{ mb: 2 }}>
            <Tabs
              value={tabValue}
              onChange={handleTabChange}
              variant="fullWidth"
              textColor="primary"
              indicatorColor="primary"
            >
              <Tab label="Current Exam" icon={<SchoolIcon />} iconPosition="start" />
              <Tab label="Historical Performance" icon={<TimelineIcon />} iconPosition="start" />
            </Tabs>
          </Box>

          {/* Current Exam Tab */}
          {tabValue === 0 && (
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <Card sx={{ height: '100%', boxShadow: theme.shadows[3] }}>
                  <CardHeader title="Answer Distribution" />
                  <CardContent>
                    <Box sx={{ height: 300 }}>
                      <ResponsiveContainer width="100%" height="100%">
                        <PieChart>
                          <Pie
                            data={pieData}
                            cx="50%"
                            cy="50%"
                            labelLine={false}
                            outerRadius={100}
                            fill="#8884d8"
                            dataKey="value"
                            label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                          >
                            {pieData.map((entry, index) => (
                              <Cell key={`cell-${index}`} fill={entry.color} />
                            ))}
                          </Pie>
                          <Tooltip formatter={(value) => [`${value} questions`, '']} />
                          <Legend />
                        </PieChart>
                      </ResponsiveContainer>
                    </Box>
                  </CardContent>
                </Card>
              </Grid>
              <Grid item xs={12} md={6}>
                <Card sx={{ height: '100%', boxShadow: theme.shadows[3] }}>
                  <CardHeader title="Question Analysis" />
                  <CardContent>
                    <TableContainer component={Paper} variant="outlined" sx={{ maxHeight: 300, overflow: 'auto' }}>
                      <Table stickyHeader size="small">
                        <TableHead>
                          <TableRow>
                            <TableCell>Question</TableCell>
                            <TableCell>Your Answer</TableCell>
                            <TableCell>Correct</TableCell>
                          </TableRow>
                        </TableHead>
                        <TableBody>
                          {examWithAttempts?.attempts?.length > 0 ? (
                            examWithAttempts.attempts.map((attempt) => (
                              <TableRow key={attempt.id}>
                                <TableCell>Question {attempt.question_id}</TableCell>
                                <TableCell>{attempt.selected_answer || 'No answer'}</TableCell>
                                <TableCell>
                                  {attempt.is_correct ? (
                                    <CorrectIcon color="success" />
                                  ) : (
                                    <IncorrectIcon color="error" />
                                  )}
                                </TableCell>
                              </TableRow>
                            ))
                          ) : (
                            <TableRow>
                              <TableCell colSpan={3} align="center">
                                <Typography variant="body2" color="text.secondary">
                                  No attempt data available
                                </Typography>
                              </TableCell>
                            </TableRow>
                          )}
                        </TableBody>
                      </Table>
                    </TableContainer>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          )}

          {/* Historical Performance Tab */}
          {tabValue === 1 && (
            <Grid container spacing={3}>
              <Grid item xs={12}>
                <Card sx={{ boxShadow: theme.shadows[3] }}>
                  <CardHeader title="Score History" />
                  <CardContent>
                    <Box sx={{ height: 300 }}>
                      {historicalData.length > 0 ? (
                        <ResponsiveContainer width="100%" height="100%">
                          <LineChart
                            data={historicalData}
                            margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                          >
                            <CartesianGrid strokeDasharray="3 3" />
                            <XAxis dataKey="date" />
                            <YAxis domain={[0, 100]} />
                            <Tooltip formatter={(value) => [`${value}%`, 'Score']} />
                            <Legend />
                            <Line
                              type="monotone"
                              dataKey="score"
                              stroke={theme.palette.primary.main}
                              activeDot={{ r: 8 }}
                              strokeWidth={2}
                            />
                          </LineChart>
                        </ResponsiveContainer>
                      ) : (
                        <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
                          <Typography variant="body1">
                            No historical data available. Take more exams to see your progress.
                          </Typography>
                        </Box>
                      )}
                    </Box>
                  </CardContent>
                </Card>
              </Grid>
              <Grid item xs={12}>
                <Card sx={{ boxShadow: theme.shadows[3] }}>
                  <CardHeader title="Performance Scoreboard" />
                  <CardContent>
                    <TableContainer component={Paper} variant="outlined" sx={{ maxHeight: 300, overflow: 'auto' }}>
                      <Table stickyHeader size="small">
                        <TableHead>
                          <TableRow>
                            <TableCell>Exam</TableCell>
                            <TableCell>Date</TableCell>
                            <TableCell>Score</TableCell>
                            <TableCell>Grade</TableCell>
                            <TableCell>Correct/Total</TableCell>
                          </TableRow>
                        </TableHead>
                        <TableBody>
                          {historicalData.map((exam, index) => {
                            const examGrade = getGrade(exam.score);
                            return (
                              <TableRow key={exam.id}>
                                <TableCell>Exam {index + 1}</TableCell>
                                <TableCell>{exam.date}</TableCell>
                                <TableCell>{exam.score.toFixed(1)}%</TableCell>
                                <TableCell>
                                  <Chip
                                    label={examGrade.grade}
                                    size="small"
                                    sx={{
                                      bgcolor: examGrade.color,
                                      color: 'white',
                                      fontWeight: 'bold'
                                    }}
                                  />
                                </TableCell>
                                <TableCell>
                                  {exam.correctAnswers}/{exam.totalQuestions}
                                </TableCell>
                              </TableRow>
                            );
                          })}
                        </TableBody>
                      </Table>
                    </TableContainer>
                  </CardContent>
                </Card>
              </Grid>
            </Grid>
          )}

          <Box sx={{ display: 'flex', justifyContent: 'center', gap: 2, mt: 4 }}>
            <Button
              variant="outlined"
              onClick={() => navigate('/mcq')}
            >
              Back to Dashboard
            </Button>
            <Button
              variant="contained"
              color="primary"
              onClick={() => navigate(`/mcq/practice/${courseId}`)}
            >
              Practice More
            </Button>
          </Box>
        </>
      )}
    </Box>
  );
};

export default ExamResultsAnalytics;
