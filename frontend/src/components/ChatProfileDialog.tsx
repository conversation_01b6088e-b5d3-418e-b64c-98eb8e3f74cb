import React from 'react';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  <PERSON>alogA<PERSON>,
  Button,
  Avatar,
  Typography,
  Box,
  Chip,
  Divider,
  Rating,
  IconButton,
  useTheme,
  useMediaQuery,
  Stack,
  Card,
  CardContent,
} from '@mui/material';
import {
  Close as CloseIcon,
  Person as PersonIcon,
  Phone as PhoneIcon,
  LocationOn as LocationIcon,
  School as SchoolIcon,
  Work as WorkIcon,
  Star as StarIcon,
  Language as LanguageIcon,
  AccessTime as AccessTimeIcon,
} from '@mui/icons-material';
import { useQuery } from '@tanstack/react-query';
import { getChatParticipantProfile, ChatParticipantProfile } from '../api/chat';
import { format } from 'date-fns';

interface ChatProfileDialogProps {
  open: boolean;
  onClose: () => void;
  chatRoomId: number;
  userId: number;
  userName: string;
}

const ChatProfileDialog: React.FC<ChatProfileDialogProps> = ({
  open,
  onClose,
  chatRoomId,
  userId,
  userName,
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  const {
    data: profile,
    isLoading,
    error,
  } = useQuery({
    queryKey: ['chatParticipantProfile', chatRoomId, userId],
    queryFn: () => getChatParticipantProfile(chatRoomId, userId),
    enabled: open && !!chatRoomId && !!userId,
  });

  const formatGender = (gender?: string) => {
    if (!gender) return null;
    return gender === 'prefer_not_to_say' ? 'Prefer not to say' : 
           gender.charAt(0).toUpperCase() + gender.slice(1);
  };

  const formatSessionType = (type?: string) => {
    if (!type) return null;
    switch (type) {
      case 'online': return 'Online';
      case 'in_person': return 'In Person';
      case 'both': return 'Online & In Person';
      default: return type;
    }
  };

  return (
    <Dialog
      open={open}
      onClose={onClose}
      maxWidth="sm"
      fullWidth
      fullScreen={isMobile}
      PaperProps={{
        sx: {
          borderRadius: isMobile ? 0 : 2,
          maxHeight: isMobile ? '100vh' : '90vh',
        },
      }}
    >
      <DialogTitle sx={{ 
        display: 'flex', 
        alignItems: 'center', 
        justifyContent: 'space-between',
        pb: 1
      }}>
        <Typography variant="h6">Profile Details</Typography>
        <IconButton onClick={onClose} size="small">
          <CloseIcon />
        </IconButton>
      </DialogTitle>

      <DialogContent sx={{ px: { xs: 2, sm: 3 } }}>
        {isLoading && (
          <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
            <Typography>Loading profile...</Typography>
          </Box>
        )}

        {error && (
          <Box sx={{ textAlign: 'center', py: 4 }}>
            <Typography color="error">Failed to load profile</Typography>
          </Box>
        )}

        {profile && (
          <Stack spacing={3}>
            {/* Header Section */}
            <Box sx={{ textAlign: 'center', py: 2 }}>
              <Avatar
                src={profile.profile_picture_url}
                sx={{
                  width: 80,
                  height: 80,
                  mx: 'auto',
                  mb: 2,
                  bgcolor: 'primary.main',
                  fontSize: '2rem',
                  border: '3px solid',
                  borderColor: 'primary.main',
                }}
              >
                {profile.full_name.charAt(0)}
              </Avatar>
              <Typography variant="h5" gutterBottom>
                {profile.full_name}
              </Typography>
              <Chip
                label={profile.role.charAt(0).toUpperCase() + profile.role.slice(1)}
                color={profile.role === 'tutor' ? 'primary' : 'secondary'}
                sx={{ mb: 1 }}
              />
              <Typography variant="body2" color="text.secondary">
                Member since {format(new Date(profile.created_at), 'MMMM yyyy')}
              </Typography>
            </Box>

            <Divider />

            {/* Basic Information */}
            <Card variant="outlined">
              <CardContent>
                <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <PersonIcon color="primary" />
                  Basic Information
                </Typography>
                <Stack spacing={2}>
                  {profile.gender && (
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Typography variant="body2" color="text.secondary" sx={{ minWidth: 80 }}>
                        Gender:
                      </Typography>
                      <Typography variant="body2">
                        {formatGender(profile.gender)}
                      </Typography>
                    </Box>
                  )}
                  {profile.level && (
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Typography variant="body2" color="text.secondary" sx={{ minWidth: 80 }}>
                        Level:
                      </Typography>
                      <Typography variant="body2">{profile.level}</Typography>
                    </Box>
                  )}
                  {profile.state_of_origin && (
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <LocationIcon fontSize="small" color="action" />
                      <Typography variant="body2" color="text.secondary" sx={{ minWidth: 80 }}>
                        State:
                      </Typography>
                      <Typography variant="body2">{profile.state_of_origin}</Typography>
                    </Box>
                  )}
                  {profile.department && (
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <SchoolIcon fontSize="small" color="action" />
                      <Typography variant="body2" color="text.secondary" sx={{ minWidth: 80 }}>
                        Department:
                      </Typography>
                      <Typography variant="body2">{profile.department.name}</Typography>
                    </Box>
                  )}
                </Stack>
              </CardContent>
            </Card>

            {/* Tutor-specific Information */}
            {profile.tutor_profile && (
              <Card variant="outlined">
                <CardContent>
                  <Typography variant="h6" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <WorkIcon color="primary" />
                    Tutor Information
                  </Typography>
                  <Stack spacing={2}>
                    {profile.tutor_profile.bio && (
                      <Box>
                        <Typography variant="body2" color="text.secondary" gutterBottom>
                          Bio:
                        </Typography>
                        <Typography variant="body2">{profile.tutor_profile.bio}</Typography>
                      </Box>
                    )}
                    
                    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 2 }}>
                      {profile.tutor_profile.experience_years !== undefined && profile.tutor_profile.experience_years !== null && (
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <AccessTimeIcon fontSize="small" color="action" />
                          <Typography variant="body2">
                            {Number(profile.tutor_profile.experience_years) || 0} years experience
                          </Typography>
                        </Box>
                      )}
                      
                      {profile.tutor_profile.average_rating && (
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <Rating
                            value={Number(profile.tutor_profile.average_rating) || 0}
                            readOnly
                            size="small"
                          />
                          <Typography variant="body2">
                            ({Number(profile.tutor_profile.total_reviews) || 0} reviews)
                          </Typography>
                        </Box>
                      )}
                    </Box>

                    {profile.tutor_profile.hourly_rate && (
                      <Typography variant="body2">
                        <strong>Rate:</strong> ₦{Number(profile.tutor_profile.hourly_rate).toLocaleString()}/hour
                      </Typography>
                    )}

                    {profile.tutor_profile.preferred_session_type && (
                      <Typography variant="body2">
                        <strong>Session Type:</strong> {formatSessionType(profile.tutor_profile.preferred_session_type)}
                      </Typography>
                    )}

                    {profile.tutor_profile.location && (
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <LocationIcon fontSize="small" color="action" />
                        <Typography variant="body2">{profile.tutor_profile.location}</Typography>
                      </Box>
                    )}

                    {profile.tutor_profile.languages && Array.isArray(profile.tutor_profile.languages) && profile.tutor_profile.languages.length > 0 && (
                      <Box>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                          <LanguageIcon fontSize="small" color="action" />
                          <Typography variant="body2" color="text.secondary">Languages:</Typography>
                        </Box>
                        <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                          {profile.tutor_profile.languages.map((language, index) => (
                            <Chip
                              key={index}
                              label={typeof language === 'string' ? language : String(language)}
                              size="small"
                              variant="outlined"
                            />
                          ))}
                        </Box>
                      </Box>
                    )}

                    <Typography variant="body2">
                      <strong>Sessions Completed:</strong> {Number(profile.tutor_profile.total_sessions_completed) || 0}
                    </Typography>
                  </Stack>
                </CardContent>
              </Card>
            )}
          </Stack>
        )}
      </DialogContent>

      <DialogActions sx={{ px: { xs: 2, sm: 3 }, pb: { xs: 2, sm: 3 } }}>
        <Button onClick={onClose} variant="contained" fullWidth={isMobile}>
          Close
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default ChatProfileDialog;
