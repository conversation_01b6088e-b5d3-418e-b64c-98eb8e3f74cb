import React from 'react';
import { Box, Typography, Paper, Divider } from '@mui/material';
import MathMarkdown from '../common/MathMarkdown';

/**
 * Test component to verify mathematical expression rendering
 * This component tests the specific LaTeX format from your examples
 */
const MathRenderingTest: React.FC = () => {
  // Test cases based on your examples
  const testCases = [
    {
      title: "Question with LaTeX delimiters",
      content: `\\[ \\text{When using direction cosine method, which of the following is used in finding the angle between two vectors?} \\]`
    },
    {
      title: "Options with various math expressions",
      content: `Options:
\\[ [l, m, n] \\]
\\[ \\cos \\theta \\]
\\[ l_1l_2 + m_1m_2 + n_1n_2 \\]
\\[ \\cos \\theta = \\frac{\\vec{a} \\cdot \\vec{b}}{|\\vec{a}||\\vec{b}|} \\]`
    },
    {
      title: "Explanation with complex math",
      content: `\\[ \\text{The angle } \\theta \\text{ between two vectors } \\vec{a} \\text{ and } \\vec{b} \\text{ is found using:} \\] \\[ \\cos \\theta = \\frac{\\vec{a} \\cdot \\vec{b}}{|\\vec{a}| |\\vec{b}|} \\Rightarrow \\boxed{\\text{D}} \\]`
    },
    {
      title: "Complex question with coordinates",
      content: `\\[ \\text{Given that } P(2,1,3),\\ Q(1,4,5),\\ R(2,5,3),\\ \\text{and } S(3,2,1) \\text{ are the vertices of a parallelogram } PQRS.\\ \\text{The area of the parallelogram is} \\]`
    },
    {
      title: "Vector calculations",
      content: `\\[ \\vec{PQ} = Q - P = (-1, 3, 2), \\quad \\vec{PR} = R - P = (0, 4, 0) \\]`
    },
    {
      title: "Cross product with determinant",
      content: `\\[ \\vec{PQ} \\times \\vec{PR} = \\begin{vmatrix} \\mathbf{i} & \\mathbf{j} & \\mathbf{k} \\\\ -1 & 3 & 2 \\\\ 0 & 4 & 0 \\end{vmatrix} = \\mathbf{i}(3 \\cdot 0 - 2 \\cdot 4) - \\mathbf{j}(-1 \\cdot 0 - 2 \\cdot 0) + \\mathbf{k}(-1 \\cdot 4 - 3 \\cdot 0) = -8\\mathbf{i} + 0\\mathbf{j} -4\\mathbf{k} \\]`
    },
    {
      title: "Final calculation with boxed answer",
      content: `\\[ |\\vec{PQ} \\times \\vec{PR}| = \\sqrt{(-8)^2 + 0^2 + (-4)^2} = \\sqrt{64 + 16} = \\sqrt{80} = 4\\sqrt{5} \\]
\\[ \\text{Area of parallelogram} = |\\vec{PQ} \\times \\vec{PR}| = 4\\sqrt{5} \\]
\\[ \\boxed{\\text{B}} \\]`
    },
    {
      title: "Mixed inline and display math",
      content: `The formula $E = mc^2$ is famous, but for vectors we use:
\\[ \\vec{F} = m\\vec{a} \\]
where $\\vec{F}$ is force and $\\vec{a}$ is acceleration.`
    },
    {
      title: "Physics problem with mixed math and options",
      content: `$\\text{A particle is projected with velocity } u = 49\\ \\text{m/s at an angle } \\theta.

\\text{ If the greatest height attained equals the horizontal range, find } \\theta.$

Select your answer:
A:
\\tan^{-1}2

B:
\\tan^{-1}3

C:
\\tan^{-1}4

D:
\\tan^{-1}5`
    },
    {
      title: "Another mixed format example",
      content: `$\\text{The equation of motion is } s = ut + \\frac{1}{2}at^2$

Find the acceleration when:
A:
$a = 2\\text{ m/s}^2$

B:
$a = 4\\text{ m/s}^2$

C:
$a = 6\\text{ m/s}^2$

D:
$a = 8\\text{ m/s}^2$`
    }
  ];

  return (
    <Box sx={{ p: 3, maxWidth: 1200, mx: 'auto' }}>
      <Typography variant="h4" gutterBottom>
        Mathematical Expression Rendering Test
      </Typography>
      <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
        This page tests the rendering of mathematical expressions using LaTeX notation.
      </Typography>

      {testCases.map((testCase, index) => (
        <Paper key={index} sx={{ p: 3, mb: 3 }} elevation={2}>
          <Typography variant="h6" color="primary" gutterBottom>
            {testCase.title}
          </Typography>
          
          <Box sx={{ mb: 2 }}>
            <Typography variant="subtitle2" color="text.secondary">
              Raw LaTeX:
            </Typography>
            <Box 
              component="pre" 
              sx={{ 
                bgcolor: 'grey.100', 
                p: 1, 
                borderRadius: 1, 
                fontSize: '0.875rem',
                overflow: 'auto',
                whiteSpace: 'pre-wrap'
              }}
            >
              {testCase.content}
            </Box>
          </Box>

          <Divider sx={{ my: 2 }} />

          <Box>
            <Typography variant="subtitle2" color="text.secondary" gutterBottom>
              Rendered Output:
            </Typography>
            <Box sx={{ 
              border: 1, 
              borderColor: 'divider', 
              borderRadius: 1, 
              p: 2,
              bgcolor: 'background.paper'
            }}>
              <MathMarkdown>{testCase.content}</MathMarkdown>
            </Box>
          </Box>
        </Paper>
      ))}

      <Paper sx={{ p: 3, bgcolor: 'info.light', color: 'info.contrastText' }}>
        <Typography variant="h6" gutterBottom>
          Usage Instructions
        </Typography>
        <Typography variant="body2">
          • Use <code>\\[...\\]</code> for display math (centered, larger)
          <br />
          • Use <code>$...$</code> for inline math (within text)
          <br />
          • Use <code>\\text{'{...}'}</code> for text within math expressions
          <br />
          • Use <code>\\vec{'{...}'}</code> for vector notation
          <br />
          • Use <code>\\boxed{'{...}'}</code> for highlighted answers
        </Typography>
      </Paper>
    </Box>
  );
};

export default MathRenderingTest;
