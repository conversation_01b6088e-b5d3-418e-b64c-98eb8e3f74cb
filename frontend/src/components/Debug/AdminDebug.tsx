import React from 'react';
import { Box, Paper, Typography, Button, Alert } from '@mui/material';
import { useAuth } from '../../contexts/AuthContext';
import { useLocation, useNavigate } from 'react-router-dom';

const AdminDebug: React.FC = () => {
  const { user } = useAuth();
  const location = useLocation();
  const navigate = useNavigate();

  return (
    <Paper sx={{ p: 3, m: 2 }}>
      <Typography variant="h6" gutterBottom>
        🔍 Admin Access Debug Info
      </Typography>
      
      <Box sx={{ mb: 2 }}>
        <Typography variant="subtitle2">Current User:</Typography>
        <Typography variant="body2" sx={{ fontFamily: 'monospace', bgcolor: 'grey.100', p: 1, borderRadius: 1 }}>
          {user ? JSON.stringify(user, null, 2) : 'No user logged in'}
        </Typography>
      </Box>

      <Box sx={{ mb: 2 }}>
        <Typography variant="subtitle2">Current Location:</Typography>
        <Typography variant="body2" sx={{ fontFamily: 'monospace', bgcolor: 'grey.100', p: 1, borderRadius: 1 }}>
          {location.pathname}
        </Typography>
      </Box>

      <Box sx={{ mb: 2 }}>
        <Typography variant="subtitle2">User Role Check:</Typography>
        {user?.role === 'admin' ? (
          <Alert severity="success">✅ User has admin role</Alert>
        ) : (
          <Alert severity="error">❌ User does not have admin role (current: {user?.role || 'none'})</Alert>
        )}
      </Box>

      <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
        <Button 
          variant="outlined" 
          onClick={() => navigate('/admin/dashboard')}
        >
          Go to Admin Dashboard
        </Button>
        <Button 
          variant="outlined" 
          onClick={() => navigate('/admin/flagged-questions')}
        >
          Go to Flagged Questions
        </Button>
        <Button 
          variant="outlined" 
          onClick={() => navigate('/admin/questions')}
        >
          Go to Questions
        </Button>
      </Box>

      <Box sx={{ mt: 2 }}>
        <Typography variant="subtitle2">Quick Fixes:</Typography>
        <Typography variant="body2">
          1. If you're not an admin, run: <code>python backend/make_admin.py <EMAIL></code>
        </Typography>
        <Typography variant="body2">
          2. Make sure you're accessing /admin/ URLs
        </Typography>
        <Typography variant="body2">
          3. Check browser console for errors
        </Typography>
      </Box>
    </Paper>
  );
};

export default AdminDebug;
