import React from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  Typo<PERSON>,
  Grid,
  Chip,
  useTheme,
  useMediaQuery,
  IconButton,
  Button,
  CardActionArea,
  Badge
} from '@mui/material';
import {
  Quiz as QuizIcon,
  School as FlashcardsIcon,
  AutoAwesome as AIIcon,
  TrendingUp as TrendingIcon,
  NewReleases as NewIcon,
  ArrowForward as ArrowForwardIcon,
  Psychology as PsychologyIcon,
  MenuBook as StudyIcon,
  Analytics as AnalyticsIcon
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';

interface Tool {
  id: string;
  name: string;
  description: string;
  icon: React.ReactNode;
  route: string;
  isNew?: boolean;
  isPopular?: boolean;
  usageCount?: number;
  category: 'study' | 'practice' | 'ai';
}

const PopularToolsSection: React.FC = () => {
  const theme = useTheme();
  const navigate = useNavigate();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  // Mock data for now - this should come from backend
  const tools: Tool[] = [
    {
      id: 'mcq-practice',
      name: 'MCQ Practice',
      description: 'Practice with multiple choice questions',
      icon: <QuizIcon />,
      route: '/mcq',
      isPopular: true,
      usageCount: 1250,
      category: 'practice'
    },
    {
      id: 'flashcards',
      name: 'Course Flashcards',
      description: 'Study with interactive flashcards',
      icon: <FlashcardsIcon />,
      route: '/flash-cards/courses',
      isPopular: true,
      usageCount: 980,
      category: 'study'
    },
    {
      id: 'ai-tutor',
      name: 'AI Study Assistant',
      description: 'Get help from AI tutor',
      icon: <AIIcon />,
      route: '/tools',
      isNew: true,
      usageCount: 450,
      category: 'ai'
    },
    {
      id: 'pdf-flashcards',
      name: 'PDF to Flashcards',
      description: 'Convert your notes to flashcards',
      icon: <PsychologyIcon />,
      route: '/tools',
      isNew: true,
      usageCount: 320,
      category: 'ai'
    },
    {
      id: 'study-planner',
      name: 'Study Planner',
      description: 'Plan your study sessions',
      icon: <StudyIcon />,
      route: '/tools',
      isPopular: true,
      usageCount: 750,
      category: 'study'
    }
  ];

  // Sort tools by usage count and get top 6
  const popularTools = tools
    .filter(tool => tool.isPopular)
    .sort((a, b) => (b.usageCount || 0) - (a.usageCount || 0))
    .slice(0, 3);

  const newTools = tools
    .filter(tool => tool.isNew)
    .sort((a, b) => (b.usageCount || 0) - (a.usageCount || 0))
    .slice(0, 3);

  const handleToolClick = (tool: Tool) => {
    navigate(tool.route);
  };

  const cardVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        type: 'spring',
        stiffness: 100,
        damping: 15
      }
    }
  };

  const ToolCard: React.FC<{ tool: Tool; index: number }> = ({ tool, index }) => (
    <motion.div
      variants={cardVariants}
      initial="hidden"
      animate="visible"
      transition={{ delay: index * 0.1 }}
    >
      <Card
        sx={{
          height: '100%',
          transition: 'all 0.3s ease',
          '&:hover': {
            transform: 'translateY(-4px)',
            boxShadow: theme.shadows[8],
          },
          border: `1px solid ${theme.palette.divider}`,
          borderRadius: 2
        }}
      >
        <CardActionArea
          onClick={() => handleToolClick(tool)}
          sx={{ height: '100%' }}
        >
          <CardContent sx={{ p: 2, height: '100%', display: 'flex', flexDirection: 'column' }}>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
              <Box
                sx={{
                  width: 40,
                  height: 40,
                  borderRadius: '50%',
                  bgcolor: `${theme.palette.primary.main}20`,
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  color: theme.palette.primary.main,
                  mr: 1.5
                }}
              >
                {tool.icon}
              </Box>
              <Box sx={{ flexGrow: 1 }}>
                <Typography variant="subtitle2" fontWeight="bold">
                  {tool.name}
                </Typography>
                {tool.isNew && (
                  <Chip
                    icon={<NewIcon fontSize="small" />}
                    label="New"
                    size="small"
                    color="success"
                    sx={{ height: 18, fontSize: '0.7rem', mt: 0.5 }}
                  />
                )}
                {tool.isPopular && (
                  <Chip
                    icon={<TrendingIcon fontSize="small" />}
                    label="Popular"
                    size="small"
                    color="primary"
                    sx={{ height: 18, fontSize: '0.7rem', mt: 0.5 }}
                  />
                )}
              </Box>
            </Box>
            
            <Typography
              variant="body2"
              color="text.secondary"
              sx={{ flexGrow: 1, mb: 1 }}
            >
              {tool.description}
            </Typography>

            {tool.usageCount && (
              <Typography variant="caption" color="text.secondary">
                {tool.usageCount.toLocaleString()} users
              </Typography>
            )}
          </CardContent>
        </CardActionArea>
      </Card>
    </motion.div>
  );

  return (
    <Box sx={{ mb: isMobile ? 2 : 3 }}>
      {/* Popular Tools */}
      <Card
        sx={{
          borderRadius: 2,
          border: `1px solid ${theme.palette.divider}`,
          mb: 2
        }}
      >
        <CardContent sx={{ p: isMobile ? 2 : 3 }}>
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              mb: 2
            }}
          >
            <Typography
              variant={isMobile ? "subtitle1" : "h6"}
              fontWeight="bold"
              sx={{ display: 'flex', alignItems: 'center', gap: 1 }}
            >
              <TrendingIcon color="primary" />
              Popular Tools
            </Typography>
            <Button
              size="small"
              endIcon={<ArrowForwardIcon />}
              onClick={() => navigate('/tools')}
              sx={{ borderRadius: 1.5 }}
            >
              View All
            </Button>
          </Box>

          <Grid container spacing={2}>
            {popularTools.map((tool, index) => (
              <Grid item xs={12} sm={6} md={4} key={tool.id}>
                <ToolCard tool={tool} index={index} />
              </Grid>
            ))}
          </Grid>
        </CardContent>
      </Card>

      {/* New Tools */}
      <Card
        sx={{
          borderRadius: 2,
          border: `1px solid ${theme.palette.divider}`,
        }}
      >
        <CardContent sx={{ p: isMobile ? 2 : 3 }}>
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              mb: 2
            }}
          >
            <Typography
              variant={isMobile ? "subtitle1" : "h6"}
              fontWeight="bold"
              sx={{ display: 'flex', alignItems: 'center', gap: 1 }}
            >
              <NewIcon color="success" />
              New Tools
            </Typography>
            <Button
              size="small"
              endIcon={<ArrowForwardIcon />}
              onClick={() => navigate('/tools')}
              sx={{ borderRadius: 1.5 }}
            >
              Explore
            </Button>
          </Box>

          <Grid container spacing={2}>
            {newTools.map((tool, index) => (
              <Grid item xs={12} sm={6} md={4} key={tool.id}>
                <ToolCard tool={tool} index={index} />
              </Grid>
            ))}
          </Grid>
        </CardContent>
      </Card>
    </Box>
  );
};

export default PopularToolsSection;
