import React from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  Typography,
  Grid,
  Button,
  useTheme,
  useMediaQuery,
  Avatar,
  Chip
} from '@mui/material';
import {
  Quiz as QuizIcon,
  School as CourseIcon,
  EmojiEvents as LeaderboardIcon,
  Note as NotesIcon,
  Style as FlashCardIcon,
  Bookmark as BookmarkIcon,
  TrendingUp as AnalyticsIcon,
  QuestionAnswer as QuestionsIcon
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import { useNavigate } from 'react-router-dom';

const StudentToolsSection: React.FC = () => {
  const theme = useTheme();
  const navigate = useNavigate();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  const tools = [
    {
      title: 'Practice MCQs',
      description: 'Practice questions and take exams',
      icon: <QuizIcon />,
      color: '#2196f3',
      path: '/mcq',
      badge: 'Popular'
    },
    {
      title: 'Course Flashcards',
      description: 'Study flashcards organized by course',
      icon: <FlashCardIcon />,
      color: '#795548',
      path: '/flash-cards/courses',
      badge: 'New'
    },
    {
      title: 'Courses',
      description: 'Browse and enroll in courses',
      icon: <CourseIcon />,
      color: '#4caf50',
      path: '/courses'
    },
    {
      title: 'Leaderboard',
      description: 'See your ranking and compete',
      icon: <LeaderboardIcon />,
      color: '#ff9800',
      path: '/leaderboard'
    },
    {
      title: 'Study Notes',
      description: 'Create and manage your notes',
      icon: <NotesIcon />,
      color: '#9c27b0',
      path: '/notes'
    },
    {
      title: 'AI Flash Cards',
      description: 'Generate flash cards from your notes',
      icon: <FlashCardIcon />,
      color: '#e91e63',
      path: '/flash-cards'
    },
    {
      title: 'Bookmarks',
      description: 'Your saved questions',
      icon: <BookmarkIcon />,
      color: '#607d8b',
      path: '/bookmarks'
    }
  ];

  const cardVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        type: 'spring',
        stiffness: 100,
        damping: 15
      }
    }
  };

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  return (
    <Box sx={{ mb: isMobile ? 2 : 3 }}>
      <Typography
        variant={isMobile ? "h6" : "h5"}
        fontWeight="bold"
        sx={{ mb: isMobile ? 1.5 : 2 }}
      >
        Study Tools
      </Typography>
      
      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate="visible"
      >
        <Grid container spacing={isMobile ? 1.5 : 2}>
          {tools.map((tool, index) => (
            <Grid item xs={6} sm={4} md={4} key={tool.title}>
              <motion.div variants={cardVariants}>
                <Card
                  sx={{
                    height: '100%',
                    cursor: 'pointer',
                    transition: 'all 0.3s ease',
                    border: `1px solid ${theme.palette.divider}`,
                    '&:hover': {
                      transform: 'translateY(-4px)',
                      boxShadow: '0 8px 25px rgba(0,0,0,0.15)',
                      borderColor: tool.color
                    }
                  }}
                  onClick={() => navigate(tool.path)}
                >
                  <CardContent sx={{ p: isMobile ? 1.5 : 2, textAlign: 'center' }}>
                    <Box sx={{ position: 'relative', mb: 1 }}>
                      <Avatar
                        sx={{
                          bgcolor: tool.color,
                          width: isMobile ? 40 : 48,
                          height: isMobile ? 40 : 48,
                          mx: 'auto',
                          mb: 1
                        }}
                      >
                        {tool.icon}
                      </Avatar>
                      {tool.badge && (
                        <Chip
                          label={tool.badge}
                          size="small"
                          color="primary"
                          sx={{
                            position: 'absolute',
                            top: -8,
                            right: '50%',
                            transform: 'translateX(50%)',
                            fontSize: '0.7rem',
                            height: 20
                          }}
                        />
                      )}
                    </Box>
                    
                    <Typography
                      variant={isMobile ? "body2" : "subtitle1"}
                      fontWeight="bold"
                      sx={{ mb: 0.5 }}
                    >
                      {tool.title}
                    </Typography>
                    
                    <Typography
                      variant="caption"
                      color="text.secondary"
                      sx={{
                        display: isMobile ? 'none' : 'block',
                        lineHeight: 1.2
                      }}
                    >
                      {tool.description}
                    </Typography>
                  </CardContent>
                </Card>
              </motion.div>
            </Grid>
          ))}
        </Grid>
      </motion.div>
    </Box>
  );
};

export default StudentToolsSection;
