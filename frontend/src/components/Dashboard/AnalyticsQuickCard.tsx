import React from 'react';
import {
  <PERSON>,
  Card<PERSON>ontent,
  Typo<PERSON>,
  Box,
  Button,
  Grid,
  useTheme,
  useMediaQuery,
  Avatar,
  Chip,
  LinearProgress
} from '@mui/material';
import {
  Analytics as AnalyticsIcon,
  TrendingUp as TrendingUpIcon,
  Assessment as AssessmentIcon,
  ArrowForward as ArrowForwardIcon
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';
import { getComprehensiveAnalytics } from '../../api/studentProgress';

const AnalyticsQuickCard: React.FC = () => {
  const theme = useTheme();
  const navigate = useNavigate();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  // Fetch quick analytics data
  const { data: analytics, isLoading } = useQuery({
    queryKey: ['quickAnalytics'],
    queryFn: () => getComprehensiveAnalytics(undefined, 7), // Last 7 days
    staleTime: 5 * 60 * 1000
  });

  const handleViewAnalytics = () => {
    navigate('/analytics');
  };

  const practiceAccuracy = analytics?.overview.practice_accuracy || 0;
  const examScore = analytics?.overview.avg_exam_score || 0;
  const improvementRate = analytics?.insights.improvement_rate || 0;
  const activeDays = analytics?.insights.active_days || 0;

  return (
    <Card
      component={motion.div}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: 0.3 }}
      sx={{
        borderRadius: 3,
        border: `1px solid ${theme.palette.divider}`,
        background: theme.palette.mode === 'light' 
          ? 'linear-gradient(135deg, rgba(255,255,255,0.9) 0%, rgba(248,250,252,0.9) 100%)'
          : 'linear-gradient(135deg, rgba(30,30,30,0.9) 0%, rgba(20,20,20,0.9) 100%)',
        backdropFilter: 'blur(10px)',
        boxShadow: theme.shadows[4],
        position: 'relative',
        overflow: 'hidden',
        '&:hover': {
          transform: 'translateY(-4px)',
          boxShadow: theme.shadows[8],
          transition: 'all 0.3s ease'
        }
      }}
    >
      {/* Background decoration */}
      <Box
        sx={{
          position: 'absolute',
          top: -50,
          right: -50,
          width: 100,
          height: 100,
          borderRadius: '50%',
          background: `linear-gradient(45deg, ${theme.palette.primary.main}20, ${theme.palette.secondary.main}20)`,
          opacity: 0.5
        }}
      />
      
      <CardContent sx={{ p: isMobile ? 2 : 3, position: 'relative' }}>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Avatar
            sx={{
              bgcolor: theme.palette.primary.main,
              width: 48,
              height: 48,
              mr: 2
            }}
          >
            <AnalyticsIcon />
          </Avatar>
          <Box sx={{ flex: 1 }}>
            <Typography variant="h6" fontWeight="bold" gutterBottom>
              Performance Analytics
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Track your learning progress and insights
            </Typography>
          </Box>
          <Button
            variant="outlined"
            size="small"
            endIcon={<ArrowForwardIcon />}
            onClick={handleViewAnalytics}
            sx={{ borderRadius: 2 }}
          >
            View All
          </Button>
        </Box>

        {!isLoading && analytics ? (
          <Grid container spacing={2}>
            <Grid item xs={6} sm={3}>
              <Box sx={{ textAlign: 'center' }}>
                <Typography variant="h5" fontWeight="bold" color="primary">
                  {practiceAccuracy.toFixed(0)}%
                </Typography>
                <Typography variant="caption" color="text.secondary">
                  Practice Accuracy
                </Typography>
              </Box>
            </Grid>
            
            <Grid item xs={6} sm={3}>
              <Box sx={{ textAlign: 'center' }}>
                <Typography variant="h5" fontWeight="bold" color="secondary">
                  {examScore.toFixed(0)}%
                </Typography>
                <Typography variant="caption" color="text.secondary">
                  Exam Average
                </Typography>
              </Box>
            </Grid>
            
            <Grid item xs={6} sm={3}>
              <Box sx={{ textAlign: 'center' }}>
                <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', mb: 0.5 }}>
                  <TrendingUpIcon 
                    fontSize="small" 
                    color={improvementRate > 0 ? 'success' : 'warning'}
                    sx={{ mr: 0.5 }}
                  />
                  <Typography 
                    variant="h5" 
                    fontWeight="bold" 
                    color={improvementRate > 0 ? 'success.main' : 'warning.main'}
                  >
                    {improvementRate > 0 ? '+' : ''}{improvementRate.toFixed(1)}%
                  </Typography>
                </Box>
                <Typography variant="caption" color="text.secondary">
                  Improvement
                </Typography>
              </Box>
            </Grid>
            
            <Grid item xs={6} sm={3}>
              <Box sx={{ textAlign: 'center' }}>
                <Typography variant="h5" fontWeight="bold" color="info.main">
                  {activeDays}
                </Typography>
                <Typography variant="caption" color="text.secondary">
                  Active Days
                </Typography>
              </Box>
            </Grid>
          </Grid>
        ) : (
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center', py: 2 }}>
            <LinearProgress sx={{ width: '100%', borderRadius: 1 }} />
          </Box>
        )}

        {/* Quick insights */}
        {!isLoading && analytics && (
          <Box sx={{ mt: 2, display: 'flex', gap: 1, flexWrap: 'wrap' }}>
            {analytics.insights.improvement_rate > 5 && (
              <Chip
                icon={<TrendingUpIcon />}
                label="Great progress!"
                color="success"
                size="small"
                variant="outlined"
              />
            )}
            {analytics.insights.consistency_score > 80 && (
              <Chip
                icon={<AssessmentIcon />}
                label="Consistent learner"
                color="info"
                size="small"
                variant="outlined"
              />
            )}
            {analytics.insights.active_days >= 5 && (
              <Chip
                label="Very active"
                color="primary"
                size="small"
                variant="outlined"
              />
            )}
          </Box>
        )}
      </CardContent>
    </Card>
  );
};

export default AnalyticsQuickCard;
