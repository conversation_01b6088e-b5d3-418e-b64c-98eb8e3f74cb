import React from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  Typography,
  List,
  ListItem,
  ListItemIcon,
  ListItemText,
  Chip,
  useTheme,
  useMediaQuery,
  CircularProgress,
  <PERSON>ert,
  Button
} from '@mui/material';
import {
  Quiz as QuizIcon,
  School as CourseIcon,
  Bookmark as BookmarkIcon,
  Note as NoteIcon,
  EmojiEvents as AchievementIcon,
  TrendingUp as StreakIcon,
  ArrowForward as ArrowForwardIcon
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import { useQuery } from '@tanstack/react-query';
import { useNavigate } from 'react-router-dom';
import { format, isToday, isYesterday } from 'date-fns';
import { getRecentActivity, RecentActivity } from '../../api/gamification';

const RecentActivitySection: React.FC = () => {
  const theme = useTheme();
  const navigate = useNavigate();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  // Fetch recent activity data
  const { data: activityData, isLoading, error } = useQuery({
    queryKey: ['recentActivity'],
    queryFn: () => getRecentActivity(isMobile ? 3 : 5),
    retry: 2,
    staleTime: 2 * 60 * 1000, // 2 minutes
  });

  const activities = activityData?.activities || [];

  const getActivityIcon = (type: RecentActivity['type']) => {
    switch (type) {
      case 'quiz': return <QuizIcon />;
      case 'course': return <CourseIcon />;
      case 'bookmark': return <BookmarkIcon />;
      case 'note': return <NoteIcon />;
      case 'achievement': return <AchievementIcon />;
      case 'streak': return <StreakIcon />;
      default: return <QuizIcon />;
    }
  };

  const getActivityColor = (type: RecentActivity['type']) => {
    switch (type) {
      case 'quiz': return theme.palette.primary.main;
      case 'course': return theme.palette.success.main;
      case 'bookmark': return theme.palette.info.main;
      case 'note': return theme.palette.secondary.main;
      case 'achievement': return theme.palette.warning.main;
      case 'streak': return theme.palette.error.main;
      default: return theme.palette.primary.main;
    }
  };

  const formatTimestamp = (timestamp: string) => {
    const date = new Date(timestamp);
    if (isToday(date)) {
      return `Today at ${format(date, 'HH:mm')}`;
    } else if (isYesterday(date)) {
      return `Yesterday at ${format(date, 'HH:mm')}`;
    } else {
      return format(date, 'MMM dd, HH:mm');
    }
  };

  const cardVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        type: 'spring',
        stiffness: 100,
        damping: 15
      }
    }
  };

  return (
    <motion.div
      variants={cardVariants}
      initial="hidden"
      animate="visible"
    >
      <Card
        sx={{
          borderRadius: 2,
          border: `1px solid ${theme.palette.divider}`,
          mb: isMobile ? 2 : 3
        }}
      >
        <CardContent sx={{ p: isMobile ? 2 : 3 }}>
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'space-between',
              alignItems: 'center',
              mb: 2
            }}
          >
            <Typography
              variant={isMobile ? "subtitle1" : "h6"}
              fontWeight="bold"
            >
              Recent Activity
            </Typography>
            <Button
              size="small"
              endIcon={<ArrowForwardIcon />}
              onClick={() => navigate('/profile')}
              sx={{ borderRadius: 1.5 }}
            >
              View All
            </Button>
          </Box>

          {isLoading ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', p: 2 }}>
              <CircularProgress size={24} />
            </Box>
          ) : activities.length === 0 ? (
            <Alert severity="info" sx={{ borderRadius: 1.5 }}>
              No recent activity. Start practicing to see your progress here!
            </Alert>
          ) : (
            <List sx={{ p: 0 }}>
              {activities.slice(0, isMobile ? 3 : 5).map((activity, index) => (
                <ListItem
                  key={activity.id}
                  sx={{
                    px: 0,
                    py: 1,
                    borderBottom: index < activities.length - 1 ? `1px solid ${theme.palette.divider}` : 'none'
                  }}
                >
                  <ListItemIcon sx={{ minWidth: 40 }}>
                    <Box
                      sx={{
                        width: 32,
                        height: 32,
                        borderRadius: '50%',
                        bgcolor: `${getActivityColor(activity.type)}20`,
                        display: 'flex',
                        alignItems: 'center',
                        justifyContent: 'center',
                        color: getActivityColor(activity.type)
                      }}
                    >
                      {getActivityIcon(activity.type)}
                    </Box>
                  </ListItemIcon>
                  
                  <ListItemText
                    primary={
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <Typography variant="body2" fontWeight="medium">
                          {activity.title}
                        </Typography>
                        {activity.points && (
                          <Chip
                            label={`+${activity.points} pts`}
                            size="small"
                            color="primary"
                            sx={{ height: 20, fontSize: '0.7rem' }}
                          />
                        )}
                      </Box>
                    }
                    secondary={
                      <Box sx={{ mt: 0.5 }}>
                        <Typography variant="caption" color="text.secondary">
                          {activity.description}
                        </Typography>
                        <Typography
                          variant="caption"
                          sx={{
                            display: 'block',
                            color: theme.palette.info.main,
                            fontWeight: 'medium',
                            mt: 0.25
                          }}
                        >
                          {formatTimestamp(activity.timestamp)}
                        </Typography>
                      </Box>
                    }
                  />
                </ListItem>
              ))}
            </List>
          )}
        </CardContent>
      </Card>
    </motion.div>
  );
};

export default RecentActivitySection;
