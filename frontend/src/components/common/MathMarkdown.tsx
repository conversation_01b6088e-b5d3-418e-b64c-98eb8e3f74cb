import React from 'react';
import ReactMarkdown from 'react-markdown';
import remarkMath from 'remark-math';
import rehypeKatex from 'rehype-katex';

interface MathMarkdownProps {
  children: string;
  className?: string;
}

/**
 * A component that renders markdown content with math expressions
 * using KaTeX for math rendering.
 *
 * Supports both inline math with $ and block math with $$.
 * Also automatically detects and converts common math patterns.
 *
 * Example:
 * Inline math: $E = mc^2$
 * Block math: $$\int_{a}^{b} f(x) dx = F(b) - F(a)$$
 */
const MathMarkdown: React.FC<MathMarkdownProps> = ({ children, className }) => {
  // Handle errors in KaTeX rendering
  const katexOptions = {
    throwOnError: false,
    strict: false,
    trust: true,
    output: 'html',
    displayMode: false,
    macros: {
      // Common math macros
      "\\R": "\\mathbb{R}",
      "\\N": "\\mathbb{N}",
      "\\Z": "\\mathbb{Z}",
      "\\Q": "\\mathbb{Q}",
      "\\C": "\\mathbb{C}",
      // Vector notation
      "\\vec": "\\overrightarrow{#1}",
      // Common functions
      "\\cos": "\\cos",
      "\\sin": "\\sin",
      "\\tan": "\\tan",
      // Inverse trig functions
      "\\arcsin": "\\sin^{-1}",
      "\\arccos": "\\cos^{-1}",
      "\\arctan": "\\tan^{-1}",
      // Boxed answers (common in MCQ explanations)
      "\\boxed": "\\fbox{$#1$}",
      // Units and spacing
      "\\unit": "\\,\\text{#1}",
    }
  };

  // Function to convert LaTeX delimiters to markdown-compatible format
  const preprocessMath = (text: string): string => {
    // Ensure we have a string
    if (!text || typeof text !== 'string') {
      return String(text || '');
    }

    try {
      let processed = text;

      // First, handle display math patterns \[...\] to $$...$$
      // Handle both escaped and non-escaped versions
      processed = processed.replace(/\\?\\\[/g, '$$').replace(/\\?\\\]/g, '$$');

      // Also handle the specific pattern from your examples: \[ content \]
      processed = processed.replace(/\\\[\s*/g, '$$').replace(/\s*\\\]/g, '$$');

      // Handle cases where \[ and \] appear without escaping
      processed = processed.replace(/(?<!\\)\\\[/g, '$$').replace(/(?<!\\)\\\]/g, '$$');

      // Convert \(...\) to $...$ for inline math
      processed = processed.replace(/\\?\\\(/g, '$').replace(/\\?\\\)/g, '$');

      // Handle mixed content with inline math and text
      // Look for patterns like: $math$ text $more math$
      // Ensure proper spacing and line breaks

      // Handle standalone math expressions that should be on their own lines
      // Pattern: text ending with period/colon, then math expression
      processed = processed.replace(/([.:])\s*\n\s*([A-D]):\s*\n([^$\n]+(?:\$[^$]+\$[^$\n]*)*)/g,
        (match, punctuation, option, mathContent) => {
          // This handles option patterns like "A:\n\tan^{-1}2"
          return `${punctuation}\n\n**${option}:** $${mathContent.trim()}$`;
        });

      // Handle option patterns without explicit math delimiters
      processed = processed.replace(/^([A-D]):\s*\n(.+)$/gm, (match, option, content) => {
        // If content looks like math (contains backslashes, ^, _, etc.), wrap in math
        if (/[\\^_{}]/.test(content) || /tan|sin|cos|log|sqrt|frac/.test(content)) {
          return `**${option}:** $${content.trim()}$`;
        }
        return `**${option}:** ${content.trim()}`;
      });

      // Handle option patterns that are on the same line as the letter
      processed = processed.replace(/^([A-D]):\s*([^\n]+)$/gm, (match, option, content) => {
        const trimmedContent = content.trim();
        // If content looks like math, wrap in math
        if (/[\\^_{}]/.test(trimmedContent) || /tan|sin|cos|log|sqrt|frac/.test(trimmedContent)) {
          return `**${option}:** $${trimmedContent}$`;
        }
        return `**${option}:** ${trimmedContent}`;
      });

      // Handle standalone math expressions on their own lines (not in options)
      processed = processed.replace(/^\s*([\\^_{}].*|.*[\\^_{}].*)$/gm, (match) => {
        const trimmed = match.trim();
        // Skip if already wrapped in $ or $$
        if (trimmed.startsWith('$') || trimmed.includes('**')) {
          return match;
        }
        // If it looks like math, wrap it
        if (/[\\^_{}]/.test(trimmed) || /tan|sin|cos|log|sqrt|frac/.test(trimmed)) {
          return `$$${trimmed}$$`;
        }
        return match;
      });

      // Clean up any double dollar signs that might have been created
      processed = processed.replace(/\$\$\$\$/g, '$$');

      // Handle text wrapped in \[ \] that might contain \text{} commands
      processed = processed.replace(/\$\$\s*\\text\{([^}]+)\}\s*\$\$/g, (match, textContent) => {
        return `$$\\text{${textContent}}$$`;
      });

      return processed;
    } catch (error) {
      console.error('Error in preprocessMath:', error);
      return text;
    }
  };

  // Process content to ensure proper line breaks and math formatting
  const preprocessedContent = preprocessMath(children);

  // Ensure we have a string before further processing
  if (typeof preprocessedContent !== 'string') {
    console.error('preprocessMath did not return a string:', preprocessedContent);
    return <div className={className}>{children}</div>;
  }

  const processedContent = preprocessedContent
    // Handle line breaks more intelligently for mixed content
    .replace(/\n(?!\n)/g, (match, offset, string) => {
      // Don't double newlines if we're in a math block or after certain patterns
      const before = string.substring(Math.max(0, offset - 20), offset);
      const after = string.substring(offset + 1, Math.min(string.length, offset + 21));

      // Keep single newlines in certain contexts
      if (before.includes('**') || after.includes('**') ||
          before.includes('$') || after.includes('$')) {
        return '\n';
      }
      return '\n\n';
    })
    // Ensure math blocks have proper spacing
    .replace(/\$\$(.*?)\$\$/gs, (match, mathContent) => {
      // Clean up the math content
      let cleanMath = mathContent.trim();

      // Handle common LaTeX patterns that might need adjustment
      cleanMath = cleanMath
        // Ensure proper spacing around operators
        .replace(/([a-zA-Z0-9])\s*=\s*([a-zA-Z0-9])/g, '$1 = $2')
        // Handle vector notation properly
        .replace(/\\vec\{([^}]+)\}/g, '\\overrightarrow{$1}')
        // Handle text blocks properly
        .replace(/\\text\{([^}]+)\}/g, '\\text{$1}')
        // Handle boxed answers
        .replace(/\\boxed\{([^}]+)\}/g, '\\boxed{$1}')
        // Handle inverse trig functions
        .replace(/\\tan\^?\{?-1\}?/g, '\\tan^{-1}')
        .replace(/\\sin\^?\{?-1\}?/g, '\\sin^{-1}')
        .replace(/\\cos\^?\{?-1\}?/g, '\\cos^{-1}');

      // Reconstruct the math block with proper spacing
      let result = `$$${cleanMath}$$`;

      // Add newlines around display math if not already present
      if (!result.startsWith('\n\n')) {
        result = '\n\n' + result;
      }
      if (!result.endsWith('\n\n')) {
        result = result + '\n\n';
      }
      return result;
    })
    // Handle inline math spacing and cleanup
    .replace(/\$([^$]+)\$/g, (match, mathContent) => {
      // Clean up inline math content
      let cleanMath = mathContent.trim();

      // Handle common patterns in inline math
      cleanMath = cleanMath
        // Handle inverse trig functions
        .replace(/tan\^?\{?-1\}?/g, '\\tan^{-1}')
        .replace(/sin\^?\{?-1\}?/g, '\\sin^{-1}')
        .replace(/cos\^?\{?-1\}?/g, '\\cos^{-1}')
        // Handle units and spacing
        .replace(/(\d+)\s*\\?\s*text\{([^}]+)\}/g, '$1\\,\\text{$2}')
        // Handle theta and other Greek letters
        .replace(/\\theta/g, '\\theta');

      return `$${cleanMath}$`;
    });



  return (
    <div className={className} style={{
      width: '100%',
      maxWidth: '100%',
      overflowX: 'auto',
      overflowY: 'hidden'
    }}>
      <ReactMarkdown
        remarkPlugins={[remarkMath]}
        rehypePlugins={[[rehypeKatex, katexOptions]]}
        components={{
          // Add custom styling for code blocks
          code: ({ className, children, ...props }) => {
            return (
              <code className={className} {...props}>
                {children}
              </code>
            );
          },
          // Ensure paragraphs have proper spacing
          p: ({ children }) => (
            <p style={{ marginBottom: '1em' }}>{children}</p>
          ),
        }}
      >
        {processedContent}
      </ReactMarkdown>
    </div>
  );
};

export default MathMarkdown;
