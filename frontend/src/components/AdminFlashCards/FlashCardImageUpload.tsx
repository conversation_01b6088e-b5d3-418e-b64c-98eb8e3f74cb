import React, { useState, useRef } from 'react';
import {
  Box,
  Typography,
  Button,
  Paper,
  Card,
  CardMedia,
  CardActions,
  Alert,
  CircularProgress,
  IconButton,
  Tooltip,
} from '@mui/material';
import {
  CloudUpload as UploadIcon,
  Delete as DeleteIcon,
  Image as ImageIcon,
} from '@mui/icons-material';
import { useMutation } from '@tanstack/react-query';

import { uploadFlashCardImage, AdminFlashCardImageUploadResponse } from '../../api/adminFlashcards';

interface FlashCardImageUploadProps {
  flashcardId: number;
  currentImageUrl?: string;
  onImageUploaded: (imageUrl: string) => void;
  onImageRemoved?: () => void;
}

const FlashCardImageUpload: React.FC<FlashCardImageUploadProps> = ({
  flashcardId,
  currentImageUrl,
  onImageUploaded,
  onImageRemoved,
}) => {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [uploadResult, setUploadResult] = useState<AdminFlashCardImageUploadResponse | null>(null);
  const [error, setError] = useState<string | null>(null);

  // Upload mutation
  const uploadMutation = useMutation({
    mutationFn: (file: File) => uploadFlashCardImage(flashcardId, file),
    onSuccess: (result) => {
      setUploadResult(result);
      setError(null);
      onImageUploaded(result.image_url);
    },
    onError: (error: any) => {
      setError(error.message || 'Failed to upload image');
      setUploadResult(null);
    },
  });

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      handleUpload(file);
    }
  };

  const handleDragOver = (event: React.DragEvent) => {
    event.preventDefault();
  };

  const handleDrop = (event: React.DragEvent) => {
    event.preventDefault();
    const file = event.dataTransfer.files[0];
    if (file && file.type.startsWith('image/')) {
      handleUpload(file);
    }
  };

  const handleUpload = (file: File) => {
    setError(null);
    setUploadResult(null);
    uploadMutation.mutate(file);
  };

  const handleRemoveImage = () => {
    setUploadResult(null);
    setError(null);
    if (onImageRemoved) {
      onImageRemoved();
    }
  };

  const displayImageUrl = uploadResult?.image_url || currentImageUrl;

  return (
    <Box sx={{ width: '100%' }}>
      {!displayImageUrl ? (
        <Paper
          sx={{
            p: 3,
            border: '2px dashed',
            borderColor: uploadMutation.isPending ? 'primary.main' : 'divider',
            textAlign: 'center',
            cursor: 'pointer',
            transition: 'all 0.3s ease',
            '&:hover': {
              borderColor: 'primary.main',
              bgcolor: 'action.hover',
            },
          }}
          onDragOver={handleDragOver}
          onDrop={handleDrop}
          onClick={() => fileInputRef.current?.click()}
        >
          <input
            ref={fileInputRef}
            type="file"
            accept="image/*"
            onChange={handleFileSelect}
            style={{ display: 'none' }}
            disabled={uploadMutation.isPending}
          />

          <ImageIcon sx={{ fontSize: 48, color: 'text.secondary', mb: 2 }} />
          
          <Typography variant="h6" gutterBottom>
            {uploadMutation.isPending ? 'Uploading...' : 'Upload Flashcard Image'}
          </Typography>
          
          <Typography variant="body2" color="text.secondary" paragraph>
            {uploadMutation.isPending 
              ? 'Please wait while your image is being uploaded...'
              : 'Click to select an image or drag and drop an image file here'
            }
          </Typography>

          {uploadMutation.isPending && (
            <CircularProgress size={24} />
          )}

          <Typography variant="caption" color="text.secondary" sx={{ mt: 2, display: 'block' }}>
            Supported formats: JPG, PNG, GIF (Max size: 10MB)
          </Typography>
        </Paper>
      ) : (
        <Card sx={{ maxWidth: 400 }}>
          <CardMedia
            component="img"
            height="200"
            image={displayImageUrl}
            alt="Flashcard image"
            sx={{ objectFit: 'contain', bgcolor: 'grey.100' }}
          />
          <CardActions sx={{ justifyContent: 'space-between' }}>
            <Button
              size="small"
              startIcon={<UploadIcon />}
              onClick={() => fileInputRef.current?.click()}
              disabled={uploadMutation.isPending}
            >
              Replace
            </Button>
            <Tooltip title="Remove image">
              <IconButton
                size="small"
                onClick={handleRemoveImage}
                disabled={uploadMutation.isPending}
              >
                <DeleteIcon />
              </IconButton>
            </Tooltip>
          </CardActions>
          
          <input
            ref={fileInputRef}
            type="file"
            accept="image/*"
            onChange={handleFileSelect}
            style={{ display: 'none' }}
            disabled={uploadMutation.isPending}
          />
        </Card>
      )}

      {error && (
        <Alert severity="error" sx={{ mt: 2 }}>
          {error}
        </Alert>
      )}

      {uploadResult && (
        <Alert severity="success" sx={{ mt: 2 }}>
          {uploadResult.message}
        </Alert>
      )}
    </Box>
  );
};

export default FlashCardImageUpload;
