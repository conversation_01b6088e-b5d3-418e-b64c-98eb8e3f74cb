import React, { useState, useRef } from 'react';
import {
  <PERSON><PERSON>,
  DialogTitle,
  DialogContent,
  <PERSON>alogA<PERSON>,
  Button,
  Box,
  Typography,
  Paper,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  CircularProgress,
  IconButton,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
} from '@mui/material';
import {
  CloudUpload as UploadIcon,
  Close as CloseIcon,
  Download as DownloadIcon,
  ExpandMore as ExpandMoreIcon,
  CheckCircle as SuccessIcon,
  Error as ErrorIcon,
} from '@mui/icons-material';
import { useQuery, useMutation } from '@tanstack/react-query';

import { getCourses } from '../../api/courses';
import { uploadCSVFlashCards, downloadFlashCardCSVTemplate, AdminFlashCardCSVUploadResponse } from '../../api/adminFlashcards';

interface CSVUploadDialogProps {
  open: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

const CSVUploadDialog: React.FC<CSVUploadDialogProps> = ({ open, onClose, onSuccess }) => {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [selectedFile, setSelectedFile] = useState<File | null>(null);
  const [selectedCourse, setSelectedCourse] = useState<number | ''>('');
  const [uploadResult, setUploadResult] = useState<AdminFlashCardCSVUploadResponse | null>(null);
  const [uploading, setUploading] = useState(false);
  const [downloadingTemplate, setDownloadingTemplate] = useState(false);

  // Fetch courses
  const { data: courses = [], isLoading: isLoadingCourses } = useQuery({
    queryKey: ['courses'],
    queryFn: () => getCourses(),
  });

  // Upload mutation
  const uploadMutation = useMutation({
    mutationFn: ({ file, courseId }: { file: File; courseId: number }) =>
      uploadCSVFlashCards(file, courseId),
    onSuccess: (result) => {
      setUploadResult(result);
      if (result.success) {
        onSuccess();
      }
    },
    onError: (error: any) => {
      setUploadResult({
        success: false,
        total_rows: 0,
        created_flashcards: 0,
        validation_errors: [error.message || 'Upload failed'],
        creation_errors: [],
        flashcards: [],
      });
    },
  });

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setSelectedFile(file);
      setUploadResult(null);
    }
  };

  const handleDragOver = (event: React.DragEvent) => {
    event.preventDefault();
  };

  const handleDrop = (event: React.DragEvent) => {
    event.preventDefault();
    const file = event.dataTransfer.files[0];
    if (file && file.type === 'text/csv') {
      setSelectedFile(file);
      setUploadResult(null);
    }
  };

  const handleUpload = () => {
    if (!selectedFile || !selectedCourse) {
      return;
    }

    uploadMutation.mutate({ file: selectedFile, courseId: selectedCourse as number });
  };

  const handleClose = () => {
    setSelectedFile(null);
    setSelectedCourse('');
    setUploadResult(null);
    setDownloadingTemplate(false);
    onClose();
  };

  const createManualTemplate = () => {
    const csvContent = 'front_content,back_content,topic,difficulty\n"What is the capital of France?","Paris is the capital and largest city of France.","Geography","easy"\n"Define photosynthesis","The process by which plants convert light energy into chemical energy using chlorophyll.","Biology","medium"\n"What is the formula for the area of a circle?","A = πr², where r is the radius of the circle.","Mathematics","medium"';
    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = 'flashcards_template.csv';
    a.click();
    window.URL.revokeObjectURL(url);
  };

  const downloadTemplate = async () => {
    setDownloadingTemplate(true);
    try {
      const blob = await downloadFlashCardCSVTemplate();
      const url = window.URL.createObjectURL(blob);
      const link = document.createElement('a');
      link.href = url;
      link.download = 'flashcards_template.csv';
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);
      window.URL.revokeObjectURL(url);
    } catch (error: any) {
      console.error('Failed to download template from server, using fallback:', error);
      // Fallback to manual template creation
      createManualTemplate();
    } finally {
      setDownloadingTemplate(false);
    }
  };

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: {
          m: { xs: 1, sm: 2 },
          maxHeight: { xs: '95vh', sm: '90vh' }
        }
      }}
    >
      <DialogTitle>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <Typography variant="h6">Upload Flashcards from CSV</Typography>
          <IconButton onClick={handleClose} size="small">
            <CloseIcon />
          </IconButton>
        </Box>
      </DialogTitle>

      <DialogContent>
        <Box sx={{ mb: 3 }}>
          <Alert severity="info" sx={{ mb: 2 }}>
            Upload a CSV file containing flashcards to bulk import them into the system.
            First select the course, then download the template or upload your file.
          </Alert>

          {/* Course Selection */}
          <FormControl fullWidth sx={{ mb: 3 }}>
            <InputLabel>Select Course *</InputLabel>
            <Select
              value={selectedCourse}
              onChange={(e) => setSelectedCourse(e.target.value as number)}
              label="Select Course *"
              disabled={isLoadingCourses}
            >
              {courses.map((course) => (
                <MenuItem key={course.id} value={course.id}>
                  {course.name} ({course.code})
                </MenuItem>
              ))}
            </Select>
          </FormControl>

          {/* Template Download */}
          <Accordion sx={{ mb: 3 }}>
            <AccordionSummary expandIcon={<ExpandMoreIcon />}>
              <Typography variant="subtitle1">CSV Template & Format</Typography>
            </AccordionSummary>
            <AccordionDetails>
              <Box sx={{ mb: 2 }}>
                <Button
                  variant="outlined"
                  startIcon={downloadingTemplate ? <CircularProgress size={20} /> : <DownloadIcon />}
                  onClick={downloadTemplate}
                  disabled={downloadingTemplate}
                  sx={{ mb: 2 }}
                >
                  {downloadingTemplate ? 'Downloading...' : 'Download Template'}
                </Button>
              </Box>

              <Typography variant="body2" gutterBottom>
                <strong>Required columns:</strong>
              </Typography>
              <ul>
                <li><strong>front_content:</strong> The question or front side of the flashcard</li>
                <li><strong>back_content:</strong> The answer or back side of the flashcard</li>
              </ul>

              <Typography variant="body2" gutterBottom sx={{ mt: 2 }}>
                <strong>Optional columns:</strong>
              </Typography>
              <ul>
                <li><strong>topic:</strong> Topic or category for the flashcard</li>
                <li><strong>difficulty:</strong> easy, medium, or hard</li>
              </ul>

              <Alert severity="info" sx={{ mt: 2 }}>
                <Typography variant="body2">
                  <strong>Note:</strong> Course is selected in the form above,
                  not in the CSV file. The template will be customized based on your selection.
                </Typography>
              </Alert>
            </AccordionDetails>
          </Accordion>
        </Box>

        {/* File Upload Area */}
        <Paper
          sx={{
            p: 3,
            border: '2px dashed',
            borderColor: 'divider',
            textAlign: 'center',
            cursor: 'pointer',
            '&:hover': {
              borderColor: 'primary.main',
              bgcolor: 'action.hover',
            },
          }}
          onDragOver={handleDragOver}
          onDrop={handleDrop}
          onClick={() => fileInputRef.current?.click()}
        >
          <input
            ref={fileInputRef}
            type="file"
            accept=".csv"
            onChange={handleFileSelect}
            style={{ display: 'none' }}
          />

          <UploadIcon sx={{ fontSize: 48, color: 'text.secondary', mb: 2 }} />
          
          <Typography variant="h6" gutterBottom>
            {selectedFile ? selectedFile.name : 'Upload CSV File'}
          </Typography>
          
          <Typography variant="body2" color="text.secondary">
            {selectedFile 
              ? 'Click to select a different file or drag and drop'
              : 'Click to select a file or drag and drop your CSV file here'
            }
          </Typography>
        </Paper>

        {/* Upload Results */}
        {uploadResult && (
          <Box sx={{ mt: 3 }}>
            <Alert 
              severity={uploadResult.success ? 'success' : 'error'}
              icon={uploadResult.success ? <SuccessIcon /> : <ErrorIcon />}
              sx={{ mb: 2 }}
            >
              <Typography variant="subtitle2">
                {uploadResult.success ? 'Upload Successful!' : 'Upload Failed'}
              </Typography>
              <Typography variant="body2">
                Processed {uploadResult.total_rows} rows, created {uploadResult.created_flashcards} flashcards
              </Typography>
            </Alert>

            {/* Validation Errors */}
            {uploadResult.validation_errors.length > 0 && (
              <Accordion sx={{ mb: 2 }}>
                <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                  <Typography variant="subtitle2" color="error">
                    Validation Errors ({uploadResult.validation_errors.length})
                  </Typography>
                </AccordionSummary>
                <AccordionDetails>
                  {uploadResult.validation_errors.map((error, index) => (
                    <Alert key={index} severity="warning" sx={{ mb: 1 }}>
                      {error}
                    </Alert>
                  ))}
                </AccordionDetails>
              </Accordion>
            )}

            {/* Creation Errors */}
            {uploadResult.creation_errors.length > 0 && (
              <Accordion sx={{ mb: 2 }}>
                <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                  <Typography variant="subtitle2" color="error">
                    Creation Errors ({uploadResult.creation_errors.length})
                  </Typography>
                </AccordionSummary>
                <AccordionDetails>
                  {uploadResult.creation_errors.map((error, index) => (
                    <Alert key={index} severity="error" sx={{ mb: 1 }}>
                      {error}
                    </Alert>
                  ))}
                </AccordionDetails>
              </Accordion>
            )}

            {/* Created Flashcards */}
            {uploadResult.flashcards.length > 0 && (
              <Accordion>
                <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                  <Typography variant="subtitle2" color="success.main">
                    Created Flashcards ({uploadResult.flashcards.length})
                  </Typography>
                </AccordionSummary>
                <AccordionDetails>
                  <TableContainer>
                    <Table size="small">
                      <TableHead>
                        <TableRow>
                          <TableCell>Front Content</TableCell>
                          <TableCell>Topic</TableCell>
                          <TableCell>Difficulty</TableCell>
                        </TableRow>
                      </TableHead>
                      <TableBody>
                        {uploadResult.flashcards.slice(0, 10).map((flashcard) => (
                          <TableRow key={flashcard.id}>
                            <TableCell>
                              <Typography variant="body2" noWrap sx={{ maxWidth: 200 }}>
                                {flashcard.front_content}
                              </Typography>
                            </TableCell>
                            <TableCell>
                              {flashcard.topic && (
                                <Chip label={flashcard.topic} size="small" variant="outlined" />
                              )}
                            </TableCell>
                            <TableCell>
                              {flashcard.difficulty && (
                                <Chip label={flashcard.difficulty} size="small" />
                              )}
                            </TableCell>
                          </TableRow>
                        ))}
                      </TableBody>
                    </Table>
                  </TableContainer>
                  {uploadResult.flashcards.length > 10 && (
                    <Typography variant="caption" color="text.secondary" sx={{ mt: 1 }}>
                      Showing first 10 of {uploadResult.flashcards.length} created flashcards
                    </Typography>
                  )}
                </AccordionDetails>
              </Accordion>
            )}
          </Box>
        )}
      </DialogContent>

      <DialogActions>
        <Button onClick={handleClose}>
          {uploadResult?.success ? 'Close' : 'Cancel'}
        </Button>
        {!uploadResult?.success && (
          <Button
            variant="contained"
            onClick={handleUpload}
            disabled={!selectedFile || !selectedCourse || uploadMutation.isPending}
            startIcon={uploadMutation.isPending ? <CircularProgress size={20} /> : <UploadIcon />}
          >
            {uploadMutation.isPending ? 'Uploading...' : 'Upload'}
          </Button>
        )}
      </DialogActions>
    </Dialog>
  );
};

export default CSVUploadDialog;
