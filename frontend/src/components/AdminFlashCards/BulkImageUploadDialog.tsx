import React, { useState, useRef } from 'react';
import {
  <PERSON><PERSON>,
  DialogTitle,
  DialogContent,
  <PERSON>alogActions,
  Button,
  Box,
  Typography,
  Paper,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TextField,
  Alert,
  CircularProgress,
  IconButton,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  Grid,
} from '@mui/material';
import {
  CloudUpload as UploadIcon,
  Close as CloseIcon,
  ExpandMore as ExpandMoreIcon,
  CheckCircle as SuccessIcon,
  Error as ErrorIcon,
  Image as ImageIcon,
} from '@mui/icons-material';
import { useQuery, useMutation } from '@tanstack/react-query';

import { getCourses } from '../../api/courses';
import { getAdminFlashCards, uploadFlashCardImage } from '../../api/adminFlashcards';

interface BulkImageUploadDialogProps {
  open: boolean;
  onClose: () => void;
  onSuccess: () => void;
}

interface UploadResult {
  flashcardId: number;
  filename: string;
  success: boolean;
  imageUrl?: string;
  error?: string;
}

const BulkImageUploadDialog: React.FC<BulkImageUploadDialogProps> = ({ open, onClose, onSuccess }) => {
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [selectedFiles, setSelectedFiles] = useState<File[]>([]);
  const [selectedCourse, setSelectedCourse] = useState<number | ''>('');
  const [flashcardNumbers, setFlashcardNumbers] = useState<string>('');
  const [uploadResults, setUploadResults] = useState<UploadResult[]>([]);
  const [uploading, setUploading] = useState(false);

  // Fetch courses
  const { data: courses = [], isLoading: isLoadingCourses } = useQuery({
    queryKey: ['courses'],
    queryFn: () => getCourses(),
  });

  // Fetch flashcards for selected course
  const { data: flashcards = [] } = useQuery({
    queryKey: ['adminFlashcards', selectedCourse],
    queryFn: () => getAdminFlashCards({ course_id: selectedCourse as number, limit: 1000 }),
    enabled: Boolean(selectedCourse),
  });

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    const imageFiles = files.filter(file => file.type.startsWith('image/'));
    setSelectedFiles(imageFiles);
    setUploadResults([]);
  };

  const handleDragOver = (event: React.DragEvent) => {
    event.preventDefault();
  };

  const handleDrop = (event: React.DragEvent) => {
    event.preventDefault();
    const files = Array.from(event.dataTransfer.files);
    const imageFiles = files.filter(file => file.type.startsWith('image/'));
    setSelectedFiles(imageFiles);
    setUploadResults([]);
  };

  const parseFlashcardNumbers = (input: string): number[] => {
    if (!input.trim()) return [];
    
    const numbers: number[] = [];
    const parts = input.split(',');
    
    for (const part of parts) {
      const trimmed = part.trim();
      if (trimmed.includes('-')) {
        // Range like "1-5"
        const [start, end] = trimmed.split('-').map(n => parseInt(n.trim()));
        if (!isNaN(start) && !isNaN(end) && start <= end) {
          for (let i = start; i <= end; i++) {
            numbers.push(i);
          }
        }
      } else {
        // Single number
        const num = parseInt(trimmed);
        if (!isNaN(num)) {
          numbers.push(num);
        }
      }
    }
    
    return [...new Set(numbers)].sort((a, b) => a - b);
  };

  const handleUpload = async () => {
    if (!selectedCourse || selectedFiles.length === 0) {
      return;
    }

    setUploading(true);
    setUploadResults([]);

    const parsedFlashcardNumbers = parseFlashcardNumbers(flashcardNumbers);
    const results: UploadResult[] = [];

    // Map files to flashcards
    for (let i = 0; i < selectedFiles.length; i++) {
      const file = selectedFiles[i];
      let flashcardId: number | null = null;

      if (parsedFlashcardNumbers.length > 0) {
        // Use specified flashcard numbers
        if (i < parsedFlashcardNumbers.length) {
          const flashcardIndex = parsedFlashcardNumbers[i] - 1; // Convert to 0-based index
          if (flashcardIndex >= 0 && flashcardIndex < flashcards.length) {
            flashcardId = flashcards[flashcardIndex].id;
          }
        }
      } else {
        // Auto-assign to flashcards in order
        if (i < flashcards.length) {
          flashcardId = flashcards[i].id;
        }
      }

      if (flashcardId) {
        try {
          const result = await uploadFlashCardImage(flashcardId, file);
          results.push({
            flashcardId,
            filename: file.name,
            success: true,
            imageUrl: result.image_url,
          });
        } catch (error: any) {
          results.push({
            flashcardId,
            filename: file.name,
            success: false,
            error: error.message || 'Upload failed',
          });
        }
      } else {
        results.push({
          flashcardId: 0,
          filename: file.name,
          success: false,
          error: 'No flashcard available for this image',
        });
      }
    }

    setUploadResults(results);
    setUploading(false);

    if (results.some(r => r.success)) {
      onSuccess();
    }
  };

  const handleClose = () => {
    setSelectedFiles([]);
    setSelectedCourse('');
    setFlashcardNumbers('');
    setUploadResults([]);
    onClose();
  };

  const successCount = uploadResults.filter(r => r.success).length;
  const errorCount = uploadResults.filter(r => !r.success).length;

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: {
          m: { xs: 1, sm: 2 },
          maxHeight: { xs: '95vh', sm: '90vh' }
        }
      }}
    >
      <DialogTitle>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <Typography variant="h6">Bulk Upload Flashcard Images</Typography>
          <IconButton onClick={handleClose} size="small">
            <CloseIcon />
          </IconButton>
        </Box>
      </DialogTitle>

      <DialogContent>
        <Box sx={{ mb: 3 }}>
          <Alert severity="info" sx={{ mb: 2 }}>
            Upload multiple images and assign them to flashcards. Images will be assigned in order or based on specified flashcard numbers.
          </Alert>

          {/* Course Selection */}
          <FormControl fullWidth sx={{ mb: 3 }}>
            <InputLabel>Select Course *</InputLabel>
            <Select
              value={selectedCourse}
              onChange={(e) => setSelectedCourse(e.target.value as number)}
              label="Select Course *"
              disabled={isLoadingCourses}
            >
              {courses.map((course) => (
                <MenuItem key={course.id} value={course.id}>
                  {course.name} ({course.code})
                </MenuItem>
              ))}
            </Select>
          </FormControl>

          {/* Flashcard Numbers */}
          {selectedCourse && (
            <TextField
              fullWidth
              label="Flashcard Numbers (optional)"
              value={flashcardNumbers}
              onChange={(e) => setFlashcardNumbers(e.target.value)}
              placeholder="e.g., 1,3,5-8,10"
              helperText={`Available flashcards: 1-${flashcards.length}. Leave empty to auto-assign in order.`}
              sx={{ mb: 3 }}
            />
          )}
        </Box>

        {/* File Upload Area */}
        <Paper
          sx={{
            p: 3,
            border: '2px dashed',
            borderColor: 'divider',
            textAlign: 'center',
            cursor: 'pointer',
            '&:hover': {
              borderColor: 'primary.main',
              bgcolor: 'action.hover',
            },
          }}
          onDragOver={handleDragOver}
          onDrop={handleDrop}
          onClick={() => fileInputRef.current?.click()}
        >
          <input
            ref={fileInputRef}
            type="file"
            accept="image/*"
            multiple
            onChange={handleFileSelect}
            style={{ display: 'none' }}
          />

          <ImageIcon sx={{ fontSize: 48, color: 'text.secondary', mb: 2 }} />
          
          <Typography variant="h6" gutterBottom>
            {selectedFiles.length > 0 ? `${selectedFiles.length} files selected` : 'Upload Images'}
          </Typography>
          
          <Typography variant="body2" color="text.secondary">
            Click to select files or drag and drop multiple image files here
          </Typography>

          {selectedFiles.length > 0 && (
            <Box sx={{ mt: 2 }}>
              <Typography variant="body2" color="text.secondary">
                Selected files: {selectedFiles.map(f => f.name).join(', ')}
              </Typography>
            </Box>
          )}
        </Paper>

        {/* Upload Results */}
        {uploadResults.length > 0 && (
          <Box sx={{ mt: 3 }}>
            <Alert 
              severity={errorCount === 0 ? 'success' : successCount > 0 ? 'warning' : 'error'}
              sx={{ mb: 2 }}
            >
              <Typography variant="subtitle2">
                Upload Complete: {successCount} successful, {errorCount} failed
              </Typography>
            </Alert>

            <Accordion>
              <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                <Typography variant="subtitle2">
                  Upload Results ({uploadResults.length} files)
                </Typography>
              </AccordionSummary>
              <AccordionDetails>
                <TableContainer>
                  <Table size="small">
                    <TableHead>
                      <TableRow>
                        <TableCell>File</TableCell>
                        <TableCell>Flashcard ID</TableCell>
                        <TableCell>Status</TableCell>
                        <TableCell>Result</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {uploadResults.map((result, index) => (
                        <TableRow key={index}>
                          <TableCell>{result.filename}</TableCell>
                          <TableCell>{result.flashcardId || 'N/A'}</TableCell>
                          <TableCell>
                            <Chip
                              icon={result.success ? <SuccessIcon /> : <ErrorIcon />}
                              label={result.success ? 'Success' : 'Failed'}
                              color={result.success ? 'success' : 'error'}
                              size="small"
                            />
                          </TableCell>
                          <TableCell>
                            {result.success ? (
                              <Typography variant="caption" color="success.main">
                                Image uploaded
                              </Typography>
                            ) : (
                              <Typography variant="caption" color="error.main">
                                {result.error}
                              </Typography>
                            )}
                          </TableCell>
                        </TableRow>
                      ))}
                    </TableBody>
                  </Table>
                </TableContainer>
              </AccordionDetails>
            </Accordion>
          </Box>
        )}
      </DialogContent>

      <DialogActions>
        <Button onClick={handleClose}>
          {uploadResults.length > 0 ? 'Close' : 'Cancel'}
        </Button>
        {uploadResults.length === 0 && (
          <Button
            variant="contained"
            onClick={handleUpload}
            disabled={!selectedCourse || selectedFiles.length === 0 || uploading}
            startIcon={uploading ? <CircularProgress size={20} /> : <UploadIcon />}
          >
            {uploading ? 'Uploading...' : 'Upload Images'}
          </Button>
        )}
      </DialogActions>
    </Dialog>
  );
};

export default BulkImageUploadDialog;
