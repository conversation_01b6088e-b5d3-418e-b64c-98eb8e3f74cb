import React, { useState } from 'react';
import {
  Box,
  Typography,
  Card,
  CardContent,
  CardMedia,
  Chip,
  useTheme,
  useMediaQuery,
} from '@mui/material';
import {
  Topic as TopicIcon,
  TouchApp as TouchIcon,
} from '@mui/icons-material';
import { motion, AnimatePresence } from 'framer-motion';

interface FlashCardPreviewProps {
  frontContent: string;
  backContent: string;
  topic?: string;
  difficulty?: string;
  imageUrl?: string;
}

const FlashCardPreview: React.FC<FlashCardPreviewProps> = ({
  frontContent,
  backContent,
  topic,
  difficulty,
  imageUrl,
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const [isFlipped, setIsFlipped] = useState(false);

  const handleFlip = () => {
    setIsFlipped(!isFlipped);
  };

  const getDifficultyColor = (difficulty?: string) => {
    switch (difficulty?.toLowerCase()) {
      case 'easy':
        return theme.palette.success.main;
      case 'medium':
        return theme.palette.warning.main;
      case 'hard':
        return theme.palette.error.main;
      default:
        return theme.palette.primary.main;
    }
  };

  const generateGradient = () => {
    const colors = [
      ['#667eea', '#764ba2'],
      ['#f093fb', '#f5576c'],
      ['#4facfe', '#00f2fe'],
      ['#43e97b', '#38f9d7'],
      ['#fa709a', '#fee140'],
      ['#a8edea', '#fed6e3'],
      ['#ff9a9e', '#fecfef'],
      ['#ffecd2', '#fcb69f'],
    ];
    const randomColors = colors[Math.floor(Math.random() * colors.length)];
    return `linear-gradient(135deg, ${randomColors[0]} 0%, ${randomColors[1]} 100%)`;
  };

  const cardStyle = {
    width: '100%',
    maxWidth: 400,
    height: 280,
    margin: '0 auto',
    perspective: '1000px',
    cursor: 'pointer',
  };

  const cardInnerStyle = {
    position: 'relative',
    width: '100%',
    height: '100%',
    textAlign: 'center',
    transition: 'transform 0.6s',
    transformStyle: 'preserve-3d',
    transform: isFlipped ? 'rotateY(180deg)' : 'rotateY(0deg)',
  };

  const cardFaceStyle = {
    position: 'absolute',
    width: '100%',
    height: '100%',
    backfaceVisibility: 'hidden',
    borderRadius: theme.shape.borderRadius * 2,
    boxShadow: theme.shadows[4],
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
    padding: theme.spacing(2),
    background: generateGradient(),
    color: 'white',
  };

  const cardBackStyle = {
    ...cardFaceStyle,
    transform: 'rotateY(180deg)',
    background: `linear-gradient(135deg, ${getDifficultyColor(difficulty)} 0%, ${theme.palette.primary.dark} 100%)`,
  };

  return (
    <Box sx={{ textAlign: 'center' }}>
      {/* Instructions */}
      <Box sx={{ mb: 2, display: 'flex', alignItems: 'center', justifyContent: 'center', gap: 1 }}>
        <TouchIcon fontSize="small" color="action" />
        <Typography variant="body2" color="text.secondary">
          {isFlipped ? 'Showing Answer (Click to see question)' : 'Showing Question (Click to see answer)'}
        </Typography>
      </Box>

      {/* Flashcard */}
      <Box sx={cardStyle} onClick={handleFlip}>
        <Box sx={cardInnerStyle}>
          {/* Front of card (Question) */}
          <Box sx={cardFaceStyle}>
            {topic && (
              <Chip
                icon={<TopicIcon fontSize="small" />}
                label={topic}
                size="small"
                sx={{ 
                  mb: 2, 
                  bgcolor: 'rgba(255, 255, 255, 0.2)',
                  color: 'white',
                  '& .MuiChip-icon': { color: 'white' }
                }}
              />
            )}
            
            {imageUrl && (
              <Box
                component="img"
                src={imageUrl}
                alt="Flashcard"
                sx={{
                  maxWidth: '100%',
                  maxHeight: 120,
                  objectFit: 'contain',
                  mb: 2,
                  borderRadius: 1,
                }}
              />
            )}
            
            <Typography
              variant={isMobile ? 'body1' : 'h6'}
              sx={{
                fontWeight: 'bold',
                textAlign: 'center',
                overflow: 'hidden',
                display: '-webkit-box',
                WebkitLineClamp: imageUrl ? 3 : 6,
                WebkitBoxOrient: 'vertical',
                lineHeight: 1.4,
              }}
            >
              {frontContent || 'Enter front content...'}
            </Typography>
          </Box>

          {/* Back of card (Answer) */}
          <Box sx={cardBackStyle}>
            {difficulty && (
              <Chip
                label={difficulty.toUpperCase()}
                size="small"
                sx={{ 
                  mb: 2, 
                  bgcolor: 'rgba(255, 255, 255, 0.2)',
                  color: 'white',
                  fontWeight: 'bold'
                }}
              />
            )}
            
            <Typography
              variant={isMobile ? 'body1' : 'h6'}
              sx={{
                fontWeight: 'bold',
                textAlign: 'center',
                overflow: 'hidden',
                display: '-webkit-box',
                WebkitLineClamp: 6,
                WebkitBoxOrient: 'vertical',
                lineHeight: 1.4,
              }}
            >
              {backContent || 'Enter back content...'}
            </Typography>
          </Box>
        </Box>
      </Box>

      {/* Flip Indicator */}
      <Box sx={{ mt: 2, display: 'flex', justifyContent: 'center', gap: 1 }}>
        <Box
          sx={{
            width: 8,
            height: 8,
            borderRadius: '50%',
            bgcolor: !isFlipped ? 'primary.main' : 'grey.300',
            transition: 'background-color 0.3s',
          }}
        />
        <Box
          sx={{
            width: 8,
            height: 8,
            borderRadius: '50%',
            bgcolor: isFlipped ? 'primary.main' : 'grey.300',
            transition: 'background-color 0.3s',
          }}
        />
      </Box>

      {/* Content Preview */}
      <Box sx={{ mt: 3, textAlign: 'left' }}>
        <Typography variant="subtitle2" color="text.secondary" gutterBottom>
          Content Preview:
        </Typography>
        <Box sx={{ display: 'flex', gap: 2, flexDirection: { xs: 'column', md: 'row' } }}>
          <Box sx={{ flex: 1 }}>
            <Typography variant="caption" color="text.secondary">
              Front (Question):
            </Typography>
            <Typography variant="body2" sx={{ 
              p: 1, 
              bgcolor: 'grey.50', 
              borderRadius: 1, 
              minHeight: 40,
              border: `1px solid ${theme.palette.divider}`,
              whiteSpace: 'pre-wrap'
            }}>
              {frontContent || 'No content'}
            </Typography>
          </Box>
          <Box sx={{ flex: 1 }}>
            <Typography variant="caption" color="text.secondary">
              Back (Answer):
            </Typography>
            <Typography variant="body2" sx={{ 
              p: 1, 
              bgcolor: 'grey.50', 
              borderRadius: 1, 
              minHeight: 40,
              border: `1px solid ${theme.palette.divider}`,
              whiteSpace: 'pre-wrap'
            }}>
              {backContent || 'No content'}
            </Typography>
          </Box>
        </Box>
      </Box>
    </Box>
  );
};

export default FlashCardPreview;
