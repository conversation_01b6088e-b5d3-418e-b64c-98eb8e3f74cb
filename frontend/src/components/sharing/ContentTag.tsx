/**
 * Content tag component to show sharing indicators
 */

import React from 'react';
import {
  Chip,
  Box,
  Tooltip,
  Typography
} from '@mui/material';
import {
  Share as ShareIcon,
  Lock as LockIcon,
  Person as PersonIcon,
  Visibility as VisibilityIcon
} from '@mui/icons-material';
import { ContentTagProps } from '../../types/contentSharing';

const ContentTag: React.FC<ContentTagProps> = ({
  is_shared,
  shared_by_name,
  tags,
  size = 'medium'
}) => {
  if (!is_shared && tags.length === 0) {
    return null;
  }

  const getChipSize = () => {
    return size === 'small' ? 'small' as const : 'medium' as const;
  };

  const getIconSize = () => {
    return size === 'small' ? 16 : 20;
  };

  return (
    <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, alignItems: 'center' }}>
      {is_shared && (
        <Tooltip 
          title={shared_by_name ? `Shared by ${shared_by_name}` : 'Shared content'}
          arrow
        >
          <Chip
            icon={<ShareIcon sx={{ fontSize: getIconSize() }} />}
            label={shared_by_name ? `By ${shared_by_name}` : 'Shared'}
            size={getChipSize()}
            variant="outlined"
            color="primary"
            sx={{
              backgroundColor: 'primary.light',
              color: 'primary.dark',
              '& .MuiChip-icon': {
                color: 'primary.main'
              }
            }}
          />
        </Tooltip>
      )}

      {tags.includes('read-only') && (
        <Tooltip title="Read-only content - cannot be edited or deleted" arrow>
          <Chip
            icon={<LockIcon sx={{ fontSize: getIconSize() }} />}
            label="Read-only"
            size={getChipSize()}
            variant="outlined"
            color="warning"
            sx={{
              backgroundColor: 'warning.light',
              color: 'warning.dark',
              '& .MuiChip-icon': {
                color: 'warning.main'
              }
            }}
          />
        </Tooltip>
      )}

      {tags.includes('public') && (
        <Tooltip title="Publicly shared content" arrow>
          <Chip
            icon={<VisibilityIcon sx={{ fontSize: getIconSize() }} />}
            label="Public"
            size={getChipSize()}
            variant="outlined"
            color="info"
            sx={{
              backgroundColor: 'info.light',
              color: 'info.dark',
              '& .MuiChip-icon': {
                color: 'info.main'
              }
            }}
          />
        </Tooltip>
      )}

      {tags.includes('private') && (
        <Tooltip title="Privately shared content" arrow>
          <Chip
            icon={<PersonIcon sx={{ fontSize: getIconSize() }} />}
            label="Private"
            size={getChipSize()}
            variant="outlined"
            color="secondary"
            sx={{
              backgroundColor: 'secondary.light',
              color: 'secondary.dark',
              '& .MuiChip-icon': {
                color: 'secondary.main'
              }
            }}
          />
        </Tooltip>
      )}
    </Box>
  );
};

export default ContentTag;
