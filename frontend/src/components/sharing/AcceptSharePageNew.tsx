/**
 * Accept share page for the new simplified content sharing system
 */

import React, { useState, useEffect } from 'react';
import { useSearchParams, useNavigate, useLocation, useParams } from 'react-router-dom';
import {
  Card,
  CardContent,
  CardHeader,
  Button,
  Typography,
  Alert,
  Box,
  Container,
  CircularProgress,
  Chip
} from '@mui/material';
import {
  Share as ShareIcon,
  CheckCircle as CheckCircleIcon,
  Warning as WarningIcon,
  Quiz as QuizIcon,
  Style as StyleIcon,
  Description as DescriptionIcon
} from '@mui/icons-material';
import { useAuth } from '../../contexts/AuthContext';
import { useContentSharing } from '../../hooks/useContentSharing';
import { ContentType } from '../../types/contentSharing';

const AcceptSharePageNew: React.FC = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const location = useLocation();
  const params = useParams();
  const { isAuthenticated, isLoading: authLoading } = useAuth();
  const { acceptInvitation, accessPublicShare, isLoading } = useContentSharing();
  
  // Get tokens from URL
  const shareToken = params.shareToken; // For public shares: /share/{token}
  const invitationToken = searchParams.get('token') || searchParams.get('invitation_token'); // For invitations
  
  const [isProcessing, setIsProcessing] = useState(false);
  const [result, setResult] = useState<{
    success: boolean;
    message: string;
    content_type?: ContentType;
    redirect_url?: string;
  } | null>(null);
  const [error, setError] = useState<string | null>(null);

  // Check authentication and redirect to login if needed
  useEffect(() => {
    if (!authLoading && !isAuthenticated) {
      // User is not authenticated, redirect to login with current URL as redirect parameter
      const currentUrl = `${location.pathname}${location.search}`;
      navigate(`/login?redirect=${encodeURIComponent(currentUrl)}`);
      return;
    }
  }, [isAuthenticated, authLoading, location, navigate]);

  // Process share access when user is authenticated
  useEffect(() => {
    if (isAuthenticated && !isProcessing && !result) {
      if (shareToken) {
        handlePublicShare();
      } else if (invitationToken) {
        handleInvitation();
      } else {
        setError('No share token or invitation token provided');
      }
    }
  }, [isAuthenticated, shareToken, invitationToken, isProcessing, result]);

  const handlePublicShare = async () => {
    if (!shareToken) return;
    
    setIsProcessing(true);
    setError(null);

    try {
      const response = await accessPublicShare(shareToken);
      if (response) {
        setResult({
          success: true,
          message: response.message,
          content_type: response.content_type,
          redirect_url: response.redirect_url
        });
      }
    } catch (err) {
      setError('Failed to access shared content');
    } finally {
      setIsProcessing(false);
    }
  };

  const handleInvitation = async () => {
    if (!invitationToken) return;
    
    setIsProcessing(true);
    setError(null);

    try {
      const response = await acceptInvitation(invitationToken);
      if (response) {
        setResult({
          success: true,
          message: response.message,
          content_type: response.content_type,
          redirect_url: response.redirect_url
        });
      }
    } catch (err) {
      setError('Failed to accept invitation');
    } finally {
      setIsProcessing(false);
    }
  };

  const handleViewContent = () => {
    if (result?.redirect_url) {
      navigate(result.redirect_url);
    } else {
      navigate('/dashboard');
    }
  };

  const getContentIcon = (contentType?: ContentType) => {
    switch (contentType) {
      case ContentType.MCQ:
        return <QuizIcon sx={{ fontSize: 40, color: 'primary.main' }} />;
      case ContentType.FLASHCARD:
        return <StyleIcon sx={{ fontSize: 40, color: 'primary.main' }} />;
      case ContentType.SUMMARY:
        return <DescriptionIcon sx={{ fontSize: 40, color: 'primary.main' }} />;
      default:
        return <ShareIcon sx={{ fontSize: 40, color: 'primary.main' }} />;
    }
  };

  const getContentTypeLabel = (contentType?: ContentType) => {
    switch (contentType) {
      case ContentType.MCQ: return 'MCQ Questions';
      case ContentType.FLASHCARD: return 'Flashcards';
      case ContentType.SUMMARY: return 'Summary';
      default: return 'Content';
    }
  };

  // Show loading while checking authentication or processing
  if (authLoading || isProcessing) {
    return (
      <Container maxWidth="sm" sx={{ minHeight: '100vh', display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
        <Box textAlign="center">
          <CircularProgress size={60} />
          <Typography variant="body1" sx={{ mt: 2 }}>
            {authLoading ? 'Checking authentication...' : 'Processing share...'}
          </Typography>
        </Box>
      </Container>
    );
  }

  // Don't render anything if user is not authenticated (redirect will happen)
  if (!isAuthenticated) {
    return null;
  }

  return (
    <Container maxWidth="sm" sx={{ minHeight: '100vh', display: 'flex', alignItems: 'center', justifyContent: 'center', py: 4 }}>
      <Card sx={{ width: '100%', borderRadius: 3, boxShadow: 3 }}>
        <CardHeader
          title={
            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
              <ShareIcon sx={{ mr: 1, fontSize: 28 }} />
              <Typography variant="h5" component="h1">
                Shared Content
              </Typography>
            </Box>
          }
          sx={{ textAlign: 'center', pb: 1 }}
        />
        
        <CardContent sx={{ pt: 0 }}>
          {error && (
            <Alert 
              severity="error" 
              sx={{ mb: 3 }}
              icon={<WarningIcon />}
            >
              <Typography variant="body1" fontWeight="medium">
                {error}
              </Typography>
              <Typography variant="body2" sx={{ mt: 1 }}>
                The share link may be invalid, expired, or you may not have permission to access this content.
              </Typography>
            </Alert>
          )}

          {result && (
            <Box sx={{ textAlign: 'center' }}>
              {result.success ? (
                <>
                  <Box sx={{ mb: 3 }}>
                    {getContentIcon(result.content_type)}
                  </Box>
                  
                  <Alert 
                    severity="success" 
                    sx={{ mb: 3 }}
                    icon={<CheckCircleIcon />}
                  >
                    <Typography variant="body1" fontWeight="medium">
                      {result.message}
                    </Typography>
                  </Alert>

                  {result.content_type && (
                    <Box sx={{ mb: 3 }}>
                      <Chip
                        label={getContentTypeLabel(result.content_type)}
                        color="primary"
                        variant="outlined"
                        size="medium"
                      />
                    </Box>
                  )}

                  <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
                    You now have access to this shared content. It will appear in your content library alongside your own content.
                  </Typography>

                  <Button
                    variant="contained"
                    size="large"
                    onClick={handleViewContent}
                    sx={{ borderRadius: 2, px: 4 }}
                  >
                    View Content
                  </Button>
                </>
              ) : (
                <Alert severity="error" sx={{ mb: 3 }}>
                  <Typography variant="body1" fontWeight="medium">
                    {result.message}
                  </Typography>
                </Alert>
              )}
            </Box>
          )}

          {!result && !error && (
            <Box sx={{ textAlign: 'center' }}>
              <Typography variant="body1" color="text.secondary">
                Processing your request...
              </Typography>
            </Box>
          )}
        </CardContent>
      </Card>
    </Container>
  );
};

export default AcceptSharePageNew;
