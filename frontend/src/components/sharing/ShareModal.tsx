/**
 * Share modal component for the new simplified content sharing system
 */

import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  TextField,
  Chip,
  Box,
  Typography,
  Switch,
  FormControlLabel,
  Alert,
  CircularProgress,
  IconButton,
  InputAdornment,
  Divider
} from '@mui/material';
import {
  Close as CloseIcon,
  Add as AddIcon,
  ContentCopy as CopyIcon,
  Email as EmailIcon,
  Link as LinkIcon,
  Share as ShareIcon,
  Visibility as VisibilityIcon
} from '@mui/icons-material';
import { ShareModalProps, ContentType } from '../../types/contentSharing';
import { contentSharingApi } from '../../api/contentSharing';
import { useToast } from '../../contexts/ToastContext';

const ShareModal: React.FC<ShareModalProps> = ({
  open,
  onClose,
  content_type,
  content_id,
  content_ids,
  content_title,
  course_name
}) => {
  const navigate = useNavigate();
  const [emails, setEmails] = useState<string[]>([]);
  const [emailInput, setEmailInput] = useState('');
  const [shareMessage, setShareMessage] = useState('');
  const [createPublicLink, setCreatePublicLink] = useState(false);
  const [isSharing, setIsSharing] = useState(false);
  const [publicShareUrl, setPublicShareUrl] = useState<string | null>(null);
  const [error, setError] = useState<string | null>(null);
  const { showSuccess, showError } = useToast();

  const handleAddEmail = () => {
    const email = emailInput.trim();
    if (email && !emails.includes(email)) {
      if (isValidEmail(email)) {
        setEmails([...emails, email]);
        setEmailInput('');
        setError(null);
      } else {
        setError('Please enter a valid email address');
      }
    }
  };

  const handleRemoveEmail = (emailToRemove: string) => {
    setEmails(emails.filter(email => email !== emailToRemove));
  };

  const handleKeyPress = (event: React.KeyboardEvent) => {
    if (event.key === 'Enter') {
      event.preventDefault();
      handleAddEmail();
    }
  };

  const isValidEmail = (email: string): boolean => {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
  };

  const handleShare = async () => {
    if (emails.length === 0 && !createPublicLink) {
      setError('Please add at least one email or enable public link sharing');
      return;
    }

    setIsSharing(true);
    setError(null);

    try {
      // Always include content_ids if provided, even if it's just one item
      const shareRequest = {
        content_type,
        content_id,
        content_ids: content_ids && content_ids.length > 0 ? content_ids : undefined,
        share_message: shareMessage || undefined,
        invited_emails: emails.length > 0 ? emails : undefined,
        create_public_link: createPublicLink
      };

      console.log('🔍 Sharing request:', shareRequest);
      console.log('📋 Content IDs to share:', content_ids);
      console.log('📊 Number of items to share:', content_ids?.length || 1);

      const response = await contentSharingApi.shareContent(shareRequest);

      if (response.public_share_url) {
        setPublicShareUrl(response.public_share_url);
      }

      showSuccess(response.message);
      
      if (response.invitations_failed.length > 0) {
        showError(`Failed to send invitations to: ${response.invitations_failed.join(', ')}`);
      }

    } catch (err: any) {
      setError(err.response?.data?.detail || 'Failed to share content');
      showError('Failed to share content');
    } finally {
      setIsSharing(false);
    }
  };

  const handleCopyLink = async () => {
    if (publicShareUrl) {
      try {
        await navigator.clipboard.writeText(publicShareUrl);
        showSuccess('Share link copied to clipboard');
      } catch (err) {
        showError('Failed to copy link');
      }
    }
  };

  const handleClose = () => {
    setEmails([]);
    setEmailInput('');
    setShareMessage('');
    setCreatePublicLink(false);
    setPublicShareUrl(null);
    setError(null);
    onClose();
  };

  const getContentTypeLabel = () => {
    switch (content_type) {
      case ContentType.MCQ: return 'MCQ Questions';
      case ContentType.FLASHCARD: return 'Flashcards';
      case ContentType.SUMMARY: return 'Summary';
      default: return 'Content';
    }
  };

  const handleViewContent = () => {
    // Navigate to the appropriate content page based on content type
    if (content_type === ContentType.MCQ) {
      if (content_ids && content_ids.length > 0) {
        // For multiple MCQs, store IDs in session storage and navigate to generated practice
        sessionStorage.setItem('practiceQuestionIds', JSON.stringify(content_ids));
        sessionStorage.setItem('practiceCourseName', course_name || content_title);
        navigate('/mcq/practice/generated');
      } else {
        // For single MCQ, try to navigate to course practice or generated practice
        if (course_name) {
          sessionStorage.setItem('practiceQuestionIds', JSON.stringify([content_id]));
          sessionStorage.setItem('practiceCourseName', course_name);
          navigate('/mcq/practice/generated');
        } else {
          // Fallback to MCQ dashboard
          navigate('/mcq');
        }
      }
    } else if (content_type === ContentType.FLASHCARD) {
      if (content_ids && content_ids.length > 0) {
        // For multiple flashcards, store IDs in session storage and navigate to generated practice
        sessionStorage.setItem('practiceFlashcardIds', JSON.stringify(content_ids));
        sessionStorage.setItem('practiceCourseName', course_name || content_title);
        navigate('/flashcards/practice/generated');
      } else {
        // For single flashcard, store ID and navigate to practice
        sessionStorage.setItem('practiceFlashcardIds', JSON.stringify([content_id]));
        sessionStorage.setItem('practiceCourseName', course_name || content_title);
        navigate('/flashcards/practice/generated');
      }
    } else if (content_type === ContentType.SUMMARY) {
      // Navigate to summaries/notes page
      navigate('/my-notes');
    } else {
      // Fallback to dashboard
      navigate('/dashboard');
    }

    // Close the modal after navigation
    handleClose();
  };

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      maxWidth="sm"
      fullWidth
      PaperProps={{
        sx: { borderRadius: 2 }
      }}
    >
      <DialogTitle sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <Box component="span">
          Share {getContentTypeLabel()}
        </Box>
        <IconButton onClick={handleClose} size="small">
          <CloseIcon />
        </IconButton>
      </DialogTitle>

      <DialogContent dividers>
        <Box sx={{ mb: 3 }}>
          <Typography variant="subtitle2" color="text.secondary" gutterBottom>
            Content: {content_title}
          </Typography>
          {course_name && (
            <Typography variant="body2" color="text.secondary">
              Course: {course_name}
            </Typography>
          )}
        </Box>

        {/* Share Message */}
        <TextField
          fullWidth
          label="Share Message (Optional)"
          multiline
          rows={3}
          value={shareMessage}
          onChange={(e) => setShareMessage(e.target.value)}
          placeholder="Add a message to share with this content..."
          sx={{ mb: 3 }}
        />

        {/* Email Invitations */}
        <Box sx={{ mb: 3 }}>
          <Typography variant="subtitle1" gutterBottom sx={{ display: 'flex', alignItems: 'center' }}>
            <EmailIcon sx={{ mr: 1, fontSize: 20 }} />
            Email Invitations
          </Typography>
          
          <TextField
            fullWidth
            label="Email Address"
            value={emailInput}
            onChange={(e) => setEmailInput(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder="Enter email address and press Enter"
            InputProps={{
              endAdornment: (
                <InputAdornment position="end">
                  <IconButton onClick={handleAddEmail} disabled={!emailInput.trim()}>
                    <AddIcon />
                  </IconButton>
                </InputAdornment>
              )
            }}
            sx={{ mb: 2 }}
          />

          {emails.length > 0 && (
            <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 2 }}>
              {emails.map((email) => (
                <Chip
                  key={email}
                  label={email}
                  onDelete={() => handleRemoveEmail(email)}
                  size="small"
                  color="primary"
                  variant="outlined"
                />
              ))}
            </Box>
          )}
        </Box>

        <Divider sx={{ my: 2 }} />

        {/* Public Link */}
        <Box sx={{ mb: 2 }}>
          <FormControlLabel
            control={
              <Switch
                checked={createPublicLink}
                onChange={(e) => setCreatePublicLink(e.target.checked)}
              />
            }
            label={
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <LinkIcon sx={{ mr: 1, fontSize: 20 }} />
                Create Public Share Link
              </Box>
            }
          />
          <Typography variant="body2" color="text.secondary" sx={{ ml: 4 }}>
            Anyone with the link can access this content
          </Typography>
        </Box>

        {publicShareUrl && (
          <Alert 
            severity="success" 
            sx={{ mb: 2 }}
            action={
              <Button size="small" onClick={handleCopyLink} startIcon={<CopyIcon />}>
                Copy
              </Button>
            }
          >
            <Typography variant="body2" sx={{ wordBreak: 'break-all' }}>
              Share link: {publicShareUrl}
            </Typography>
          </Alert>
        )}

        {error && (
          <Alert severity="error" sx={{ mb: 2 }}>
            {error}
          </Alert>
        )}
      </DialogContent>

      <DialogActions sx={{ p: 2, justifyContent: 'space-between' }}>
        <Button
          onClick={handleViewContent}
          startIcon={<VisibilityIcon />}
          disabled={isSharing}
          sx={{ mr: 'auto' }}
        >
          View Content
        </Button>
        <Box sx={{ display: 'flex', gap: 1 }}>
          <Button onClick={handleClose} disabled={isSharing}>
            Cancel
          </Button>
          <Button
            variant="contained"
            onClick={handleShare}
            disabled={isSharing || (emails.length === 0 && !createPublicLink)}
            startIcon={isSharing ? <CircularProgress size={16} /> : <ShareIcon />}
          >
            {isSharing ? 'Sharing...' : 'Share'}
          </Button>
        </Box>
      </DialogActions>
    </Dialog>
  );
};

export default ShareModal;
