/**
 * Share button component for the new simplified content sharing system
 */

import React, { useState } from 'react';
import {
  IconButton,
  Button,
  Tooltip,
  CircularProgress
} from '@mui/material';
import {
  Share as ShareIcon
} from '@mui/icons-material';
import { ShareButtonProps } from '../../types/contentSharing';
import ShareModal from './ShareModal';

interface ShareButtonComponentProps extends ShareButtonProps {
  variant?: 'icon' | 'button';
  showLabel?: boolean;
  tooltip?: string;
}

const ShareButton: React.FC<ShareButtonComponentProps> = ({
  content_type,
  content_id,
  content_ids,
  content_title,
  course_name,
  disabled = false,
  size = 'medium',
  variant = 'icon',
  showLabel = false,
  tooltip = 'Share this content'
}) => {
  const [shareModalOpen, setShareModalOpen] = useState(false);
  const [isSharing, setIsSharing] = useState(false);

  const handleShareClick = () => {
    setShareModalOpen(true);
  };

  const handleShareModalClose = () => {
    setShareModalOpen(false);
  };

  const getIconSize = () => {
    switch (size) {
      case 'small': return 'small' as const;
      case 'large': return 'large' as const;
      default: return 'medium' as const;
    }
  };

  const getButtonSize = () => {
    switch (size) {
      case 'small': return 'small' as const;
      case 'large': return 'large' as const;
      default: return 'medium' as const;
    }
  };

  if (variant === 'button') {
    return (
      <>
        <Button
          variant="outlined"
          size={getButtonSize()}
          startIcon={isSharing ? <CircularProgress size={16} /> : <ShareIcon />}
          onClick={handleShareClick}
          disabled={disabled || isSharing}
          sx={{
            minWidth: showLabel ? 'auto' : '40px',
            borderRadius: 2,
            textTransform: 'none'
          }}
        >
          {showLabel && 'Share'}
        </Button>
        
        <ShareModal
          open={shareModalOpen}
          onClose={handleShareModalClose}
          content_type={content_type}
          content_id={content_id}
          content_ids={content_ids}
          content_title={content_title}
          course_name={course_name}
        />
      </>
    );
  }

  return (
    <>
      <Tooltip title={disabled ? 'Cannot share this content' : tooltip}>
        <span>
          <IconButton
            size={getIconSize()}
            onClick={handleShareClick}
            disabled={disabled || isSharing}
            sx={{
              color: 'primary.main',
              '&:hover': {
                backgroundColor: 'primary.light',
                color: 'primary.dark'
              },
              '&.Mui-disabled': {
                color: 'action.disabled'
              }
            }}
          >
            {isSharing ? (
              <CircularProgress size={size === 'small' ? 16 : size === 'large' ? 24 : 20} />
            ) : (
              <ShareIcon fontSize={getIconSize()} />
            )}
          </IconButton>
        </span>
      </Tooltip>
      
      <ShareModal
        open={shareModalOpen}
        onClose={handleShareModalClose}
        content_type={content_type}
        content_id={content_id}
        content_ids={content_ids}
        content_title={content_title}
        course_name={course_name}
      />
    </>
  );
};

export default ShareButton;
