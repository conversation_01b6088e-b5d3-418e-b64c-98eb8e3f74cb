/**
 * Delete button component for removing user's access to shared content
 */

import React, { useState } from 'react';
import {
  IconButton,
  Button,
  Tooltip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Typography,
  CircularProgress
} from '@mui/material';
import {
  Delete as DeleteIcon,
  Warning as WarningIcon
} from '@mui/icons-material';
import { ContentType } from '../../types/contentSharing';
import useContentSharing from '../../hooks/useContentSharing';

interface DeleteSharedContentButtonProps {
  content_type: ContentType;
  content_id: number;
  content_title: string;
  onDeleted?: () => void;
  variant?: 'icon' | 'button';
  size?: 'small' | 'medium' | 'large';
  disabled?: boolean;
}

const DeleteSharedContentButton: React.FC<DeleteSharedContentButtonProps> = ({
  content_type,
  content_id,
  content_title,
  onDeleted,
  variant = 'icon',
  size = 'medium',
  disabled = false
}) => {
  const [confirmDialogOpen, setConfirmDialogOpen] = useState(false);
  const [isDeleting, setIsDeleting] = useState(false);
  const { removeSharedContentAccess } = useContentSharing();

  const handleDeleteClick = () => {
    setConfirmDialogOpen(true);
  };

  const handleConfirmDelete = async () => {
    setIsDeleting(true);
    try {
      await removeSharedContentAccess(content_type, content_id);
      setConfirmDialogOpen(false);
      onDeleted?.();
    } catch (error) {
      console.error('Failed to delete shared content access:', error);
    } finally {
      setIsDeleting(false);
    }
  };

  const handleCancelDelete = () => {
    setConfirmDialogOpen(false);
  };

  const getIconSize = () => {
    switch (size) {
      case 'small': return 'small' as const;
      case 'large': return 'large' as const;
      default: return 'medium' as const;
    }
  };

  const getButtonSize = () => {
    switch (size) {
      case 'small': return 'small' as const;
      case 'large': return 'large' as const;
      default: return 'medium' as const;
    }
  };

  if (variant === 'button') {
    return (
      <>
        <Button
          variant="outlined"
          color="error"
          size={getButtonSize()}
          startIcon={isDeleting ? <CircularProgress size={16} /> : <DeleteIcon />}
          onClick={handleDeleteClick}
          disabled={disabled || isDeleting}
          sx={{
            borderRadius: 2,
            textTransform: 'none'
          }}
        >
          Remove
        </Button>
        
        <Dialog
          open={confirmDialogOpen}
          onClose={handleCancelDelete}
          maxWidth="sm"
          fullWidth
        >
          <DialogTitle sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <WarningIcon color="warning" />
            Remove Shared Content
          </DialogTitle>
          <DialogContent>
            <Typography>
              Are you sure you want to remove your access to "{content_title}"? 
              This will only remove it from your account - the original content will remain unchanged.
            </Typography>
          </DialogContent>
          <DialogActions>
            <Button onClick={handleCancelDelete} disabled={isDeleting}>
              Cancel
            </Button>
            <Button
              onClick={handleConfirmDelete}
              color="error"
              variant="contained"
              disabled={isDeleting}
              startIcon={isDeleting ? <CircularProgress size={16} /> : <DeleteIcon />}
            >
              {isDeleting ? 'Removing...' : 'Remove'}
            </Button>
          </DialogActions>
        </Dialog>
      </>
    );
  }

  return (
    <>
      <Tooltip title={disabled ? 'Cannot remove this content' : 'Remove shared content'}>
        <span>
          <IconButton
            size={getIconSize()}
            onClick={handleDeleteClick}
            disabled={disabled || isDeleting}
            sx={{
              color: 'error.main',
              '&:hover': {
                backgroundColor: 'error.light',
                color: 'error.dark'
              },
              '&.Mui-disabled': {
                color: 'action.disabled'
              }
            }}
          >
            {isDeleting ? (
              <CircularProgress size={size === 'small' ? 16 : size === 'large' ? 24 : 20} />
            ) : (
              <DeleteIcon fontSize={getIconSize()} />
            )}
          </IconButton>
        </span>
      </Tooltip>
      
      <Dialog
        open={confirmDialogOpen}
        onClose={handleCancelDelete}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <WarningIcon color="warning" />
          Remove Shared Content
        </DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to remove your access to "{content_title}"? 
            This will only remove it from your account - the original content will remain unchanged.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCancelDelete} disabled={isDeleting}>
            Cancel
          </Button>
          <Button
            onClick={handleConfirmDelete}
            color="error"
            variant="contained"
            disabled={isDeleting}
            startIcon={isDeleting ? <CircularProgress size={16} /> : <DeleteIcon />}
          >
            {isDeleting ? 'Removing...' : 'Remove'}
          </Button>
        </DialogActions>
      </Dialog>
    </>
  );
};

export default DeleteSharedContentButton;
