import React, { useState } from 'react';
import {
  Button,
  CircularProgress,
  Snackbar,
  Alert,
  Box
} from '@mui/material';
import { Send as SendIcon, Check as CheckIcon, Schedule as ScheduleIcon, Chat as ChatIcon } from '@mui/icons-material';
import { useMutation, useQueryClient, useQuery } from '@tanstack/react-query';
import { createBookingRequest, BookingRequestCreate, getBookingRequests, BookingRequestStatus } from '../api/bookingRequests';
import { TutorProfile } from '../api/tutors';
import { useNavigate } from 'react-router-dom';
import { getChatRooms } from '../api/chat';

interface SimpleBookingButtonProps {
  tutor: TutorProfile;
  disabled?: boolean;
  variant?: 'contained' | 'outlined' | 'text';
  size?: 'small' | 'medium' | 'large';
  fullWidth?: boolean;
}

const SimpleBookingButton: React.FC<SimpleBookingButtonProps> = ({
  tutor,
  disabled = false,
  variant = 'contained',
  size = 'medium',
  fullWidth = false
}) => {
  const queryClient = useQueryClient();
  const navigate = useNavigate();
  const [isLoading, setIsLoading] = useState(false);
  const [notification, setNotification] = useState<{
    open: boolean;
    message: string;
    severity: 'success' | 'error' | 'warning' | 'info';
  }>({
    open: false,
    message: '',
    severity: 'success'
  });

  // Check for existing booking requests with this tutor (both pending and accepted)
  const { data: existingRequests = [] } = useQuery({
    queryKey: ['booking-requests', 'all'],
    queryFn: () => getBookingRequests(0, 100), // Get all requests, not just pending
    staleTime: 30000, // 30 seconds
  });

  // Check if there's already a pending or accepted request with this tutor
  const hasPendingRequest = existingRequests.some(
    request => request.tutor_id === tutor.user_id && request.status === BookingRequestStatus.PENDING
  );

  const hasAcceptedRequest = existingRequests.some(
    request => request.tutor_id === tutor.user_id && request.status === BookingRequestStatus.ACCEPTED
  );

  const hasExistingRequest = hasPendingRequest || hasAcceptedRequest;

  // Get chat rooms to find the room with this tutor
  const { data: chatRooms = [] } = useQuery({
    queryKey: ['chat-rooms'],
    queryFn: () => getChatRooms(0, 100),
    staleTime: 30000,
    enabled: hasAcceptedRequest, // Only fetch when there's an accepted request
  });

  // Find the chat room with this tutor
  const chatRoom = chatRooms.find(room =>
    room.other_user.id === tutor.user_id
  );

  const createMutation = useMutation({
    mutationFn: createBookingRequest,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['booking-requests'] });
      setIsLoading(false);
      setNotification({
        open: true,
        message: 'Booking request sent successfully!',
        severity: 'success'
      });
    },
    onError: (error: any) => {
      console.error('Failed to send request:', error);
      setIsLoading(false);

      // Handle specific error cases
      let errorMessage = 'Failed to send booking request';
      if (error.message.includes('already have a pending request')) {
        errorMessage = 'You already have a pending request with this tutor. Please wait for their response.';
      } else if (error.message.includes('already have an active session')) {
        errorMessage = 'You already have an active session with this tutor. Please use the chat to communicate.';
      } else if (error.message.includes('Tutor not found')) {
        errorMessage = 'This tutor is no longer available.';
      } else if (error.message) {
        errorMessage = error.message;
      }

      setNotification({
        open: true,
        message: errorMessage,
        severity: 'error'
      });
    }
  });

  const handleDirectRequest = () => {
    if (hasPendingRequest) {
      setNotification({
        open: true,
        message: 'You already have a pending request with this tutor. Please wait for their response.',
        severity: 'warning'
      });
      return;
    }

    if (hasAcceptedRequest) {
      setNotification({
        open: true,
        message: 'You already have an active session with this tutor. Please use the chat to communicate.',
        severity: 'info'
      });
      return;
    }

    setIsLoading(true);
    const requestData: BookingRequestCreate = {
      tutor_id: tutor.user_id
    };

    createMutation.mutate(requestData);
  };

  const handleChatNavigation = () => {
    if (chatRoom) {
      navigate(`/chat/${chatRoom.id}`);
    } else {
      navigate('/chat');
    }
  };

  const handleCloseNotification = () => {
    setNotification(prev => ({ ...prev, open: false }));
  };

  // Determine button state and content
  const getButtonContent = () => {
    if (isLoading) {
      return {
        text: 'Sending...',
        icon: <CircularProgress size={16} />,
        disabled: true
      };
    }

    if (hasPendingRequest) {
      return {
        text: 'Request Pending',
        icon: <ScheduleIcon />,
        disabled: true
      };
    }

    if (hasAcceptedRequest) {
      return {
        text: 'Active Session',
        icon: <CheckIcon />,
        disabled: true
      };
    }

    return {
      text: 'Join Tutorial',
      icon: <SendIcon />,
      disabled: disabled
    };
  };

  const buttonContent = getButtonContent();

  return (
    <>
      {hasAcceptedRequest ? (
        // Show both status and chat button when there's an accepted request
        <Box sx={{ display: 'flex', gap: 1, flexDirection: fullWidth ? 'column' : 'row' }}>
          <Button
            variant="outlined"
            color="success"
            size={size}
            fullWidth={fullWidth}
            disabled
            startIcon={<CheckIcon />}
            sx={{
              textTransform: 'none',
              borderRadius: 2,
              flex: fullWidth ? undefined : 1
            }}
          >
            Active Session
          </Button>
          <Button
            variant="contained"
            color="primary"
            size={size}
            fullWidth={fullWidth}
            startIcon={<ChatIcon />}
            onClick={handleChatNavigation}
            sx={{
              textTransform: 'none',
              borderRadius: 2,
              flex: fullWidth ? undefined : 1
            }}
          >
            Chat
          </Button>
        </Box>
      ) : (
        // Show regular booking button for pending or no request
        <Button
          variant={hasExistingRequest ? 'outlined' : variant}
          color={hasPendingRequest ? 'warning' : 'primary'}
          size={size}
          fullWidth={fullWidth}
          disabled={buttonContent.disabled}
          startIcon={buttonContent.icon}
          onClick={handleDirectRequest}
          sx={{
            textTransform: 'none',
            borderRadius: 2
          }}
        >
          {buttonContent.text}
        </Button>
      )}

      <Snackbar
        open={notification.open}
        autoHideDuration={6000}
        onClose={handleCloseNotification}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert
          onClose={handleCloseNotification}
          severity={notification.severity}
          sx={{ width: '100%' }}
        >
          {notification.message}
        </Alert>
      </Snackbar>
    </>
  );
};

export default SimpleBookingButton;
