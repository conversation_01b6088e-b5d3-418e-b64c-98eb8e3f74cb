import React, { useState } from 'react';
import {
  <PERSON><PERSON>,
  <PERSON>alogTitle,
  <PERSON>alogContent,
  DialogActions,
  Button,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Box,
  Typography,
  Alert,
  Grid,
  Chip,
  Avatar,
  Rating
} from '@mui/material';
import { DateTimePicker } from '@mui/x-date-pickers/DateTimePicker';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { useMutation, useQueryClient } from '@tanstack/react-query';
import { createBookingRequest, BookingRequestCreate } from '../api/bookingRequests';
import { TutorProfile } from '../api/tutors';
import { Course } from '../api/courses';

interface BookingRequestDialogProps {
  open: boolean;
  onClose: () => void;
  tutor: TutorProfile;
  courses: Course[];
}

const BookingRequestDialog: React.FC<BookingRequestDialogProps> = ({
  open,
  onClose,
  tutor,
  courses
}) => {
  const queryClient = useQueryClient();
  const [formData, setFormData] = useState<Partial<BookingRequestCreate>>({
    subject: '',
    message: '',
    preferred_date: '',
    duration_hours: 1,
    session_type: 'online',
    tutor_id: tutor.user_id,
    course_id: undefined
  });
  const [selectedDate, setSelectedDate] = useState<Date | null>(new Date());
  const [errors, setErrors] = useState<Record<string, string>>({});

  const createMutation = useMutation({
    mutationFn: createBookingRequest,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['booking-requests'] });
      onClose();
      // Reset form
      setFormData({
        subject: '',
        message: '',
        preferred_date: '',
        duration_hours: 1,
        session_type: 'online',
        tutor_id: tutor.user_id,
        course_id: undefined
      });
      setSelectedDate(new Date());
      setErrors({});
    },
    onError: (error: any) => {
      setErrors({ submit: error.message });
    }
  });

  const handleSubmit = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.subject?.trim()) {
      newErrors.subject = 'Subject is required';
    }
    if (!formData.message?.trim()) {
      newErrors.message = 'Message is required';
    }
    if (!selectedDate) {
      newErrors.preferred_date = 'Preferred date is required';
    }

    setErrors(newErrors);

    if (Object.keys(newErrors).length === 0 && selectedDate) {
      const requestData: BookingRequestCreate = {
        ...formData as BookingRequestCreate,
        preferred_date: selectedDate.toISOString(),
        tutor_id: tutor.user_id
      };
      createMutation.mutate(requestData);
    }
  };

  const handleInputChange = (field: keyof BookingRequestCreate, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  return (
    <LocalizationProvider dateAdapter={AdapterDateFns}>
      <Dialog open={open} onClose={onClose} maxWidth="md" fullWidth>
        <DialogTitle>
          Request Tutoring Session
        </DialogTitle>
        
        <DialogContent>
          {/* Tutor Info */}
          <Box sx={{ mb: 3, p: 2, bgcolor: 'grey.50', borderRadius: 1 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
              <Avatar
                src={tutor.user?.profile_picture_url}
                sx={{ mr: 2 }}
              >
                {tutor.user?.full_name?.charAt(0) || 'T'}
              </Avatar>
              <Box>
                <Typography variant="h6">
                  {tutor.user?.full_name || 'Tutor'}
                </Typography>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                  <Rating value={tutor.average_rating || 0} readOnly size="small" />
                  <Typography variant="body2" color="text.secondary">
                    ({tutor.total_reviews || 0} reviews)
                  </Typography>
                </Box>
              </Box>
            </Box>
            
            {tutor.specializations && tutor.specializations.length > 0 && (
              <Box>
                <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                  Specializations:
                </Typography>
                <Box>
                  {tutor.specializations.map((course) => (
                    <Chip
                      key={course.id}
                      label={course.name}
                      size="small"
                      sx={{ mr: 0.5, mb: 0.5 }}
                    />
                  ))}
                </Box>
              </Box>
            )}
            
            {tutor.hourly_rate && (
              <Typography variant="body2" color="primary" sx={{ mt: 1, fontWeight: 'bold' }}>
                Rate: ${tutor.hourly_rate}/hour
              </Typography>
            )}
          </Box>

          {errors.submit && (
            <Alert severity="error" sx={{ mb: 2 }}>
              {errors.submit}
            </Alert>
          )}

          <Grid container spacing={2}>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label="Subject/Topic"
                value={formData.subject || ''}
                onChange={(e) => handleInputChange('subject', e.target.value)}
                error={!!errors.subject}
                helperText={errors.subject}
                placeholder="e.g., Calculus help, Python programming, etc."
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <FormControl fullWidth error={!!errors.course_id}>
                <InputLabel>Course (Optional)</InputLabel>
                <Select
                  value={formData.course_id || ''}
                  onChange={(e) => handleInputChange('course_id', e.target.value || undefined)}
                  label="Course (Optional)"
                >
                  <MenuItem value="">No specific course</MenuItem>
                  {courses.map((course) => (
                    <MenuItem key={course.id} value={course.id}>
                      {course.name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} sm={6}>
              <DateTimePicker
                label="Preferred Date & Time"
                value={selectedDate}
                onChange={(newValue) => setSelectedDate(newValue)}
                slotProps={{
                  textField: {
                    fullWidth: true,
                    error: !!errors.preferred_date,
                    helperText: errors.preferred_date
                  }
                }}
                minDate={new Date()}
              />
            </Grid>

            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>Duration</InputLabel>
                <Select
                  value={formData.duration_hours || 1}
                  onChange={(e) => handleInputChange('duration_hours', e.target.value)}
                  label="Duration"
                >
                  <MenuItem value={1}>1 hour</MenuItem>
                  <MenuItem value={2}>2 hours</MenuItem>
                  <MenuItem value={3}>3 hours</MenuItem>
                  <MenuItem value={4}>4 hours</MenuItem>
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} sm={6}>
              <FormControl fullWidth>
                <InputLabel>Session Type</InputLabel>
                <Select
                  value={formData.session_type || 'online'}
                  onChange={(e) => handleInputChange('session_type', e.target.value)}
                  label="Session Type"
                >
                  <MenuItem value="online">Online</MenuItem>
                  <MenuItem value="in_person">In Person</MenuItem>
                  <MenuItem value="both">Either</MenuItem>
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12}>
              <TextField
                fullWidth
                multiline
                rows={4}
                label="Message to Tutor"
                value={formData.message || ''}
                onChange={(e) => handleInputChange('message', e.target.value)}
                error={!!errors.message}
                helperText={errors.message || 'Describe what you need help with, your current level, and any specific requirements'}
                placeholder="Hi! I'm struggling with calculus derivatives and would love some help understanding the concepts better. I'm a sophomore taking Calc I and need help preparing for my upcoming exam..."
              />
            </Grid>
          </Grid>
        </DialogContent>

        <DialogActions>
          <Button onClick={onClose} disabled={createMutation.isPending}>
            Cancel
          </Button>
          <Button
            onClick={handleSubmit}
            variant="contained"
            disabled={createMutation.isPending}
          >
            {createMutation.isPending ? 'Sending...' : 'Send Request'}
          </Button>
        </DialogActions>
      </Dialog>
    </LocalizationProvider>
  );
};

export default BookingRequestDialog;
