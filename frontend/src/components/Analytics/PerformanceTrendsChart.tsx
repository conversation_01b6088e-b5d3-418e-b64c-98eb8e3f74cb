import React, { useState } from 'react';
import {
  <PERSON>,
  <PERSON><PERSON><PERSON>nt,
  <PERSON><PERSON><PERSON>er,
  <PERSON>po<PERSON>,
  Box,
  ToggleButton,
  ToggleButtonGroup,
  useTheme,
  useMediaQuery,
  Chip,
  Avatar
} from '@mui/material';
import {
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  Area,
  AreaChart,
  ReferenceLine
} from 'recharts';
import {
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  Timeline as TimelineIcon
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import { PerformanceTrendPoint, PerformanceInsights } from '../../api/studentProgress';

interface PerformanceTrendsChartProps {
  trendData: PerformanceTrendPoint[];
  insights: PerformanceInsights;
  isLoading?: boolean;
}

const PerformanceTrendsChart: React.FC<PerformanceTrendsChartProps> = ({
  trendData,
  insights,
  isLoading = false
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const [chartType, setChartType] = useState<'line' | 'area'>('area');

  // Format data for chart
  const chartData = trendData.map(point => ({
    ...point,
    date: new Date(point.date).toLocaleDateString('en-US', { 
      month: 'short', 
      day: 'numeric' 
    })
  }));

  // Calculate trend direction
  const getTrendDirection = () => {
    if (insights.improvement_rate > 2) return 'up';
    if (insights.improvement_rate < -2) return 'down';
    return 'stable';
  };

  const trendDirection = getTrendDirection();
  const trendColor = trendDirection === 'up' ? theme.palette.success.main :
                    trendDirection === 'down' ? theme.palette.error.main :
                    theme.palette.info.main;

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <Box
          sx={{
            bgcolor: theme.palette.background.paper,
            p: 2,
            borderRadius: 2,
            border: `1px solid ${theme.palette.divider}`,
            boxShadow: theme.shadows[4]
          }}
        >
          <Typography variant="subtitle2" fontWeight="bold" gutterBottom>
            {label}
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Accuracy: {data.accuracy.toFixed(1)}%
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Questions: {data.total_questions}
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Correct: {data.correct_answers}
          </Typography>
        </Box>
      );
    }
    return null;
  };

  return (
    <Card
      component={motion.div}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: 0.2 }}
      sx={{
        borderRadius: 3,
        border: `1px solid ${theme.palette.divider}`,
        background: theme.palette.mode === 'light' 
          ? 'linear-gradient(135deg, rgba(255,255,255,0.9) 0%, rgba(248,250,252,0.9) 100%)'
          : 'linear-gradient(135deg, rgba(30,30,30,0.9) 0%, rgba(20,20,20,0.9) 100%)',
        backdropFilter: 'blur(10px)',
        boxShadow: theme.shadows[4]
      }}
    >
      <CardHeader
        avatar={
          <Avatar sx={{ bgcolor: trendColor }}>
            <TimelineIcon />
          </Avatar>
        }
        title="Performance Trends"
        subheader={`${insights.days_analyzed} days of activity`}
        action={
          <ToggleButtonGroup
            value={chartType}
            exclusive
            onChange={(_, newType) => newType && setChartType(newType)}
            size="small"
          >
            <ToggleButton value="line">Line</ToggleButton>
            <ToggleButton value="area">Area</ToggleButton>
          </ToggleButtonGroup>
        }
      />
      
      <CardContent sx={{ pt: 0 }}>
        {/* Trend insights */}
        <Box sx={{ mb: 3, display: 'flex', flexWrap: 'wrap', gap: 1 }}>
          <Chip
            icon={trendDirection === 'up' ? <TrendingUpIcon /> : 
                  trendDirection === 'down' ? <TrendingDownIcon /> : 
                  <TimelineIcon />}
            label={`${insights.improvement_rate > 0 ? '+' : ''}${insights.improvement_rate.toFixed(1)}% trend`}
            color={trendDirection === 'up' ? 'success' : 
                   trendDirection === 'down' ? 'error' : 'info'}
            variant="outlined"
          />
          <Chip
            label={`${insights.consistency_score.toFixed(0)}% consistency`}
            variant="outlined"
          />
          <Chip
            label={`${insights.avg_daily_questions.toFixed(0)} avg daily questions`}
            variant="outlined"
          />
        </Box>

        {/* Chart */}
        <Box sx={{ height: isMobile ? 250 : 300, mt: 2 }}>
          {chartData.length > 0 ? (
            <ResponsiveContainer width="100%" height="100%">
              {chartType === 'area' ? (
                <AreaChart data={chartData} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
                  <defs>
                    <linearGradient id="accuracyGradient" x1="0" y1="0" x2="0" y2="1">
                      <stop offset="5%" stopColor={theme.palette.primary.main} stopOpacity={0.3}/>
                      <stop offset="95%" stopColor={theme.palette.primary.main} stopOpacity={0.05}/>
                    </linearGradient>
                  </defs>
                  <CartesianGrid strokeDasharray="3 3" stroke={theme.palette.divider} />
                  <XAxis 
                    dataKey="date" 
                    stroke={theme.palette.text.secondary}
                    fontSize={12}
                  />
                  <YAxis 
                    domain={[0, 100]}
                    stroke={theme.palette.text.secondary}
                    fontSize={12}
                  />
                  <Tooltip content={<CustomTooltip />} />
                  <ReferenceLine y={70} stroke={theme.palette.warning.main} strokeDasharray="5 5" />
                  <Area
                    type="monotone"
                    dataKey="accuracy"
                    stroke={theme.palette.primary.main}
                    strokeWidth={3}
                    fill="url(#accuracyGradient)"
                    dot={{ fill: theme.palette.primary.main, strokeWidth: 2, r: 4 }}
                    activeDot={{ r: 6, stroke: theme.palette.primary.main, strokeWidth: 2 }}
                  />
                </AreaChart>
              ) : (
                <LineChart data={chartData} margin={{ top: 5, right: 30, left: 20, bottom: 5 }}>
                  <CartesianGrid strokeDasharray="3 3" stroke={theme.palette.divider} />
                  <XAxis 
                    dataKey="date" 
                    stroke={theme.palette.text.secondary}
                    fontSize={12}
                  />
                  <YAxis 
                    domain={[0, 100]}
                    stroke={theme.palette.text.secondary}
                    fontSize={12}
                  />
                  <Tooltip content={<CustomTooltip />} />
                  <ReferenceLine y={70} stroke={theme.palette.warning.main} strokeDasharray="5 5" />
                  <Line
                    type="monotone"
                    dataKey="accuracy"
                    stroke={theme.palette.primary.main}
                    strokeWidth={3}
                    dot={{ fill: theme.palette.primary.main, strokeWidth: 2, r: 4 }}
                    activeDot={{ r: 6, stroke: theme.palette.primary.main, strokeWidth: 2 }}
                  />
                </LineChart>
              )}
            </ResponsiveContainer>
          ) : (
            <Box
              sx={{
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center',
                justifyContent: 'center',
                height: '100%',
                color: 'text.secondary'
              }}
            >
              <TimelineIcon sx={{ fontSize: 48, mb: 2, opacity: 0.5 }} />
              <Typography variant="h6" gutterBottom>
                No trend data available
              </Typography>
              <Typography variant="body2" textAlign="center">
                Start practicing to see your performance trends over time
              </Typography>
            </Box>
          )}
        </Box>

        {/* Additional insights */}
        {chartData.length > 0 && (
          <Box sx={{ mt: 3, p: 2, bgcolor: theme.palette.mode === 'light' ? 'rgba(0,0,0,0.02)' : 'rgba(255,255,255,0.02)', borderRadius: 2 }}>
            <Typography variant="subtitle2" fontWeight="medium" gutterBottom>
              Activity Summary
            </Typography>
            <Typography variant="body2" color="text.secondary">
              You've been active for {insights.active_days} days out of the last {insights.days_analyzed} days, 
              averaging {insights.avg_daily_questions.toFixed(1)} questions per day.
              {insights.consistency_score > 80 && " Great consistency!"}
              {insights.improvement_rate > 5 && " You're showing excellent improvement!"}
            </Typography>
          </Box>
        )}
      </CardContent>
    </Card>
  );
};

export default PerformanceTrendsChart;
