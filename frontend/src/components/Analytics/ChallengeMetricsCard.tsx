import React from 'react';
import {
  <PERSON>,
  Card<PERSON>ontent,
  CardHeader,
  Typography,
  Box,
  LinearProgress,
  Chip,
  Avatar,
  Grid,
  Button,
  useTheme,
  useMediaQuery,
  Alert
} from '@mui/material';
import {
  EmojiEvents as TrophyIcon,
  LocalFireDepartment as FireIcon,
  Flag as FlagIcon,
  TrendingUp as TrendingUpIcon,
  PlayArrow as PlayIcon,
  School as SchoolIcon
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import { useNavigate } from 'react-router-dom';
import { ChallengeMetrics } from '../../api/studentProgress';

interface ChallengeMetricsCardProps {
  challengeMetrics: ChallengeMetrics;
  onStartPractice?: () => void;
  isLoading?: boolean;
}

const ChallengeMetricsCard: React.FC<ChallengeMetricsCardProps> = ({
  challengeMetrics,
  onStartPractice,
  isLoading = false
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const navigate = useNavigate();

  const { streak_potential, next_milestone, improvement_areas } = challengeMetrics;

  const getStreakColor = (streak: number) => {
    if (streak >= 7) return theme.palette.success.main;
    if (streak >= 3) return theme.palette.warning.main;
    return theme.palette.info.main;
  };

  const getMilestoneProgress = () => {
    if (next_milestone.progress !== undefined) {
      return Math.min(next_milestone.progress, 100);
    }
    return 0;
  };

  const handlePracticeClick = () => {
    if (onStartPractice) {
      onStartPractice();
    } else {
      navigate('/mcq');
    }
  };

  return (
    <Card
      component={motion.div}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: 0.4 }}
      sx={{
        borderRadius: 3,
        border: `1px solid ${theme.palette.divider}`,
        background: theme.palette.mode === 'light' 
          ? 'linear-gradient(135deg, rgba(255,255,255,0.9) 0%, rgba(248,250,252,0.9) 100%)'
          : 'linear-gradient(135deg, rgba(30,30,30,0.9) 0%, rgba(20,20,20,0.9) 100%)',
        backdropFilter: 'blur(10px)',
        boxShadow: theme.shadows[4]
      }}
    >
      <CardHeader
        avatar={
          <Avatar sx={{ bgcolor: theme.palette.warning.main }}>
            <TrophyIcon />
          </Avatar>
        }
        title="Challenges & Goals"
        subheader="Stay motivated and track your progress"
      />
      
      <CardContent sx={{ pt: 0 }}>
        <Grid container spacing={3}>
          {/* Streak Section */}
          <Grid item xs={12} md={6}>
            <Box
              sx={{
                p: 2,
                borderRadius: 2,
                background: theme.palette.mode === 'light' 
                  ? 'rgba(255,255,255,0.7)' 
                  : 'rgba(40,40,40,0.7)',
                border: `1px solid ${theme.palette.divider}`
              }}
            >
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Avatar
                  sx={{
                    bgcolor: getStreakColor(streak_potential.current_streak),
                    width: 32,
                    height: 32,
                    mr: 1
                  }}
                >
                  <FireIcon fontSize="small" />
                </Avatar>
                <Typography variant="h6" fontWeight="bold">
                  Streak Tracker
                </Typography>
              </Box>
              
              <Box sx={{ mb: 2 }}>
                <Typography variant="h4" fontWeight="bold" color={getStreakColor(streak_potential.current_streak)}>
                  {streak_potential.current_streak}
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Current streak (days)
                </Typography>
              </Box>

              <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                <Typography variant="caption" color="text.secondary">
                  Personal Best
                </Typography>
                <Typography variant="caption" fontWeight="medium">
                  {streak_potential.best_streak} days
                </Typography>
              </Box>

              <LinearProgress
                variant="determinate"
                value={streak_potential.current_streak > 0 ? 
                       (streak_potential.current_streak / Math.max(streak_potential.best_streak, 7)) * 100 : 0}
                sx={{
                  height: 8,
                  borderRadius: 4,
                  bgcolor: theme.palette.mode === 'light' ? 'rgba(0,0,0,0.05)' : 'rgba(255,255,255,0.05)',
                  '& .MuiLinearProgress-bar': {
                    borderRadius: 4,
                    bgcolor: getStreakColor(streak_potential.current_streak)
                  }
                }}
              />

              {streak_potential.current_streak === 0 && (
                <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
                  Start practicing to begin your streak!
                </Typography>
              )}
            </Box>
          </Grid>

          {/* Milestone Section */}
          <Grid item xs={12} md={6}>
            <Box
              sx={{
                p: 2,
                borderRadius: 2,
                background: theme.palette.mode === 'light' 
                  ? 'rgba(255,255,255,0.7)' 
                  : 'rgba(40,40,40,0.7)',
                border: `1px solid ${theme.palette.divider}`
              }}
            >
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Avatar
                  sx={{
                    bgcolor: theme.palette.primary.main,
                    width: 32,
                    height: 32,
                    mr: 1
                  }}
                >
                  <FlagIcon fontSize="small" />
                </Avatar>
                <Typography variant="h6" fontWeight="bold">
                  Next Milestone
                </Typography>
              </Box>
              
              <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                {next_milestone.description}
              </Typography>

              {next_milestone.progress !== undefined && (
                <>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                    <Typography variant="caption" color="text.secondary">
                      Progress
                    </Typography>
                    <Typography variant="caption" fontWeight="medium">
                      {getMilestoneProgress().toFixed(0)}%
                    </Typography>
                  </Box>

                  <LinearProgress
                    variant="determinate"
                    value={getMilestoneProgress()}
                    sx={{
                      height: 8,
                      borderRadius: 4,
                      bgcolor: theme.palette.mode === 'light' ? 'rgba(0,0,0,0.05)' : 'rgba(255,255,255,0.05)',
                      '& .MuiLinearProgress-bar': {
                        borderRadius: 4,
                        bgcolor: theme.palette.primary.main
                      }
                    }}
                  />

                  {next_milestone.current !== undefined && next_milestone.target !== undefined && (
                    <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
                      {next_milestone.current} / {next_milestone.target}
                    </Typography>
                  )}
                </>
              )}
            </Box>
          </Grid>

          {/* Improvement Areas */}
          {improvement_areas.length > 0 && (
            <Grid item xs={12}>
              <Box
                sx={{
                  p: 2,
                  borderRadius: 2,
                  background: theme.palette.mode === 'light' 
                    ? 'rgba(255,255,255,0.7)' 
                    : 'rgba(40,40,40,0.7)',
                  border: `1px solid ${theme.palette.divider}`
                }}
              >
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <Avatar
                    sx={{
                      bgcolor: theme.palette.info.main,
                      width: 32,
                      height: 32,
                      mr: 1
                    }}
                  >
                    <TrendingUpIcon fontSize="small" />
                  </Avatar>
                  <Typography variant="h6" fontWeight="bold">
                    Areas for Improvement
                  </Typography>
                </Box>
                
                <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                  {improvement_areas.map((area, index) => (
                    <Alert
                      key={index}
                      severity="info"
                      variant="outlined"
                      sx={{
                        '& .MuiAlert-message': {
                          fontSize: '0.875rem'
                        }
                      }}
                    >
                      {area}
                    </Alert>
                  ))}
                </Box>
              </Box>
            </Grid>
          )}

          {/* Action Buttons */}
          <Grid item xs={12}>
            <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap' }}>
              <Button
                variant="contained"
                startIcon={<PlayIcon />}
                onClick={handlePracticeClick}
                sx={{
                  borderRadius: 2,
                  px: 3,
                  py: 1.5,
                  background: `linear-gradient(45deg, ${theme.palette.primary.main}, ${theme.palette.primary.dark})`,
                  '&:hover': {
                    background: `linear-gradient(45deg, ${theme.palette.primary.dark}, ${theme.palette.primary.main})`
                  }
                }}
              >
                Start Practice
              </Button>
              
              <Button
                variant="outlined"
                startIcon={<SchoolIcon />}
                onClick={() => navigate('/mcq/exam-history')}
                sx={{
                  borderRadius: 2,
                  px: 3,
                  py: 1.5
                }}
              >
                View History
              </Button>
            </Box>
          </Grid>
        </Grid>

        {/* Motivational message */}
        <Box sx={{ mt: 3, p: 2, bgcolor: theme.palette.mode === 'light' ? 'rgba(0,0,0,0.02)' : 'rgba(255,255,255,0.02)', borderRadius: 2 }}>
          <Typography variant="body2" color="text.secondary" textAlign="center">
            {streak_potential.current_streak >= 7 ? 
              "🔥 You're on fire! Keep up the amazing streak!" :
              streak_potential.current_streak >= 3 ?
              "💪 Great momentum! You're building a solid habit!" :
              "🚀 Ready to challenge yourself? Start your learning journey today!"
            }
          </Typography>
        </Box>
      </CardContent>
    </Card>
  );
};

export default ChallengeMetricsCard;
