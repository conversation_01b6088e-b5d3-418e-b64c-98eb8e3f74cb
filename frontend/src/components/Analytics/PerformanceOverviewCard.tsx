import React from 'react';
import {
  Card,
  CardContent,
  Typography,
  Grid,
  Box,
  Chip,
  useTheme,
  useMediaQuery,
  LinearProgress,
  Avatar
} from '@mui/material';
import {
  TrendingUp as TrendingUpIcon,
  TrendingDown as TrendingDownIcon,
  School as SchoolIcon,
  Quiz as QuizIcon,
  CheckCircle as CheckIcon,
  Cancel as CancelIcon,
  EmojiEvents as TrophyIcon
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import { PerformanceOverview } from '../../api/studentProgress';

interface PerformanceOverviewCardProps {
  overview: PerformanceOverview;
  isLoading?: boolean;
}

const PerformanceOverviewCard: React.FC<PerformanceOverviewCardProps> = ({
  overview,
  isLoading = false
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  const practiceAccuracy = overview.practice_accuracy || 0;
  const examAccuracy = overview.avg_exam_score || 0;

  // Calculate performance level
  const getPerformanceLevel = (accuracy: number) => {
    if (accuracy >= 90) return { level: 'Excellent', color: theme.palette.success.main };
    if (accuracy >= 80) return { level: 'Good', color: theme.palette.info.main };
    if (accuracy >= 70) return { level: 'Fair', color: theme.palette.warning.main };
    return { level: 'Needs Work', color: theme.palette.error.main };
  };

  const practiceLevel = getPerformanceLevel(practiceAccuracy);
  const examLevel = getPerformanceLevel(examAccuracy);

  const stats = [
    {
      title: 'Practice Questions',
      value: overview.total_practice_questions,
      subtitle: `${overview.correct_practice} correct`,
      icon: <QuizIcon />,
      color: theme.palette.primary.main,
      accuracy: practiceAccuracy,
      level: practiceLevel
    },
    {
      title: 'Exams Taken',
      value: overview.total_exams,
      subtitle: `${examAccuracy}% average`,
      icon: <SchoolIcon />,
      color: theme.palette.secondary.main,
      accuracy: examAccuracy,
      level: examLevel
    },
    {
      title: 'Best Score',
      value: `${overview.best_exam_score}%`,
      subtitle: 'Personal best',
      icon: <TrophyIcon />,
      color: theme.palette.warning.main,
      showProgress: false
    },
    {
      title: 'Correct Answers',
      value: overview.correct_practice,
      subtitle: `${overview.incorrect_practice} incorrect`,
      icon: <CheckIcon />,
      color: theme.palette.success.main,
      showProgress: false
    }
  ];

  return (
    <Card
      component={motion.div}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      sx={{
        borderRadius: 3,
        border: `1px solid ${theme.palette.divider}`,
        background: theme.palette.mode === 'light' 
          ? 'linear-gradient(135deg, rgba(255,255,255,0.9) 0%, rgba(248,250,252,0.9) 100%)'
          : 'linear-gradient(135deg, rgba(30,30,30,0.9) 0%, rgba(20,20,20,0.9) 100%)',
        backdropFilter: 'blur(10px)',
        boxShadow: theme.shadows[4]
      }}
    >
      <CardContent sx={{ p: isMobile ? 2 : 3 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
          <Avatar
            sx={{
              bgcolor: theme.palette.primary.main,
              width: 48,
              height: 48,
              mr: 2
            }}
          >
            <TrendingUpIcon />
          </Avatar>
          <Box>
            <Typography variant="h5" fontWeight="bold" gutterBottom>
              Performance Overview
            </Typography>
            <Typography variant="body2" color="text.secondary">
              Your learning progress at a glance
            </Typography>
          </Box>
        </Box>

        <Grid container spacing={isMobile ? 2 : 3}>
          {stats.map((stat, index) => (
            <Grid item xs={12} sm={6} md={3} key={index}>
              <Card
                component={motion.div}
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: index * 0.1, duration: 0.3 }}
                sx={{
                  height: '100%',
                  background: theme.palette.mode === 'light'
                    ? 'rgba(255,255,255,0.7)'
                    : 'rgba(40,40,40,0.7)',
                  border: `1px solid ${theme.palette.divider}`,
                  borderRadius: 2,
                  transition: 'all 0.3s ease',
                  '&:hover': {
                    transform: 'translateY(-4px)',
                    boxShadow: theme.shadows[8]
                  }
                }}
              >
                <CardContent sx={{ p: 2, textAlign: 'center' }}>
                  <Box sx={{ display: 'flex', justifyContent: 'center', mb: 1 }}>
                    <Avatar
                      sx={{
                        bgcolor: stat.color,
                        width: 40,
                        height: 40
                      }}
                    >
                      {stat.icon}
                    </Avatar>
                  </Box>
                  
                  <Typography variant="h4" fontWeight="bold" color={stat.color}>
                    {stat.value}
                  </Typography>
                  
                  <Typography variant="body2" color="text.secondary" gutterBottom>
                    {stat.title}
                  </Typography>
                  
                  <Typography variant="caption" color="text.secondary">
                    {stat.subtitle}
                  </Typography>

                  {stat.accuracy !== undefined && stat.level && (
                    <Box sx={{ mt: 2 }}>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 0.5 }}>
                        <Typography variant="caption" color="text.secondary">
                          Accuracy
                        </Typography>
                        <Typography variant="caption" fontWeight="medium">
                          {stat.accuracy.toFixed(1)}%
                        </Typography>
                      </Box>
                      <LinearProgress
                        variant="determinate"
                        value={stat.accuracy}
                        sx={{
                          height: 6,
                          borderRadius: 3,
                          bgcolor: theme.palette.mode === 'light' 
                            ? 'rgba(0,0,0,0.05)' 
                            : 'rgba(255,255,255,0.05)',
                          '& .MuiLinearProgress-bar': {
                            borderRadius: 3,
                            bgcolor: stat.level.color
                          }
                        }}
                      />
                      <Box sx={{ mt: 1 }}>
                        <Chip
                          label={stat.level.level}
                          size="small"
                          sx={{
                            bgcolor: stat.level.color,
                            color: 'white',
                            fontWeight: 'bold',
                            fontSize: '0.7rem'
                          }}
                        />
                      </Box>
                    </Box>
                  )}
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>

        {/* Quick insights */}
        <Box sx={{ mt: 3, p: 2, bgcolor: theme.palette.mode === 'light' ? 'rgba(0,0,0,0.02)' : 'rgba(255,255,255,0.02)', borderRadius: 2 }}>
          <Typography variant="subtitle2" fontWeight="medium" gutterBottom>
            Quick Insights
          </Typography>
          <Grid container spacing={1}>
            <Grid item xs={12} sm={6}>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                {practiceAccuracy > examAccuracy ? (
                  <TrendingUpIcon color="success" sx={{ mr: 1, fontSize: 16 }} />
                ) : (
                  <TrendingDownIcon color="warning" sx={{ mr: 1, fontSize: 16 }} />
                )}
                <Typography variant="caption" color="text.secondary">
                  Practice vs Exam: {Math.abs(practiceAccuracy - examAccuracy).toFixed(1)}% difference
                </Typography>
              </Box>
            </Grid>
            <Grid item xs={12} sm={6}>
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <CheckIcon color="success" sx={{ mr: 1, fontSize: 16 }} />
                <Typography variant="caption" color="text.secondary">
                  Success rate: {overview.total_practice_questions > 0 ? 
                    ((overview.correct_practice / overview.total_practice_questions) * 100).toFixed(1) : 0}%
                </Typography>
              </Box>
            </Grid>
          </Grid>
        </Box>
      </CardContent>
    </Card>
  );
};

export default PerformanceOverviewCard;
