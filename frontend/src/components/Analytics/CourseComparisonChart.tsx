import React, { useState } from 'react';
import {
  <PERSON>,
  Card<PERSON><PERSON>nt,
  CardHeader,
  Ty<PERSON>graphy,
  Box,
  ToggleButton,
  ToggleButtonGroup,
  useTheme,
  useMediaQuery,
  Avatar,
  Chip,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Paper,
  LinearProgress
} from '@mui/material';
import {
  BarChart,
  Bar,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip,
  ResponsiveContainer,
  RadarChart,
  PolarGrid,
  PolarAngleAxis,
  PolarRadiusAxis,
  Radar,
  Legend
} from 'recharts';
import {
  School as SchoolIcon,
  TrendingUp as TrendingUpIcon,
  Assessment as AssessmentIcon
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import { CoursePerformance } from '../../api/studentProgress';

interface CourseComparisonChartProps {
  courseData: CoursePerformance[];
  isLoading?: boolean;
}

const CourseComparisonChart: React.FC<CourseComparisonChartProps> = ({
  courseData,
  isLoading = false
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const [viewType, setViewType] = useState<'chart' | 'table' | 'radar'>('chart');

  // Prepare data for charts
  const chartData = courseData.map(course => ({
    name: course.course_name.length > 15 ? 
          course.course_name.substring(0, 15) + '...' : 
          course.course_name,
    fullName: course.course_name,
    practiceAccuracy: course.practice_accuracy,
    examScore: course.avg_exam_score,
    totalQuestions: course.total_practice
  }));

  // Prepare data for radar chart
  const radarData = courseData.slice(0, 6).map(course => ({
    course: course.course_name.length > 10 ? 
            course.course_name.substring(0, 10) + '...' : 
            course.course_name,
    practice: course.practice_accuracy,
    exam: course.avg_exam_score,
    activity: Math.min((course.total_practice / 100) * 100, 100) // Scale activity to 0-100
  }));

  const getPerformanceColor = (score: number) => {
    if (score >= 85) return theme.palette.success.main;
    if (score >= 70) return theme.palette.info.main;
    if (score >= 60) return theme.palette.warning.main;
    return theme.palette.error.main;
  };

  const CustomTooltip = ({ active, payload, label }: any) => {
    if (active && payload && payload.length) {
      const data = payload[0].payload;
      return (
        <Box
          sx={{
            bgcolor: theme.palette.background.paper,
            p: 2,
            borderRadius: 2,
            border: `1px solid ${theme.palette.divider}`,
            boxShadow: theme.shadows[4]
          }}
        >
          <Typography variant="subtitle2" fontWeight="bold" gutterBottom>
            {data.fullName}
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Practice: {data.practiceAccuracy.toFixed(1)}%
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Exam: {data.examScore.toFixed(1)}%
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Questions: {data.totalQuestions}
          </Typography>
        </Box>
      );
    }
    return null;
  };

  return (
    <Card
      component={motion.div}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: 0.3 }}
      sx={{
        borderRadius: 3,
        border: `1px solid ${theme.palette.divider}`,
        background: theme.palette.mode === 'light' 
          ? 'linear-gradient(135deg, rgba(255,255,255,0.9) 0%, rgba(248,250,252,0.9) 100%)'
          : 'linear-gradient(135deg, rgba(30,30,30,0.9) 0%, rgba(20,20,20,0.9) 100%)',
        backdropFilter: 'blur(10px)',
        boxShadow: theme.shadows[4]
      }}
    >
      <CardHeader
        avatar={
          <Avatar sx={{ bgcolor: theme.palette.secondary.main }}>
            <SchoolIcon />
          </Avatar>
        }
        title="Course Performance Comparison"
        subheader={`Performance across ${courseData.length} courses`}
        action={
          <ToggleButtonGroup
            value={viewType}
            exclusive
            onChange={(_, newType) => newType && setViewType(newType)}
            size="small"
          >
            <ToggleButton value="chart">Chart</ToggleButton>
            <ToggleButton value="radar">Radar</ToggleButton>
            <ToggleButton value="table">Table</ToggleButton>
          </ToggleButtonGroup>
        }
      />
      
      <CardContent sx={{ pt: 0 }}>
        {courseData.length === 0 ? (
          <Box
            sx={{
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center',
              justifyContent: 'center',
              height: 300,
              color: 'text.secondary'
            }}
          >
            <SchoolIcon sx={{ fontSize: 48, mb: 2, opacity: 0.5 }} />
            <Typography variant="h6" gutterBottom>
              No course data available
            </Typography>
            <Typography variant="body2" textAlign="center">
              Start practicing questions from different courses to see comparisons
            </Typography>
          </Box>
        ) : (
          <>
            {/* Summary chips */}
            <Box sx={{ mb: 3, display: 'flex', flexWrap: 'wrap', gap: 1 }}>
              <Chip
                icon={<TrendingUpIcon />}
                label={`Best: ${Math.max(...courseData.map(c => c.practice_accuracy)).toFixed(1)}%`}
                color="success"
                variant="outlined"
              />
              <Chip
                icon={<AssessmentIcon />}
                label={`${courseData.length} courses`}
                variant="outlined"
              />
              <Chip
                label={`${courseData.reduce((sum, c) => sum + c.total_practice, 0)} total questions`}
                variant="outlined"
              />
            </Box>

            {/* Chart View */}
            {viewType === 'chart' && (
              <Box sx={{ height: isMobile ? 300 : 400, mt: 2 }}>
                <ResponsiveContainer width="100%" height="100%">
                  <BarChart
                    data={chartData}
                    margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
                  >
                    <CartesianGrid strokeDasharray="3 3" stroke={theme.palette.divider} />
                    <XAxis 
                      dataKey="name" 
                      stroke={theme.palette.text.secondary}
                      fontSize={12}
                      angle={isMobile ? -45 : 0}
                      textAnchor={isMobile ? 'end' : 'middle'}
                      height={isMobile ? 80 : 60}
                    />
                    <YAxis 
                      domain={[0, 100]}
                      stroke={theme.palette.text.secondary}
                      fontSize={12}
                    />
                    <Tooltip content={<CustomTooltip />} />
                    <Legend />
                    <Bar
                      dataKey="practiceAccuracy"
                      name="Practice Accuracy"
                      fill={theme.palette.primary.main}
                      radius={[4, 4, 0, 0]}
                    />
                    <Bar
                      dataKey="examScore"
                      name="Exam Score"
                      fill={theme.palette.secondary.main}
                      radius={[4, 4, 0, 0]}
                    />
                  </BarChart>
                </ResponsiveContainer>
              </Box>
            )}

            {/* Radar View */}
            {viewType === 'radar' && (
              <Box sx={{ height: isMobile ? 300 : 400, mt: 2 }}>
                <ResponsiveContainer width="100%" height="100%">
                  <RadarChart data={radarData}>
                    <PolarGrid stroke={theme.palette.divider} />
                    <PolarAngleAxis 
                      dataKey="course" 
                      tick={{ fontSize: 12, fill: theme.palette.text.secondary }}
                    />
                    <PolarRadiusAxis 
                      angle={90} 
                      domain={[0, 100]}
                      tick={{ fontSize: 10, fill: theme.palette.text.secondary }}
                    />
                    <Radar
                      name="Practice"
                      dataKey="practice"
                      stroke={theme.palette.primary.main}
                      fill={theme.palette.primary.main}
                      fillOpacity={0.3}
                      strokeWidth={2}
                    />
                    <Radar
                      name="Exam"
                      dataKey="exam"
                      stroke={theme.palette.secondary.main}
                      fill={theme.palette.secondary.main}
                      fillOpacity={0.3}
                      strokeWidth={2}
                    />
                    <Legend />
                  </RadarChart>
                </ResponsiveContainer>
              </Box>
            )}

            {/* Table View */}
            {viewType === 'table' && (
              <TableContainer component={Paper} variant="outlined" sx={{ mt: 2 }}>
                <Table size={isMobile ? 'small' : 'medium'}>
                  <TableHead>
                    <TableRow>
                      <TableCell>Course</TableCell>
                      <TableCell align="center">Practice</TableCell>
                      <TableCell align="center">Exam</TableCell>
                      <TableCell align="center">Questions</TableCell>
                    </TableRow>
                  </TableHead>
                  <TableBody>
                    {courseData
                      .sort((a, b) => b.practice_accuracy - a.practice_accuracy)
                      .map((course, index) => (
                        <TableRow key={course.course_id}>
                          <TableCell>
                            <Typography variant="body2" fontWeight="medium">
                              {course.course_name}
                            </Typography>
                          </TableCell>
                          <TableCell align="center">
                            <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
                              <Typography variant="body2" sx={{ mr: 1 }}>
                                {course.practice_accuracy.toFixed(1)}%
                              </Typography>
                              <LinearProgress
                                variant="determinate"
                                value={course.practice_accuracy}
                                sx={{
                                  width: 60,
                                  height: 6,
                                  borderRadius: 3,
                                  '& .MuiLinearProgress-bar': {
                                    bgcolor: getPerformanceColor(course.practice_accuracy)
                                  }
                                }}
                              />
                            </Box>
                          </TableCell>
                          <TableCell align="center">
                            <Typography 
                              variant="body2" 
                              color={getPerformanceColor(course.avg_exam_score)}
                              fontWeight="medium"
                            >
                              {course.avg_exam_score.toFixed(1)}%
                            </Typography>
                          </TableCell>
                          <TableCell align="center">
                            <Typography variant="body2">
                              {course.total_practice}
                            </Typography>
                          </TableCell>
                        </TableRow>
                      ))}
                  </TableBody>
                </Table>
              </TableContainer>
            )}
          </>
        )}
      </CardContent>
    </Card>
  );
};

export default CourseComparisonChart;
