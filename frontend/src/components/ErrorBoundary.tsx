import React, { Component, ErrorInfo, ReactNode } from 'react';
import { Box, Typography, Button, Alert } from '@mui/material';
import { forceLogout } from '../utils/authUtils';

interface Props {
  children: ReactNode;
}

interface State {
  hasError: boolean;
  error: Error | null;
}

class ErrorBoundary extends Component<Props, State> {
  public state: State = {
    hasError: false,
    error: null,
  };

  public static getDerivedStateFromError(error: Error): State {
    // Update state so the next render will show the fallback UI
    return { hasError: true, error };
  }

  public componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('ErrorBoundary caught an error:', error, errorInfo);

    // Check if this is an authentication-related error
    if (error.message.includes('session has expired') ||
        error.message.includes('authentication') ||
        error.message.includes('unauthorized') ||
        error.message.includes('401')) {
      console.log('Authentication error detected in ErrorBoundary, forcing logout');
      forceLogout();
      // Don't show error UI for auth errors - just redirect
      return;
    }
  }

  private handleReload = () => {
    window.location.reload();
  };

  private handleGoHome = () => {
    window.location.href = '/';
  };

  public render() {
    if (this.state.hasError) {
      // Check if this is an authentication error - if so, don't show error UI
      const errorMessage = this.state.error?.message || '';
      const isAuthError = errorMessage.includes('session has expired') ||
                         errorMessage.includes('authentication') ||
                         errorMessage.includes('unauthorized') ||
                         errorMessage.includes('401');

      if (isAuthError) {
        // For auth errors, just return null and let the redirect happen
        return null;
      }

      return (
        <Box
          sx={{
            display: 'flex',
            flexDirection: 'column',
            alignItems: 'center',
            justifyContent: 'center',
            minHeight: '100vh',
            padding: 3,
            textAlign: 'center',
          }}
        >
          <Alert severity="error" sx={{ mb: 3, maxWidth: 600 }}>
            <Typography variant="h6" gutterBottom>
              Something went wrong
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {errorMessage || 'An unexpected error occurred'}
            </Typography>
          </Alert>

          <Box sx={{ display: 'flex', gap: 2 }}>
            <Button variant="contained" onClick={this.handleReload}>
              Reload Page
            </Button>
            <Button variant="outlined" onClick={this.handleGoHome}>
              Go Home
            </Button>
          </Box>
        </Box>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;
