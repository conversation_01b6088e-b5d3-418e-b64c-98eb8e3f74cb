import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Grid,
  Chip,
  Button,
  LinearProgress,
  Avatar,
  IconButton,
  Tooltip,
  Skeleton,
  Alert,
  useTheme,
  useMediaQuery,
  Accordion,
  AccordionSummary,
  AccordionDetails,
} from '@mui/material';
import {
  Psychology as BrainIcon,
  TrendingUp as TrendingUpIcon,
  School as SchoolIcon,
  Speed as SpeedIcon,
  Star as StarIcon,
  Visibility as ViewIcon,
  CheckCircle as AcceptIcon,
  ExpandMore as ExpandMoreIcon,
  Refresh as RefreshIcon,
  Analytics as AnalyticsIcon,
  EmojiEvents as TrophyIcon,
  LocalFireDepartment as StreakIcon,
  PlayArrow as PlayArrowIcon,
} from '@mui/icons-material';
import { motion, AnimatePresence } from 'framer-motion';
import { useMLRecommendations } from '../../hooks/useMLRecommendations';
import {
  getConfidenceColor,
  getConfidenceLabel,
  formatPerformancePrediction,
  getDifficultyMatchLabel,
  getInterestScoreLabel,
  formatStudyFrequency,
  formatConsistencyScore,
  getTopicStrengths,
  getTopicWeaknesses,
  calculateOverallProgress,
  getRecommendationPriorityScore,
  shouldShowRecommendation,
  MLRecommendation,
} from '../../api/mlRecommendations';

interface MLRecommendationsDashboardProps {
  maxRecommendations?: number;
  showInsights?: boolean;
  showProgress?: boolean;
  compact?: boolean;
}

const MLRecommendationsDashboard: React.FC<MLRecommendationsDashboardProps> = ({
  maxRecommendations = 5,
  showInsights = true,
  showProgress = true,
  compact = false,
}) => {
  const theme = useTheme();
  const navigate = useNavigate();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const [expandedRecommendation, setExpandedRecommendation] = useState<number | null>(null);

  const {
    recommendations,
    highConfidenceRecommendations,
    unviewedRecommendations,
    learningInsights,
    userFeatures,
    sessionMetrics,
    isLoading,
    hasError,
    markRecommendationViewed,
    acceptRecommendation,
    completeRecommendation,
    refreshAll,
    forceRefreshRecommendations,
    isRefreshingRecommendations,
  } = useMLRecommendations({
    autoRefresh: true,
    enableBehaviorTracking: true,
    sessionTracking: true
  });

  // Filter, deduplicate, and sort recommendations
  const validRecommendations = recommendations
    .filter(shouldShowRecommendation)
    .reduce((unique: MLRecommendation[], current: MLRecommendation) => {
      // Check if we already have a recommendation for this content
      const isDuplicate = unique.some(rec =>
        rec.content_type === current.content_type &&
        rec.content_id === current.content_id
      );

      if (!isDuplicate) {
        unique.push(current);
      } else {
        // If duplicate, keep the one with higher confidence or more recent
        const existingIndex = unique.findIndex(rec =>
          rec.content_type === current.content_type &&
          rec.content_id === current.content_id
        );

        if (existingIndex !== -1) {
          const existing = unique[existingIndex];
          // Keep the one with higher confidence, or if equal, the more recent one
          if (current.confidence_score > existing.confidence_score ||
              (current.confidence_score === existing.confidence_score &&
               new Date(current.created_at) > new Date(existing.created_at))) {
            unique[existingIndex] = current;
          }
        }
      }

      return unique;
    }, [])
    .sort((a, b) => getRecommendationPriorityScore(b) - getRecommendationPriorityScore(a))
    .slice(0, maxRecommendations);

  const overallProgress = calculateOverallProgress(userFeatures);

  const handleRecommendationClick = (recommendation: MLRecommendation) => {
    if (!recommendation.is_viewed) {
      markRecommendationViewed(recommendation.id);
    }

    // Get all recommendations of the same type for the same course to create a filtered practice session
    const sameTypeRecommendations = recommendations.filter(rec =>
      rec.content_type === recommendation.content_type &&
      rec.course_id === recommendation.course_id &&
      shouldShowRecommendation(rec)
    );

    // Extract content IDs for filtering
    const recommendedContentIds = sameTypeRecommendations
      .map(rec => rec.content_id)
      .filter(id => id !== null && id !== undefined);

    // Navigate to the appropriate practice page based on content type
    if (recommendation.content_type === 'question' && recommendation.course_id) {
      // Navigate to MCQ practice with recommended questions only
      const contentIdsParam = recommendedContentIds.length > 0 ?
        `&recommendedIds=${recommendedContentIds.join(',')}` : '';
      navigate(`/mcq/practice/${recommendation.course_id}?mlRecommended=true${contentIdsParam}`);
    } else if (recommendation.content_type === 'flashcard' && recommendation.course_id) {
      // Navigate to flashcard practice with recommended flashcards only
      const contentIdsParam = recommendedContentIds.length > 0 ?
        `&recommendedIds=${recommendedContentIds.join(',')}` : '';
      navigate(`/flash-cards/study?courseId=${recommendation.course_id}&mlRecommended=true${contentIdsParam}`);
    } else if (recommendation.content_type === 'flashcard' && recommendation.content_id) {
      // Navigate to specific flashcard study mode with ML context
      navigate(`/flash-cards/card/${recommendation.content_id}?mlRecommended=true`);
    } else if (recommendation.course_id) {
      // Fallback to course practice for questions
      navigate(`/mcq/practice/${recommendation.course_id}`);
    } else {
      // Final fallback to MCQ dashboard
      navigate('/mcq');
    }
  };

  const handleAcceptRecommendation = (recommendationId: number, event: React.MouseEvent) => {
    event.stopPropagation();
    acceptRecommendation(recommendationId);
  };

  const handleCompleteRecommendation = (recommendationId: number, event: React.MouseEvent) => {
    event.stopPropagation();
    completeRecommendation(recommendationId, 5); // Default 5-star rating
  };

  if (isLoading) {
    return (
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            AI Learning Assistant
          </Typography>
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
            {[1, 2, 3].map((i) => (
              <Skeleton key={i} variant="rectangular" height={100} />
            ))}
          </Box>
        </CardContent>
      </Card>
    );
  }

  if (hasError) {
    // Don't show error for missing user features - just hide the component
    return null;
  }

  // Don't show if no valid recommendations available after filtering and deduplication
  if (!recommendations || recommendations.length === 0 || validRecommendations.length === 0) {
    return null;
  }

  return (
    <Card sx={{
      mb: 3,
      overflow: 'hidden',
      maxWidth: '100%',
      width: '100%'
    }}>
      <CardContent sx={{ p: compact ? 2 : 3 }}>
        <Box sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          mb: 2,
          overflow: 'hidden'
        }}>
          <Typography
            variant="h6"
            sx={{
              display: 'flex',
              alignItems: 'center',
              gap: 1,
              overflow: 'hidden',
              textOverflow: 'ellipsis',
              whiteSpace: 'nowrap',
              flex: 1,
              minWidth: 0
            }}
          >
            <BrainIcon color="primary" />
            Recommended for You
          </Typography>
          <IconButton
            size="small"
            onClick={() => forceRefreshRecommendations('question')}
            disabled={isRefreshingRecommendations}
            title="Force refresh recommendations"
            sx={{ flexShrink: 0 }}
          >
            <RefreshIcon />
          </IconButton>
        </Box>

        {validRecommendations.length > 0 && (
          <Box sx={{
            display: 'flex',
            flexDirection: 'column',
            gap: compact ? 0.5 : 1,
            overflow: 'hidden',
            width: '100%'
          }}>
            <AnimatePresence>
              {validRecommendations.map((recommendation, index) => (
                <motion.div
                  key={recommendation.id}
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: 20 }}
                  transition={{ duration: 0.3, delay: index * 0.1 }}
                >
                  <Card
                    sx={{
                      cursor: 'pointer',
                      transition: 'all 0.2s',
                      border: `2px solid ${
                        expandedRecommendation === recommendation.id
                          ? theme.palette.primary.main
                          : 'transparent'
                      }`,
                      '&:hover': {
                        transform: compact ? 'none' : 'translateY(-2px)',
                        boxShadow: compact ? theme.shadows[2] : theme.shadows[4],
                      },
                      overflow: 'hidden',
                      borderRadius: compact ? 1 : 2,
                      maxWidth: '100%',
                      width: '100%'
                    }}
                    onClick={() => handleRecommendationClick(recommendation)}
                  >
                    <CardContent sx={{ p: compact ? 1.5 : 2, overflow: 'hidden' }}>
                      <Box sx={{
                        display: 'flex',
                        flexDirection: compact ? 'column' : 'row',
                        alignItems: compact ? 'flex-start' : 'center',
                        gap: compact ? 1 : 2,
                        overflow: 'hidden',
                        width: '100%',
                        maxWidth: '100%'
                      }}>
                        {/* Mobile Layout */}
                        {compact ? (
                          <>
                            <Box sx={{
                              display: 'flex',
                              alignItems: 'flex-start',
                              gap: 1.5,
                              width: '100%',
                              mb: 1
                            }}>
                              <Box sx={{
                                width: 32,
                                height: 32,
                                borderRadius: '50%',
                                bgcolor: 'primary.main',
                                display: 'flex',
                                alignItems: 'center',
                                justifyContent: 'center',
                                fontSize: '1rem',
                                flexShrink: 0
                              }}>
                                {recommendation.content_type === 'question' ? '🧠' :
                                 recommendation.content_type === 'flashcard' ? '📚' : '📝'}
                              </Box>

                              <Box sx={{ flex: 1, minWidth: 0 }}>
                                <Typography
                                  variant="body2"
                                  fontWeight="bold"
                                  sx={{
                                    overflow: 'hidden',
                                    textOverflow: 'ellipsis',
                                    display: '-webkit-box',
                                    WebkitLineClamp: 2,
                                    WebkitBoxOrient: 'vertical',
                                    wordBreak: 'break-word',
                                    lineHeight: 1.3
                                  }}
                                >
                                  {(recommendation.topic || 'Practice').length > 25
                                    ? `${(recommendation.topic || 'Practice').substring(0, 25)}...`
                                    : recommendation.topic || 'Practice'
                                  } {recommendation.content_type === 'question' ? 'Questions' :
                                     recommendation.content_type === 'flashcard' ? 'Flashcards' : 'Practice'}
                                </Typography>

                                <Typography
                                  variant="caption"
                                  color="text.secondary"
                                  sx={{
                                    overflow: 'hidden',
                                    textOverflow: 'ellipsis',
                                    display: '-webkit-box',
                                    WebkitLineClamp: 1,
                                    WebkitBoxOrient: 'vertical',
                                    wordBreak: 'break-word',
                                    mt: 0.5
                                  }}
                                >
                                  {(recommendation.explanation || 'Recommended based on your learning progress').length > 50
                                    ? `${(recommendation.explanation || 'Recommended based on your learning progress').substring(0, 50)}...`
                                    : recommendation.explanation || 'Recommended based on your learning progress'
                                  }
                                </Typography>
                              </Box>
                            </Box>

                            {/* Practice button moved to bottom for mobile */}
                            <Box sx={{
                              display: 'flex',
                              justifyContent: 'flex-end',
                              width: '100%',
                              mt: 1
                            }}>
                              <Chip
                                label="Practice"
                                size="small"
                                color="primary"
                                icon={<PlayArrowIcon />}
                                sx={{
                                  fontSize: '0.7rem',
                                  height: 24,
                                  flexShrink: 0
                                }}
                              />
                            </Box>
                          </>
                        ) : (
                          /* Desktop Layout */
                          <>
                            <Box sx={{
                              width: 40,
                              height: 40,
                              borderRadius: '50%',
                              bgcolor: 'primary.main',
                              display: 'flex',
                              alignItems: 'center',
                              justifyContent: 'center',
                              fontSize: '1.2rem',
                              flexShrink: 0
                            }}>
                              {recommendation.content_type === 'question' ? '🧠' :
                               recommendation.content_type === 'flashcard' ? '📚' : '📝'}
                            </Box>

                            <Box sx={{ flex: 1, minWidth: 0, maxWidth: 'calc(100% - 140px)' }}>
                              <Typography
                                variant="subtitle2"
                                fontWeight="bold"
                                sx={{
                                  overflow: 'hidden',
                                  textOverflow: 'ellipsis',
                                  display: '-webkit-box',
                                  WebkitLineClamp: 1,
                                  WebkitBoxOrient: 'vertical',
                                  wordBreak: 'break-word'
                                }}
                              >
                                {(recommendation.topic || 'Practice').length > 30
                                  ? `${(recommendation.topic || 'Practice').substring(0, 30)}...`
                                  : recommendation.topic || 'Practice'
                                } {recommendation.content_type === 'question' ? 'Questions' :
                                   recommendation.content_type === 'flashcard' ? 'Flashcards' : 'Practice'}
                              </Typography>

                              <Typography
                                variant="body2"
                                color="text.secondary"
                                sx={{
                                  overflow: 'hidden',
                                  textOverflow: 'ellipsis',
                                  display: '-webkit-box',
                                  WebkitLineClamp: 2,
                                  WebkitBoxOrient: 'vertical',
                                  wordBreak: 'break-word',
                                  mt: 0.5
                                }}
                              >
                                {(recommendation.explanation || 'Recommended based on your learning progress').length > 80
                                  ? `${(recommendation.explanation || 'Recommended based on your learning progress').substring(0, 80)}...`
                                  : recommendation.explanation || 'Recommended based on your learning progress'
                                }
                              </Typography>
                            </Box>

                            <Box sx={{
                              display: 'flex',
                              alignItems: 'center',
                              gap: 1,
                              flexShrink: 0,
                              minWidth: 'fit-content'
                            }}>
                              <Chip
                                label="Practice"
                                size="small"
                                color="primary"
                                icon={<PlayArrowIcon />}
                                sx={{
                                  fontSize: '0.75rem',
                                  whiteSpace: 'nowrap'
                                }}
                              />
                            </Box>
                          </>
                        )}
                      </Box>
                    </CardContent>
                  </Card>
                </motion.div>
              ))}
            </AnimatePresence>
          </Box>
        )}


      </CardContent>
    </Card>
  );
};

export default MLRecommendationsDashboard;
