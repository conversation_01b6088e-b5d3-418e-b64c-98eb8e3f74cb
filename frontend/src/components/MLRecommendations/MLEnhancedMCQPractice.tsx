import React, { useState, useEffect, useCallback } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  <PERSON>po<PERSON>,
  Button,
  Chip,
  LinearProgress,
  Alert,
  Fade,
  useTheme,
} from '@mui/material';
import {
  Psychology as BrainIcon,
  TrendingUp as TrendingUpIcon,
  CheckCircle as CorrectIcon,
  Cancel as IncorrectIcon,
  Timer as TimerIcon,
} from '@mui/icons-material';
import { motion, AnimatePresence } from 'framer-motion';
import { useMLRecommendations } from '../../hooks/useMLRecommendations';
import {
  getConfidenceColor,
  getConfidenceLabel,
  formatPerformancePrediction,
  MLRecommendation,
} from '../../api/mlRecommendations';

interface Question {
  id: number;
  content: string;
  options: string[];
  correct_answer: string;
  topic?: string;
  difficulty?: string;
  course_id?: number;
}

interface MLEnhancedMCQPracticeProps {
  questions: Question[];
  onQuestionComplete?: (questionId: number, isCorrect: boolean, responseTime: number) => void;
  showMLInsights?: boolean;
}

const MLEnhancedMCQPractice: React.FC<MLEnhancedMCQPracticeProps> = ({
  questions,
  onQuestionComplete,
  showMLInsights = true,
}) => {
  const theme = useTheme();
  const [currentQuestionIndex, setCurrentQuestionIndex] = useState(0);
  const [selectedAnswer, setSelectedAnswer] = useState<string | null>(null);
  const [isAnswered, setIsAnswered] = useState(false);
  const [startTime, setStartTime] = useState<Date>(new Date());
  const [questionStartTime, setQuestionStartTime] = useState<Date>(new Date());
  const [score, setScore] = useState({ correct: 0, total: 0 });
  const [showMLPrediction, setShowMLPrediction] = useState(false);

  const {
    recommendations,
    trackQuestionAttempt,
    sessionMetrics,
    userFeatures,
  } = useMLRecommendations({
    enableBehaviorTracking: true,
    sessionTracking: true,
  });

  const currentQuestion = questions[currentQuestionIndex];
  const isLastQuestion = currentQuestionIndex === questions.length - 1;

  // Find ML recommendation for current question
  const currentQuestionRecommendation = recommendations.find(
    rec => rec.content_type === 'question' && rec.content_id === currentQuestion?.id
  );

  useEffect(() => {
    setQuestionStartTime(new Date());
    setShowMLPrediction(false);
    
    // Show ML prediction after a delay if available
    if (currentQuestionRecommendation && showMLInsights) {
      const timer = setTimeout(() => {
        setShowMLPrediction(true);
      }, 2000);
      return () => clearTimeout(timer);
    }
  }, [currentQuestionIndex, currentQuestionRecommendation, showMLInsights]);

  const handleAnswerSelect = useCallback((answer: string) => {
    if (isAnswered) return;
    setSelectedAnswer(answer);
  }, [isAnswered]);

  const handleSubmitAnswer = useCallback(() => {
    if (!selectedAnswer || isAnswered) return;

    const responseTime = (new Date().getTime() - questionStartTime.getTime()) / 1000;
    const isCorrect = selectedAnswer === currentQuestion.correct_answer;
    
    setIsAnswered(true);
    setScore(prev => ({
      correct: prev.correct + (isCorrect ? 1 : 0),
      total: prev.total + 1,
    }));

    // Track with ML system
    trackQuestionAttempt(
      currentQuestion.id,
      isCorrect,
      responseTime,
      currentQuestion.topic,
      currentQuestion.course_id,
      currentQuestion.difficulty
    );

    // Call parent callback
    onQuestionComplete?.(currentQuestion.id, isCorrect, responseTime);

    // Auto-advance after delay
    setTimeout(() => {
      if (!isLastQuestion) {
        setCurrentQuestionIndex(prev => prev + 1);
        setSelectedAnswer(null);
        setIsAnswered(false);
      }
    }, 2000);
  }, [
    selectedAnswer,
    isAnswered,
    questionStartTime,
    currentQuestion,
    trackQuestionAttempt,
    onQuestionComplete,
    isLastQuestion,
  ]);

  const handleNextQuestion = useCallback(() => {
    if (isLastQuestion) return;
    
    setCurrentQuestionIndex(prev => prev + 1);
    setSelectedAnswer(null);
    setIsAnswered(false);
  }, [isLastQuestion]);

  const getAnswerColor = (option: string) => {
    if (!isAnswered) {
      return selectedAnswer === option ? theme.palette.primary.main : 'transparent';
    }
    
    if (option === currentQuestion.correct_answer) {
      return theme.palette.success.main;
    }
    
    if (option === selectedAnswer && option !== currentQuestion.correct_answer) {
      return theme.palette.error.main;
    }
    
    return 'transparent';
  };

  const getAnswerIcon = (option: string) => {
    if (!isAnswered) return null;
    
    if (option === currentQuestion.correct_answer) {
      return <CorrectIcon sx={{ color: 'white', ml: 1 }} />;
    }
    
    if (option === selectedAnswer && option !== currentQuestion.correct_answer) {
      return <IncorrectIcon sx={{ color: 'white', ml: 1 }} />;
    }
    
    return null;
  };

  if (!currentQuestion) {
    return (
      <Card>
        <CardContent>
          <Typography variant="h6" gutterBottom>
            Practice Complete!
          </Typography>
          <Typography variant="body1">
            Final Score: {score.correct}/{score.total} ({Math.round((score.correct / score.total) * 100)}%)
          </Typography>
          {sessionMetrics && (
            <Box sx={{ mt: 2 }}>
              <Typography variant="body2" color="text.secondary">
                Session Summary: {sessionMetrics.questionsAttempted} questions attempted, 
                {sessionMetrics.correctAnswers} correct
              </Typography>
            </Box>
          )}
        </CardContent>
      </Card>
    );
  }

  return (
    <Box sx={{ maxWidth: 800, mx: 'auto' }}>
      {/* Progress Bar */}
      <Box sx={{ mb: 3 }}>
        <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
          <Typography variant="body2" color="text.secondary">
            Question {currentQuestionIndex + 1} of {questions.length}
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Score: {score.correct}/{score.total}
          </Typography>
        </Box>
        <LinearProgress
          variant="determinate"
          value={((currentQuestionIndex + 1) / questions.length) * 100}
          sx={{ height: 8, borderRadius: 4 }}
        />
      </Box>

      {/* ML Prediction Alert */}
      <AnimatePresence>
        {showMLPrediction && currentQuestionRecommendation && showMLInsights && (
          <motion.div
            initial={{ opacity: 0, y: -20 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -20 }}
            transition={{ duration: 0.3 }}
          >
            <Alert
              severity="info"
              icon={<BrainIcon />}
              sx={{ mb: 2 }}
            >
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                <Box sx={{ flex: 1 }}>
                  <Typography variant="body2" fontWeight="bold">
                    AI Prediction: {formatPerformancePrediction(currentQuestionRecommendation.predicted_performance)}
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    Based on your learning patterns and this question's characteristics
                  </Typography>
                </Box>
                <Chip
                  label={getConfidenceLabel(currentQuestionRecommendation.confidence_score)}
                  size="small"
                  sx={{
                    bgcolor: getConfidenceColor(currentQuestionRecommendation.confidence_score),
                    color: 'white',
                  }}
                />
              </Box>
            </Alert>
          </motion.div>
        )}
      </AnimatePresence>

      {/* Question Card */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Box sx={{ display: 'flex', alignItems: 'flex-start', gap: 2, mb: 3 }}>
            <Box sx={{ flex: 1 }}>
              <Typography variant="h6" gutterBottom>
                {currentQuestion.content}
              </Typography>
              
              {/* Question metadata */}
              <Box sx={{ display: 'flex', gap: 1, mb: 2 }}>
                {currentQuestion.topic && (
                  <Chip label={currentQuestion.topic} size="small" variant="outlined" />
                )}
                {currentQuestion.difficulty && (
                  <Chip 
                    label={currentQuestion.difficulty} 
                    size="small" 
                    color={
                      currentQuestion.difficulty === 'easy' ? 'success' :
                      currentQuestion.difficulty === 'medium' ? 'warning' : 'error'
                    }
                  />
                )}
              </Box>
            </Box>
            
            {/* Timer */}
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <TimerIcon fontSize="small" color="action" />
              <Typography variant="body2" color="text.secondary">
                {Math.floor((new Date().getTime() - questionStartTime.getTime()) / 1000)}s
              </Typography>
            </Box>
          </Box>

          {/* Answer Options */}
          <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
            {currentQuestion.options.map((option, index) => (
              <motion.div
                key={index}
                whileHover={{ scale: isAnswered ? 1 : 1.02 }}
                whileTap={{ scale: isAnswered ? 1 : 0.98 }}
              >
                <Button
                  fullWidth
                  variant="outlined"
                  onClick={() => handleAnswerSelect(option)}
                  disabled={isAnswered}
                  sx={{
                    p: 2,
                    justifyContent: 'flex-start',
                    textAlign: 'left',
                    bgcolor: getAnswerColor(option),
                    color: isAnswered && (option === currentQuestion.correct_answer || option === selectedAnswer) 
                      ? 'white' : 'inherit',
                    border: `2px solid ${getAnswerColor(option)}`,
                    '&:hover': {
                      bgcolor: isAnswered ? getAnswerColor(option) : theme.palette.action.hover,
                    },
                  }}
                >
                  <Box sx={{ display: 'flex', alignItems: 'center', width: '100%' }}>
                    <Typography variant="body1" sx={{ flex: 1 }}>
                      {String.fromCharCode(65 + index)}. {option}
                    </Typography>
                    {getAnswerIcon(option)}
                  </Box>
                </Button>
              </motion.div>
            ))}
          </Box>

          {/* Submit Button */}
          {!isAnswered && (
            <Box sx={{ mt: 3, textAlign: 'center' }}>
              <Button
                variant="contained"
                size="large"
                onClick={handleSubmitAnswer}
                disabled={!selectedAnswer}
              >
                Submit Answer
              </Button>
            </Box>
          )}

          {/* Next Button */}
          {isAnswered && !isLastQuestion && (
            <Box sx={{ mt: 3, textAlign: 'center' }}>
              <Button
                variant="contained"
                size="large"
                onClick={handleNextQuestion}
              >
                Next Question
              </Button>
            </Box>
          )}
        </CardContent>
      </Card>

      {/* ML Insights Panel */}
      {showMLInsights && userFeatures && (
        <Card>
          <CardContent>
            <Typography variant="subtitle2" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <TrendingUpIcon fontSize="small" />
              Your Performance Insights
            </Typography>
            <Box sx={{ display: 'flex', gap: 3 }}>
              <Box>
                <Typography variant="caption" color="text.secondary">
                  Overall Accuracy
                </Typography>
                <Typography variant="h6" color="primary">
                  {Math.round(userFeatures.accuracy_rate * 100)}%
                </Typography>
              </Box>
              <Box>
                <Typography variant="caption" color="text.secondary">
                  Avg Response Time
                </Typography>
                <Typography variant="h6" color="primary">
                  {Math.round(userFeatures.avg_response_time)}s
                </Typography>
              </Box>
              <Box>
                <Typography variant="caption" color="text.secondary">
                  Session Progress
                </Typography>
                <Typography variant="h6" color="primary">
                  {score.total > 0 ? Math.round((score.correct / score.total) * 100) : 0}%
                </Typography>
              </Box>
            </Box>
          </CardContent>
        </Card>
      )}
    </Box>
  );
};

export default MLEnhancedMCQPractice;
