import React, { useState, useEffect, useRef, useCallback } from 'react';
import {
  Box,
  Paper,
  Typography,
  TextField,
  IconButton,
  Avatar,
  Chip,
  Divider,
  InputAdornment,
  useTheme,
  useMediaQuery,
  Stack,
  CircularProgress,
  Fab,
  ButtonBase,
  Tooltip
} from '@mui/material';
import {
  Send as SendIcon,
  Circle as CircleIcon,
  ArrowBack as ArrowBackIcon
} from '@mui/icons-material';
import { format, isToday, isYesterday } from 'date-fns';
import { useWebSocketChat, ChatMessage, ChatRoom } from '../hooks/useWebSocketChat';
import { useAuth } from '../contexts/AuthContext';
import { getChatMessages } from '../api/chat';
import ChatProfileDialog from './ChatProfileDialog';

interface ChatComponentProps {
  chatRoom: ChatRoom;
  onClose?: () => void;
}

const ChatComponent: React.FC<ChatComponentProps> = ({ chatRoom, onClose }) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const { user } = useAuth();
  const {
    isConnected,
    messages,
    onlineUsers,
    sendMessage,
    joinChatRoom,
    markMessagesAsRead,
    setMessagesForRoom
  } = useWebSocketChat();

  const [newMessage, setNewMessage] = useState('');
  const [isLoadingMessages, setIsLoadingMessages] = useState(false);
  const [forceRender, setForceRender] = useState(0);
  const [showProfileDialog, setShowProfileDialog] = useState(false);
  const [profileUserId, setProfileUserId] = useState<number | null>(null);
  const [profileUserName, setProfileUserName] = useState<string>('');
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const inputRef = useRef<HTMLInputElement>(null);
  const messagesContainerRef = useRef<HTMLDivElement>(null);
  const scrollPositionRef = useRef<number>(0);
  const shouldScrollToBottomRef = useRef<boolean>(true);

  // Force re-render function to ensure UI updates
  const triggerRerender = useCallback(() => {
    setForceRender(prev => prev + 1);
  }, []);

  // Get messages for this chat room and ensure they're sorted by creation time
  const chatMessages = (messages[chatRoom.id] || []).sort((a, b) =>
    new Date(a.created_at).getTime() - new Date(b.created_at).getTime()
  );

  // Debug logging
  console.log('🔍 ChatComponent Debug:');
  console.log('  chatRoom.id:', chatRoom.id);
  console.log('  messages object:', messages);
  console.log('  chatMessages for this room:', chatMessages);
  console.log('  chatMessages.length:', chatMessages.length);
  console.log('  isConnected:', isConnected);
  console.log('  user.id:', user?.id);
  console.log('  forceRender:', forceRender);

  // Force re-render when messages change to ensure UI updates
  useEffect(() => {
    console.log('🔥 Messages changed, triggering re-render');
    triggerRerender();
  }, [messages, triggerRerender]);

  // Additional effect to handle real-time message updates specifically for current room
  useEffect(() => {
    const currentRoomMessages = messages[chatRoom.id];
    if (currentRoomMessages && currentRoomMessages.length > 0) {
      console.log('🔥 Real-time message update detected for room', chatRoom.id);
      // Force immediate re-render
      setForceRender(prev => prev + 1);
    }
  }, [messages[chatRoom.id], chatRoom.id]);

  // Load existing messages when component mounts or chat room changes
  useEffect(() => {
    const loadMessages = async () => {
      if (chatRoom.id) {
        setIsLoadingMessages(true);
        try {
          console.log('Loading messages for chat room:', chatRoom.id);
          const existingMessages = await getChatMessages(chatRoom.id, 0, 100);
          console.log('Loaded messages:', existingMessages);

          // Convert API messages to WebSocket message format
          const formattedMessages: ChatMessage[] = existingMessages.map(msg => ({
            id: msg.id,
            content: msg.content,
            message_type: msg.message_type,
            sender_id: msg.sender_id,
            chat_room_id: msg.chat_room_id,
            is_read: msg.is_read,
            delivery_status: msg.delivery_status,
            created_at: msg.created_at,
            delivered_at: msg.delivered_at,
            read_at: msg.read_at,
            sender_name: msg.sender?.name || 'Unknown',
            sender_profile_picture_url: msg.sender?.profile_picture_url
          }));

          setMessagesForRoom(chatRoom.id, formattedMessages);
        } catch (error) {
          console.error('Failed to load messages:', error);
        } finally {
          setIsLoadingMessages(false);
        }
      }
    };

    loadMessages();
  }, [chatRoom.id]); // Only depend on chatRoom.id to prevent unnecessary reloads

  // Join chat room when WebSocket is connected
  useEffect(() => {
    if (isConnected && chatRoom.id) {
      joinChatRoom(chatRoom.id);
      markMessagesAsRead(chatRoom.id);
    }
  }, [isConnected, chatRoom.id]); // Removed function dependencies to prevent loops



  // Save scroll position before re-renders
  const saveScrollPosition = () => {
    if (messagesContainerRef.current) {
      const container = messagesContainerRef.current;
      const isAtBottom = container.scrollHeight - container.scrollTop <= container.clientHeight + 50;
      shouldScrollToBottomRef.current = isAtBottom;
      scrollPositionRef.current = container.scrollTop;
    }
  };

  // Handle scroll events to track user position
  const handleScroll = () => {
    saveScrollPosition();
  };

  // Handle new messages for current chat room
  useEffect(() => {
    console.log('🔥 Chat messages updated for room', chatRoom.id, '- count:', chatMessages.length);

    // Force component re-render to ensure messages display
    triggerRerender();

    // Auto-scroll to bottom when new messages arrive (only if user was already at bottom)
    if (shouldScrollToBottomRef.current && messagesEndRef.current) {
      setTimeout(() => {
        messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
      }, 100);
    }
  }, [chatMessages.length, chatRoom.id, triggerRerender]);

  // Save scroll position when messages change
  useEffect(() => {
    saveScrollPosition();
  }, [chatMessages]);

  // Mark messages as read when chat is opened
  useEffect(() => {
    if (chatRoom.id && isConnected) {
      markMessagesAsRead(chatRoom.id);
    }
  }, [chatRoom.id, isConnected]); // Removed function dependency

  // Handle viewport changes and maintain input functionality
  useEffect(() => {
    const handleResize = () => {
      // Ensure input remains focusable after viewport changes
      if (inputRef.current && document.activeElement === inputRef.current) {
        setTimeout(() => {
          inputRef.current?.focus();
        }, 100);
      }
    };

    const handleOrientationChange = () => {
      // Handle orientation changes on mobile
      setTimeout(() => {
        if (inputRef.current) {
          inputRef.current.blur();
          setTimeout(() => {
            inputRef.current?.focus();
          }, 300);
        }
      }, 500);
    };

    const handleVisibilityChange = () => {
      // Re-establish focus when page becomes visible
      if (!document.hidden && inputRef.current) {
        setTimeout(() => {
          inputRef.current?.focus();
        }, 200);
      }
    };

    window.addEventListener('resize', handleResize);
    window.addEventListener('orientationchange', handleOrientationChange);
    document.addEventListener('visibilitychange', handleVisibilityChange);

    return () => {
      window.removeEventListener('resize', handleResize);
      window.removeEventListener('orientationchange', handleOrientationChange);
      document.removeEventListener('visibilitychange', handleVisibilityChange);
    };
  }, []);

  const handleSendMessage = async () => {
    if (newMessage.trim() && isConnected) {
      console.log('🔥 CHAT COMPONENT: Sending message:', newMessage.trim());
      const messageContent = newMessage.trim();

      // Clear input immediately for better UX
      setNewMessage('');

      const success = sendMessage(chatRoom.id, messageContent);
      if (success) {
        console.log('🔥 CHAT COMPONENT: Message sent successfully');

        // Keep focus on input for continuous typing
        setTimeout(() => {
          if (inputRef.current) {
            inputRef.current.focus();
          }
        }, 50);
      } else {
        console.log('🔥 CHAT COMPONENT: Failed to send message, restoring input');
        // Restore message if sending failed
        setNewMessage(messageContent);
      }
    }
  };

  const handleKeyPress = (event: React.KeyboardEvent) => {
    if (event.key === 'Enter' && !event.shiftKey) {
      event.preventDefault();
      handleSendMessage();
    }
  };

  const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setNewMessage(event.target.value);
  };

  const formatMessageTime = (dateString: string) => {
    const date = new Date(dateString);
    
    if (isToday(date)) {
      return format(date, 'HH:mm');
    } else if (isYesterday(date)) {
      return `Yesterday ${format(date, 'HH:mm')}`;
    } else {
      return format(date, 'MMM dd, HH:mm');
    }
  };

  const isOtherUserOnline = onlineUsers[chatRoom.other_user.id] || false;

  // Helper functions for profile dialog
  const openOtherUserProfile = () => {
    setProfileUserId(chatRoom.other_user.id);
    setProfileUserName(chatRoom.other_user.name);
    setShowProfileDialog(true);
  };

  const openCurrentUserProfile = () => {
    if (user?.id) {
      setProfileUserId(user.id);
      setProfileUserName(user.full_name || 'You');
      setShowProfileDialog(true);
    }
  };

  const closeProfileDialog = () => {
    setShowProfileDialog(false);
    setProfileUserId(null);
    setProfileUserName('');
  };

  return (
    <Box
      sx={{
        height: '100%',
        display: 'flex',
        flexDirection: 'column',
        bgcolor: 'background.default',
        position: 'relative'
      }}
    >
      {/* Header */}
      <Paper
        elevation={1}
        sx={{
          p: { xs: 1.5, sm: 2 },
          borderRadius: 0,
          bgcolor: 'background.paper',
          borderBottom: 1,
          borderColor: 'divider',
          zIndex: 1
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
          {isMobile && onClose && (
            <IconButton
              onClick={onClose}
              size="small"
              sx={{ mr: 1 }}
            >
              <ArrowBackIcon />
            </IconButton>
          )}

          <Tooltip title="View profile" arrow>
            <ButtonBase
              onClick={openOtherUserProfile}
              sx={{
                borderRadius: '50%',
                transition: 'all 0.2s ease',
                '&:hover': {
                  bgcolor: 'action.hover',
                  transform: 'scale(1.05)',
                },
              }}
            >
              <Avatar
                src={chatRoom.other_user.profile_picture_url}
                sx={{
                  width: { xs: 36, sm: 40 },
                  height: { xs: 36, sm: 40 },
                  bgcolor: 'primary.main'
                }}
              >
                {chatRoom.other_user.name.charAt(0).toUpperCase()}
              </Avatar>
            </ButtonBase>
          </Tooltip>

          <Box sx={{ flex: 1, minWidth: 0 }}>
            <Tooltip title="View profile" arrow>
              <ButtonBase
                onClick={openOtherUserProfile}
                sx={{
                  textAlign: 'left',
                  width: '100%',
                  borderRadius: 1,
                  px: 1,
                  py: 0.5,
                  transition: 'all 0.2s ease',
                  '&:hover': {
                    bgcolor: 'action.hover',
                    transform: 'translateX(2px)',
                  },
                }}
              >
                <Typography
                  variant="subtitle1"
                  sx={{
                    fontWeight: 600,
                    fontSize: { xs: '0.95rem', sm: '1.1rem' }
                  }}
                  noWrap
                >
                  {chatRoom.other_user.name}
                </Typography>
              </ButtonBase>
            </Tooltip>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <CircleIcon
                sx={{
                  fontSize: 8,
                  color: isOtherUserOnline ? 'success.main' : 'grey.400'
                }}
              />
              <Typography
                variant="caption"
                color={isOtherUserOnline ? 'success.main' : 'text.secondary'}
                sx={{ fontSize: '0.75rem' }}
              >
                {isOtherUserOnline ? 'Online' : 'Offline'}
              </Typography>
              {!isConnected && (
                <Chip
                  label="Connecting..."
                  size="small"
                  color="warning"
                  variant="outlined"
                  sx={{ ml: 1, height: 20, fontSize: '0.7rem' }}
                />
              )}
            </Box>
          </Box>
        </Box>
      </Paper>

      {/* Messages Area */}
      <Box
        key={`messages-${chatRoom.id}-${chatMessages.length}-${forceRender}`}
        ref={messagesContainerRef}
        onScroll={handleScroll}
        sx={{
          flex: 1,
          overflow: 'auto',
          bgcolor: (theme) => theme.palette.mode === 'dark' ? 'grey.900' : 'grey.50',
          position: 'relative'
        }}
      >
        {isLoadingMessages ? (
          <Box sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            height: '100%'
          }}>
            <CircularProgress size={40} />
          </Box>
        ) : chatMessages.length === 0 ? (
          <Box sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            height: '100%',
            textAlign: 'center',
            p: 3
          }}>
            <Box>
              <Typography variant="h6" color="text.secondary" gutterBottom>
                👋 Start the conversation!
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Send a message to begin chatting
              </Typography>
            </Box>
          </Box>
        ) : (
          <Stack
            key={`stack-${chatRoom.id}-${chatMessages.length}-${forceRender}`}
            spacing={1}
            sx={{ p: { xs: 1, sm: 2 } }}
          >
            {chatMessages.map((message, index) => {
              const isOwnMessage = message.sender_id === user?.id;
              const showAvatar = index === 0 ||
                chatMessages[index - 1].sender_id !== message.sender_id;
              const showTime = index === chatMessages.length - 1 ||
                chatMessages[index + 1]?.sender_id !== message.sender_id;

              return (
                <Box
                  key={message.id}
                  sx={{
                    display: 'flex',
                    flexDirection: isOwnMessage ? 'row-reverse' : 'row',
                    alignItems: 'flex-end',
                    gap: 1,
                    mb: showTime ? 1 : 0
                  }}
                >
                  {showAvatar && !isOwnMessage && (
                    <ButtonBase
                      onClick={openOtherUserProfile}
                      sx={{
                        borderRadius: '50%',
                        '&:hover': {
                          bgcolor: 'action.hover',
                        },
                      }}
                    >
                      <Avatar
                        src={message.sender_profile_picture_url}
                        sx={{
                          width: 28,
                          height: 28,
                          bgcolor: 'secondary.main',
                          fontSize: '0.8rem'
                        }}
                      >
                        {message.sender_name?.charAt(0) || 'U'}
                      </Avatar>
                    </ButtonBase>
                  )}

                  <Box sx={{
                    maxWidth: { xs: '85%', sm: '70%' },
                    ml: !showAvatar && !isOwnMessage ? 4 : 0,
                    mr: !showAvatar && isOwnMessage ? 4 : 0
                  }}>
                    <Paper
                      elevation={0}
                      sx={{
                        p: { xs: 1.5, sm: 2 },
                        bgcolor: isOwnMessage
                          ? 'primary.main'
                          : (theme) => theme.palette.mode === 'dark' ? 'grey.800' : 'background.paper',
                        color: isOwnMessage
                          ? 'primary.contrastText'
                          : 'text.primary',
                        borderRadius: 3,
                        borderBottomLeftRadius: isOwnMessage ? 3 : (showAvatar ? 3 : 1),
                        borderBottomRightRadius: isOwnMessage ? (showAvatar ? 3 : 1) : 3,
                        boxShadow: (theme) => theme.palette.mode === 'dark'
                          ? '0 1px 3px rgba(0,0,0,0.3)'
                          : '0 1px 2px rgba(0,0,0,0.1)',
                        wordBreak: 'break-word'
                      }}
                    >
                      <Typography
                        variant="body2"
                        sx={{
                          fontSize: { xs: '0.9rem', sm: '0.95rem' },
                          lineHeight: 1.4
                        }}
                      >
                        {message.content}
                      </Typography>
                    </Paper>

                    {showTime && (
                      <Typography
                        variant="caption"
                        color="text.secondary"
                        sx={{
                          display: 'block',
                          textAlign: isOwnMessage ? 'right' : 'left',
                          mt: 0.5,
                          px: 1,
                          fontSize: '0.7rem'
                        }}
                      >
                        {formatMessageTime(message.created_at)}
                      </Typography>
                    )}
                  </Box>

                  {showAvatar && isOwnMessage && (
                    <ButtonBase
                      onClick={openCurrentUserProfile}
                      sx={{
                        borderRadius: '50%',
                        '&:hover': {
                          bgcolor: 'action.hover',
                        },
                      }}
                    >
                      <Avatar
                        src={user?.profile_picture_url}
                        sx={{
                          width: 28,
                          height: 28,
                          bgcolor: 'primary.main',
                          fontSize: '0.8rem'
                        }}
                      >
                        {user?.full_name?.charAt(0) || 'Y'}
                      </Avatar>
                    </ButtonBase>
                  )}
                </Box>
              );
            })}
          </Stack>
        )}
        <div ref={messagesEndRef} />
      </Box>

      {/* Message Input */}
      <Paper
        elevation={3}
        sx={{
          p: { xs: 1.5, sm: 2 },
          borderRadius: 0,
          borderTop: 1,
          borderColor: 'divider',
          bgcolor: 'background.paper'
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'flex-end', gap: 1 }}>
          <TextField
            ref={inputRef}
            fullWidth
            multiline
            maxRows={4}
            value={newMessage}
            onChange={handleInputChange}
            onKeyPress={handleKeyPress}
            placeholder={isConnected ? "Type a message..." : "Connecting..."}
            disabled={!isConnected}
            variant="outlined"
            size="small"
            autoFocus={false}
            onFocus={(e) => {
              // Ensure proper focus handling on mobile
              e.target.scrollIntoView({ behavior: 'smooth', block: 'center' });
            }}
            sx={{
              '& .MuiOutlinedInput-root': {
                borderRadius: 3,
                bgcolor: (theme) => theme.palette.mode === 'dark' ? 'grey.800' : 'background.default',
                transition: 'all 0.2s ease-in-out',
                '&:hover': {
                  bgcolor: (theme) => theme.palette.mode === 'dark' ? 'grey.700' : 'background.default',
                },
                '&.Mui-focused': {
                  bgcolor: (theme) => theme.palette.mode === 'dark' ? 'grey.800' : 'background.default',
                  transform: 'none', // Prevent transform issues on mobile
                },
                '& .MuiOutlinedInput-notchedOutline': {
                  borderColor: (theme) => theme.palette.mode === 'dark' ? 'grey.600' : 'divider',
                },
                '&:hover .MuiOutlinedInput-notchedOutline': {
                  borderColor: (theme) => theme.palette.mode === 'dark' ? 'grey.500' : 'primary.main',
                },
                '&.Mui-focused .MuiOutlinedInput-notchedOutline': {
                  borderColor: 'primary.main',
                },
              },
              '& .MuiOutlinedInput-input': {
                fontSize: { xs: '16px', sm: '1rem' }, // 16px prevents zoom on iOS
                py: { xs: 1, sm: 1.5 },
                color: 'text.primary',
                WebkitAppearance: 'none', // Remove iOS styling
                resize: 'none', // Prevent manual resize
              }
            }}
          />

          {isMobile ? (
            <Fab
              size="small"
              color="primary"
              onClick={handleSendMessage}
              disabled={!newMessage.trim() || !isConnected}
              sx={{
                minHeight: 40,
                width: 40,
                height: 40
              }}
            >
              <SendIcon sx={{ fontSize: '1.2rem' }} />
            </Fab>
          ) : (
            <IconButton
              onClick={handleSendMessage}
              disabled={!newMessage.trim() || !isConnected}
              color="primary"
              size="large"
              sx={{
                bgcolor: 'primary.main',
                color: 'primary.contrastText',
                '&:hover': {
                  bgcolor: 'primary.dark',
                },
                '&:disabled': {
                  bgcolor: (theme) => theme.palette.mode === 'dark' ? 'grey.700' : 'grey.300',
                  color: (theme) => theme.palette.mode === 'dark' ? 'grey.400' : 'grey.500',
                },
                width: 48,
                height: 48
              }}
            >
              <SendIcon />
            </IconButton>
          )}
        </Box>
      </Paper>

      {/* Profile Dialog */}
      {profileUserId && (
        <ChatProfileDialog
          open={showProfileDialog}
          onClose={closeProfileDialog}
          chatRoomId={chatRoom.id}
          userId={profileUserId}
          userName={profileUserName}
        />
      )}
    </Box>
  );
};

export default ChatComponent;
