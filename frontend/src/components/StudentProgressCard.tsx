import React from 'react';
import {
  Box,
  Card,
  CardContent,
  Typography,
  LinearProgress,
  Chip,
  Grid,
  useTheme,
  useMediaQuery,
  Avatar,
  CircularProgress,
  Alert
} from '@mui/material';
import {
  TrendingUp as TrendingUpIcon,
  EmojiEvents as TrophyIcon,
  School as GraduationIcon,
  Quiz as QuizIcon
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import { useQuery } from '@tanstack/react-query';
import { getGamificationProfile } from '../api/gamification';

interface StudentProgressCardProps {
  userId?: number;
}

const StudentProgressCard: React.FC<StudentProgressCardProps> = ({ userId }) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  // Fetch gamification profile
  const { data: profile, isLoading, error } = useQuery({
    queryKey: ['gamificationProfile', userId],
    queryFn: () => getGamificationProfile(userId),
    retry: 2,
    staleTime: 5 * 60 * 1000, // 5 minutes
  });

  if (isLoading) {
    return (
      <Card sx={{ borderRadius: 2, height: '100%' }}>
        <CardContent sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: 200 }}>
          <CircularProgress />
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card sx={{ borderRadius: 2, height: '100%' }}>
        <CardContent>
          <Alert severity="info">
            Unable to load progress data. You may need to complete some activities first.
          </Alert>
        </CardContent>
      </Card>
    );
  }

  // Use profile data or defaults
  const totalPoints = profile?.total_points || 0;
  const level = profile?.current_level?.level_number || 1;
  const levelName = profile?.current_level?.name || 'Beginner';
  const streak = profile?.streak?.current_streak || 0;
  const badgeCount = profile?.badges?.length || 0;

  // Calculate progress to next level
  const progress = profile?.points_to_next_level !== null && profile?.next_level
    ? Math.max(0, Math.min(100, ((profile.next_level.min_points - (profile.points_to_next_level || 0)) / (profile.next_level.min_points - profile.current_level.min_points)) * 100))
    : 100;
  
  // Animation variants
  const cardVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        type: 'spring',
        stiffness: 100,
        damping: 15
      }
    }
  };
  
  const MotionCard = motion.create(Card);

  return (
    <MotionCard
      variants={cardVariants}
      initial="hidden"
      animate="visible"
      sx={{
        borderRadius: 2,
        overflow: 'hidden',
        boxShadow: '0 4px 20px rgba(0,0,0,0.08)',
        border: `1px solid ${theme.palette.divider}`,
        height: '100%'
      }}
    >
      <CardContent sx={{ p: isMobile ? 2 : 3 }}>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Avatar
            sx={{
              bgcolor: theme.palette.primary.main,
              width: 40,
              height: 40,
              mr: 2
            }}
          >
            <TrophyIcon />
          </Avatar>
          <Box>
            <Typography variant="h6" fontWeight="bold">
              Level {level}: {levelName}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {totalPoints} total points
            </Typography>
          </Box>
        </Box>
        
        <Box sx={{ mb: 3 }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 0.5 }}>
            <Typography variant="body2" color="text.secondary">
              {profile?.next_level ? `Progress to Level ${profile.next_level.level_number}` : 'Max Level Reached'}
            </Typography>
            <Typography variant="body2" fontWeight="medium">
              {Math.round(progress)}%
            </Typography>
          </Box>
          <LinearProgress 
            variant="determinate" 
            value={progress} 
            sx={{ 
              height: 8, 
              borderRadius: 4,
              bgcolor: theme.palette.mode === 'light' ? 'rgba(0,0,0,0.05)' : 'rgba(255,255,255,0.05)',
              '& .MuiLinearProgress-bar': {
                borderRadius: 4,
                background: `linear-gradient(90deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`
              }
            }} 
          />
        </Box>
        
        <Grid container spacing={isMobile ? 1 : 2}>
          <Grid item xs={6}>
            <Card 
              sx={{ 
                bgcolor: theme.palette.mode === 'light' ? 'rgba(0,0,0,0.02)' : 'rgba(255,255,255,0.03)',
                boxShadow: 'none',
                border: `1px solid ${theme.palette.divider}`,
                borderRadius: 1.5
              }}
            >
              <CardContent sx={{ p: 1.5, '&:last-child': { pb: 1.5 } }}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 0.5 }}>
                  <TrophyIcon fontSize="small" color="primary" sx={{ mr: 0.5 }} />
                  <Typography variant="body2" color="text.secondary">
                    Badges
                  </Typography>
                </Box>
                <Typography variant="h6" fontWeight="bold">
                  {badgeCount}
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={6}>
            <Card 
              sx={{ 
                bgcolor: theme.palette.mode === 'light' ? 'rgba(0,0,0,0.02)' : 'rgba(255,255,255,0.03)',
                boxShadow: 'none',
                border: `1px solid ${theme.palette.divider}`,
                borderRadius: 1.5
              }}
            >
              <CardContent sx={{ p: 1.5, '&:last-child': { pb: 1.5 } }}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 0.5 }}>
                  <GraduationIcon fontSize="small" color="secondary" sx={{ mr: 0.5 }} />
                  <Typography variant="body2" color="text.secondary">
                    Level
                  </Typography>
                </Box>
                <Typography variant="h6" fontWeight="bold">
                  {level}
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
        
        {streak > 0 && (
          <Box sx={{ mt: 2, display: 'flex', alignItems: 'center', justifyContent: 'center' }}>
            <Chip 
              icon={<TrendingUpIcon />} 
              label={`${streak} Day Streak!`} 
              color="primary" 
              sx={{ borderRadius: 1 }}
            />
          </Box>
        )}
      </CardContent>
    </MotionCard>
  );
};

export default StudentProgressCard;
