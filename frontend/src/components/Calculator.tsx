import React, { useState } from 'react';
import {
  Box,
  Button,
  Paper,
  Typography,
  IconButton,
  useTheme,
  Drawer,
  Toolt<PERSON>,
  ToggleButton,
  ToggleButtonGroup
} from '@mui/material';
import {
  Calculate as CalculateIcon,
  Backspace as BackspaceIcon,
  Close as CloseIcon,
  Science as ScienceIcon,
  Calculate as BasicIcon
} from '@mui/icons-material';
import { motion } from 'framer-motion';

const MotionButton = motion(Button);

interface CalculatorProps {
  position?: 'left' | 'right';
}

const Calculator: React.FC<CalculatorProps> = ({ position = 'right' }) => {
  const theme = useTheme();
  const [open, setOpen] = useState(false);
  const [display, setDisplay] = useState('0');
  const [memory, setMemory] = useState<number | null>(null);
  const [operation, setOperation] = useState<string | null>(null);
  const [waitingForOperand, setWaitingForOperand] = useState(false);
  const [calculatorMode, setCalculatorMode] = useState<'basic' | 'scientific'>('basic');

  const toggleCalculator = () => {
    setOpen(!open);
  };

  const clearDisplay = () => {
    setDisplay('0');
    setOperation(null);
    setMemory(null);
    setWaitingForOperand(false);
  };

  const handleDigitClick = (digit: string) => {
    if (waitingForOperand) {
      setDisplay(digit);
      setWaitingForOperand(false);
    } else {
      setDisplay(display === '0' ? digit : display + digit);
    }
  };

  const handleDecimalClick = () => {
    if (waitingForOperand) {
      setDisplay('0.');
      setWaitingForOperand(false);
    } else if (display.indexOf('.') === -1) {
      setDisplay(display + '.');
    }
  };

  const handleOperationClick = (op: string) => {
    const inputValue = parseFloat(display);

    if (memory === null) {
      setMemory(inputValue);
    } else if (operation) {
      const result = performCalculation(memory, inputValue, operation);
      setMemory(result);
      setDisplay(String(result));
    }

    setWaitingForOperand(true);
    setOperation(op);
  };

  const handleEqualsClick = () => {
    const inputValue = parseFloat(display);

    if (operation && memory !== null) {
      const result = performCalculation(memory, inputValue, operation);
      setDisplay(String(result));
      setMemory(null);
      setOperation(null);
      setWaitingForOperand(true);
    }
  };

  const handleBackspaceClick = () => {
    if (display.length > 1) {
      setDisplay(display.substring(0, display.length - 1));
    } else {
      setDisplay('0');
    }
  };

  const performCalculation = (a: number, b: number, op: string): number => {
    switch (op) {
      case '+':
        return a + b;
      case '-':
        return a - b;
      case '×':
        return a * b;
      case '÷':
        return b === 0 ? NaN : a / b;
      case 'x^y':
        return Math.pow(a, b);
      case 'log':
        return Math.log10(a);
      case 'ln':
        return Math.log(a);
      case 'sin':
        return Math.sin(a);
      case 'cos':
        return Math.cos(a);
      case 'tan':
        return Math.tan(a);
      default:
        return b;
    }
  };

  const handleScientificFunction = (func: string) => {
    const inputValue = parseFloat(display);
    let result: number;

    switch (func) {
      case 'sqrt':
        result = Math.sqrt(inputValue);
        break;
      case 'square':
        result = Math.pow(inputValue, 2);
        break;
      case 'cube':
        result = Math.pow(inputValue, 3);
        break;
      case '1/x':
        result = 1 / inputValue;
        break;
      case 'sin':
        result = Math.sin(inputValue);
        break;
      case 'cos':
        result = Math.cos(inputValue);
        break;
      case 'tan':
        result = Math.tan(inputValue);
        break;
      case 'log':
        result = Math.log10(inputValue);
        break;
      case 'ln':
        result = Math.log(inputValue);
        break;
      case 'pi':
        result = Math.PI;
        break;
      case 'e':
        result = Math.E;
        break;
      default:
        result = inputValue;
    }

    setDisplay(String(result));
    setWaitingForOperand(true);
  };

  const handleModeChange = (event: React.MouseEvent<HTMLElement>, newMode: 'basic' | 'scientific' | null) => {
    if (newMode !== null) {
      setCalculatorMode(newMode);
    }
  };

  const buttonVariants = {
    hover: {
      scale: 1.05,
      boxShadow: '0px 3px 8px rgba(0, 0, 0, 0.2)',
    },
    tap: {
      scale: 0.95,
    },
  };

  return (
    <>
      <Tooltip title="Calculator">
        <IconButton
          onClick={toggleCalculator}
          color="primary"
          sx={{
            position: 'fixed',
            bottom: 80, // Higher from bottom to avoid blocking content
            [position]: 20,
            zIndex: 1000,
            bgcolor: theme.palette.background.paper,
            boxShadow: theme.shadows[3],
            width: 48, // Smaller size to be less intrusive
            height: 48,
            '&:hover': {
              bgcolor: theme.palette.mode === 'light'
                ? theme.palette.grey[100]
                : theme.palette.grey[800],
              transform: 'scale(1.05)',
            },
            transition: 'all 0.2s ease-in-out',
            // Ensure it doesn't block important content
            '@media (max-height: 600px)': {
              bottom: 60,
              width: 40,
              height: 40,
            },
          }}
        >
          <CalculateIcon />
        </IconButton>
      </Tooltip>

      <Drawer
        anchor={position}
        open={open}
        onClose={() => setOpen(false)}
        PaperProps={{
          sx: {
            width: calculatorMode === 'scientific' ? 360 : 280,
            p: 2,
            borderRadius: position === 'left' ? '0 8px 8px 0' : '8px 0 0 8px',
            transition: 'width 0.3s ease-in-out',
          },
        }}
      >
        <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
          <Typography variant="h6" fontWeight="bold">
            Calculator
          </Typography>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <ToggleButtonGroup
              value={calculatorMode}
              exclusive
              onChange={handleModeChange}
              size="small"
              sx={{ mr: 1 }}
            >
              <ToggleButton value="basic">
                <Tooltip title="Basic">
                  <BasicIcon fontSize="small" />
                </Tooltip>
              </ToggleButton>
              <ToggleButton value="scientific">
                <Tooltip title="Scientific">
                  <ScienceIcon fontSize="small" />
                </Tooltip>
              </ToggleButton>
            </ToggleButtonGroup>
            <IconButton onClick={() => setOpen(false)} size="small">
              <CloseIcon />
            </IconButton>
          </Box>
        </Box>

        <Paper
          elevation={0}
          sx={{
            p: 2,
            mb: 2,
            bgcolor: theme.palette.mode === 'light' ? theme.palette.grey[100] : theme.palette.grey[900],
            borderRadius: 2,
            textAlign: 'right',
            border: `1px solid ${theme.palette.divider}`,
          }}
        >
          <Typography variant="h4" component="div" sx={{ fontFamily: 'monospace' }}>
            {display}
          </Typography>
        </Paper>

        {calculatorMode === 'scientific' && (
          <Box sx={{ display: 'grid', gridTemplateColumns: 'repeat(5, 1fr)', gap: 1, mb: 2 }}>
            <MotionButton
              variant="outlined"
              color="info"
              onClick={() => handleScientificFunction('sqrt')}
              variants={buttonVariants}
              whileHover="hover"
              whileTap="tap"
            >
              √x
            </MotionButton>
            <MotionButton
              variant="outlined"
              color="info"
              onClick={() => handleScientificFunction('square')}
              variants={buttonVariants}
              whileHover="hover"
              whileTap="tap"
            >
              x²
            </MotionButton>
            <MotionButton
              variant="outlined"
              color="info"
              onClick={() => handleScientificFunction('cube')}
              variants={buttonVariants}
              whileHover="hover"
              whileTap="tap"
            >
              x³
            </MotionButton>
            <MotionButton
              variant="outlined"
              color="info"
              onClick={() => handleOperationClick('x^y')}
              variants={buttonVariants}
              whileHover="hover"
              whileTap="tap"
            >
              x^y
            </MotionButton>
            <MotionButton
              variant="outlined"
              color="info"
              onClick={() => handleScientificFunction('1/x')}
              variants={buttonVariants}
              whileHover="hover"
              whileTap="tap"
            >
              1/x
            </MotionButton>

            <MotionButton
              variant="outlined"
              color="info"
              onClick={() => handleScientificFunction('sin')}
              variants={buttonVariants}
              whileHover="hover"
              whileTap="tap"
            >
              sin
            </MotionButton>
            <MotionButton
              variant="outlined"
              color="info"
              onClick={() => handleScientificFunction('cos')}
              variants={buttonVariants}
              whileHover="hover"
              whileTap="tap"
            >
              cos
            </MotionButton>
            <MotionButton
              variant="outlined"
              color="info"
              onClick={() => handleScientificFunction('tan')}
              variants={buttonVariants}
              whileHover="hover"
              whileTap="tap"
            >
              tan
            </MotionButton>
            <MotionButton
              variant="outlined"
              color="info"
              onClick={() => handleScientificFunction('log')}
              variants={buttonVariants}
              whileHover="hover"
              whileTap="tap"
            >
              log
            </MotionButton>
            <MotionButton
              variant="outlined"
              color="info"
              onClick={() => handleScientificFunction('ln')}
              variants={buttonVariants}
              whileHover="hover"
              whileTap="tap"
            >
              ln
            </MotionButton>

            <MotionButton
              variant="outlined"
              color="info"
              onClick={() => handleScientificFunction('pi')}
              variants={buttonVariants}
              whileHover="hover"
              whileTap="tap"
            >
              π
            </MotionButton>
            <MotionButton
              variant="outlined"
              color="info"
              onClick={() => handleScientificFunction('e')}
              variants={buttonVariants}
              whileHover="hover"
              whileTap="tap"
            >
              e
            </MotionButton>
            <MotionButton
              variant="outlined"
              color="info"
              onClick={() => setDisplay(String(Math.abs(parseFloat(display))))}
              variants={buttonVariants}
              whileHover="hover"
              whileTap="tap"
            >
              |x|
            </MotionButton>
            <MotionButton
              variant="outlined"
              color="info"
              onClick={() => setDisplay(String(Math.floor(parseFloat(display))))}
              variants={buttonVariants}
              whileHover="hover"
              whileTap="tap"
            >
              ⌊x⌋
            </MotionButton>
            <MotionButton
              variant="outlined"
              color="info"
              onClick={() => setDisplay(String(Math.ceil(parseFloat(display))))}
              variants={buttonVariants}
              whileHover="hover"
              whileTap="tap"
            >
              ⌈x⌉
            </MotionButton>
          </Box>
        )}

        <Box sx={{ display: 'grid', gridTemplateColumns: 'repeat(4, 1fr)', gap: 1 }}>
          {/* First row */}
          <MotionButton
            variant="outlined"
            color="error"
            onClick={clearDisplay}
            variants={buttonVariants}
            whileHover="hover"
            whileTap="tap"
          >
            C
          </MotionButton>
          <MotionButton
            variant="outlined"
            onClick={() => setDisplay(String(parseFloat(display) * -1))}
            variants={buttonVariants}
            whileHover="hover"
            whileTap="tap"
          >
            +/-
          </MotionButton>
          <MotionButton
            variant="outlined"
            onClick={() => setDisplay(String(parseFloat(display) / 100))}
            variants={buttonVariants}
            whileHover="hover"
            whileTap="tap"
          >
            %
          </MotionButton>
          <MotionButton
            variant="contained"
            color="primary"
            onClick={() => handleOperationClick('÷')}
            variants={buttonVariants}
            whileHover="hover"
            whileTap="tap"
          >
            ÷
          </MotionButton>

          {/* Second row */}
          <MotionButton
            variant="outlined"
            onClick={() => handleDigitClick('7')}
            variants={buttonVariants}
            whileHover="hover"
            whileTap="tap"
          >
            7
          </MotionButton>
          <MotionButton
            variant="outlined"
            onClick={() => handleDigitClick('8')}
            variants={buttonVariants}
            whileHover="hover"
            whileTap="tap"
          >
            8
          </MotionButton>
          <MotionButton
            variant="outlined"
            onClick={() => handleDigitClick('9')}
            variants={buttonVariants}
            whileHover="hover"
            whileTap="tap"
          >
            9
          </MotionButton>
          <MotionButton
            variant="contained"
            color="primary"
            onClick={() => handleOperationClick('×')}
            variants={buttonVariants}
            whileHover="hover"
            whileTap="tap"
          >
            ×
          </MotionButton>

          {/* Third row */}
          <MotionButton
            variant="outlined"
            onClick={() => handleDigitClick('4')}
            variants={buttonVariants}
            whileHover="hover"
            whileTap="tap"
          >
            4
          </MotionButton>
          <MotionButton
            variant="outlined"
            onClick={() => handleDigitClick('5')}
            variants={buttonVariants}
            whileHover="hover"
            whileTap="tap"
          >
            5
          </MotionButton>
          <MotionButton
            variant="outlined"
            onClick={() => handleDigitClick('6')}
            variants={buttonVariants}
            whileHover="hover"
            whileTap="tap"
          >
            6
          </MotionButton>
          <MotionButton
            variant="contained"
            color="primary"
            onClick={() => handleOperationClick('-')}
            variants={buttonVariants}
            whileHover="hover"
            whileTap="tap"
          >
            -
          </MotionButton>

          {/* Fourth row */}
          <MotionButton
            variant="outlined"
            onClick={() => handleDigitClick('1')}
            variants={buttonVariants}
            whileHover="hover"
            whileTap="tap"
          >
            1
          </MotionButton>
          <MotionButton
            variant="outlined"
            onClick={() => handleDigitClick('2')}
            variants={buttonVariants}
            whileHover="hover"
            whileTap="tap"
          >
            2
          </MotionButton>
          <MotionButton
            variant="outlined"
            onClick={() => handleDigitClick('3')}
            variants={buttonVariants}
            whileHover="hover"
            whileTap="tap"
          >
            3
          </MotionButton>
          <MotionButton
            variant="contained"
            color="primary"
            onClick={() => handleOperationClick('+')}
            variants={buttonVariants}
            whileHover="hover"
            whileTap="tap"
          >
            +
          </MotionButton>

          {/* Fifth row */}
          <MotionButton
            variant="outlined"
            onClick={() => handleDigitClick('0')}
            sx={{ gridColumn: 'span 2' }}
            variants={buttonVariants}
            whileHover="hover"
            whileTap="tap"
          >
            0
          </MotionButton>
          <MotionButton
            variant="outlined"
            onClick={handleDecimalClick}
            variants={buttonVariants}
            whileHover="hover"
            whileTap="tap"
          >
            .
          </MotionButton>
          <MotionButton
            variant="contained"
            color="secondary"
            onClick={handleEqualsClick}
            variants={buttonVariants}
            whileHover="hover"
            whileTap="tap"
          >
            =
          </MotionButton>
        </Box>

        <Box sx={{ mt: 2, display: 'flex', justifyContent: 'center' }}>
          <MotionButton
            variant="outlined"
            color="error"
            onClick={handleBackspaceClick}
            startIcon={<BackspaceIcon />}
            variants={buttonVariants}
            whileHover="hover"
            whileTap="tap"
            fullWidth
          >
            Backspace
          </MotionButton>
        </Box>
      </Drawer>
    </>
  );
};

export default Calculator;
