import React, { useState, useRef } from 'react';
import {
  Box,
  Button,
  Typography,
  Paper,
  Alert,
  LinearProgress,
  IconButton,
  Tooltip,
  Card,
  CardMedia,
  CardActions,
} from '@mui/material';
import {
  CloudUpload as UploadIcon,
  Delete as DeleteIcon,
  Image as ImageIcon,
} from '@mui/icons-material';
import { useQueryClient } from '@tanstack/react-query';

import { uploadQuestionImage, ImageUploadResponse } from '../../api/questions';

interface QuestionImageUploadProps {
  questionId: number;
  currentImageUrl?: string;
  onImageUploaded?: (imageUrl: string) => void;
  onImageRemoved?: () => void;
}

const QuestionImageUpload: React.FC<QuestionImageUploadProps> = ({
  questionId,
  currentImageUrl,
  onImageUploaded,
  onImageRemoved,
}) => {
  const [uploading, setUploading] = useState(false);
  const [uploadResult, setUploadResult] = useState<ImageUploadResponse | null>(null);
  const [error, setError] = useState<string | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const queryClient = useQueryClient();

  const handleFileSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      handleUpload(file);
    }
  };

  const handleUpload = async (file: File) => {
    setUploading(true);
    setError(null);
    setUploadResult(null);

    try {
      const result = await uploadQuestionImage(questionId, file);
      setUploadResult(result);
      
      // Invalidate questions cache to refresh the list
      queryClient.invalidateQueries({ queryKey: ['questions'] });
      queryClient.invalidateQueries({ queryKey: ['question', questionId] });
      
      // Call callback if provided
      if (onImageUploaded) {
        onImageUploaded(result.image_url);
      }
    } catch (error: any) {
      setError(error.message || 'Failed to upload image');
    } finally {
      setUploading(false);
    }
  };

  const handleRemoveImage = () => {
    setUploadResult(null);
    setError(null);
    if (onImageRemoved) {
      onImageRemoved();
    }
  };

  const handleDragOver = (event: React.DragEvent) => {
    event.preventDefault();
  };

  const handleDrop = (event: React.DragEvent) => {
    event.preventDefault();
    const file = event.dataTransfer.files[0];
    if (file && file.type.startsWith('image/')) {
      handleUpload(file);
    }
  };

  const displayImageUrl = uploadResult?.image_url || currentImageUrl;

  return (
    <Box sx={{ width: '100%' }}>
      {!displayImageUrl ? (
        <Paper
          sx={{
            p: 3,
            border: '2px dashed',
            borderColor: uploading ? 'primary.main' : 'divider',
            textAlign: 'center',
            cursor: 'pointer',
            transition: 'all 0.3s ease',
            '&:hover': {
              borderColor: 'primary.main',
              bgcolor: 'action.hover',
            },
          }}
          onDragOver={handleDragOver}
          onDrop={handleDrop}
          onClick={() => fileInputRef.current?.click()}
        >
          <input
            ref={fileInputRef}
            type="file"
            accept="image/*"
            onChange={handleFileSelect}
            style={{ display: 'none' }}
            disabled={uploading}
          />

          <ImageIcon sx={{ fontSize: 48, color: 'text.secondary', mb: 2 }} />
          
          <Typography variant="h6" gutterBottom>
            {uploading ? 'Uploading...' : 'Upload Question Image'}
          </Typography>
          
          <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
            Drop an image here or click to browse
          </Typography>
          
          <Typography variant="caption" color="text.secondary">
            Supported formats: JPG, PNG, GIF, WebP (max 10MB)
          </Typography>

          {uploading && (
            <Box sx={{ mt: 2 }}>
              <LinearProgress />
            </Box>
          )}
        </Paper>
      ) : (
        <Card sx={{ maxWidth: 400 }}>
          <CardMedia
            component="img"
            height="200"
            image={displayImageUrl}
            alt="Question image"
            sx={{ objectFit: 'contain', bgcolor: 'grey.100' }}
          />
          <CardActions sx={{ justifyContent: 'space-between' }}>
            <Button
              size="small"
              startIcon={<UploadIcon />}
              onClick={() => fileInputRef.current?.click()}
              disabled={uploading}
            >
              Replace
            </Button>
            <Tooltip title="Remove image">
              <IconButton
                size="small"
                onClick={handleRemoveImage}
                disabled={uploading}
              >
                <DeleteIcon />
              </IconButton>
            </Tooltip>
          </CardActions>
          
          <input
            ref={fileInputRef}
            type="file"
            accept="image/*"
            onChange={handleFileSelect}
            style={{ display: 'none' }}
            disabled={uploading}
          />
        </Card>
      )}

      {error && (
        <Alert severity="error" sx={{ mt: 2 }}>
          {error}
        </Alert>
      )}

      {uploadResult && (
        <Alert severity="success" sx={{ mt: 2 }}>
          Image uploaded successfully! 
          {uploadResult.image_metadata.width && uploadResult.image_metadata.height && (
            <Typography variant="caption" display="block">
              Dimensions: {uploadResult.image_metadata.width} × {uploadResult.image_metadata.height}px
            </Typography>
          )}
        </Alert>
      )}
    </Box>
  );
};

export default QuestionImageUpload;
