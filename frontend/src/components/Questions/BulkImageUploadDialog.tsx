import React, { useState, useRef } from 'react';
import {
  <PERSON>alog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Button,
  Box,
  Typography,
  Paper,
  List,
  ListItem,
  ListItemText,
  Alert,
  LinearProgress,
  Chip,
  IconButton,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  SelectChangeEvent,
} from '@mui/material';
import {
  CloudUpload as UploadIcon,
  Close as CloseIcon,
  Delete as DeleteIcon,
  Image as ImageIcon,
} from '@mui/icons-material';
import { useQueryClient, useQuery } from '@tanstack/react-query';

import { bulkUploadQuestionImages, BulkImageUploadResponse } from '../../api/questions';
import { getQuestions, Question } from '../../api/questions';
import { getCourses, Course } from '../../api/courses';

interface BulkImageUploadDialogProps {
  open: boolean;
  onClose: () => void;
}

const BulkImageUploadDialog: React.FC<BulkImageUploadDialogProps> = ({ open, onClose }) => {
  const [selectedImages, setSelectedImages] = useState<File[]>([]);
  const [imageQuestionMapping, setImageQuestionMapping] = useState<Record<string, number | string>>({});
  const [selectedCourse, setSelectedCourse] = useState<number | ''>('');
  const [mappingMode, setMappingMode] = useState<'dropdown' | 'number'>('dropdown');
  const [uploading, setUploading] = useState(false);
  const [uploadResult, setUploadResult] = useState<BulkImageUploadResponse | null>(null);
  const fileInputRef = useRef<HTMLInputElement>(null);
  const queryClient = useQueryClient();

  // Fetch courses for dropdown
  const { data: courses = [] } = useQuery({
    queryKey: ['courses'],
    queryFn: () => getCourses(),
  });

  // Fetch questions for dropdown (filtered by course if selected)
  const { data: questions = [] } = useQuery({
    queryKey: ['questions', selectedCourse],
    queryFn: () => getQuestions({ course_id: selectedCourse || undefined }),
    enabled: mappingMode === 'dropdown' && !!selectedCourse,
  });

  const handleImageSelect = (event: React.ChangeEvent<HTMLInputElement>) => {
    const files = Array.from(event.target.files || []);
    setSelectedImages(files);

    // Initialize mapping
    const newMapping: Record<string, number | string> = {};
    files.forEach((file, index) => {
      if (mappingMode === 'number') {
        newMapping[file.name] = (index + 1).toString(); // Default to sequential numbers
      } else {
        newMapping[file.name] = 0; // Default to no selection for dropdown
      }
    });
    setImageQuestionMapping(newMapping);
  };

  const updateImageMapping = (filename: string, value: number | string) => {
    setImageQuestionMapping(prev => ({
      ...prev,
      [filename]: value
    }));
  };

  const removeImage = (filename: string) => {
    setSelectedImages(prev => prev.filter(file => file.name !== filename));
    setImageQuestionMapping(prev => {
      const newMapping = { ...prev };
      delete newMapping[filename];
      return newMapping;
    });
  };

  const handleUpload = async () => {
    // Validate course selection
    if (!selectedCourse) {
      setUploadResult({
        message: 'Please select a course first',
        results: [],
        total_files: 0,
        successful_uploads: 0,
        failed_uploads: 0
      });
      return;
    }

    // Validate that all images have question mappings
    const unmappedImages = selectedImages.filter(file => {
      const mapping = imageQuestionMapping[file.name];
      return !mapping || mapping === 0 || mapping === '';
    });

    if (unmappedImages.length > 0) {
      setUploadResult({
        message: `Please ${mappingMode === 'dropdown' ? 'select a question' : 'enter question numbers'} for all images`,
        results: [],
        total_files: 0,
        successful_uploads: 0,
        failed_uploads: 0
      });
      return;
    }

    setUploading(true);
    try {
      let questionIds: number[];

      if (mappingMode === 'dropdown') {
        // Direct question IDs from dropdown
        questionIds = selectedImages.map(file => imageQuestionMapping[file.name] as number);
      } else {
        // Convert question numbers to IDs by finding questions in the selected course
        const courseQuestions = await getQuestions({ course_id: selectedCourse });
        questionIds = selectedImages.map(file => {
          const questionNumber = parseInt(imageQuestionMapping[file.name] as string);
          const question = courseQuestions.find(q => q.id === questionNumber);
          if (!question) {
            throw new Error(`Question #${questionNumber} not found in selected course`);
          }
          return question.id;
        });
      }

      const result = await bulkUploadQuestionImages(selectedImages, questionIds);
      setUploadResult(result);

      if (result.successful_uploads > 0) {
        // Invalidate questions cache to refresh the list
        queryClient.invalidateQueries({ queryKey: ['questions'] });
      }
    } catch (error: any) {
      setUploadResult({
        message: error.message || 'Upload failed',
        results: [],
        total_files: selectedImages.length,
        successful_uploads: 0,
        failed_uploads: selectedImages.length
      });
    } finally {
      setUploading(false);
    }
  };

  const handleClose = () => {
    setSelectedImages([]);
    setImageQuestionMapping({});
    setSelectedCourse('');
    setMappingMode('dropdown');
    setUploadResult(null);
    setUploading(false);
    onClose();
  };

  const handleDragOver = (event: React.DragEvent) => {
    event.preventDefault();
  };

  const handleDrop = (event: React.DragEvent) => {
    event.preventDefault();
    const files = Array.from(event.dataTransfer.files).filter(file =>
      file.type.startsWith('image/')
    );
    if (files.length > 0) {
      setSelectedImages(files);

      // Initialize mapping
      const newMapping: Record<string, number | string> = {};
      files.forEach((file, index) => {
        if (mappingMode === 'number') {
          newMapping[file.name] = (index + 1).toString();
        } else {
          newMapping[file.name] = 0;
        }
      });
      setImageQuestionMapping(newMapping);
    }
  };

  const canUpload = selectedCourse && selectedImages.length > 0 &&
    selectedImages.every(file => {
      const mapping = imageQuestionMapping[file.name];
      return mapping && mapping !== 0 && mapping !== '';
    });

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      maxWidth="md"
      fullWidth
      PaperProps={{
        sx: {
          m: { xs: 1, sm: 2 },
          maxHeight: { xs: '95vh', sm: '90vh' }
        }
      }}
    >
      <DialogTitle>
        <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
          <Typography variant="h6">Bulk Upload Question Images</Typography>
          <IconButton onClick={handleClose} size="small">
            <CloseIcon />
          </IconButton>
        </Box>
      </DialogTitle>

      <DialogContent>
        <Box sx={{ mb: 3 }}>
          <Alert severity="info" sx={{ mb: 2 }}>
            Upload multiple images and assign each one to a specific question.
          </Alert>

          {/* Course Selection and Mapping Mode */}
          <Box sx={{
            display: 'flex',
            gap: 2,
            mb: 3,
            flexDirection: { xs: 'column', sm: 'row' }
          }}>
            <FormControl fullWidth>
              <InputLabel>Course</InputLabel>
              <Select
                value={selectedCourse}
                label="Course"
                onChange={(e: SelectChangeEvent<number | ''>) => {
                  setSelectedCourse(e.target.value);
                  // Reset image mapping when course changes
                  setImageQuestionMapping({});
                }}
              >
                {courses.map((course) => (
                  <MenuItem key={course.id} value={course.id}>
                    <Box sx={{
                      display: 'flex',
                      flexDirection: { xs: 'column', sm: 'row' },
                      gap: { xs: 0, sm: 1 }
                    }}>
                      <Typography variant="body2">{course.name}</Typography>
                      <Typography variant="caption" color="text.secondary">
                        ({course.code})
                      </Typography>
                    </Box>
                  </MenuItem>
                ))}
              </Select>
            </FormControl>

            <FormControl fullWidth>
              <InputLabel>Mapping Method</InputLabel>
              <Select
                value={mappingMode}
                label="Mapping Method"
                onChange={(e: SelectChangeEvent<'dropdown' | 'number'>) => {
                  setMappingMode(e.target.value as 'dropdown' | 'number');
                  // Reset image mapping when mode changes
                  const newMapping: Record<string, number | string> = {};
                  selectedImages.forEach((file, index) => {
                    if (e.target.value === 'number') {
                      newMapping[file.name] = (index + 1).toString();
                    } else {
                      newMapping[file.name] = 0;
                    }
                  });
                  setImageQuestionMapping(newMapping);
                }}
              >
                <MenuItem value="dropdown">
                  <Box sx={{ display: { xs: 'block', sm: 'none' } }}>List</Box>
                  <Box sx={{ display: { xs: 'none', sm: 'block' } }}>Select from List</Box>
                </MenuItem>
                <MenuItem value="number">
                  <Box sx={{ display: { xs: 'block', sm: 'none' } }}>ID</Box>
                  <Box sx={{ display: { xs: 'none', sm: 'block' } }}>Enter Question ID</Box>
                </MenuItem>
              </Select>
            </FormControl>
          </Box>

          {selectedImages.length === 0 ? (
            <Paper
              sx={{
                p: 3,
                border: '2px dashed',
                borderColor: 'divider',
                textAlign: 'center',
                cursor: 'pointer',
                '&:hover': {
                  borderColor: 'primary.main',
                  bgcolor: 'action.hover',
                },
              }}
              onDragOver={handleDragOver}
              onDrop={handleDrop}
              onClick={() => fileInputRef.current?.click()}
            >
              <input
                ref={fileInputRef}
                type="file"
                multiple
                accept="image/*"
                onChange={handleImageSelect}
                style={{ display: 'none' }}
              />

              <ImageIcon sx={{ fontSize: 48, color: 'text.secondary', mb: 2 }} />
              
              <Typography variant="h6" gutterBottom>
                Select Images to Upload
              </Typography>
              
              <Typography variant="body2" color="text.secondary">
                Drop images here or click to browse
              </Typography>
            </Paper>
          ) : (
            <Box>
              <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                <Typography variant="subtitle1">
                  Selected Images ({selectedImages.length})
                </Typography>
                <Button
                  variant="outlined"
                  onClick={() => fileInputRef.current?.click()}
                  startIcon={<UploadIcon />}
                >
                  Add More
                </Button>
                <input
                  ref={fileInputRef}
                  type="file"
                  multiple
                  accept="image/*"
                  onChange={handleImageSelect}
                  style={{ display: 'none' }}
                />
              </Box>

              <List>
                {selectedImages.map((file, index) => (
                  <ListItem key={index} sx={{ border: '1px solid', borderColor: 'divider', mb: 1, borderRadius: 1 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', width: '100%', gap: 2 }}>
                      <Chip
                        label={file.name}
                        onDelete={() => removeImage(file.name)}
                        deleteIcon={<DeleteIcon />}
                        sx={{ minWidth: 200 }}
                      />

                      {mappingMode === 'dropdown' ? (
                        <FormControl sx={{ minWidth: 300 }}>
                          <InputLabel>Assign to Question</InputLabel>
                          <Select
                            value={imageQuestionMapping[file.name] || 0}
                            label="Assign to Question"
                            onChange={(e: SelectChangeEvent<number>) =>
                              updateImageMapping(file.name, e.target.value as number)
                            }
                            disabled={!selectedCourse}
                          >
                            <MenuItem value={0}>Select a question...</MenuItem>
                            {questions.map((question) => (
                              <MenuItem key={question.id} value={question.id}>
                                #{question.id}: {question.content.substring(0, 50)}
                                {question.content.length > 50 ? '...' : ''}
                              </MenuItem>
                            ))}
                          </Select>
                        </FormControl>
                      ) : (
                        <Box sx={{ minWidth: 300 }}>
                          <input
                            type="number"
                            placeholder="Enter Question ID"
                            value={imageQuestionMapping[file.name] || ''}
                            onChange={(e) => updateImageMapping(file.name, e.target.value)}
                            style={{
                              width: '100%',
                              padding: '16px 14px',
                              border: '1px solid #ccc',
                              borderRadius: '4px',
                              fontSize: '16px',
                              fontFamily: 'inherit'
                            }}
                          />
                        </Box>
                      )}
                    </Box>
                  </ListItem>
                ))}
              </List>

              {mappingMode === 'dropdown' && !selectedCourse && (
                <Alert severity="warning" sx={{ mt: 2 }}>
                  Please select a course first to see available questions.
                </Alert>
              )}

              {mappingMode === 'number' && (
                <Alert severity="info" sx={{ mt: 2 }}>
                  Enter the exact Question ID for each image. You can find question IDs in the questions list.
                </Alert>
              )}
            </Box>
          )}

          {uploading && (
            <Box sx={{ mt: 2 }}>
              <Typography variant="body2" gutterBottom>
                Uploading images...
              </Typography>
              <LinearProgress />
            </Box>
          )}

          {uploadResult && (
            <Box sx={{ mt: 3 }}>
              <Alert 
                severity={uploadResult.successful_uploads > 0 ? 'success' : 'error'} 
                sx={{ mb: 2 }}
              >
                {uploadResult.message}
              </Alert>

              {uploadResult.results.length > 0 && (
                <Box>
                  <Typography variant="subtitle2" gutterBottom>
                    Upload Results:
                  </Typography>
                  <List dense>
                    {uploadResult.results.map((result, index) => (
                      <ListItem key={index}>
                        <ListItemText 
                          primary={`${result.filename} → Question #${result.question_id}`}
                          secondary={result.success ? 'Success' : `Error: ${result.error}`}
                        />
                      </ListItem>
                    ))}
                  </List>
                </Box>
              )}
            </Box>
          )}
        </Box>
      </DialogContent>

      <DialogActions>
        <Button onClick={handleClose}>
          {uploadResult?.successful_uploads ? 'Close' : 'Cancel'}
        </Button>
        <Button
          variant="contained"
          onClick={handleUpload}
          disabled={!canUpload || uploading}
          startIcon={<UploadIcon />}
        >
          {uploading ? 'Uploading...' : 'Upload Images'}
        </Button>
      </DialogActions>
    </Dialog>
  );
};

export default BulkImageUploadDialog;
