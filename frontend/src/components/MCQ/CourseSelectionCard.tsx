import React from 'react';
import {
  Box,
  Typography,
  Paper,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  SelectChangeEvent,
  Button,
  Chip,
  Avatar,
  useTheme,
  useMediaQuery,
  Divider
} from '@mui/material';
import {
  PlayArrow as PlayArrowIcon,
  Assessment as AssessmentIcon,
  School as SchoolIcon
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import { Course } from '../../api/courses';
import { DifficultyLevel } from '../../api/questions';

interface CourseSelectionCardProps {
  courses: Course[];
  selectedCourse: string;
  selectedDifficulty: DifficultyLevel | '';
  isLoadingCourses: boolean;
  onCourseChange: (event: SelectChangeEvent) => void;
  onDifficultyChange: (event: SelectChangeEvent) => void;
  onPracticeClick: (courseId: number) => void;
  onExamClick: (courseId: number) => void;
}

const CourseSelectionCard: React.FC<CourseSelectionCardProps> = ({
  courses,
  selectedCourse,
  selectedDifficulty,
  isLoadingCourses,
  onCourseChange,
  onDifficultyChange,
  onPracticeClick,
  onExamClick
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  
  // Animation variants
  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        type: 'spring',
        stiffness: 100,
        damping: 15
      }
    }
  };
  
  const MotionPaper = motion.create(Paper);

  return (
    <MotionPaper
      variants={itemVariants}
      sx={{ 
        p: isMobile ? 2 : 3, 
        mb: isMobile ? 2 : 3, 
        borderRadius: 2, 
        maxWidth: '100%',
        border: `1px solid ${theme.palette.divider}`
      }}
    >
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
        <Avatar
          sx={{
            bgcolor: theme.palette.primary.main,
            mr: 1.5,
            width: 36,
            height: 36
          }}
        >
          <SchoolIcon fontSize="small" />
        </Avatar>
        <Typography variant={isMobile ? "subtitle1" : "h6"} fontWeight="bold">
          Select Course
        </Typography>
      </Box>

      <FormControl fullWidth variant="outlined" size={isMobile ? "small" : "medium"} sx={{ mb: 2 }}>
        <InputLabel id="course-select-label">Course</InputLabel>
        <Select
          labelId="course-select-label"
          id="course-select"
          value={selectedCourse}
          onChange={onCourseChange}
          label="Course"
          disabled={isLoadingCourses || courses.length === 0}
        >
          <MenuItem value="">
            <em>Select a course</em>
          </MenuItem>
          {courses.map((course) => (
            <MenuItem key={course.id} value={course.id.toString()}>
              {course.name}
            </MenuItem>
          ))}
        </Select>
      </FormControl>

      <FormControl fullWidth variant="outlined" size={isMobile ? "small" : "medium"} sx={{ mb: 2 }}>
        <InputLabel id="difficulty-select-label">Difficulty</InputLabel>
        <Select
          labelId="difficulty-select-label"
          id="difficulty-select"
          value={selectedDifficulty}
          onChange={onDifficultyChange}
          label="Difficulty"
          disabled={!selectedCourse}
        >
          <MenuItem value="">
            <em>All Difficulties</em>
          </MenuItem>
          <MenuItem value="easy">
            <Chip 
              label="Easy" 
              size="small" 
              color="success" 
              sx={{ height: 24, fontSize: '0.75rem' }} 
            />
          </MenuItem>
          <MenuItem value="medium">
            <Chip 
              label="Medium" 
              size="small" 
              color="warning" 
              sx={{ height: 24, fontSize: '0.75rem' }} 
            />
          </MenuItem>
          <MenuItem value="hard">
            <Chip 
              label="Hard" 
              size="small" 
              color="error" 
              sx={{ height: 24, fontSize: '0.75rem' }} 
            />
          </MenuItem>
        </Select>
      </FormControl>

      {selectedCourse && (
        <>
          <Divider sx={{ my: 2 }} />
          <Box sx={{ 
            mt: 2, 
            display: 'flex', 
            flexDirection: isMobile ? 'column' : 'row',
            gap: 1.5 
          }}>
            <Button
              variant="contained"
              color="primary"
              fullWidth
              size={isMobile ? "medium" : "large"}
              startIcon={<PlayArrowIcon />}
              onClick={() => onPracticeClick(parseInt(selectedCourse))}
              sx={{ borderRadius: 1.5 }}
            >
              Practice Mode
            </Button>
            <Button
              variant="contained"
              color="secondary"
              fullWidth
              size={isMobile ? "medium" : "large"}
              startIcon={<AssessmentIcon />}
              onClick={() => onExamClick(parseInt(selectedCourse))}
              sx={{ borderRadius: 1.5 }}
            >
              Exam Mode
            </Button>
          </Box>
        </>
      )}
    </MotionPaper>
  );
};

export default CourseSelectionCard;
