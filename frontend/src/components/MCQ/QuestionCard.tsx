import React from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  Card,
  CardContent,
  CardActions,
  Button,
  Chip,
  IconButton,
  Tooltip,
  useTheme,
  useMediaQuery,
  Avatar
} from '@mui/material';
import {
  Bookmark as BookmarkIcon,
  BookmarkBorder as BookmarkBorderIcon,
  School as SchoolIcon,
  Timer as TimerIcon,
  PlayArrow as PlayArrowIcon
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import { Question } from '../../api/questions';
import { Bookmark } from '../../api/studentProgress';

interface QuestionCardProps {
  question: Question;
  isBookmarked: boolean;
  onBookmarkToggle: () => void;
  onViewDetails: () => void;
  onPracticeQuestion: () => void;
  getCourseName: (courseId: number) => string;
}

const QuestionCard: React.FC<QuestionCardProps> = ({
  question,
  isBookmarked,
  onBookmarkToggle,
  onViewDetails,
  onPracticeQuestion,
  getCourseName
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  // Animation variants
  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        type: 'spring',
        stiffness: 100,
        damping: 15
      }
    }
  };

  const MotionCard = motion.create(Card);

  // Get difficulty color
  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'easy':
        return 'success';
      case 'medium':
        return 'warning';
      case 'hard':
        return 'error';
      default:
        return 'default';
    }
  };

  return (
    <MotionCard
      variants={itemVariants}
      sx={{
        position: 'relative',
        transition: 'transform 0.3s ease, box-shadow 0.3s ease',
        borderRadius: 2,
        overflow: 'hidden',
        border: `1px solid ${theme.palette.divider}`,
        '&:hover': {
          transform: 'translateY(-4px)',
          boxShadow: theme.shadows[4]
        },
      }}
    >
      <Box
        sx={{
          position: 'absolute',
          top: 12,
          right: 12,
          zIndex: 1
        }}
      >
        <IconButton
          size="small"
          onClick={onBookmarkToggle}
          sx={{
            bgcolor: theme.palette.background.paper,
            boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
            '&:hover': {
              bgcolor: theme.palette.background.paper,
            }
          }}
        >
          {isBookmarked ? (
            <BookmarkIcon fontSize="small" color="primary" />
          ) : (
            <BookmarkBorderIcon fontSize="small" />
          )}
        </IconButton>
      </Box>

      <CardContent sx={{
        p: isMobile ? 2 : 3,
        pb: isMobile ? 1 : 2
      }}>
        <Box sx={{
          display: 'flex',
          alignItems: 'flex-start',
          mb: 1.5,
          gap: 1.5
        }}>
          <Avatar
            sx={{
              bgcolor: theme.palette.primary.main,
              width: 32,
              height: 32
            }}
          >
            <Typography variant="body2" fontWeight="bold">
              {question.id}
            </Typography>
          </Avatar>

          <Box sx={{ flex: 1 }}>
            <Typography
              variant="subtitle1"
              component="div"
              fontWeight="medium"
              sx={{
                mb: 1,
                pr: 4 // Space for bookmark icon
              }}
            >
              {question.content.length > 100
                ? `${question.content.substring(0, 100)}...`
                : question.content}
            </Typography>

            <Box sx={{
              display: 'flex',
              flexWrap: 'wrap',
              alignItems: 'center',
              gap: 1,
              mb: 1
            }}>
              <Chip
                size="small"
                label={question.difficulty}
                color={getDifficultyColor(question.difficulty) as any}
                sx={{ height: 24, fontSize: '0.75rem' }}
              />

              <Box sx={{
                display: 'flex',
                alignItems: 'center',
                color: 'text.secondary',
                fontSize: '0.75rem'
              }}>
                <SchoolIcon fontSize="inherit" sx={{ mr: 0.5 }} />
                {getCourseName(question.course_id)}
              </Box>

              {question.time_limit && (
                <Box sx={{
                  display: 'flex',
                  alignItems: 'center',
                  color: 'text.secondary',
                  fontSize: '0.75rem'
                }}>
                  <TimerIcon fontSize="inherit" sx={{ mr: 0.5 }} />
                  {question.time_limit} min
                </Box>
              )}
            </Box>
          </Box>
        </Box>
      </CardContent>

      <CardActions sx={{
        p: isMobile ? '0 16px 16px' : '0 24px 24px',
        display: 'flex',
        justifyContent: 'space-between'
      }}>
        <Button
          size="small"
          variant="outlined"
          onClick={onViewDetails}
          sx={{ borderRadius: 1.5 }}
        >
          View Details
        </Button>
        <Button
          size="small"
          variant="contained"
          color="primary"
          startIcon={<PlayArrowIcon />}
          onClick={onPracticeQuestion}
          sx={{ borderRadius: 1.5 }}
        >
          Practice
        </Button>
      </CardActions>
    </MotionCard>
  );
};

export default QuestionCard;
