import React, { useState, useEffect, useRef } from 'react';
import {
  Box,
  TextField,
  Paper,
  Typography,
  InputAdornment,
  useTheme,
  useMediaQuery,
} from '@mui/material';
import { Search as SearchIcon } from '@mui/icons-material';
import { Course } from '../../api/courses';
import { Department } from '../../api/departments';

interface CourseSearchInputProps {
  courses: Course[];
  departments: Department[];
  onCourseSelect: (courseId: string) => void;
  placeholder?: string;
}

const CourseSearchInput: React.FC<CourseSearchInputProps> = ({
  courses,
  departments,
  onCourseSelect,
  placeholder = "Search courses by name or code"
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  
  // Local state - completely isolated
  const [query, setQuery] = useState('');
  const [results, setResults] = useState<Course[]>([]);
  const [showResults, setShowResults] = useState(false);
  
  // Refs to maintain focus
  const inputRef = useRef<HTMLInputElement>(null);
  const containerRef = useRef<HTMLDivElement>(null);

  // Search logic with debounce
  useEffect(() => {
    if (!query.trim()) {
      setResults([]);
      setShowResults(false);
      return;
    }

    const timeoutId = setTimeout(() => {
      const searchTerm = query.toLowerCase();
      const filtered = courses.filter(course =>
        course.name.toLowerCase().includes(searchTerm) ||
        course.code.toLowerCase().includes(searchTerm) ||
        course.description.toLowerCase().includes(searchTerm)
      );
      setResults(filtered);
      setShowResults(true);
    }, 150);

    return () => clearTimeout(timeoutId);
  }, [query, courses]);

  // Handle input change
  const handleInputChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setQuery(event.target.value);
  };

  // Handle course selection
  const handleCourseSelect = (course: Course) => {
    onCourseSelect(course.id.toString());
    setQuery('');
    setShowResults(false);
    // Keep focus on input
    if (inputRef.current) {
      inputRef.current.focus();
    }
  };

  // Handle click outside to close dropdown
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (containerRef.current && !containerRef.current.contains(event.target as Node)) {
        setShowResults(false);
      }
    };

    document.addEventListener('mousedown', handleClickOutside);
    return () => document.removeEventListener('mousedown', handleClickOutside);
  }, []);

  return (
    <Box ref={containerRef} sx={{ position: 'relative', width: '100%' }}>
      <TextField
        ref={inputRef}
        fullWidth
        variant="outlined"
        size={isMobile ? "small" : "medium"}
        placeholder={placeholder}
        value={query}
        onChange={handleInputChange}
        onFocus={() => {
          if (query && results.length > 0) {
            setShowResults(true);
          }
        }}
        InputProps={{
          startAdornment: (
            <InputAdornment position="start">
              <SearchIcon fontSize="small" />
            </InputAdornment>
          ),
        }}
        sx={{ mb: 1 }}
      />

      {/* Search Results Dropdown */}
      {showResults && (
        <Paper
          sx={{
            position: 'absolute',
            top: '100%',
            left: 0,
            right: 0,
            zIndex: 1300,
            maxHeight: 300,
            overflow: 'auto',
            border: 1,
            borderColor: 'divider',
            borderRadius: 1,
            mt: 0.5,
          }}
        >
          {results.length > 0 ? (
            results.map((course) => (
              <Box
                key={course.id}
                sx={{
                  p: 2,
                  cursor: 'pointer',
                  borderBottom: 1,
                  borderColor: 'divider',
                  '&:hover': {
                    backgroundColor: 'action.hover'
                  },
                  '&:last-child': {
                    borderBottom: 0
                  }
                }}
                onClick={() => handleCourseSelect(course)}
              >
                <Typography variant="body1" fontWeight="medium">
                  {course.name}
                </Typography>
                <Typography variant="caption" color="text.secondary">
                  {course.code} • {departments.find(d => d.id === course.department_id)?.name || 'Unknown Department'}
                </Typography>
              </Box>
            ))
          ) : (
            <Box sx={{ p: 2, textAlign: 'center' }}>
              <Typography variant="body2" color="text.secondary">
                No courses found matching "{query}"
              </Typography>
            </Box>
          )}
        </Paper>
      )}
    </Box>
  );
};

export default CourseSearchInput;
