import React, { useState } from 'react';
import {
  <PERSON>alog,
  Dialog<PERSON><PERSON>le,
  DialogContent,
  DialogActions,
  Button,
  FormGroup,
  FormControlLabel,
  Checkbox,
  TextField,
  Typography,
  Box,
  Alert,
  CircularProgress,
  Chip,
  Divider
} from '@mui/material';
import {
  Flag as FlagIcon,
  Close as CloseIcon,
  Send as SendIcon
} from '@mui/icons-material';
import { motion } from 'framer-motion';

interface FlagReason {
  value: string;
  label: string;
  description: string;
}

interface QuestionFlagModalProps {
  open: boolean;
  onClose: () => void;
  questionId: number;
  questionContent: string;
  onSubmit: (reasons: string[], description?: string) => Promise<void>;
}

const FLAG_REASONS: FlagReason[] = [
  {
    value: 'wrong_answer',
    label: 'Wrong Answer',
    description: 'The correct answer is marked incorrectly'
  },
  {
    value: 'incorrect_spelling',
    label: 'Incorrect Spelling',
    description: 'There are spelling or grammar errors'
  },
  {
    value: 'not_visible',
    label: 'Content Not Visible',
    description: 'Question content is not displaying properly'
  },
  {
    value: 'no_diagram',
    label: 'Missing Diagram',
    description: 'Question references a diagram that is missing'
  },
  {
    value: 'incorrect_rendering',
    label: 'Incorrect Math/Text Rendering',
    description: 'Mathematical expressions or formatting is broken'
  },
  {
    value: 'no_options',
    label: 'Missing Answer Options',
    description: 'Answer choices are missing or incomplete'
  },
  {
    value: 'unclear_question',
    label: 'Unclear Question',
    description: 'Question is confusing or ambiguous'
  },
  {
    value: 'duplicate_question',
    label: 'Duplicate Question',
    description: 'This question appears to be a duplicate'
  },
  {
    value: 'inappropriate_content',
    label: 'Inappropriate Content',
    description: 'Content is inappropriate or offensive'
  },
  {
    value: 'other',
    label: 'Other',
    description: 'Other issue not listed above'
  }
];

const QuestionFlagModal: React.FC<QuestionFlagModalProps> = ({
  open,
  onClose,
  questionId,
  questionContent,
  onSubmit
}) => {
  const [selectedReasons, setSelectedReasons] = useState<string[]>([]);
  const [description, setDescription] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [success, setSuccess] = useState(false);

  const handleReasonChange = (reasonValue: string, checked: boolean) => {
    if (checked) {
      setSelectedReasons(prev => [...prev, reasonValue]);
    } else {
      setSelectedReasons(prev => prev.filter(r => r !== reasonValue));
    }
    setError(null);
  };

  const handleSubmit = async () => {
    if (selectedReasons.length === 0) {
      setError('Please select at least one reason for flagging this question.');
      return;
    }

    setIsSubmitting(true);
    setError(null);

    try {
      await onSubmit(selectedReasons, description.trim() || undefined);
      setSuccess(true);
      
      // Auto-close after success
      setTimeout(() => {
        handleClose();
      }, 2000);
    } catch (err: any) {
      setError(err.message || 'Failed to submit flag. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleClose = () => {
    if (!isSubmitting) {
      setSelectedReasons([]);
      setDescription('');
      setError(null);
      setSuccess(false);
      onClose();
    }
  };

  const getSelectedReasonLabels = () => {
    return FLAG_REASONS
      .filter(reason => selectedReasons.includes(reason.value))
      .map(reason => reason.label);
  };

  return (
    <Dialog
      open={open}
      onClose={handleClose}
      maxWidth="md"
      fullWidth
      PaperProps={{
        component: motion.div,
        initial: { opacity: 0, scale: 0.9 },
        animate: { opacity: 1, scale: 1 },
        exit: { opacity: 0, scale: 0.9 },
        transition: { duration: 0.2 }
      }}
    >
      <DialogTitle sx={{ display: 'flex', alignItems: 'center', gap: 1, pb: 1 }}>
        <FlagIcon color="warning" />
        <Typography variant="h6" component="span">
          Flag Question
        </Typography>
      </DialogTitle>

      <DialogContent>
        {success ? (
          <Alert severity="success" sx={{ mb: 2 }}>
            <Typography variant="body1" fontWeight="bold">
              Thank you for your feedback!
            </Typography>
            <Typography variant="body2">
              Your flag has been submitted and will be reviewed by our team.
            </Typography>
          </Alert>
        ) : (
          <>
            <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
              Help us improve by reporting issues with this question. Select all reasons that apply:
            </Typography>

            {error && (
              <Alert severity="error" sx={{ mb: 2 }}>
                {error}
              </Alert>
            )}

            <Box sx={{ mb: 3 }}>
              <Typography variant="subtitle2" gutterBottom>
                Question Preview:
              </Typography>
              <Box
                sx={{
                  p: 2,
                  bgcolor: 'grey.50',
                  borderRadius: 1,
                  border: 1,
                  borderColor: 'grey.200',
                  maxHeight: 100,
                  overflow: 'auto'
                }}
              >
                <Typography variant="body2" sx={{ fontSize: '0.875rem' }}>
                  {questionContent.length > 200 
                    ? `${questionContent.substring(0, 200)}...` 
                    : questionContent
                  }
                </Typography>
              </Box>
            </Box>

            <Typography variant="subtitle2" gutterBottom>
              What's wrong with this question?
            </Typography>

            <FormGroup sx={{ mb: 2 }}>
              {FLAG_REASONS.map((reason) => (
                <FormControlLabel
                  key={reason.value}
                  control={
                    <Checkbox
                      checked={selectedReasons.includes(reason.value)}
                      onChange={(e) => handleReasonChange(reason.value, e.target.checked)}
                      disabled={isSubmitting}
                    />
                  }
                  label={
                    <Box>
                      <Typography variant="body2" fontWeight="medium">
                        {reason.label}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        {reason.description}
                      </Typography>
                    </Box>
                  }
                  sx={{ mb: 1, alignItems: 'flex-start' }}
                />
              ))}
            </FormGroup>

            {selectedReasons.length > 0 && (
              <Box sx={{ mb: 2 }}>
                <Typography variant="caption" color="text.secondary">
                  Selected reasons:
                </Typography>
                <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, mt: 0.5 }}>
                  {getSelectedReasonLabels().map((label) => (
                    <Chip
                      key={label}
                      label={label}
                      size="small"
                      color="primary"
                      variant="outlined"
                    />
                  ))}
                </Box>
              </Box>
            )}

            <Divider sx={{ my: 2 }} />

            <TextField
              fullWidth
              multiline
              rows={3}
              label="Additional Details (Optional)"
              placeholder="Provide any additional information that might help us understand the issue..."
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              disabled={isSubmitting}
              sx={{ mb: 2 }}
            />
          </>
        )}
      </DialogContent>

      <DialogActions sx={{ px: 3, pb: 2 }}>
        <Button
          onClick={handleClose}
          disabled={isSubmitting}
          startIcon={<CloseIcon />}
        >
          {success ? 'Close' : 'Cancel'}
        </Button>
        
        {!success && (
          <Button
            onClick={handleSubmit}
            variant="contained"
            disabled={isSubmitting || selectedReasons.length === 0}
            startIcon={isSubmitting ? <CircularProgress size={16} /> : <SendIcon />}
            color="warning"
          >
            {isSubmitting ? 'Submitting...' : 'Submit Flag'}
          </Button>
        )}
      </DialogActions>
    </Dialog>
  );
};

export default QuestionFlagModal;
