import React from 'react';
import {
  Paper,
  BottomNavigation as MuiBottomNavigation,
  BottomNavigationAction,
  useTheme,
  Badge
} from '@mui/material';
import {
  Bookmark as BookmarkIcon,
  TrendingUp as TrendingUpIcon,
  School as CourseIcon,
  PlayArrow as PracticeIcon,
  Assessment as AssessmentIcon
} from '@mui/icons-material';
import { motion } from 'framer-motion';

interface MCQBottomNavigationProps {
  value: number;
  onChange: (event: React.SyntheticEvent, newValue: number) => void;
  bookmarksCount?: number;
}

const MCQBottomNavigation: React.FC<MCQBottomNavigationProps> = ({
  value,
  onChange,
  bookmarksCount = 0
}) => {
  const theme = useTheme();

  return (
    <Paper
      sx={{
        position: 'fixed',
        bottom: 0,
        left: 0,
        right: 0,
        zIndex: 1000,
        borderRadius: '16px 16px 0 0',
        overflow: 'hidden',
        boxShadow: '0 -2px 10px rgba(0,0,0,0.1)',
        height: '64px',
      }}
      elevation={3}
    >
      <MuiBottomNavigation
        showLabels
        value={value}
        onChange={onChange}
        sx={{
          height: '100%',
          '& .MuiBottomNavigationAction-root': {
            color: theme.palette.text.secondary,
            '&.Mui-selected': {
              color: theme.palette.primary.main,
            },
            minWidth: 0,
            padding: '6px 0',
            '& .MuiBottomNavigationAction-label': {
              fontSize: '0.65rem',
              marginTop: '2px'
            }
          },
        }}
      >
        <BottomNavigationAction
          label="Courses"
          icon={
            <motion.div
              animate={{
                scale: value === 0 ? 1.1 : 1,
                y: value === 0 ? -4 : 0
              }}
              transition={{ type: 'spring', stiffness: 400, damping: 10 }}
            >
              <CourseIcon />
            </motion.div>
          }
        />
        <BottomNavigationAction
          label="Exam"
          icon={
            <motion.div
              animate={{
                scale: value === 1 ? 1.1 : 1,
                y: value === 1 ? -4 : 0
              }}
              transition={{ type: 'spring', stiffness: 400, damping: 10 }}
            >
              <AssessmentIcon />
            </motion.div>
          }
        />
        <BottomNavigationAction
          label="Practice"
          icon={
            <motion.div
              animate={{
                scale: value === 2 ? 1.1 : 1,
                y: value === 2 ? -4 : 0
              }}
              transition={{ type: 'spring', stiffness: 400, damping: 10 }}
            >
              <PracticeIcon />
            </motion.div>
          }
        />
        <BottomNavigationAction
          label="Bookmarks"
          icon={
            <motion.div
              animate={{
                scale: value === 3 ? 1.1 : 1,
                y: value === 3 ? -4 : 0
              }}
              transition={{ type: 'spring', stiffness: 400, damping: 10 }}
            >
              {bookmarksCount > 0 ? (
                <Badge color="secondary" badgeContent={bookmarksCount > 99 ? '99+' : bookmarksCount}>
                  <BookmarkIcon />
                </Badge>
              ) : (
                <BookmarkIcon />
              )}
            </motion.div>
          }
        />
        <BottomNavigationAction
          label="Stats"
          icon={
            <motion.div
              animate={{
                scale: value === 4 ? 1.1 : 1,
                y: value === 4 ? -4 : 0
              }}
              transition={{ type: 'spring', stiffness: 400, damping: 10 }}
            >
              <TrendingUpIcon />
            </motion.div>
          }
        />
      </MuiBottomNavigation>
    </Paper>
  );
};

export default MCQBottomNavigation;
