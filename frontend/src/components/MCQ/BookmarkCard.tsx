import React from 'react';
import {
  <PERSON>,
  Typo<PERSON>,
  Card,
  CardContent,
  CardActions,
  Button,
  Chip,
  IconButton,
  Tooltip,
  useTheme,
  useMediaQuery,
  Avatar
} from '@mui/material';
import {
  Bookmark as BookmarkIcon,
  Delete as DeleteIcon,
  School as SchoolIcon,
  Edit as EditIcon
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import { Bookmark } from '../../api/studentProgress';

interface BookmarkCardProps {
  bookmark: Bookmark;
  onRemoveBookmark: () => void;
  onViewQuestion: () => void;
  onEditNotes?: () => void;
  getCourseName: (courseId: number) => string;
}

const BookmarkCard: React.FC<BookmarkCardProps> = ({
  bookmark,
  onRemoveBookmark,
  onViewQuestion,
  onEditNotes,
  getCourseName
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  // Animation variants
  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        type: 'spring',
        stiffness: 100,
        damping: 15
      }
    }
  };

  const MotionCard = motion.create(Card);

  // Get difficulty color
  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'easy':
        return 'success';
      case 'medium':
        return 'warning';
      case 'hard':
        return 'error';
      default:
        return 'default';
    }
  };

  return (
    <MotionCard
      variants={itemVariants}
      sx={{
        position: 'relative',
        transition: 'transform 0.3s ease, box-shadow 0.3s ease',
        borderRadius: 2,
        overflow: 'hidden',
        border: `1px solid ${theme.palette.divider}`,
        '&:hover': {
          transform: 'translateY(-4px)',
          boxShadow: theme.shadows[4]
        },
      }}
    >
      <Box
        sx={{
          position: 'absolute',
          top: 12,
          right: 12,
          zIndex: 1,
          display: 'flex',
          gap: 1
        }}
      >
        {onEditNotes && (
          <IconButton
            size="small"
            onClick={onEditNotes}
            sx={{
              bgcolor: theme.palette.background.paper,
              boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
              '&:hover': {
                bgcolor: theme.palette.background.paper,
              }
            }}
          >
            <EditIcon fontSize="small" />
          </IconButton>
        )}

        <IconButton
          size="small"
          onClick={onRemoveBookmark}
          sx={{
            bgcolor: theme.palette.background.paper,
            boxShadow: '0 2px 8px rgba(0,0,0,0.1)',
            '&:hover': {
              bgcolor: theme.palette.background.paper,
            }
          }}
        >
          <DeleteIcon fontSize="small" color="error" />
        </IconButton>
      </Box>

      <CardContent sx={{
        p: isMobile ? 2 : 3,
        pb: isMobile ? 1 : 2
      }}>
        <Box sx={{
          display: 'flex',
          alignItems: 'flex-start',
          mb: 1.5,
          gap: 1.5
        }}>
          <Avatar
            sx={{
              bgcolor: theme.palette.secondary.main,
              width: 32,
              height: 32
            }}
          >
            <BookmarkIcon fontSize="small" />
          </Avatar>

          <Box sx={{ flex: 1 }}>
            <Typography
              variant="subtitle1"
              component="div"
              fontWeight="medium"
              sx={{
                mb: 1,
                pr: 8 // Space for action icons
              }}
            >
              {`Question #${bookmark.question_id}`}
            </Typography>

            <Box sx={{
              display: 'flex',
              flexWrap: 'wrap',
              alignItems: 'center',
              gap: 1,
              mb: 1
            }}>
              <Chip
                size="small"
                label={bookmark.difficulty}
                color={getDifficultyColor(bookmark.difficulty) as any}
                sx={{ height: 24, fontSize: '0.75rem' }}
              />

              <Box sx={{
                display: 'flex',
                alignItems: 'center',
                color: 'text.secondary',
                fontSize: '0.75rem'
              }}>
                <SchoolIcon fontSize="inherit" sx={{ mr: 0.5 }} />
                {getCourseName(bookmark.course_id)}
              </Box>

              <Typography variant="caption" color="text.secondary">
                Bookmarked: {new Date(bookmark.created_at).toLocaleDateString()}
              </Typography>
            </Box>

            {bookmark.notes && (
              <Box sx={{
                mt: 1.5,
                p: 1.5,
                bgcolor: theme.palette.mode === 'light' ? 'rgba(0, 0, 0, 0.02)' : 'rgba(255, 255, 255, 0.02)',
                borderRadius: 1.5,
                border: `1px solid ${theme.palette.divider}`
              }}>
                <Typography variant="caption" color="text.secondary" display="block" gutterBottom>
                  Your Notes:
                </Typography>
                <Typography variant="body2">
                  {bookmark.notes}
                </Typography>
              </Box>
            )}
          </Box>
        </Box>
      </CardContent>

      <CardActions sx={{
        p: isMobile ? '0 16px 16px' : '0 24px 24px',
        justifyContent: 'flex-end'
      }}>
        <Button
          size="small"
          variant="contained"
          color="primary"
          onClick={onViewQuestion}
          sx={{ borderRadius: 1.5 }}
        >
          Practice Question
        </Button>
      </CardActions>
    </MotionCard>
  );
};

export default BookmarkCard;
