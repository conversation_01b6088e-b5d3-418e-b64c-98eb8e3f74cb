import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  SelectChangeEvent,
  Button,
  Chip,
  Avatar,
  useTheme,
  useMediaQuery,
  Divider,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Slider,
  IconButton,
  Tooltip,
  CircularProgress,
} from '@mui/material';
import {
  PlayArrow as PlayArrowIcon,
  Assessment as AssessmentIcon,
  School as SchoolIcon,
  FilterList as FilterIcon,
  Timer as TimerIcon,
  QuestionMark as QuestionIcon,
  Close as CloseIcon,
  Info as InfoIcon,
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import { Course } from '../../api/courses';
import { DifficultyLevel } from '../../api/questions';
import { Department } from '../../api/departments';
import { School } from '../../api/schools';
import CourseSearchInput from './CourseSearchInput';

interface EnhancedCourseSelectionProps {
  courses: Course[];
  departments: Department[];
  schools: School[];
  selectedCourse: string;
  selectedDifficulty: DifficultyLevel | '';
  isLoadingCourses: boolean;
  onCourseChange: (event: SelectChangeEvent) => void;
  onDifficultyChange: (event: SelectChangeEvent) => void;
  onPracticeClick: (courseId: number) => void;
  onExamClick: (courseId: number, examConfig: ExamConfig) => void;
}

export interface ExamConfig {
  timeLimit: number; // in minutes
  questionCount: number;
}

const EnhancedCourseSelection: React.FC<EnhancedCourseSelectionProps> = ({
  courses,
  departments,
  schools,
  selectedCourse,
  selectedDifficulty,
  isLoadingCourses,
  onCourseChange,
  onDifficultyChange,
  onPracticeClick,
  onExamClick
}) => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  
  // State for filters
  const [selectedDepartment, setSelectedDepartment] = useState<string>('');
  const [selectedSchool, setSelectedSchool] = useState<string>('');
  const [filteredCourses, setFilteredCourses] = useState<Course[]>(courses);
  
  // State for exam configuration dialog
  const [examConfigOpen, setExamConfigOpen] = useState(false);
  const [examTimeLimit, setExamTimeLimit] = useState(60); // Default: 60 minutes
  const [examQuestionCount, setExamQuestionCount] = useState(60); // Default: 60 questions
  const [maxQuestions, setMaxQuestions] = useState(60); // Will be updated based on selected course
  
  // Animation variants
  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        type: 'spring',
        stiffness: 100,
        damping: 15
      }
    }
  };
  
  const MotionPaper = motion.create(Paper);

  // Filter departments based on selected school
  const filteredDepartments = selectedSchool
    ? departments.filter(dept => dept.school_id === parseInt(selectedSchool))
    : departments;

  // Filter courses for main dropdown based on department/school filters only
  useEffect(() => {
    let filtered = [...courses];

    // Filter by department (which implicitly filters by school)
    if (selectedDepartment) {
      filtered = filtered.filter(course =>
        course.department_id === parseInt(selectedDepartment)
      );
    } else if (selectedSchool) {
      // If school is selected but no department, show courses from all departments in that school
      const schoolDepartmentIds = departments
        .filter(dept => dept.school_id === parseInt(selectedSchool))
        .map(dept => dept.id);
      filtered = filtered.filter(course =>
        schoolDepartmentIds.includes(course.department_id)
      );
    }

    setFilteredCourses(filtered);
  }, [courses, selectedDepartment, selectedSchool, departments]);

  // Reset selected course if it's not in the filtered results (only for department/school filters)
  useEffect(() => {
    if (selectedCourse && filteredCourses.length > 0) {
      const courseExists = filteredCourses.find(course => course.id.toString() === selectedCourse);
      if (!courseExists) {
        onCourseChange({ target: { value: '' } } as SelectChangeEvent);
      }
    }
  }, [selectedDepartment, selectedSchool]);

  // Reset department when school changes
  useEffect(() => {
    if (selectedSchool && selectedDepartment) {
      const isDepartmentInSchool = departments
        .filter(dept => dept.school_id === parseInt(selectedSchool))
        .some(dept => dept.id.toString() === selectedDepartment);
      if (!isDepartmentInSchool) {
        setSelectedDepartment('');
      }
    }
  }, [selectedSchool, selectedDepartment, departments]);
  
  // Update max questions when course changes
  useEffect(() => {
    if (selectedCourse && courses.length > 0) {
      const courseId = parseInt(selectedCourse);
      // In a real implementation, you would fetch the actual question count
      // For now, we'll use a placeholder value
      const newMaxQuestions = 100; // Placeholder
      setMaxQuestions(newMaxQuestions);

      // Set default question count (60 or all available)
      setExamQuestionCount(Math.min(60, newMaxQuestions));
    }
  }, [selectedCourse, courses]);
  




  // Handle opening exam config dialog
  const handleOpenExamConfig = () => {
    setExamConfigOpen(true);
  };

  // Handle closing exam config dialog
  const handleCloseExamConfig = () => {
    setExamConfigOpen(false);
  };
  
  // Handle starting exam with config
  const handleStartExam = () => {
    if (selectedCourse) {
      onExamClick(parseInt(selectedCourse), {
        timeLimit: examTimeLimit,
        questionCount: examQuestionCount
      });
      setExamConfigOpen(false);
    }
  };
  
  // Format time for display
  const formatTime = (minutes: number) => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    
    if (hours > 0) {
      return `${hours}h ${mins}m`;
    }
    return `${mins} minutes`;
  };

  return (
    <MotionPaper
      variants={itemVariants}
      sx={{ 
        p: isMobile ? 2 : 3, 
        mb: isMobile ? 2 : 3, 
        borderRadius: 2, 
        maxWidth: '100%',
        border: `1px solid ${theme.palette.divider}`
      }}
    >
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
        <Avatar
          sx={{
            bgcolor: theme.palette.primary.main,
            mr: 1.5,
            width: 36,
            height: 36
          }}
        >
          <SchoolIcon fontSize="small" />
        </Avatar>
        <Typography variant={isMobile ? "subtitle1" : "h6"} fontWeight="bold">
          Select Course
        </Typography>
      </Box>
      
      {/* Standalone Search Component */}
      <CourseSearchInput
        courses={courses}
        departments={departments}
        onCourseSelect={(courseId) => onCourseChange({ target: { value: courseId } } as SelectChangeEvent)}
      />

      {/* School and Department Filters */}
      <Box sx={{ display: 'flex', gap: 2, flexDirection: isMobile ? 'column' : 'row', mb: 2 }}>
          <FormControl fullWidth size={isMobile ? "small" : "medium"}>
            <InputLabel id="school-filter-label">School</InputLabel>
            <Select
              labelId="school-filter-label"
              value={selectedSchool}
              onChange={(e) => setSelectedSchool(e.target.value)}
              label="School"
            >
              <MenuItem value="">All Schools</MenuItem>
              {schools.map((school) => (
                <MenuItem key={school.id} value={school.id.toString()}>
                  {school.name}
                </MenuItem>
              ))}
            </Select>
          </FormControl>

          <FormControl fullWidth size={isMobile ? "small" : "medium"}>
            <InputLabel id="department-filter-label">Department</InputLabel>
            <Select
              labelId="department-filter-label"
              value={selectedDepartment}
              onChange={(e) => setSelectedDepartment(e.target.value)}
              label="Department"
            >
              <MenuItem value="">All Departments</MenuItem>
              {filteredDepartments.map((dept) => (
                <MenuItem key={dept.id} value={dept.id.toString()}>
                  {dept.name}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Box>

      <Divider sx={{ my: 2 }} />

      {/* Course Selection */}
      <FormControl fullWidth variant="outlined" size={isMobile ? "small" : "medium"} sx={{ mb: 2 }}>
        <InputLabel id="course-select-label">Course</InputLabel>
        <Select
          labelId="course-select-label"
          id="course-select"
          value={selectedCourse}
          onChange={onCourseChange}
          label="Course"
          disabled={isLoadingCourses || filteredCourses.length === 0}
          MenuProps={{
            PaperProps: {
              style: {
                maxHeight: 300,
              },
            },
          }}
        >
          <MenuItem value="">
            <em>Select a course</em>
          </MenuItem>
          {filteredCourses.length === 0 && courses.length > 0 ? (
            <MenuItem disabled>
              <em>No courses found matching your search</em>
            </MenuItem>
          ) : (
            filteredCourses.map((course) => (
              <MenuItem key={course.id} value={course.id.toString()}>
                <Box sx={{ display: 'flex', flexDirection: 'column' }}>
                  <Typography variant="body1">{course.name}</Typography>
                  <Typography variant="caption" color="text.secondary">
                    {course.code}
                  </Typography>
                </Box>
              </MenuItem>
            ))
          )}
        </Select>
      </FormControl>

      <FormControl fullWidth variant="outlined" size={isMobile ? "small" : "medium"} sx={{ mb: 2 }}>
        <InputLabel id="difficulty-select-label">Difficulty</InputLabel>
        <Select
          labelId="difficulty-select-label"
          id="difficulty-select"
          value={selectedDifficulty}
          onChange={onDifficultyChange}
          label="Difficulty"
          disabled={!selectedCourse}
        >
          <MenuItem value="">
            <em>All Difficulties</em>
          </MenuItem>
          <MenuItem value="easy">
            <Chip 
              label="Easy" 
              size="small" 
              color="success" 
              sx={{ height: 24, fontSize: '0.75rem' }} 
            />
          </MenuItem>
          <MenuItem value="medium">
            <Chip 
              label="Medium" 
              size="small" 
              color="warning" 
              sx={{ height: 24, fontSize: '0.75rem' }} 
            />
          </MenuItem>
          <MenuItem value="hard">
            <Chip 
              label="Hard" 
              size="small" 
              color="error" 
              sx={{ height: 24, fontSize: '0.75rem' }} 
            />
          </MenuItem>
        </Select>
      </FormControl>

      {selectedCourse && (
        <>
          <Divider sx={{ my: 2 }} />
          <Box sx={{ 
            mt: 2, 
            display: 'flex', 
            flexDirection: isMobile ? 'column' : 'row',
            gap: 1.5 
          }}>
            <Button
              variant="contained"
              color="primary"
              fullWidth
              size={isMobile ? "medium" : "large"}
              startIcon={<PlayArrowIcon />}
              onClick={() => onPracticeClick(parseInt(selectedCourse))}
              sx={{ borderRadius: 1.5 }}
            >
              Practice Mode
            </Button>
            <Button
              variant="contained"
              color="secondary"
              fullWidth
              size={isMobile ? "medium" : "large"}
              startIcon={<AssessmentIcon />}
              onClick={handleOpenExamConfig}
              sx={{ borderRadius: 1.5 }}
            >
              Exam Mode
            </Button>
          </Box>
        </>
      )}
      
      {/* Exam Configuration Dialog */}
      <Dialog 
        open={examConfigOpen} 
        onClose={handleCloseExamConfig}
        maxWidth="sm"
        fullWidth
        PaperProps={{
          sx: {
            borderRadius: 2,
            boxShadow: '0 8px 32px rgba(0,0,0,0.12)',
          }
        }}
      >
        <DialogTitle sx={{ 
          bgcolor: theme.palette.secondary.main, 
          color: 'white',
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center'
        }}>
          <Box sx={{ display: 'flex', alignItems: 'center' }}>
            <AssessmentIcon sx={{ mr: 1 }} />
            Configure Exam
          </Box>
          <IconButton size="small" onClick={handleCloseExamConfig} sx={{ color: 'white' }}>
            <CloseIcon />
          </IconButton>
        </DialogTitle>
        <DialogContent sx={{ pt: 3 }}>
          <Typography variant="subtitle1" gutterBottom>
            Customize your exam settings for {courses.find(c => c.id.toString() === selectedCourse)?.name}
          </Typography>
          
          {/* Time Limit Slider */}
          <Box sx={{ mt: 4, mb: 3 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
              <TimerIcon color="primary" sx={{ mr: 1 }} />
              <Typography variant="subtitle1" fontWeight="medium">
                Time Limit: {formatTime(examTimeLimit)}
              </Typography>
            </Box>
            <Slider
              value={examTimeLimit}
              onChange={(_, value) => setExamTimeLimit(value as number)}
              min={15}
              max={180}
              step={5}
              marks={[
                { value: 15, label: '15m' },
                { value: 60, label: '1h' },
                { value: 120, label: '2h' },
                { value: 180, label: '3h' },
              ]}
              valueLabelDisplay="auto"
              valueLabelFormat={(value) => formatTime(value)}
              color="primary"
            />
          </Box>
          
          {/* Question Count Slider */}
          <Box sx={{ mt: 4, mb: 3 }}>
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
              <QuestionIcon color="primary" sx={{ mr: 1 }} />
              <Typography variant="subtitle1" fontWeight="medium">
                Number of Questions: {examQuestionCount}
              </Typography>
              <Tooltip title="Maximum available questions for this course">
                <IconButton size="small" sx={{ ml: 1 }}>
                  <InfoIcon fontSize="small" />
                </IconButton>
              </Tooltip>
            </Box>
            <Slider
              value={examQuestionCount}
              onChange={(_, value) => setExamQuestionCount(value as number)}
              min={10}
              max={maxQuestions}
              step={5}
              marks={[
                { value: 10, label: '10' },
                { value: 30, label: '30' },
                { value: 60, label: '60' },
                { value: maxQuestions, label: `${maxQuestions}` },
              ]}
              valueLabelDisplay="auto"
              color="secondary"
            />
          </Box>
          
          <Box sx={{ mt: 3, p: 2, bgcolor: 'rgba(0,0,0,0.03)', borderRadius: 1 }}>
            <Typography variant="body2" color="text.secondary">
              Default settings: 1 hour time limit and 60 questions (or all available questions if less than 60).
            </Typography>
          </Box>
        </DialogContent>
        <DialogActions sx={{ px: 3, py: 2 }}>
          <Button onClick={handleCloseExamConfig} variant="outlined">
            Cancel
          </Button>
          <Button 
            onClick={handleStartExam} 
            variant="contained" 
            color="secondary"
            startIcon={<AssessmentIcon />}
          >
            Start Exam
          </Button>
        </DialogActions>
      </Dialog>
    </MotionPaper>
  );
};

export default EnhancedCourseSelection;
