import React from 'react';
import {
  Box,
  Typography,
  LinearProgress,
  Paper,
  Chip,
  IconButton,
  useTheme,
  alpha,
  Tooltip
} from '@mui/material';
import {
  CloudUpload as CloudUploadIcon,
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon,
  Cancel as CancelIcon,
  Speed as SpeedIcon,
  Schedule as ScheduleIcon,
  InsertDriveFile as FileIcon
} from '@mui/icons-material';
import { motion, AnimatePresence } from 'framer-motion';
import { UploadTaskResponse } from '../../types/studentTools';

interface AnimatedProgressBarProps {
  upload: UploadTaskResponse;
  onCancel?: (uploadId: string) => void;
  showDetails?: boolean;
  compact?: boolean;
}

const AnimatedProgressBar: React.FC<AnimatedProgressBarProps> = ({
  upload,
  onCancel,
  showDetails = true,
  compact = false
}) => {
  const theme = useTheme();

  const getStatusColor = () => {
    switch (upload.status) {
      case 'pending':
      case 'uploading':
      case 'processing':
        return theme.palette.primary.main;
      case 'completed':
        return theme.palette.success.main;
      case 'failed':
        return theme.palette.error.main;
      case 'cancelled':
        return theme.palette.warning.main;
      default:
        return theme.palette.grey[500];
    }
  };

  const getStatusIcon = () => {
    switch (upload.status) {
      case 'pending':
      case 'uploading':
      case 'processing':
        return <CloudUploadIcon />;
      case 'completed':
        return <CheckCircleIcon />;
      case 'failed':
        return <ErrorIcon />;
      case 'cancelled':
        return <CancelIcon />;
      default:
        return <FileIcon />;
    }
  };

  const getStatusText = () => {
    switch (upload.status) {
      case 'pending':
        return 'Pending...';
      case 'uploading':
        return 'Uploading...';
      case 'processing':
        return 'Processing...';
      case 'completed':
        return 'Upload Complete';
      case 'failed':
        return 'Upload Failed';
      case 'cancelled':
        return 'Upload Cancelled';
      default:
        return 'Unknown Status';
    }
  };

  const formatFileSize = (bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  };

  const formatSpeed = (bytesPerSecond: number): string => {
    return formatFileSize(bytesPerSecond) + '/s';
  };

  const formatTime = (seconds: number): string => {
    if (!seconds || !isFinite(seconds)) return 'Unknown';
    
    const hours = Math.floor(seconds / 3600);
    const minutes = Math.floor((seconds % 3600) / 60);
    const secs = Math.floor(seconds % 60);
    
    if (hours > 0) {
      return `${hours}h ${minutes}m ${secs}s`;
    } else if (minutes > 0) {
      return `${minutes}m ${secs}s`;
    } else {
      return `${secs}s`;
    }
  };

  const progressVariants = {
    initial: { scaleX: 0, opacity: 0 },
    animate: { 
      scaleX: 1, 
      opacity: 1,
      transition: { duration: 0.5, ease: "easeOut" }
    },
    exit: { 
      scaleX: 0, 
      opacity: 0,
      transition: { duration: 0.3 }
    }
  };

  const containerVariants = {
    initial: { y: 20, opacity: 0 },
    animate: { 
      y: 0, 
      opacity: 1,
      transition: { duration: 0.4, ease: "easeOut" }
    },
    exit: { 
      y: -20, 
      opacity: 0,
      transition: { duration: 0.3 }
    }
  };

  return (
    <AnimatePresence>
      <motion.div
        variants={containerVariants}
        initial="initial"
        animate="animate"
        exit="exit"
        layout
      >
        <Paper
          elevation={2}
          sx={{
            p: compact ? { xs: 1.5, sm: 2 } : { xs: 2, sm: 3 },
            mb: 2,
            borderRadius: 3,
            background: `linear-gradient(135deg, ${alpha(getStatusColor(), 0.05)} 0%, ${alpha(getStatusColor(), 0.02)} 100%)`,
            border: `1px solid ${alpha(getStatusColor(), 0.1)}`,
            position: 'relative',
            overflow: 'hidden'
          }}
        >
          {/* Animated background gradient for uploading */}
          {upload.status === 'uploading' && (
            <motion.div
              animate={{
                x: ['-100%', '100%'],
              }}
              transition={{
                duration: 2,
                repeat: Infinity,
                ease: 'linear'
              }}
              style={{
                position: 'absolute',
                top: 0,
                left: 0,
                right: 0,
                bottom: 0,
                background: `linear-gradient(90deg, transparent 0%, ${alpha(theme.palette.primary.main, 0.1)} 50%, transparent 100%)`,
                pointerEvents: 'none'
              }}
            />
          )}

          <Box sx={{ position: 'relative', zIndex: 1 }}>
            {/* Header */}
            <Box sx={{ display: 'flex', alignItems: 'center', mb: compact ? 1 : 2 }}>
              <motion.div
                animate={{
                  rotate: upload.status === 'uploading' ? 360 : 0,
                  scale: upload.status === 'completed' ? [1, 1.2, 1] : 1
                }}
                transition={{
                  rotate: { duration: 2, repeat: upload.status === 'uploading' ? Infinity : 0, ease: 'linear' },
                  scale: { duration: 0.6, ease: 'easeOut' }
                }}
              >
                <Box
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    width: compact ? { xs: 28, sm: 32 } : { xs: 36, sm: 40 },
                    height: compact ? { xs: 28, sm: 32 } : { xs: 36, sm: 40 },
                    borderRadius: '50%',
                    backgroundColor: alpha(getStatusColor(), 0.1),
                    color: getStatusColor(),
                    mr: { xs: 1.5, sm: 2 },
                    '& svg': {
                      fontSize: compact ? { xs: '1rem', sm: '1.25rem' } : { xs: '1.25rem', sm: '1.5rem' }
                    }
                  }}
                >
                  {getStatusIcon()}
                </Box>
              </motion.div>

              <Box sx={{ flex: 1, minWidth: 0 }}>
                <Typography
                  variant={compact ? "body2" : "subtitle1"}
                  fontWeight="600"
                  noWrap
                  sx={{
                    color: theme.palette.text.primary,
                    fontSize: compact ? { xs: '0.8rem', sm: '0.875rem' } : { xs: '0.9rem', sm: '1rem' }
                  }}
                >
                  {upload.fileName}
                </Typography>
                <Typography
                  variant="caption"
                  sx={{
                    color: theme.palette.text.secondary,
                    fontSize: { xs: '0.7rem', sm: '0.75rem' },
                    display: 'block'
                  }}
                >
                  {formatFileSize(upload.fileSize)}
                  {upload.courseName && (
                    <>
                      <br />
                      <span style={{ fontSize: '0.65rem' }}>
                        {upload.courseName}
                      </span>
                    </>
                  )}
                </Typography>
              </Box>

              <Box sx={{ display: 'flex', alignItems: 'center', gap: { xs: 0.5, sm: 1 }, flexShrink: 0 }}>
                <Chip
                  label={getStatusText()}
                  size="small"
                  sx={{
                    backgroundColor: alpha(getStatusColor(), 0.1),
                    color: getStatusColor(),
                    fontWeight: 600,
                    fontSize: { xs: '0.7rem', sm: '0.75rem' },
                    height: { xs: 24, sm: 28 },
                    '& .MuiChip-label': {
                      px: { xs: 1, sm: 1.5 }
                    }
                  }}
                />

                {['pending', 'uploading', 'processing'].includes(upload.status) && onCancel && (
                  <Tooltip title="Cancel Upload">
                    <IconButton
                      size="small"
                      onClick={() => onCancel(upload.id)}
                      sx={{
                        color: theme.palette.text.secondary,
                        p: { xs: 0.5, sm: 1 }
                      }}
                    >
                      <CancelIcon fontSize="small" />
                    </IconButton>
                  </Tooltip>
                )}
              </Box>
            </Box>

            {/* Progress Bar */}
            <Box sx={{ mb: showDetails ? 2 : 0 }}>
              <motion.div
                variants={progressVariants}
                initial="initial"
                animate="animate"
                style={{ transformOrigin: 'left' }}
              >
                <LinearProgress
                  variant="determinate"
                  value={upload.progress}
                  sx={{
                    height: compact ? 6 : 8,
                    borderRadius: 3,
                    backgroundColor: alpha(getStatusColor(), 0.1),
                    '& .MuiLinearProgress-bar': {
                      borderRadius: 3,
                      backgroundColor: getStatusColor(),
                      background: upload.status === 'uploading' 
                        ? `linear-gradient(45deg, ${getStatusColor()} 25%, transparent 25%, transparent 50%, ${getStatusColor()} 50%, ${getStatusColor()} 75%, transparent 75%, transparent)`
                        : getStatusColor(),
                      backgroundSize: upload.status === 'uploading' ? '20px 20px' : 'auto',
                      animation: upload.status === 'uploading' ? 'progress-stripes 1s linear infinite' : 'none'
                    }
                  }}
                />
              </motion.div>
              
              <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 0.5 }}>
                <Typography variant="caption" sx={{ color: theme.palette.text.secondary }}>
                  {Math.round(upload.progress)}%
                </Typography>
                {upload.status === 'uploading' && upload.estimatedTimeRemaining && (
                  <Typography variant="caption" sx={{ color: theme.palette.text.secondary }}>
                    {formatTime(upload.estimatedTimeRemaining)} remaining
                  </Typography>
                )}
              </Box>
            </Box>

            {/* Details */}
            {showDetails && !compact && (
              <AnimatePresence>
                {(upload.status === 'uploading' || upload.status === 'completed') && (
                  <motion.div
                    initial={{ opacity: 0, height: 0 }}
                    animate={{ opacity: 1, height: 'auto' }}
                    exit={{ opacity: 0, height: 0 }}
                    transition={{ duration: 0.3 }}
                  >
                    <Box sx={{ display: 'flex', gap: 3, flexWrap: 'wrap' }}>
                      {['uploading', 'processing'].includes(upload.status) && upload.upload_speed && upload.upload_speed > 0 && (
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                          <SpeedIcon fontSize="small" sx={{ color: theme.palette.text.secondary }} />
                          <Typography variant="caption" sx={{ color: theme.palette.text.secondary }}>
                            {formatSpeed(upload.upload_speed)}
                          </Typography>
                        </Box>
                      )}
                      
                      {upload.estimated_time_remaining && ['uploading', 'processing'].includes(upload.status) && (
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                          <ScheduleIcon fontSize="small" sx={{ color: theme.palette.text.secondary }} />
                          <Typography variant="caption" sx={{ color: theme.palette.text.secondary }}>
                            {formatTime(upload.estimated_time_remaining)}
                          </Typography>
                        </Box>
                      )}

                      {upload.completed_at && (
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                          <CheckCircleIcon fontSize="small" sx={{ color: theme.palette.success.main }} />
                          <Typography variant="caption" sx={{ color: theme.palette.text.secondary }}>
                            Completed {new Date(upload.completed_at).toLocaleTimeString()}
                          </Typography>
                        </Box>
                      )}
                    </Box>
                  </motion.div>
                )}
                
                {upload.status === 'failed' && upload.error_message && (
                  <motion.div
                    initial={{ opacity: 0, height: 0 }}
                    animate={{ opacity: 1, height: 'auto' }}
                    exit={{ opacity: 0, height: 0 }}
                    transition={{ duration: 0.3 }}
                  >
                    <Typography
                      variant="caption"
                      sx={{
                        color: theme.palette.error.main,
                        backgroundColor: alpha(theme.palette.error.main, 0.1),
                        padding: 1,
                        borderRadius: 1,
                        display: 'block'
                      }}
                    >
                      {upload.error_message}
                    </Typography>
                  </motion.div>
                )}
              </AnimatePresence>
            )}
          </Box>
        </Paper>
      </motion.div>
    </AnimatePresence>
  );
};

export default AnimatedProgressBar;
