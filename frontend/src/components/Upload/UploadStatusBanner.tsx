import React from 'react';
import {
  Box,
  Typography,
  LinearProgress,
  Collapse,
  IconButton,
  useTheme,
  alpha,
  Chip
} from '@mui/material';
import {
  CloudUpload as CloudUploadIcon,
  ExpandLess as ExpandLessIcon,
  ExpandMore as ExpandMoreIcon,
  CheckCircle as CheckCircleIcon,
  Error as ErrorIcon
} from '@mui/icons-material';
import { motion, AnimatePresence } from 'framer-motion';
import { useUploadManager } from '../../hooks/useUploadManager';

interface UploadStatusBannerProps {
  position?: 'top' | 'bottom';
  showWhenEmpty?: boolean;
}

const UploadStatusBanner: React.FC<UploadStatusBannerProps> = ({
  position = 'top',
  showWhenEmpty = false
}) => {
  const theme = useTheme();
  const [isExpanded, setIsExpanded] = React.useState(false);
  
  const {
    uploads,
    hasActiveUploads,
    getActiveUploads,
    getCompletedUploads,
    getFailedUploads,
    formatFileSize,
    formatSpeed,
    formatTime
  } = useUploadManager();

  const activeUploads = getActiveUploads();
  const completedUploads = getCompletedUploads();
  const failedUploads = getFailedUploads();

  // Don't show if no uploads and showWhenEmpty is false
  if (!showWhenEmpty && uploads.length === 0) {
    return null;
  }

  // Calculate overall progress for active uploads
  const overallProgress = activeUploads.length > 0 
    ? activeUploads.reduce((sum, upload) => sum + upload.progress, 0) / activeUploads.length
    : 0;

  const getStatusColor = () => {
    if (failedUploads.length > 0) return theme.palette.error.main;
    if (hasActiveUploads()) return theme.palette.primary.main;
    return theme.palette.success.main;
  };

  const getStatusText = () => {
    if (hasActiveUploads()) {
      return `${activeUploads.length} file${activeUploads.length > 1 ? 's' : ''} uploading...`;
    }
    if (failedUploads.length > 0) {
      return `${failedUploads.length} upload${failedUploads.length > 1 ? 's' : ''} failed`;
    }
    if (completedUploads.length > 0) {
      return `${completedUploads.length} upload${completedUploads.length > 1 ? 's' : ''} completed`;
    }
    return 'No uploads';
  };

  const getStatusIcon = () => {
    if (failedUploads.length > 0) return <ErrorIcon />;
    if (hasActiveUploads()) return <CloudUploadIcon />;
    return <CheckCircleIcon />;
  };

  return (
    <AnimatePresence>
      {(uploads.length > 0 || showWhenEmpty) && (
        <motion.div
          initial={{ opacity: 0, y: position === 'top' ? -50 : 50 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: position === 'top' ? -50 : 50 }}
          transition={{ duration: 0.3 }}
        >
          <Box
            sx={{
              position: 'sticky',
              [position]: 0,
              zIndex: 1200,
              backgroundColor: alpha(getStatusColor(), 0.1),
              borderBottom: position === 'top' ? `1px solid ${alpha(getStatusColor(), 0.2)}` : 'none',
              borderTop: position === 'bottom' ? `1px solid ${alpha(getStatusColor(), 0.2)}` : 'none',
              backdropFilter: 'blur(10px)',
            }}
          >
            {/* Main Status Bar */}
            <Box
              sx={{
                display: 'flex',
                alignItems: 'center',
                px: { xs: 2, sm: 3 },
                py: 1,
                cursor: uploads.length > 1 ? 'pointer' : 'default'
              }}
              onClick={() => uploads.length > 1 && setIsExpanded(!isExpanded)}
            >
              <motion.div
                animate={{ 
                  rotate: hasActiveUploads() ? 360 : 0 
                }}
                transition={{ 
                  duration: 2, 
                  repeat: hasActiveUploads() ? Infinity : 0, 
                  ease: 'linear' 
                }}
              >
                <Box
                  sx={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    width: 32,
                    height: 32,
                    borderRadius: '50%',
                    backgroundColor: alpha(getStatusColor(), 0.2),
                    color: getStatusColor(),
                    mr: 2
                  }}
                >
                  {getStatusIcon()}
                </Box>
              </motion.div>

              <Box sx={{ flex: 1, minWidth: 0 }}>
                <Typography
                  variant="body2"
                  fontWeight="600"
                  sx={{ color: getStatusColor() }}
                >
                  {getStatusText()}
                </Typography>
                
                {hasActiveUploads() && (
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mt: 0.5 }}>
                    <LinearProgress
                      variant="determinate"
                      value={overallProgress}
                      sx={{
                        flex: 1,
                        height: 4,
                        borderRadius: 2,
                        backgroundColor: alpha(getStatusColor(), 0.2),
                        '& .MuiLinearProgress-bar': {
                          borderRadius: 2,
                          backgroundColor: getStatusColor()
                        }
                      }}
                    />
                    <Typography
                      variant="caption"
                      sx={{ color: theme.palette.text.secondary, minWidth: 'fit-content' }}
                    >
                      {Math.round(overallProgress)}%
                    </Typography>
                  </Box>
                )}
              </Box>

              <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                {completedUploads.length > 0 && (
                  <Chip
                    label={`${completedUploads.length} done`}
                    size="small"
                    sx={{
                      backgroundColor: alpha(theme.palette.success.main, 0.1),
                      color: theme.palette.success.main,
                      fontSize: '0.7rem',
                      height: 24
                    }}
                  />
                )}
                
                {failedUploads.length > 0 && (
                  <Chip
                    label={`${failedUploads.length} failed`}
                    size="small"
                    sx={{
                      backgroundColor: alpha(theme.palette.error.main, 0.1),
                      color: theme.palette.error.main,
                      fontSize: '0.7rem',
                      height: 24
                    }}
                  />
                )}

                {uploads.length > 1 && (
                  <IconButton
                    size="small"
                    onClick={(e) => {
                      e.stopPropagation();
                      setIsExpanded(!isExpanded);
                    }}
                    sx={{ color: theme.palette.text.secondary }}
                  >
                    {isExpanded ? <ExpandLessIcon /> : <ExpandMoreIcon />}
                  </IconButton>
                )}
              </Box>
            </Box>

            {/* Expanded Details */}
            <Collapse in={isExpanded}>
              <Box sx={{ px: { xs: 2, sm: 3 }, pb: 2 }}>
                {uploads.slice(0, 3).map((upload) => (
                  <Box
                    key={upload.id}
                    sx={{
                      display: 'flex',
                      alignItems: 'center',
                      py: 1,
                      borderBottom: `1px solid ${alpha(theme.palette.divider, 0.1)}`
                    }}
                  >
                    <Typography
                      variant="caption"
                      sx={{ 
                        flex: 1, 
                        color: theme.palette.text.secondary,
                        overflow: 'hidden',
                        textOverflow: 'ellipsis',
                        whiteSpace: 'nowrap'
                      }}
                    >
                      {upload.fileName} ({formatFileSize(upload.fileSize)})
                    </Typography>
                    
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, ml: 2 }}>
                      {upload.status === 'uploading' && (
                        <>
                          <Typography variant="caption" color="text.secondary">
                            {Math.round(upload.progress)}%
                          </Typography>
                          {upload.speed > 0 && (
                            <Typography variant="caption" color="text.secondary">
                              {formatSpeed(upload.speed)}
                            </Typography>
                          )}
                        </>
                      )}
                      
                      <Chip
                        label={upload.status}
                        size="small"
                        sx={{
                          fontSize: '0.6rem',
                          height: 20,
                          backgroundColor: alpha(
                            upload.status === 'completed' ? theme.palette.success.main :
                            upload.status === 'error' ? theme.palette.error.main :
                            upload.status === 'uploading' ? theme.palette.primary.main :
                            theme.palette.warning.main,
                            0.1
                          ),
                          color: upload.status === 'completed' ? theme.palette.success.main :
                                upload.status === 'error' ? theme.palette.error.main :
                                upload.status === 'uploading' ? theme.palette.primary.main :
                                theme.palette.warning.main
                        }}
                      />
                    </Box>
                  </Box>
                ))}
                
                {uploads.length > 3 && (
                  <Typography
                    variant="caption"
                    color="text.secondary"
                    sx={{ display: 'block', textAlign: 'center', pt: 1 }}
                  >
                    +{uploads.length - 3} more uploads
                  </Typography>
                )}
              </Box>
            </Collapse>
          </Box>
        </motion.div>
      )}
    </AnimatePresence>
  );
};

export default UploadStatusBanner;
