import React, { useState } from 'react';
import {
  Box,
  Fab,
  Badge,
  Drawer,
  Typography,
  IconButton,
  Divider,
  useTheme,
  alpha,
  Tooltip
} from '@mui/material';
import {
  CloudUpload as CloudUploadIcon,
  Close as CloseIcon,
  ExpandLess as ExpandLessIcon,
  ExpandMore as ExpandMoreIcon
} from '@mui/icons-material';
import { motion, AnimatePresence } from 'framer-motion';
import { useUploadManager } from '../../hooks/useUploadManager';
import AnimatedProgressBar from './AnimatedProgressBar';

interface GlobalUploadIndicatorProps {
  position?: 'bottom-right' | 'bottom-left' | 'top-right' | 'top-left';
}

const GlobalUploadIndicator: React.FC<GlobalUploadIndicatorProps> = ({
  position = 'bottom-right'
}) => {
  const theme = useTheme();
  const [isDrawerOpen, setIsDrawerOpen] = useState(false);
  const [isMinimized, setIsMinimized] = useState(false);
  
  const {
    uploads,
    hasActiveUploads,
    getActiveUploads,
    getCompletedUploads,
    getFailedUploads,
    cancelUpload
  } = useUploadManager();

  const activeUploads = getActiveUploads();
  const completedUploads = getCompletedUploads();
  const failedUploads = getFailedUploads();
  const totalUploads = uploads.length;

  // Don't show if no uploads
  if (totalUploads === 0) {
    return null;
  }

  const getPositionStyles = () => {
    const baseStyles = {
      position: 'fixed' as const,
      zIndex: 1300,
    };

    const spacing = { xs: 16, sm: 24 };

    switch (position) {
      case 'bottom-right':
        return { ...baseStyles, bottom: spacing, right: spacing };
      case 'bottom-left':
        return { ...baseStyles, bottom: spacing, left: spacing };
      case 'top-right':
        return { ...baseStyles, top: spacing, right: spacing };
      case 'top-left':
        return { ...baseStyles, top: spacing, left: spacing };
      default:
        return { ...baseStyles, bottom: spacing, right: spacing };
    }
  };

  const handleFabClick = () => {
    setIsDrawerOpen(true);
  };

  const handleDrawerClose = () => {
    setIsDrawerOpen(false);
  };

  const handleMinimizeToggle = () => {
    setIsMinimized(!isMinimized);
  };

  const fabVariants = {
    initial: { scale: 0, opacity: 0 },
    animate: { 
      scale: 1, 
      opacity: 1,
      transition: { type: 'spring', stiffness: 300, damping: 20 }
    },
    exit: { 
      scale: 0, 
      opacity: 0,
      transition: { duration: 0.2 }
    },
    pulse: {
      scale: [1, 1.1, 1],
      transition: { duration: 2, repeat: Infinity, ease: 'easeInOut' }
    }
  };

  return (
    <>
      {/* Floating Action Button */}
      <AnimatePresence>
        <motion.div
          style={getPositionStyles()}
          variants={fabVariants}
          initial="initial"
          animate={hasActiveUploads() ? "pulse" : "animate"}
          exit="exit"
        >
          <Badge
            badgeContent={totalUploads}
            color={failedUploads.length > 0 ? 'error' : hasActiveUploads() ? 'primary' : 'success'}
            max={99}
          >
            <Tooltip title="View Uploads">
              <Fab
                color={hasActiveUploads() ? 'primary' : 'default'}
                onClick={handleFabClick}
                size="medium"
                sx={{
                  background: hasActiveUploads()
                    ? `linear-gradient(45deg, ${theme.palette.primary.main} 30%, ${theme.palette.primary.light} 90%)`
                    : undefined,
                  '&:hover': {
                    transform: 'scale(1.1)',
                  },
                  transition: 'all 0.3s ease',
                  width: { xs: 48, sm: 56 },
                  height: { xs: 48, sm: 56 }
                }}
              >
                <motion.div
                  animate={{
                    rotate: hasActiveUploads() ? 360 : 0
                  }}
                  transition={{
                    duration: 2,
                    repeat: hasActiveUploads() ? Infinity : 0,
                    ease: 'linear'
                  }}
                >
                  <CloudUploadIcon sx={{ fontSize: { xs: '1.25rem', sm: '1.5rem' } }} />
                </motion.div>
              </Fab>
            </Tooltip>
          </Badge>
        </motion.div>
      </AnimatePresence>

      {/* Upload Drawer */}
      <Drawer
        anchor="right"
        open={isDrawerOpen}
        onClose={handleDrawerClose}
        PaperProps={{
          sx: {
            width: { xs: '100%', sm: 400, md: 450 },
            maxWidth: '100vw'
          }
        }}
      >
        <Box sx={{ p: 2 }}>
          {/* Header */}
          <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between', mb: 2 }}>
            <Typography variant="h6" fontWeight="600">
              Upload Manager
            </Typography>
            <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
              <IconButton
                size="small"
                onClick={handleMinimizeToggle}
                sx={{ color: theme.palette.text.secondary }}
              >
                {isMinimized ? <ExpandMoreIcon /> : <ExpandLessIcon />}
              </IconButton>
              <IconButton
                size="small"
                onClick={handleDrawerClose}
                sx={{ color: theme.palette.text.secondary }}
              >
                <CloseIcon />
              </IconButton>
            </Box>
          </Box>

          {/* Summary */}
          {!isMinimized && (
            <Box sx={{ mb: 3 }}>
              <Box sx={{ display: 'flex', gap: 2, mb: 2 }}>
                <Box
                  sx={{
                    flex: 1,
                    p: 2,
                    borderRadius: 2,
                    backgroundColor: alpha(theme.palette.primary.main, 0.1),
                    textAlign: 'center'
                  }}
                >
                  <Typography variant="h6" color="primary" fontWeight="600">
                    {activeUploads.length}
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    Active
                  </Typography>
                </Box>
                <Box
                  sx={{
                    flex: 1,
                    p: 2,
                    borderRadius: 2,
                    backgroundColor: alpha(theme.palette.success.main, 0.1),
                    textAlign: 'center'
                  }}
                >
                  <Typography variant="h6" color="success.main" fontWeight="600">
                    {completedUploads.length}
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    Completed
                  </Typography>
                </Box>
                <Box
                  sx={{
                    flex: 1,
                    p: 2,
                    borderRadius: 2,
                    backgroundColor: alpha(theme.palette.error.main, 0.1),
                    textAlign: 'center'
                  }}
                >
                  <Typography variant="h6" color="error.main" fontWeight="600">
                    {failedUploads.length}
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    Failed
                  </Typography>
                </Box>
              </Box>
            </Box>
          )}

          <Divider sx={{ mb: 2 }} />

          {/* Upload List */}
          <Box sx={{ maxHeight: 'calc(100vh - 200px)', overflowY: 'auto' }}>
            <AnimatePresence>
              {uploads.length === 0 ? (
                <motion.div
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  exit={{ opacity: 0 }}
                >
                  <Typography
                    variant="body2"
                    color="text.secondary"
                    textAlign="center"
                    sx={{ py: 4 }}
                  >
                    No uploads yet
                  </Typography>
                </motion.div>
              ) : (
                uploads.map((upload) => (
                  <motion.div
                    key={upload.id}
                    layout
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    exit={{ opacity: 0, y: -20 }}
                    transition={{ duration: 0.3 }}
                  >
                    <AnimatedProgressBar
                      upload={upload}
                      onCancel={cancelUpload}
                      showDetails={!isMinimized}
                      compact={isMinimized}
                    />
                  </motion.div>
                ))
              )}
            </AnimatePresence>
          </Box>
        </Box>
      </Drawer>
    </>
  );
};

export default GlobalUploadIndicator;
