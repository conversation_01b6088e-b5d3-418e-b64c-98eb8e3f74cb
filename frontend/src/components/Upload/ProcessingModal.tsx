import React, { useState, useEffect } from 'react';
import {
  Dialog,
  DialogContent,
  Box,
  Typography,
  LinearProgress,
  Fade,
  Slide,
  useTheme,
  alpha
} from '@mui/material';
import {
  CloudUpload as UploadIcon,
  Psychology as ProcessingIcon,
  Quiz as MCQIcon,
  CheckCircle as CompleteIcon,
  AutoAwesome as GeneratingIcon
} from '@mui/icons-material';
import { motion, AnimatePresence } from 'framer-motion';

export interface ProcessingStage {
  id: string;
  label: string;
  description: string;
  icon: React.ReactNode;
  duration?: number; // Optional duration in ms for auto-progression
}

interface ProcessingModalProps {
  open: boolean;
  stages: ProcessingStage[];
  currentStage: string;
  onComplete?: () => void;
  title?: string;
  progress?: number; // 0-100
}

const ProcessingModal: React.FC<ProcessingModalProps> = ({
  open,
  stages,
  currentStage,
  onComplete,
  title = "Processing",
  progress
}) => {
  const theme = useTheme();
  const [displayedStage, setDisplayedStage] = useState(currentStage);

  // Update displayed stage with smooth transition
  useEffect(() => {
    if (currentStage !== displayedStage) {
      const timer = setTimeout(() => {
        setDisplayedStage(currentStage);
      }, 150);
      return () => clearTimeout(timer);
    }
  }, [currentStage, displayedStage]);

  // Auto-complete when reaching final stage
  useEffect(() => {
    const currentStageIndex = stages.findIndex(stage => stage.id === currentStage);
    const isLastStage = currentStageIndex === stages.length - 1;
    
    if (isLastStage && currentStage === 'complete' && onComplete) {
      const timer = setTimeout(() => {
        onComplete();
      }, 2000); // Show completion for 2 seconds
      return () => clearTimeout(timer);
    }
  }, [currentStage, stages, onComplete]);

  const currentStageData = stages.find(stage => stage.id === displayedStage);
  const currentStageIndex = stages.findIndex(stage => stage.id === displayedStage);
  const progressValue = progress ?? ((currentStageIndex + 1) / stages.length) * 100;

  return (
    <Dialog
      open={open}
      maxWidth="sm"
      fullWidth
      PaperProps={{
        sx: {
          borderRadius: 3,
          backgroundColor: theme.palette.background.paper,
          backdropFilter: 'blur(10px)',
          border: `1px solid ${alpha(theme.palette.divider, 0.2)}`,
          overflow: 'hidden'
        }
      }}
      BackdropProps={{
        sx: {
          backgroundColor: alpha(theme.palette.common.black, 0.7),
          backdropFilter: 'blur(4px)'
        }
      }}
    >
      <DialogContent sx={{ p: 4, textAlign: 'center' }}>
        {/* Title */}
        <Typography
          variant="h5"
          gutterBottom
          sx={{
            fontWeight: 600,
            color: theme.palette.text.primary,
            mb: 4
          }}
        >
          {title}
        </Typography>

        {/* Stage Icon with Animation */}
        <Box sx={{ mb: 3, position: 'relative', height: 80 }}>
          <AnimatePresence mode="wait">
            <motion.div
              key={displayedStage}
              initial={{ scale: 0, rotate: -180, opacity: 0 }}
              animate={{ scale: 1, rotate: 0, opacity: 1 }}
              exit={{ scale: 0, rotate: 180, opacity: 0 }}
              transition={{ 
                type: "spring", 
                stiffness: 200, 
                damping: 20,
                duration: 0.6
              }}
              style={{
                position: 'absolute',
                top: '50%',
                left: '50%',
                transform: 'translate(-50%, -50%)'
              }}
            >
              <motion.div
                animate={currentStageData?.id !== 'complete' ? {
                  scale: [1, 1.05, 1],
                  rotate: [0, 2, -2, 0]
                } : {}}
                transition={{
                  duration: 2,
                  repeat: currentStageData?.id !== 'complete' ? Infinity : 0,
                  ease: "easeInOut"
                }}
              >
                <Box
                  sx={{
                    width: 80,
                    height: 80,
                    borderRadius: '50%',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    background: `linear-gradient(135deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
                    color: 'white',
                    fontSize: '2rem',
                    boxShadow: `0 8px 32px ${alpha(theme.palette.primary.main, 0.3)}`,
                    position: 'relative',
                    '& svg': {
                      fontSize: '2.5rem'
                    },
                    '&::before': currentStageData?.id !== 'complete' ? {
                      content: '""',
                      position: 'absolute',
                      top: -3,
                      left: -3,
                      right: -3,
                      bottom: -3,
                      borderRadius: '50%',
                      background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.2)}, ${alpha(theme.palette.secondary.main, 0.2)})`,
                      animation: 'ripple 2s infinite',
                      zIndex: -1
                    } : {},
                    '@keyframes ripple': {
                      '0%': {
                        transform: 'scale(1)',
                        opacity: 0.8
                      },
                      '100%': {
                        transform: 'scale(1.3)',
                        opacity: 0
                      }
                    }
                  }}
                >
                  {currentStageData?.icon}
                </Box>
              </motion.div>
            </motion.div>
          </AnimatePresence>
        </Box>

        {/* Stage Label */}
        <AnimatePresence mode="wait">
          <motion.div
            key={`${displayedStage}-label`}
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            exit={{ y: -20, opacity: 0 }}
            transition={{ duration: 0.3 }}
          >
            <Typography 
              variant="h6" 
              gutterBottom
              sx={{ 
                fontWeight: 600,
                color: theme.palette.text.primary,
                mb: 1
              }}
            >
              {currentStageData?.label}
            </Typography>
          </motion.div>
        </AnimatePresence>

        {/* Stage Description */}
        <AnimatePresence mode="wait">
          <motion.div
            key={`${displayedStage}-desc`}
            initial={{ y: 20, opacity: 0 }}
            animate={{ y: 0, opacity: 1 }}
            exit={{ y: -20, opacity: 0 }}
            transition={{ duration: 0.3, delay: 0.1 }}
          >
            <Typography 
              variant="body2" 
              color="text.secondary"
              sx={{ mb: 4, minHeight: 40 }}
            >
              {currentStageData?.description}
            </Typography>
          </motion.div>
        </AnimatePresence>

        {/* Progress Bar */}
        <Box sx={{ mb: 3 }}>
          <LinearProgress
            variant="determinate"
            value={progressValue}
            sx={{
              height: 8,
              borderRadius: 4,
              backgroundColor: alpha(theme.palette.primary.main, 0.1),
              '& .MuiLinearProgress-bar': {
                borderRadius: 4,
                background: `linear-gradient(90deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
                transition: 'transform 0.8s ease-in-out'
              }
            }}
          />
          <Typography 
            variant="caption" 
            color="text.secondary"
            sx={{ mt: 1, display: 'block' }}
          >
            {Math.round(progressValue)}% Complete
          </Typography>
        </Box>

        {/* Stage Indicators */}
        <Box sx={{ display: 'flex', justifyContent: 'center', gap: 1 }}>
          {stages.map((stage, index) => (
            <Box
              key={stage.id}
              sx={{
                width: 8,
                height: 8,
                borderRadius: '50%',
                backgroundColor: index <= currentStageIndex 
                  ? theme.palette.primary.main 
                  : alpha(theme.palette.primary.main, 0.2),
                transition: 'all 0.3s ease',
                transform: index === currentStageIndex ? 'scale(1.5)' : 'scale(1)'
              }}
            />
          ))}
        </Box>
      </DialogContent>
    </Dialog>
  );
};

export default ProcessingModal;

// Predefined stage configurations
export const UPLOAD_STAGES: ProcessingStage[] = [
  {
    id: 'uploading',
    label: 'Uploading File',
    description: 'Securely uploading your PDF to our servers...',
    icon: <UploadIcon />
  },
  {
    id: 'processing',
    label: 'Processing Content',
    description: 'Analyzing and extracting content from your document...',
    icon: <ProcessingIcon />
  },
  {
    id: 'indexing',
    label: 'Indexing Content',
    description: 'Creating searchable index for optimal performance...',
    icon: <GeneratingIcon />
  },
  {
    id: 'complete',
    label: 'Upload Complete',
    description: 'Your file has been successfully processed and is ready to use!',
    icon: <CompleteIcon />
  }
];

export const MCQ_GENERATION_STAGES: ProcessingStage[] = [
  {
    id: 'analyzing',
    label: 'Analyzing Content',
    description: 'Reading and understanding your document content...',
    icon: <ProcessingIcon />
  },
  {
    id: 'generating',
    label: 'Generating Questions',
    description: 'Creating high-quality multiple choice questions...',
    icon: <GeneratingIcon />
  },
  {
    id: 'validating',
    label: 'Validating Questions',
    description: 'Ensuring question quality and accuracy...',
    icon: <MCQIcon />
  },
  {
    id: 'complete',
    label: 'Generation Complete',
    description: 'Your MCQs are ready! Redirecting to practice mode...',
    icon: <CompleteIcon />
  }
];

export const NOTE_EXPLANATION_GENERATION_STAGES: ProcessingStage[] = [
  {
    id: 'analyzing',
    label: 'Analyzing Content',
    description: 'Reading and understanding your document content...',
    icon: <ProcessingIcon />
  },
  {
    id: 'processing',
    label: 'Processing Text',
    description: 'Breaking down content into manageable sections...',
    icon: <ProcessingIcon />
  },
  {
    id: 'generating',
    label: 'Generating Explanations',
    description: 'Creating detailed line-by-line explanations...',
    icon: <MCQIcon />
  },
  {
    id: 'finalizing',
    label: 'Finalizing Summary',
    description: 'Compiling comprehensive summary and key concepts...',
    icon: <MCQIcon />
  },
  {
    id: 'complete',
    label: 'Explanation Complete',
    description: 'Your note explanation is ready! Redirecting to view...',
    icon: <CompleteIcon />
  }
];
