import { useState, useCallback } from 'react';
import { uploadPDFToG<PERSON>ini } from '../api/studentTools';

export interface UseUploadManagerOptions {
  // Simplified - no background tracking needed
}

export interface UploadManagerState {
  isUploading: boolean;
  error: string | null;
  lastUploadResult: any | null;
}

// Simplified upload manager for direct uploads
// Note: This is a simplified version that doesn't track background uploads
// since we now use direct uploads to Gemini

export const useUploadManager = (options: UseUploadManagerOptions = {}) => {
  const [state, setState] = useState<UploadManagerState>({
    isUploading: false,
    error: null,
    lastUploadResult: null
  });

  // Simple direct upload function
  const startUpload = useCallback(async (
    file: File,
    courseName?: string
  ): Promise<any> => {
    setState(prev => ({ ...prev, isUploading: true, error: null }));

    try {
      const result = await uploadPDFToGemini(file, courseName);
      setState(prev => ({
        ...prev,
        isUploading: false,
        lastUploadResult: result,
        error: null
      }));
      return result;
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Upload failed';
      setState(prev => ({
        ...prev,
        isUploading: false,
        error: errorMessage
      }));
      throw error;
    }
  }, []);

  // Clear error
  const clearError = useCallback(() => {
    setState(prev => ({ ...prev, error: null }));
  }, []);



  // Utility function for file size formatting
  const formatFileSize = useCallback((bytes: number): string => {
    if (bytes === 0) return '0 Bytes';
    const k = 1024;
    const sizes = ['Bytes', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }, []);

  // Utility function for speed formatting
  const formatSpeed = useCallback((bytesPerSecond: number): string => {
    return `${formatFileSize(bytesPerSecond)}/s`;
  }, [formatFileSize]);

  // Utility function for time formatting
  const formatTime = useCallback((seconds: number): string => {
    if (seconds < 60) return `${Math.round(seconds)}s`;
    const minutes = Math.floor(seconds / 60);
    const remainingSeconds = Math.round(seconds % 60);
    return `${minutes}m ${remainingSeconds}s`;
  }, []);

  // Legacy compatibility methods for components that expect the old interface
  // These return empty arrays/false since we don't track background uploads anymore
  const uploads: any[] = [];
  const hasActiveUploads = useCallback(() => state.isUploading, [state.isUploading]);
  const getActiveUploads = useCallback(() => [], []);
  const getCompletedUploads = useCallback(() => [], []);
  const getFailedUploads = useCallback(() => [], []);
  const cancelUpload = useCallback(() => {}, []);

  return {
    // State
    isUploading: state.isUploading,
    error: state.error,
    lastUploadResult: state.lastUploadResult,

    // Legacy compatibility for old upload tracking components
    uploads,
    hasActiveUploads,
    getActiveUploads,
    getCompletedUploads,
    getFailedUploads,
    cancelUpload,

    // Actions
    startUpload,
    clearError,

    // Utilities
    formatFileSize,
    formatSpeed,
    formatTime
  };
};
