/**
 * Hook for the new simplified content sharing system
 */

import { useState, useCallback } from 'react';
import { 
  contentSharingApi, 
  shareHelpers 
} from '../api/contentSharing';
import {
  ContentType,
  ShareContentRequest,
  ShareContentResponse,
  MySharedContentSummary,
  SharedWithMeSummary
} from '../types/contentSharing';
import { useToast } from '../contexts/ToastContext';

export const useContentSharing = () => {
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const { showSuccess, showError } = useToast();

  const shareContent = useCallback(async (request: ShareContentRequest): Promise<ShareContentResponse | null> => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await contentSharingApi.shareContent(request);
      showSuccess(response.message);
      
      if (response.invitations_failed.length > 0) {
        showError(`Failed to send invitations to: ${response.invitations_failed.join(', ')}`);
      }
      
      return response;
    } catch (err: any) {
      const errorMessage = err.response?.data?.detail || 'Failed to share content';
      setError(errorMessage);
      showError(errorMessage);
      return null;
    } finally {
      setIsLoading(false);
    }
  }, [showSuccess, showError]);

  const acceptInvitation = useCallback(async (invitationToken: string) => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await contentSharingApi.acceptInvitation({ invitation_token: invitationToken });
      showSuccess(response.message);
      return response;
    } catch (err: any) {
      const errorMessage = err.response?.data?.detail || 'Failed to accept invitation';
      setError(errorMessage);
      showError(errorMessage);
      return null;
    } finally {
      setIsLoading(false);
    }
  }, [showSuccess, showError]);

  const accessPublicShare = useCallback(async (shareToken: string) => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await contentSharingApi.accessPublicShare({ share_token: shareToken });
      showSuccess(response.message);
      return response;
    } catch (err: any) {
      const errorMessage = err.response?.data?.detail || 'Failed to access shared content';
      setError(errorMessage);
      showError(errorMessage);
      return null;
    } finally {
      setIsLoading(false);
    }
  }, [showSuccess, showError]);

  const getMySharedContent = useCallback(async (skip = 0, limit = 100): Promise<MySharedContentSummary[]> => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await contentSharingApi.getMySharedContent(skip, limit);
      return response;
    } catch (err: any) {
      const errorMessage = err.response?.data?.detail || 'Failed to load shared content';
      setError(errorMessage);
      showError(errorMessage);
      return [];
    } finally {
      setIsLoading(false);
    }
  }, [showError]);

  const getSharedWithMe = useCallback(async (skip = 0, limit = 100): Promise<SharedWithMeSummary[]> => {
    setIsLoading(true);
    setError(null);

    try {
      const response = await contentSharingApi.getSharedWithMe(skip, limit);
      return response;
    } catch (err: any) {
      const errorMessage = err.response?.data?.detail || 'Failed to load shared content';
      setError(errorMessage);
      showError(errorMessage);
      return [];
    } finally {
      setIsLoading(false);
    }
  }, [showError]);

  // Helper functions for specific content types
  const shareMCQ = useCallback((
    content_id: number,
    content_title: string,
    course_name?: string,
    share_message?: string,
    invited_emails?: string[],
    create_public_link = false
  ) => {
    return shareContent({
      content_type: ContentType.MCQ,
      content_id,
      share_message,
      invited_emails,
      create_public_link
    });
  }, [shareContent]);

  const shareFlashcard = useCallback((
    content_id: number,
    content_title: string,
    course_name?: string,
    share_message?: string,
    invited_emails?: string[],
    create_public_link = false
  ) => {
    return shareContent({
      content_type: ContentType.FLASHCARD,
      content_id,
      share_message,
      invited_emails,
      create_public_link
    });
  }, [shareContent]);

  const shareSummary = useCallback((
    content_id: number,
    content_title: string,
    course_name?: string,
    share_message?: string,
    invited_emails?: string[],
    create_public_link = false
  ) => {
    return shareContent({
      content_type: ContentType.SUMMARY,
      content_id,
      share_message,
      invited_emails,
      create_public_link
    });
  }, [shareContent]);

  const copyShareLink = useCallback(async (url: string) => {
    try {
      await navigator.clipboard.writeText(url);
      showSuccess('Share link copied to clipboard');
      return true;
    } catch (err) {
      showError('Failed to copy link');
      return false;
    }
  }, [showSuccess, showError]);

  const removeSharedContentAccess = useCallback(async (content_type: ContentType, content_id: number) => {
    setIsLoading(true);
    setError(null);

    try {
      const result = await contentSharingApi.removeSharedContentAccess(content_type, content_id);
      showSuccess('Access removed successfully');
      return result;
    } catch (error: any) {
      const errorMessage = error.response?.data?.detail || 'Failed to remove access to shared content';
      setError(errorMessage);
      showError(errorMessage);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, [showSuccess, showError]);

  return {
    // State
    isLoading,
    error,

    // General functions
    shareContent,
    acceptInvitation,
    accessPublicShare,
    getMySharedContent,
    getSharedWithMe,
    removeSharedContentAccess,

    // Content-specific helpers
    shareMCQ,
    shareFlashcard,
    shareSummary,

    // Utility functions
    copyShareLink,

    // Clear error
    clearError: () => setError(null)
  };
};

export default useContentSharing;
