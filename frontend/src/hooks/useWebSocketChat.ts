import { useState, useEffect, useRef, useCallback } from 'react';
import { flushSync } from 'react-dom';
import { useAuth } from '../contexts/AuthContext';

export interface ChatMessage {
  id: number;
  content: string;
  sender_id: number;
  sender_name: string;
  sender_profile_picture_url?: string;
  chat_room_id: number;
  created_at: string;
  message_type: string;
  is_read?: boolean;
  delivery_status?: string;
  delivered_at?: string;
  read_at?: string;
  is_offline_delivery?: boolean;
}

export interface ChatRoom {
  id: number;
  student_id: number;
  tutor_id: number;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  other_user: {
    id: number;
    name: string;
    email: string;
    profile_picture_url?: string;
    is_online: boolean;
    last_seen: string | null;
  };
  last_message: {
    content: string;
    created_at: string;
    sender_id: number;
  } | null;
  unread_count: number;
}

export interface OnlineStatus {
  user_id: number;
  is_online: boolean;
  timestamp: string;
}

export interface RealtimeNotification {
  id: number;
  type: string;
  title: string;
  message: string;
  is_read: boolean;
  created_at: string;
  related_id?: number;
  related_type?: string;
  chat_room_id?: number;
}

interface WebSocketMessage {
  type: string;
  data: any;
}

export const useWebSocketChat = () => {
  const { user } = useAuth();
  const [socket, setSocket] = useState<WebSocket | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const [messages, setMessages] = useState<Record<number, ChatMessage[]>>({});
  const [onlineUsers, setOnlineUsers] = useState<Record<number, boolean>>({});
  const [currentChatRoom, setCurrentChatRoom] = useState<number | null>(null);
  const [realtimeNotifications, setRealtimeNotifications] = useState<RealtimeNotification[]>([]);
  const [unreadNotificationCount, setUnreadNotificationCount] = useState(0);
  const reconnectTimeoutRef = useRef<NodeJS.Timeout>();
  const reconnectAttempts = useRef(0);
  const maxReconnectAttempts = 10; // Increased for better reliability
  const isConnecting = useRef(false);
  const lastConnectionAttempt = useRef(0);
  const minConnectionInterval = 1000; // Minimum 1 second between connection attempts
  const heartbeatIntervalRef = useRef<NodeJS.Timeout>();
  const heartbeatInterval = 30000; // 30 seconds

  // Notification event handlers
  const notificationHandlers = useRef<((notification: RealtimeNotification) => void)[]>([]);

  const addNotificationHandler = useCallback((handler: (notification: RealtimeNotification) => void) => {
    notificationHandlers.current.push(handler);
    return () => {
      notificationHandlers.current = notificationHandlers.current.filter(h => h !== handler);
    };
  }, []);

  const connect = useCallback(() => {
    if (!user?.id) return;

    // Prevent multiple simultaneous connection attempts
    if (isConnecting.current) {
      console.log('Connection already in progress, skipping...');
      return;
    }

    // Rate limit connection attempts
    const now = Date.now();
    if (now - lastConnectionAttempt.current < minConnectionInterval) {
      console.log('Connection attempt too soon, waiting...');
      return;
    }

    // Close existing socket if any
    if (socket) {
      socket.close();
      setSocket(null);
      setIsConnected(false);
    }

    isConnecting.current = true;
    lastConnectionAttempt.current = now;

    // Get API URL from environment and convert to WebSocket URL
    const apiUrl = import.meta.env.VITE_API_URL || 'http://localhost:8000/api/v1';
    const wsUrl = apiUrl
      .replace('http://', 'ws://')
      .replace('https://', 'wss://') + `/chat/ws/${user.id}`;
    console.log('Attempting to connect to:', wsUrl);

    try {
      const newSocket = new WebSocket(wsUrl);

      newSocket.onopen = () => {
        console.log('🔥 WebSocket connected successfully for user:', user?.id);
        setIsConnected(true);
        setSocket(newSocket);
        reconnectAttempts.current = 0;
        isConnecting.current = false;

        // Start heartbeat to maintain connection
        startHeartbeat();
      };

      newSocket.onmessage = (event) => {
        try {
          const message: WebSocketMessage = JSON.parse(event.data);
          handleWebSocketMessage(message);
        } catch (error) {
          console.error('Error parsing WebSocket message:', error);
        }
      };

      newSocket.onclose = (event) => {
        console.log('WebSocket disconnected, code:', event.code, 'reason:', event.reason);
        setIsConnected(false);
        setSocket(null);
        isConnecting.current = false;

        // Don't reconnect if:
        // - Manual close (code 1000)
        // - Server rejected connection (code 1008 or 403-like codes)
        // - Max attempts reached
        const shouldReconnect = event.code !== 1000 &&
                               event.code !== 1008 &&
                               reconnectAttempts.current < maxReconnectAttempts;

        if (shouldReconnect) {
          reconnectAttempts.current++;
          const delay = Math.min(1000 * Math.pow(2, reconnectAttempts.current), 30000);

          console.log(`Scheduling reconnect attempt ${reconnectAttempts.current}/${maxReconnectAttempts} in ${delay}ms`);
          reconnectTimeoutRef.current = setTimeout(() => {
            connect();
          }, delay);
        } else {
          if (event.code === 1008) {
            console.log('Connection rejected by server (too many connections)');
          } else if (reconnectAttempts.current >= maxReconnectAttempts) {
            console.log('Max reconnection attempts reached');
          }
        }
      };

      newSocket.onerror = (error) => {
        console.error('WebSocket error:', error);
        isConnecting.current = false;
      };

      return newSocket;
    } catch (error) {
      console.error('Failed to create WebSocket:', error);
      isConnecting.current = false;
    }
  }, [user?.id, socket]);

  const sendDeliveryConfirmation = useCallback((messageId: number) => {
    if (!socket || !isConnected) {
      console.error('WebSocket not connected for delivery confirmation');
      return false;
    }

    const message = {
      type: 'message_delivered',
      message_id: messageId
    };

    try {
      socket.send(JSON.stringify(message));
      console.log('🔥 Delivery confirmation sent for message:', messageId);
      return true;
    } catch (error) {
      console.error('🔥 Error sending delivery confirmation:', error);
      return false;
    }
  }, [socket, isConnected]);

  const handleWebSocketMessage = useCallback((message: WebSocketMessage) => {
    console.log('🔥 HANDLING WEBSOCKET MESSAGE:', message);
    switch (message.type) {
      case 'new_message':
        const newMessage: ChatMessage = message.data;
        console.log('🔥 PROCESSING NEW MESSAGE:', newMessage);

        // Force immediate state update with React's flushSync for real-time feel
        flushSync(() => {
          setMessages(prev => {
            const existingMessages = prev[newMessage.chat_room_id] || [];

            // Check if message already exists to avoid duplicates
            const messageExists = existingMessages.some(msg => msg.id === newMessage.id);
            if (messageExists) {
              console.log('🔥 MESSAGE ALREADY EXISTS, SKIPPING');
              return prev;
            }

            // Insert message in chronological order
            const updatedMessages = [...existingMessages, newMessage].sort((a, b) =>
              new Date(a.created_at).getTime() - new Date(b.created_at).getTime()
            );

            console.log('🔥 REAL-TIME MESSAGE ADDED:', {
              roomId: newMessage.chat_room_id,
              messageId: newMessage.id,
              content: newMessage.content.substring(0, 50),
              previousCount: existingMessages.length,
              newCount: updatedMessages.length
            });

            return {
              ...prev,
              [newMessage.chat_room_id]: updatedMessages
            };
          });
        });

        // Send delivery confirmation for messages from other users
        if (newMessage.sender_id !== user?.id && socket && isConnected) {
          sendDeliveryConfirmation(newMessage.id);
        }
        break;

      case 'offline_message':
        const offlineMessage: ChatMessage = message.data;
        console.log('🔥 PROCESSING OFFLINE MESSAGE:', offlineMessage);
        flushSync(() => {
          setMessages(prev => {
            const existingMessages = prev[offlineMessage.chat_room_id] || [];

            // Check if message already exists to avoid duplicates
            const messageExists = existingMessages.some(msg => msg.id === offlineMessage.id);
            if (messageExists) {
              console.log('🔥 OFFLINE MESSAGE ALREADY EXISTS, SKIPPING');
              return prev;
            }

            // Insert offline message in chronological order
            const updatedMessages = [...existingMessages, offlineMessage].sort((a, b) =>
              new Date(a.created_at).getTime() - new Date(b.created_at).getTime()
            );
            return {
              ...prev,
              [offlineMessage.chat_room_id]: updatedMessages
            };
          });
        });

        // Send delivery confirmation for offline messages
        if (socket && isConnected) {
          sendDeliveryConfirmation(offlineMessage.id);
        }
        console.log('🔥 OFFLINE MESSAGE ADDED TO STATE');
        break;

      case 'online_status':
        const statusUpdate: OnlineStatus = message.data;
        setOnlineUsers(prev => ({
          ...prev,
          [statusUpdate.user_id]: statusUpdate.is_online
        }));
        break;

      case 'joined_chat':
        console.log('Joined chat room:', message.data.chat_room_id);
        break;

      case 'left_chat':
        console.log('Left chat room:', message.data.chat_room_id);
        break;

      case 'messages_marked_read':
        console.log('Messages marked as read in room:', message.data.chat_room_id);
        break;

      case 'pong':
        // Heartbeat response - connection is alive
        console.log('🔥 Received pong, connection is alive');
        break;

      case 'notification':
        const notification: RealtimeNotification = message.data;
        console.log('Received real-time notification:', notification);

        // Add to notifications list
        setRealtimeNotifications(prev => [notification, ...prev.slice(0, 19)]); // Keep latest 20

        // Update unread count if notification is unread
        if (!notification.is_read) {
          setUnreadNotificationCount(prev => prev + 1);
        }

        // Call notification handlers
        notificationHandlers.current.forEach(handler => {
          try {
            handler(notification);
          } catch (error) {
            console.error('Error in notification handler:', error);
          }
        });
        break;

      case 'message_error':
        console.error('🔥 MESSAGE ERROR:', message.data);
        // Handle message sending errors
        break;

      default:
        console.log('Unknown message type:', message.type);
    }
  }, [user?.id, socket, isConnected, sendDeliveryConfirmation]);

  const sendMessage = useCallback((chatRoomId: number, content: string) => {
    console.log('🔥 FRONTEND SEND_MESSAGE CALLED:', { chatRoomId, content, isConnected, hasSocket: !!socket });

    if (!socket || !isConnected) {
      console.error('🔥 WebSocket not connected - socket:', !!socket, 'isConnected:', isConnected);
      return false;
    }

    const message = {
      type: 'send_message',
      chat_room_id: chatRoomId,
      content: content
    };

    try {
      console.log('🔥 SENDING MESSAGE VIA WEBSOCKET:', message);
      socket.send(JSON.stringify(message));
      console.log('🔥 MESSAGE SENT SUCCESSFULLY');
      return true;
    } catch (error) {
      console.error('🔥 Error sending WebSocket message:', error);
      return false;
    }
  }, [socket, isConnected]);

  const joinChatRoom = useCallback((chatRoomId: number) => {
    console.log('🔥 FRONTEND JOIN_CHAT_ROOM CALLED:', { chatRoomId, isConnected, hasSocket: !!socket });

    if (!socket || !isConnected) {
      console.error('🔥 WebSocket not connected for join - socket:', !!socket, 'isConnected:', isConnected);
      return false;
    }

    const message = {
      type: 'join_chat',
      chat_room_id: chatRoomId
    };

    try {
      console.log('🔥 SENDING JOIN_CHAT MESSAGE:', message);
      socket.send(JSON.stringify(message));
      console.log('🔥 JOIN_CHAT MESSAGE SENT SUCCESSFULLY');
    } catch (error) {
      console.error('🔥 Error sending join message:', error);
      return false;
    }

    setCurrentChatRoom(chatRoomId);
    return true;
  }, [socket, isConnected]);

  const leaveChatRoom = useCallback((chatRoomId: number) => {
    if (!socket || !isConnected) {
      console.error('WebSocket not connected');
      return false;
    }

    const message = {
      type: 'leave_chat',
      chat_room_id: chatRoomId
    };

    socket.send(JSON.stringify(message));
    
    if (currentChatRoom === chatRoomId) {
      setCurrentChatRoom(null);
    }
    return true;
  }, [socket, isConnected, currentChatRoom]);

  const markMessagesAsRead = useCallback((chatRoomId: number) => {
    if (!socket || !isConnected) {
      console.error('WebSocket not connected');
      return false;
    }

    const message = {
      type: 'mark_read',
      chat_room_id: chatRoomId
    };

    socket.send(JSON.stringify(message));
    return true;
  }, [socket, isConnected]);

  const sendReadConfirmation = useCallback((messageId: number) => {
    if (!socket || !isConnected) {
      console.error('WebSocket not connected for read confirmation');
      return false;
    }

    const message = {
      type: 'message_read',
      message_id: messageId
    };

    try {
      socket.send(JSON.stringify(message));
      console.log('🔥 Read confirmation sent for message:', messageId);
      return true;
    } catch (error) {
      console.error('🔥 Error sending read confirmation:', error);
      return false;
    }
  }, [socket, isConnected]);

  const markNotificationAsRead = useCallback((notificationId: number) => {
    setRealtimeNotifications(prev =>
      prev.map(notif =>
        notif.id === notificationId
          ? { ...notif, is_read: true }
          : notif
      )
    );
    setUnreadNotificationCount(prev => Math.max(0, prev - 1));
  }, []);

  const clearNotification = useCallback((notificationId: number) => {
    setRealtimeNotifications(prev => prev.filter(notif => notif.id !== notificationId));
    // Don't update unread count here as the notification might already be read
  }, []);

  const clearAllNotifications = useCallback(() => {
    setRealtimeNotifications([]);
    setUnreadNotificationCount(0);
  }, []);

  const startHeartbeat = useCallback(() => {
    if (heartbeatIntervalRef.current) {
      clearInterval(heartbeatIntervalRef.current);
    }

    heartbeatIntervalRef.current = setInterval(() => {
      if (socket && isConnected) {
        try {
          // Send a ping message to keep connection alive
          socket.send(JSON.stringify({ type: 'ping' }));
        } catch (error) {
          console.error('🔥 Heartbeat failed:', error);
          // Connection is broken, try to reconnect
          disconnect();
          setTimeout(() => connect(), 1000);
        }
      }
    }, heartbeatInterval);
  }, [socket, isConnected]);

  const stopHeartbeat = useCallback(() => {
    if (heartbeatIntervalRef.current) {
      clearInterval(heartbeatIntervalRef.current);
      heartbeatIntervalRef.current = undefined;
    }
  }, []);

  const disconnect = useCallback(() => {
    console.log('Manually disconnecting WebSocket');

    // Clear any pending reconnection attempts
    if (reconnectTimeoutRef.current) {
      clearTimeout(reconnectTimeoutRef.current);
      reconnectTimeoutRef.current = undefined;
    }

    // Stop heartbeat
    stopHeartbeat();

    // Reset connection state
    isConnecting.current = false;
    reconnectAttempts.current = maxReconnectAttempts; // Prevent auto-reconnect

    if (socket) {
      socket.close(1000, 'Manual disconnect'); // Normal closure
    }

    setSocket(null);
    setIsConnected(false);
    setCurrentChatRoom(null);
    // Clear all chat data when disconnecting to prevent cross-user data leakage
    setMessages({});
    setOnlineUsers({});
  }, [socket, maxReconnectAttempts, stopHeartbeat]);

  // Connect when user is available (only when user changes, not when connect function changes)
  useEffect(() => {
    if (user?.id && !socket && !isConnecting.current) {
      console.log('User available, initiating WebSocket connection');
      connect();
    }

    return () => {
      // Only disconnect on unmount or user change
      if (!user?.id) {
        disconnect();
      }
    };
  }, [user?.id]); // Removed connect and disconnect from dependencies to prevent loops

  // Clear data when user changes to prevent cross-user data leakage
  useEffect(() => {
    // Clear all chat data when user changes
    setMessages({});
    setOnlineUsers({});
    setCurrentChatRoom(null);
  }, [user?.id]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      console.log('WebSocket hook unmounting, cleaning up...');
      disconnect();
    };
  }, []);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      if (reconnectTimeoutRef.current) {
        clearTimeout(reconnectTimeoutRef.current);
      }
    };
  }, []);

  // Function to set messages for a specific chat room (for loading existing messages)
  const setMessagesForRoom = useCallback((chatRoomId: number, roomMessages: ChatMessage[]) => {
    // Ensure messages are sorted chronologically
    const sortedMessages = roomMessages.sort((a, b) =>
      new Date(a.created_at).getTime() - new Date(b.created_at).getTime()
    );
    setMessages(prev => ({
      ...prev,
      [chatRoomId]: sortedMessages
    }));
  }, []);

  return {
    isConnected,
    messages,
    onlineUsers,
    currentChatRoom,
    sendMessage,
    joinChatRoom,
    leaveChatRoom,
    markMessagesAsRead,
    sendDeliveryConfirmation,
    sendReadConfirmation,
    setMessagesForRoom,
    connect,
    disconnect,
    // Notification functionality
    realtimeNotifications,
    unreadNotificationCount,
    addNotificationHandler,
    markNotificationAsRead,
    clearNotification,
    clearAllNotifications
  };
};
