import { renderHook, act } from '@testing-library/react';
import { useEmailResendCountdown } from '../useEmailResendCountdown';

// Mock localStorage
const localStorageMock = {
  getItem: jest.fn(),
  setItem: jest.fn(),
  removeItem: jest.fn(),
  clear: jest.fn(),
};

Object.defineProperty(window, 'localStorage', {
  value: localStorageMock,
});

// Mock timers
jest.useFakeTimers();

describe('useEmailResendCountdown', () => {
  beforeEach(() => {
    jest.clearAllMocks();
    localStorageMock.getItem.mockReturnValue(null);
  });

  afterEach(() => {
    jest.runOnlyPendingTimers();
    jest.useRealTimers();
  });

  it('should initialize with canResend true and remainingTime 0', () => {
    const { result } = renderHook(() =>
      useEmailResendCountdown({ email: '<EMAIL>' })
    );

    expect(result.current.canResend).toBe(true);
    expect(result.current.remainingTime).toBe(0);
  });

  it('should start countdown when startCountdown is called', () => {
    const { result } = renderHook(() =>
      useEmailResendCountdown({ email: '<EMAIL>', cooldownDuration: 60 })
    );

    act(() => {
      result.current.startCountdown();
    });

    expect(result.current.canResend).toBe(false);
    expect(result.current.remainingTime).toBe(60);
    expect(localStorageMock.setItem).toHaveBeenCalledWith(
      '<EMAIL>',
      expect.any(String)
    );
  });

  it('should countdown and reset after duration', () => {
    const { result } = renderHook(() =>
      useEmailResendCountdown({ email: '<EMAIL>', cooldownDuration: 3 })
    );

    act(() => {
      result.current.startCountdown();
    });

    expect(result.current.remainingTime).toBe(3);
    expect(result.current.canResend).toBe(false);

    // Fast forward 1 second
    act(() => {
      jest.advanceTimersByTime(1000);
    });

    expect(result.current.remainingTime).toBe(2);
    expect(result.current.canResend).toBe(false);

    // Fast forward 2 more seconds
    act(() => {
      jest.advanceTimersByTime(2000);
    });

    expect(result.current.remainingTime).toBe(0);
    expect(result.current.canResend).toBe(true);
    expect(localStorageMock.removeItem).toHaveBeenCalledWith(
      '<EMAIL>'
    );
  });

  it('should restore countdown from localStorage', () => {
    const now = Date.now();
    const fiveSecondsAgo = now - 5000;
    
    localStorageMock.getItem.mockReturnValue(fiveSecondsAgo.toString());
    
    const { result } = renderHook(() =>
      useEmailResendCountdown({ email: '<EMAIL>', cooldownDuration: 10 })
    );

    expect(result.current.canResend).toBe(false);
    expect(result.current.remainingTime).toBe(5);
  });

  it('should clean up expired countdown from localStorage', () => {
    const now = Date.now();
    const elevenSecondsAgo = now - 11000;
    
    localStorageMock.getItem.mockReturnValue(elevenSecondsAgo.toString());
    
    const { result } = renderHook(() =>
      useEmailResendCountdown({ email: '<EMAIL>', cooldownDuration: 10 })
    );

    expect(result.current.canResend).toBe(true);
    expect(result.current.remainingTime).toBe(0);
    expect(localStorageMock.removeItem).toHaveBeenCalledWith(
      '<EMAIL>'
    );
  });

  it('should reset countdown when resetCountdown is called', () => {
    const { result } = renderHook(() =>
      useEmailResendCountdown({ email: '<EMAIL>', cooldownDuration: 60 })
    );

    act(() => {
      result.current.startCountdown();
    });

    expect(result.current.canResend).toBe(false);
    expect(result.current.remainingTime).toBe(60);

    act(() => {
      result.current.resetCountdown();
    });

    expect(result.current.canResend).toBe(true);
    expect(result.current.remainingTime).toBe(0);
    expect(localStorageMock.removeItem).toHaveBeenCalledWith(
      '<EMAIL>'
    );
  });

  it('should handle missing email gracefully', () => {
    const { result } = renderHook(() =>
      useEmailResendCountdown({ email: undefined })
    );

    expect(result.current.canResend).toBe(true);
    expect(result.current.remainingTime).toBe(0);

    act(() => {
      result.current.startCountdown();
    });

    // Should not change state when email is missing
    expect(result.current.canResend).toBe(true);
    expect(result.current.remainingTime).toBe(0);
    expect(localStorageMock.setItem).not.toHaveBeenCalled();
  });
});
