import { useLocation } from 'react-router-dom';

/**
 * Hook to determine if we're in admin context
 * Returns true if current path starts with /admin
 */
export const useAdminContext = () => {
  const location = useLocation();
  return location.pathname.startsWith('/admin');
};

/**
 * Hook to get the correct navigation path based on context
 * Returns admin path if in admin context, regular path otherwise
 */
export const useContextualNavigation = () => {
  const isAdmin = useAdminContext();
  
  const getPath = (path: string) => {
    if (isAdmin && !path.startsWith('/admin')) {
      return `/admin${path}`;
    }
    return path;
  };
  
  return { getPath, isAdmin };
};
