import { useState, useEffect, useCallback, useRef } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import {
  getMLRecommendations,
  getLearningInsights,
  getUserFeatures,
  trackUserBehavior,
  startLearningSession,
  endLearningSession,
  provideRecommendationFeedback,
  refreshRecommendations,
  generateSessionId,
  MLRecommendation,
  LearningInsights,
  UserFeatureVector,
  UserLearningSession,
  BehaviorTrackingRequest,
  SessionStartRequest,
  SessionEndRequest,
  RecommendationFeedback,
  RecommendationResponse,
} from '../api/mlRecommendations';

export interface UseMLRecommendationsOptions {
  autoRefresh?: boolean;
  refreshInterval?: number;
  enableBehaviorTracking?: boolean;
  sessionTracking?: boolean;
}

export interface SessionMetrics {
  questionsAttempted: number;
  correctAnswers: number;
  flashcardsReviewed: number;
  helpRequests: number;
  bookmarksAdded: number;
  topicsCovered: Set<string>;
  startTime: Date;
}

export const useMLRecommendations = (options: UseMLRecommendationsOptions = {}) => {
  const {
    autoRefresh = false,
    refreshInterval = 60000, // 1 minute
    enableBehaviorTracking = true,
    sessionTracking = true,
  } = options;

  const queryClient = useQueryClient();
  const [currentSession, setCurrentSession] = useState<UserLearningSession | null>(null);
  const [sessionMetrics, setSessionMetrics] = useState<SessionMetrics>({
    questionsAttempted: 0,
    correctAnswers: 0,
    flashcardsReviewed: 0,
    helpRequests: 0,
    bookmarksAdded: 0,
    topicsCovered: new Set(),
    startTime: new Date(),
  });

  const sessionIdRef = useRef<string | null>(null);
  const behaviorQueueRef = useRef<BehaviorTrackingRequest[]>([]);

  // Query for ML recommendations
  const {
    data: recommendationsData,
    isLoading: recommendationsLoading,
    error: recommendationsError,
    refetch: refetchRecommendations,
  } = useQuery<RecommendationResponse>({
    queryKey: ['mlRecommendations'],
    queryFn: () => getMLRecommendations(),
    refetchInterval: autoRefresh ? refreshInterval : false,
    staleTime: 2 * 60 * 1000, // 2 minutes
    retry: (failureCount, error: any) => {
      // Don't retry on authentication errors
      if (error?.response?.status === 401) {
        return false;
      }
      return failureCount < 1;
    },
    throwOnError: false, // Don't throw errors - handle them silently
  });

  // Query for learning insights
  const {
    data: learningInsights,
    isLoading: insightsLoading,
    error: insightsError,
    refetch: refetchInsights,
  } = useQuery<LearningInsights>({
    queryKey: ['learningInsights'],
    queryFn: getLearningInsights,
    refetchInterval: autoRefresh ? refreshInterval * 2 : false, // Less frequent
    staleTime: 5 * 60 * 1000, // 5 minutes
    retry: (failureCount, error: any) => {
      // Don't retry on authentication errors
      if (error?.response?.status === 401) {
        return false;
      }
      return failureCount < 1;
    },
    throwOnError: false, // Don't throw errors - handle them silently
  });

  // Query for user features
  const {
    data: userFeatures,
    isLoading: featuresLoading,
    error: featuresError,
    refetch: refetchFeatures,
  } = useQuery<UserFeatureVector>({
    queryKey: ['userFeatures'],
    queryFn: getUserFeatures,
    staleTime: 10 * 60 * 1000, // 10 minutes
    retry: (failureCount, error: any) => {
      // Don't retry on authentication errors or 404s (expected for new users)
      if (error?.response?.status === 401 || error?.response?.status === 404) {
        return false;
      }
      return failureCount < 1;
    },
    throwOnError: false, // Don't throw any errors - handle them silently
  });

  // Mutation for tracking behavior
  const trackBehaviorMutation = useMutation({
    mutationFn: trackUserBehavior,
    onSuccess: () => {
      // Invalidate relevant queries after behavior tracking
      queryClient.invalidateQueries({ queryKey: ['userFeatures'] });
    },
    onError: (error) => {
      console.error('Failed to track behavior:', error);
    },
  });

  // Mutation for starting session
  const startSessionMutation = useMutation({
    mutationFn: startLearningSession,
    onSuccess: (session) => {
      setCurrentSession(session);
      sessionIdRef.current = session.session_id;
      setSessionMetrics({
        questionsAttempted: 0,
        correctAnswers: 0,
        flashcardsReviewed: 0,
        helpRequests: 0,
        bookmarksAdded: 0,
        topicsCovered: new Set(),
        startTime: new Date(),
      });
    },
    onError: (error) => {
      console.error('Failed to start session:', error);
    },
  });

  // Mutation for ending session
  const endSessionMutation = useMutation({
    mutationFn: ({ sessionId, request }: { sessionId: string; request: SessionEndRequest }) =>
      endLearningSession(sessionId, request),
    onSuccess: () => {
      setCurrentSession(null);
      sessionIdRef.current = null;
      // Refresh insights and recommendations after session ends
      queryClient.invalidateQueries({ queryKey: ['learningInsights'] });
      queryClient.invalidateQueries({ queryKey: ['mlRecommendations'] });
      queryClient.invalidateQueries({ queryKey: ['userFeatures'] });
    },
    onError: (error) => {
      console.error('Failed to end session:', error);
    },
  });

  // Mutation for recommendation feedback
  const feedbackMutation = useMutation({
    mutationFn: ({ id, feedback }: { id: number; feedback: RecommendationFeedback }) =>
      provideRecommendationFeedback(id, feedback),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['mlRecommendations'] });
    },
    onError: (error) => {
      console.error('Failed to provide feedback:', error);
    },
  });

  // Auto-start session on mount if session tracking is enabled
  useEffect(() => {
    if (sessionTracking && !currentSession && !sessionIdRef.current) {
      const sessionId = generateSessionId();
      sessionIdRef.current = sessionId;
      
      startSessionMutation.mutate({
        session_id: sessionId,
        device_type: /Mobile|Android|iPhone|iPad/.test(navigator.userAgent) ? 'mobile' : 'desktop',
      });
    }
  }, [sessionTracking, currentSession, startSessionMutation]);

  // Auto-end session on unmount
  useEffect(() => {
    return () => {
      if (currentSession && sessionIdRef.current) {
        endLearningSession(sessionIdRef.current, {
          total_questions: sessionMetrics.questionsAttempted,
          correct_answers: sessionMetrics.correctAnswers,
          total_flashcards: sessionMetrics.flashcardsReviewed,
          help_requests: sessionMetrics.helpRequests,
          bookmarks_added: sessionMetrics.bookmarksAdded,
          topics_covered: Array.from(sessionMetrics.topicsCovered),
        }).catch(console.error);
      }
    };
  }, []);

  // Batch process behavior tracking queue
  useEffect(() => {
    if (!enableBehaviorTracking) return;

    const interval = setInterval(() => {
      if (behaviorQueueRef.current.length > 0) {
        const behaviors = [...behaviorQueueRef.current];
        behaviorQueueRef.current = [];
        
        // Process behaviors in batch
        behaviors.forEach(behavior => {
          trackBehaviorMutation.mutate(behavior);
        });
      }
    }, 5000); // Process every 5 seconds

    return () => clearInterval(interval);
  }, [enableBehaviorTracking, trackBehaviorMutation]);

  // Helper functions
  const trackBehavior = useCallback(
    (behavior: Omit<BehaviorTrackingRequest, 'session_id' | 'device_type'>) => {
      if (!enableBehaviorTracking) return;

      const fullBehavior: BehaviorTrackingRequest = {
        ...behavior,
        session_id: sessionIdRef.current || undefined,
        device_type: /Mobile|Android|iPhone|iPad/.test(navigator.userAgent) ? 'mobile' : 'desktop',
      };

      // Add to queue for batch processing
      behaviorQueueRef.current.push(fullBehavior);

      // Update session metrics
      if (behavior.event_type === 'mcq_attempt') {
        setSessionMetrics(prev => ({
          ...prev,
          questionsAttempted: prev.questionsAttempted + 1,
          correctAnswers: prev.correctAnswers + (behavior.is_correct ? 1 : 0),
          topicsCovered: behavior.topic ? new Set([...prev.topicsCovered, behavior.topic]) : prev.topicsCovered,
        }));
      } else if (behavior.event_type === 'flashcard_view') {
        setSessionMetrics(prev => ({
          ...prev,
          flashcardsReviewed: prev.flashcardsReviewed + 1,
          topicsCovered: behavior.topic ? new Set([...prev.topicsCovered, behavior.topic]) : prev.topicsCovered,
        }));
      } else if (behavior.event_type === 'help_request') {
        setSessionMetrics(prev => ({
          ...prev,
          helpRequests: prev.helpRequests + 1,
        }));
      } else if (behavior.event_type === 'bookmark_add') {
        setSessionMetrics(prev => ({
          ...prev,
          bookmarksAdded: prev.bookmarksAdded + 1,
        }));
      }
    },
    [enableBehaviorTracking]
  );

  const trackQuestionAttempt = useCallback(
    (questionId: number, isCorrect: boolean, responseTime?: number, topic?: string, courseId?: number, difficulty?: string) => {
      trackBehavior({
        event_type: 'mcq_attempt',
        content_type: 'question',
        content_id: questionId,
        is_correct: isCorrect,
        response_time_seconds: responseTime,
        topic,
        course_id: courseId,
        difficulty,
      });
    },
    [trackBehavior]
  );

  const trackFlashcardView = useCallback(
    (flashcardId: number, topic?: string, courseId?: number) => {
      trackBehavior({
        event_type: 'flashcard_view',
        content_type: 'flashcard',
        content_id: flashcardId,
        topic,
        course_id: courseId,
      });
    },
    [trackBehavior]
  );

  const trackHelpRequest = useCallback(
    (contentType: string, contentId: number, topic?: string) => {
      trackBehavior({
        event_type: 'help_request',
        content_type: contentType,
        content_id: contentId,
        topic,
      });
    },
    [trackBehavior]
  );

  const trackBookmark = useCallback(
    (contentType: string, contentId: number, topic?: string) => {
      trackBehavior({
        event_type: 'bookmark_add',
        content_type: contentType,
        content_id: contentId,
        topic,
      });
    },
    [trackBehavior]
  );

  const markRecommendationViewed = useCallback(
    (recommendationId: number) => {
      feedbackMutation.mutate({
        id: recommendationId,
        feedback: { is_viewed: true },
      });
    },
    [feedbackMutation]
  );

  const acceptRecommendation = useCallback(
    (recommendationId: number) => {
      feedbackMutation.mutate({
        id: recommendationId,
        feedback: { is_viewed: true, is_accepted: true },
      });
    },
    [feedbackMutation]
  );

  const completeRecommendation = useCallback(
    (recommendationId: number, rating?: number) => {
      feedbackMutation.mutate({
        id: recommendationId,
        feedback: { is_viewed: true, is_accepted: true, is_completed: true, user_rating: rating },
      });
    },
    [feedbackMutation]
  );

  const endCurrentSession = useCallback(() => {
    if (currentSession && sessionIdRef.current) {
      endSessionMutation.mutate({
        sessionId: sessionIdRef.current,
        request: {
          total_questions: sessionMetrics.questionsAttempted,
          correct_answers: sessionMetrics.correctAnswers,
          total_flashcards: sessionMetrics.flashcardsReviewed,
          help_requests: sessionMetrics.helpRequests,
          bookmarks_added: sessionMetrics.bookmarksAdded,
          topics_covered: Array.from(sessionMetrics.topicsCovered),
        },
      });
    }
  }, [currentSession, sessionMetrics, endSessionMutation]);

  // Mutation for refreshing recommendations
  const refreshRecommendationsMutation = useMutation({
    mutationFn: (contentType: string = 'question') => refreshRecommendations(contentType),
    onSuccess: (data) => {
      // Update the cache with fresh recommendations
      queryClient.setQueryData(['mlRecommendations'], data);
      // Also refresh insights and features
      refetchInsights();
      refetchFeatures();
    },
    onError: (error) => {
      console.error('Failed to refresh recommendations:', error);
    },
  });

  const refreshAll = useCallback(() => {
    refetchRecommendations();
    refetchInsights();
    refetchFeatures();
  }, [refetchRecommendations, refetchInsights, refetchFeatures]);

  const forceRefreshRecommendations = useCallback((contentType: string = 'question') => {
    refreshRecommendationsMutation.mutate(contentType);
  }, [refreshRecommendationsMutation]);

  // Computed values
  const recommendations = recommendationsData?.recommendations || [];
  const highConfidenceRecommendations = recommendations.filter(rec => rec.confidence_score >= 0.7);
  const unviewedRecommendations = recommendations.filter(rec => !rec.is_viewed);

  const isLoading = recommendationsLoading || insightsLoading || featuresLoading;

  // Don't treat 404 user features as a fatal error - it's expected for new users
  const isFeaturesError404 = featuresError &&
    (featuresError as any)?.response?.status === 404;
  const hasError = recommendationsError || insightsError ||
    (featuresError && !isFeaturesError404);

  return {
    // Data
    recommendations,
    highConfidenceRecommendations,
    unviewedRecommendations,
    learningInsights,
    userFeatures,
    currentSession,
    sessionMetrics,

    // Loading states
    isLoading,
    recommendationsLoading,
    insightsLoading,
    featuresLoading,

    // Error states
    hasError,
    recommendationsError,
    insightsError,
    featuresError,

    // Tracking functions
    trackQuestionAttempt,
    trackFlashcardView,
    trackHelpRequest,
    trackBookmark,
    trackBehavior,

    // Recommendation interactions
    markRecommendationViewed,
    acceptRecommendation,
    completeRecommendation,

    // Session management
    endCurrentSession,

    // Utility functions
    refreshAll,
    forceRefreshRecommendations,

    // Mutation states
    isTrackingBehavior: trackBehaviorMutation.isPending,
    isStartingSession: startSessionMutation.isPending,
    isEndingSession: endSessionMutation.isPending,
    isProvidingFeedback: feedbackMutation.isPending,
    isRefreshingRecommendations: refreshRecommendationsMutation.isPending,
  };
};
