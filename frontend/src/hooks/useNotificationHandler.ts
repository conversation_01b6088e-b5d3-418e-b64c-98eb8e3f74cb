import { useEffect, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { useWebSocketChat, RealtimeNotification } from './useWebSocketChat';
import { NotificationType } from '../api/notifications';

interface NotificationHandlerOptions {
  autoNavigateToChat?: boolean;
  showToast?: boolean;
}

export const useNotificationHandler = (options: NotificationHandlerOptions = {}) => {
  const navigate = useNavigate();
  const { addNotificationHandler } = useWebSocketChat();
  const { autoNavigateToChat = true, showToast = true } = options;

  const handleNotification = useCallback((notification: RealtimeNotification) => {
    console.log('Received notification:', notification);

    // Handle booking acceptance notifications
    if (notification.type === NotificationType.BOOKING_ACCEPTED) {
      if (showToast) {
        // Show a toast notification (you can integrate with a toast library here)
        console.log('🎉 Booking accepted!', notification.message);
        
        // For now, we'll use a simple alert, but in production you'd use a proper toast library
        if (window.Notification && Notification.permission === 'granted') {
          new Notification(notification.title, {
            body: notification.message,
            icon: '/favicon.ico',
            tag: `notification-${notification.id}`
          });
        }
      }

      // Auto-navigate to chat if enabled and chat room is available
      if (autoNavigateToChat && notification.chat_room_id) {
        // Small delay to allow the notification to be processed
        setTimeout(() => {
          navigate(`/chat/${notification.chat_room_id}`);
        }, 1000);
      }
    }

    // Handle booking rejection notifications
    if (notification.type === NotificationType.BOOKING_REJECTED) {
      if (showToast) {
        console.log('❌ Booking rejected:', notification.message);
        
        if (window.Notification && Notification.permission === 'granted') {
          new Notification(notification.title, {
            body: notification.message,
            icon: '/favicon.ico',
            tag: `notification-${notification.id}`
          });
        }
      }
    }

    // Handle other notification types as needed
    if (notification.type === NotificationType.SYSTEM || 
        notification.type === NotificationType.ACHIEVEMENT ||
        notification.type === NotificationType.REMINDER) {
      if (showToast) {
        console.log('📢 System notification:', notification.message);
        
        if (window.Notification && Notification.permission === 'granted') {
          new Notification(notification.title, {
            body: notification.message,
            icon: '/favicon.ico',
            tag: `notification-${notification.id}`
          });
        }
      }
    }
  }, [navigate, autoNavigateToChat, showToast]);

  useEffect(() => {
    // Request notification permission if not already granted
    if (window.Notification && Notification.permission === 'default') {
      Notification.requestPermission();
    }

    // Register the notification handler
    const unsubscribe = addNotificationHandler(handleNotification);

    // Cleanup on unmount
    return unsubscribe;
  }, [addNotificationHandler, handleNotification]);

  return {
    // You can return additional utilities here if needed
    requestNotificationPermission: () => {
      if (window.Notification && Notification.permission === 'default') {
        return Notification.requestPermission();
      }
      return Promise.resolve(Notification.permission);
    }
  };
};
