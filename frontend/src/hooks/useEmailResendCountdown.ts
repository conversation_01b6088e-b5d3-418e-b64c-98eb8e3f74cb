import { useState, useEffect, useCallback } from 'react';

interface UseEmailResendCountdownProps {
  email?: string;
  cooldownDuration?: number; // in seconds
}

interface UseEmailResendCountdownReturn {
  canResend: boolean;
  remainingTime: number;
  startCountdown: () => void;
  resetCountdown: () => void;
}

const STORAGE_PREFIX = 'email_resend_cooldown_';

export const useEmailResendCountdown = ({
  email,
  cooldownDuration = 60
}: UseEmailResendCountdownProps): UseEmailResendCountdownReturn => {
  const [remainingTime, setRemainingTime] = useState(0);
  const [canResend, setCanResend] = useState(true);

  const getStorageKey = useCallback(() => {
    return email ? `${STORAGE_PREFIX}${email}` : null;
  }, [email]);

  const checkExistingCooldown = useCallback(() => {
    const storageKey = getStorageKey();
    if (!storageKey) return;

    const storedTimestamp = localStorage.getItem(storageKey);
    if (storedTimestamp) {
      const lastSentTime = parseInt(storedTimestamp, 10);
      const now = Date.now();
      const elapsed = Math.floor((now - lastSentTime) / 1000);
      const remaining = Math.max(0, cooldownDuration - elapsed);

      if (remaining > 0) {
        setRemainingTime(remaining);
        setCanResend(false);
      } else {
        // Cooldown expired, clean up storage
        localStorage.removeItem(storageKey);
        setRemainingTime(0);
        setCanResend(true);
      }
    }
  }, [getStorageKey, cooldownDuration]);

  const startCountdown = useCallback(() => {
    const storageKey = getStorageKey();
    if (!storageKey) return;

    const now = Date.now();
    localStorage.setItem(storageKey, now.toString());
    setRemainingTime(cooldownDuration);
    setCanResend(false);
  }, [getStorageKey, cooldownDuration]);

  const resetCountdown = useCallback(() => {
    const storageKey = getStorageKey();
    if (storageKey) {
      localStorage.removeItem(storageKey);
    }
    setRemainingTime(0);
    setCanResend(true);
  }, [getStorageKey]);

  // Check for existing cooldown on mount and when email changes
  useEffect(() => {
    checkExistingCooldown();
  }, [checkExistingCooldown]);

  // Countdown timer effect
  useEffect(() => {
    let timer: NodeJS.Timeout;

    if (remainingTime > 0) {
      timer = setInterval(() => {
        setRemainingTime((prev) => {
          const newTime = prev - 1;
          if (newTime <= 0) {
            setCanResend(true);
            const storageKey = getStorageKey();
            if (storageKey) {
              localStorage.removeItem(storageKey);
            }
            return 0;
          }
          return newTime;
        });
      }, 1000);
    }

    return () => {
      if (timer) {
        clearInterval(timer);
      }
    };
  }, [remainingTime, getStorageKey]);

  // Cleanup on unmount
  useEffect(() => {
    return () => {
      // Don't clear the countdown on unmount - let it persist
      // This allows the countdown to continue even if user navigates away
    };
  }, []);

  return {
    canResend,
    remainingTime,
    startCountdown,
    resetCountdown
  };
};
