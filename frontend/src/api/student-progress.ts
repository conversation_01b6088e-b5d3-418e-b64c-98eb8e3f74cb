import apiClient from './client';

export interface StudentProgress {
  id: number;
  user_id: number;
  course_id: number;
  total_questions_attempted: number;
  correct_answers: number;
  accuracy_percentage: number;
  total_time_spent: number;
  last_activity: string;
  created_at: string;
  updated_at: string;
}

export interface StudentQuestionAttempt {
  id: number;
  user_id: number;
  question_id: number;
  selected_answer: string;
  is_correct: boolean;
  time_taken: number;
  created_at: string;
}

export interface StudentBookmark {
  id: number;
  user_id: number;
  question_id: number;
  created_at: string;
}

export interface StudentExam {
  id: number;
  user_id: number;
  course_id: number;
  exam_type: string;
  total_questions: number;
  correct_answers: number;
  score_percentage: number;
  time_taken: number;
  completed_at: string;
  created_at: string;
}

// API Functions
export const getStudentProgress = async (courseId?: number): Promise<StudentProgress[]> => {
  const params = courseId ? `?course_id=${courseId}` : '';
  const response = await apiClient.get<StudentProgress[]>(`/student-progress${params}`);
  return response.data;
};

export const getStudentAttempts = async (questionId?: number): Promise<StudentQuestionAttempt[]> => {
  const params = questionId ? `?question_id=${questionId}` : '';
  const response = await apiClient.get<StudentQuestionAttempt[]>(`/student-attempts${params}`);
  return response.data;
};

export const getStudentBookmarks = async (): Promise<StudentBookmark[]> => {
  const response = await apiClient.get<StudentBookmark[]>('/student-bookmarks');
  return response.data;
};

export const addBookmark = async (questionId: number): Promise<StudentBookmark> => {
  const response = await apiClient.post<StudentBookmark>('/student-bookmarks', {
    question_id: questionId
  });
  return response.data;
};

export const removeBookmark = async (questionId: number): Promise<void> => {
  await apiClient.delete(`/student-bookmarks/${questionId}`);
};

export const getStudentExams = async (): Promise<StudentExam[]> => {
  const response = await apiClient.get<StudentExam[]>('/student-exams');
  return response.data;
};
