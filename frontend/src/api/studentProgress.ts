import apiClient from './client';

// Question Attempt Types
export interface QuestionAttempt {
  id: number;
  student_id: number;
  question_id: number;
  is_correct: boolean;
  selected_answer: string | null;
  attempt_time: string;
}

export interface QuestionAttemptCreate {
  student_id: number;
  question_id: number;
  is_correct: boolean;
  selected_answer?: string;
}

// Bookmark Types
export interface Bookmark {
  id: number;
  student_id: number;
  question_id: number;
  created_at: string;
  notes: string | null;
}

export interface BookmarkCreate {
  student_id: number;
  question_id: number;
  notes?: string;
}

export interface BookmarkUpdate {
  notes?: string;
}

// Exam Types
export interface Exam {
  id: number;
  student_id: number;
  course_id: number;
  start_time: string;
  end_time: string | null;
  score: number | null;
  total_questions: number;
  correct_answers: number | null;
}

export interface ExamCreate {
  student_id: number;
  course_id?: number | null;
  total_questions: number;
  score?: number;
  correct_answers?: number;
  end_time?: string;
  // Fields for note-generated content
  is_note_generated?: boolean;
  note_job_id?: number;
  course_name?: string;
}

export interface ExamUpdate {
  score?: number;
  correct_answers?: number;
  end_time?: string;
}

export interface ExamAttempt {
  id: number;
  exam_id: number;
  question_id: number;
  is_correct: boolean;
  selected_answer: string | null;
  time_spent_seconds?: number;
}

export interface ExamAttemptCreate {
  exam_id: number;
  question_id: number;
  is_correct: boolean;
  selected_answer?: string;
  time_spent_seconds?: number;
}

export interface ExamWithAttempts extends Exam {
  attempts: ExamAttempt[];
}

// Question Attempt API Functions
export const createQuestionAttempt = async (data: QuestionAttemptCreate): Promise<QuestionAttempt> => {
  const response = await apiClient.post<QuestionAttempt>('/student-progress/attempts', data);
  return response.data;
};

export const getQuestionAttempts = async (
  studentId: number,
  questionId?: number
): Promise<QuestionAttempt[]> => {
  const params: Record<string, any> = { student_id: studentId };
  if (questionId) params.question_id = questionId;

  const response = await apiClient.get<QuestionAttempt[]>('/student-progress/attempts', { params });
  return response.data;
};

export const getCourseAttempts = async (
  studentId: number,
  courseId: number
): Promise<QuestionAttempt[]> => {
  const params: Record<string, any> = { student_id: studentId };

  const response = await apiClient.get<QuestionAttempt[]>(
    `/student-progress/attempts/course/${courseId}`,
    { params }
  );
  return response.data;
};

// Bookmark API Functions
export const createBookmark = async (data: BookmarkCreate): Promise<Bookmark> => {
  const response = await apiClient.post<Bookmark>('/student-progress/bookmarks', data);
  return response.data;
};

export const getBookmarks = async (
  studentId: number,
  courseId?: number
): Promise<Bookmark[]> => {
  const params: Record<string, any> = { student_id: studentId };
  if (courseId) params.course_id = courseId;

  const response = await apiClient.get<Bookmark[]>('/student-progress/bookmarks', { params });
  return response.data;
};

export const updateBookmark = async (
  id: number,
  data: BookmarkUpdate
): Promise<Bookmark> => {
  const response = await apiClient.put<Bookmark>(`/student-progress/bookmarks/${id}`, data);
  return response.data;
};

export const deleteBookmark = async (id: number): Promise<Bookmark> => {
  const response = await apiClient.delete<Bookmark>(`/student-progress/bookmarks/${id}`);
  return response.data;
};

// Exam API Functions
export const createExam = async (data: ExamCreate): Promise<Exam> => {
  const response = await apiClient.post<Exam>('/student-progress/exams', data);
  return response.data;
};

export const getExams = async (
  studentId: number,
  courseId?: number
): Promise<Exam[]> => {
  const params: Record<string, any> = { student_id: studentId };
  if (courseId) params.course_id = courseId;

  const response = await apiClient.get<Exam[]>('/student-progress/exams', { params });
  return response.data;
};

export const getExamWithAttempts = async (id: number): Promise<ExamWithAttempts> => {
  const response = await apiClient.get<ExamWithAttempts>(`/student-progress/exams/${id}`);
  return response.data;
};

export const updateExam = async (
  id: number,
  data: ExamUpdate
): Promise<Exam> => {
  const response = await apiClient.put<Exam>(`/student-progress/exams/${id}`, data);
  return response.data;
};

export const createExamAttempt = async (data: ExamAttemptCreate): Promise<ExamAttempt> => {
  const response = await apiClient.post<ExamAttempt>('/student-progress/exams/attempts', data);
  return response.data;
};

// Topic Analysis Types
export interface TopicAnalysis {
  [topic: string]: {
    correct_count: number;
    total_count: number;
    time_spent_total: number;
    score: number;
    avg_time_spent: number;
    exams_count?: number;
  };
}

// Topic Analysis API Functions
export const getExamTopicAnalysis = async (examId: number): Promise<TopicAnalysis> => {
  const response = await apiClient.get<TopicAnalysis>(`/student-progress/exams/${examId}/topic-analysis`);
  return response.data;
};

export const getCourseTopicAnalysis = async (
  studentId: number,
  courseId: number
): Promise<TopicAnalysis> => {
  const params: Record<string, any> = { student_id: studentId };
  const response = await apiClient.get<TopicAnalysis>(
    `/student-progress/courses/${courseId}/topic-analysis`,
    { params }
  );
  return response.data;
};

// MCQ Stats Types
export interface MCQStats {
  total_exams: number;
  average_score: number;
  total_practice_questions: number;
}

// MCQ Stats API Functions
export const getMCQStats = async (): Promise<MCQStats> => {
  const response = await apiClient.get<MCQStats>('/student-progress/stats/mcq');
  return response.data;
};

// Performance Data Types
export interface PerformanceData {
  correctAnswers: number;
  incorrectAnswers: number;
  examScores: { name: string; score: number }[];
  difficultyBreakdown: { name: string; value: number; color: string }[];
}

// Comprehensive Analytics Types
export interface PerformanceOverview {
  total_practice_questions: number;
  correct_practice: number;
  incorrect_practice: number;
  practice_accuracy: number;
  total_exams: number;
  avg_exam_score: number;
  best_exam_score: number;
  worst_exam_score: number;
}

export interface PerformanceTrendPoint {
  date: string;
  accuracy: number;
  total_questions: number;
  correct_answers: number;
}

export interface ExamHistoryItem {
  id: number;
  date: string;
  score: number;
  total_questions: number;
  correct_answers: number;
  course_id?: number;
}

export interface CoursePerformance {
  course_id: number;
  course_name: string;
  practice_accuracy: number;
  total_practice: number;
  correct_practice: number;
  avg_exam_score: number;
}

export interface PerformanceInsights {
  improvement_rate: number;
  consistency_score: number;
  active_days: number;
  avg_daily_questions: number;
  days_analyzed: number;
}

export interface StreakData {
  current_streak: number;
  best_streak: number;
  streak_type: string;
}

export interface Milestone {
  type: string;
  target?: number;
  current?: number;
  progress?: number;
  description: string;
}

export interface ChallengeMetrics {
  streak_potential: StreakData;
  next_milestone: Milestone;
  improvement_areas: string[];
}

export interface ComprehensiveAnalytics {
  overview: PerformanceOverview;
  performance_trend: PerformanceTrendPoint[];
  exam_history: ExamHistoryItem[];
  course_performance: CoursePerformance[];
  insights: PerformanceInsights;
  challenge_metrics: ChallengeMetrics;
}

export interface TrendAnalytics {
  performance_trend: PerformanceTrendPoint[];
  insights: PerformanceInsights;
  overview: PerformanceOverview;
}

// Performance Data API Functions
export const getPerformanceData = async (): Promise<PerformanceData> => {
  const response = await apiClient.get<PerformanceData>('/student-progress/stats/performance');
  return response.data;
};

// Comprehensive Analytics API Functions
export const getComprehensiveAnalytics = async (
  courseId?: number,
  days: number = 30
): Promise<ComprehensiveAnalytics> => {
  const params = new URLSearchParams();
  if (courseId) params.append('course_id', courseId.toString());
  params.append('days', days.toString());

  const response = await apiClient.get<ComprehensiveAnalytics>(
    `/student-progress/analytics/comprehensive?${params.toString()}`
  );
  return response.data;
};

export const getCourseComparisonAnalytics = async (): Promise<ComprehensiveAnalytics> => {
  const response = await apiClient.get<ComprehensiveAnalytics>('/student-progress/analytics/course-comparison');
  return response.data;
};

export const getPerformanceTrends = async (
  courseId?: number,
  period: 'week' | 'month' | 'quarter' = 'month'
): Promise<TrendAnalytics> => {
  const params = new URLSearchParams();
  if (courseId) params.append('course_id', courseId.toString());
  params.append('period', period);

  const response = await apiClient.get<TrendAnalytics>(
    `/student-progress/analytics/trends?${params.toString()}`
  );
  return response.data;
};
