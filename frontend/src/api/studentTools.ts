import apiClient from './client';
import {
  GeminiFileUploadResponse,
  GeminiMCQGenerationResponse,
  GeminiFlashcardGenerationResponse,
  GeminiGeneratedFlashcard,
  GeminiFileListResponse,
  GeminiNoteExplanationGenerationResponse,
  GeminiGeneratedNoteExplanation,
  GeneratedNoteExplanation
} from '../types/studentTools';
import { Question } from './questions';





























// Gemini Files API Functions

/**
 * Get list of uploaded files for the current student
 * @param skip Number of items to skip (default: 0)
 * @param limit Maximum number of items to return (default: 100)
 * @param courseName Optional filter by course name
 * @returns List of uploaded files
 */
export const getUploadedFiles = async (
  skip: number = 0,
  limit: number = 100,
  courseName?: string
): Promise<GeminiFileListResponse> => {
  try {
    console.log('Fetching uploaded files:', { skip, limit, courseName });

    const params = new URLSearchParams();
    params.append('skip', skip.toString());
    params.append('limit', limit.toString());
    if (courseName) {
      params.append('course_name', courseName);
    }

    const response = await apiClient.get(`/student-tools/gemini/files?${params.toString()}`);

    console.log('Uploaded files response:', response.data);
    return response.data;
  } catch (error) {
    console.error('Error fetching uploaded files:', error);
    throw error;
  }
};



/**
 * Upload PDF directly to Gemini Files API
 * @param file PDF file to upload
 * @param courseName Optional course name for context
 * @returns Upload response with file details
 */
export const uploadPDFToGemini = async (
  file: File,
  courseName?: string
): Promise<any> => {
  try {
    console.log('Starting direct upload for PDF:', file.name);

    const formData = new FormData();
    formData.append('file', file);
    if (courseName) {
      formData.append('course_name', courseName);
    }

    const response = await apiClient.post('/uploads/start', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      timeout: 120000, // 2 minutes for direct upload
    });

    console.log('Direct upload completed:', response.data);
    return response.data;
  } catch (error) {
    console.error('Error during direct upload:', error);
    throw error;
  }
};

// Upload status tracking removed - using direct uploads now

// Upload list tracking removed - using direct uploads now

// Active uploads tracking removed - using direct uploads now

// Upload cancellation removed - using direct uploads now

// Upload deletion removed - using direct uploads now

/**
 * Generate MCQs from Gemini file using structured output
 * @param geminiFileId Gemini file ID from upload
 * @param questionCount Number of questions to generate (1-50)
 * @param courseName Optional course context
 * @returns Gemini MCQ generation response with saved question IDs
 */
export const generateMCQsFromGeminiFile = async (
  geminiFileId: string,
  questionCount: number = 10,
  courseName?: string
): Promise<{
  questions: GeminiGeneratedMCQ[];
  total_count: number;
  source_topics: string[];
  saved_question_ids: number[];
  message: string;
}> => {
  try {
    console.log('Generating MCQs from Gemini file:', {
      geminiFileId,
      questionCount,
      courseName,
    });

    const formData = new FormData();
    formData.append('gemini_file_id', geminiFileId);
    formData.append('question_count', questionCount.toString());
    if (courseName) {
      formData.append('course_name', courseName);
    }

    const response = await apiClient.post('/student-tools/gemini/generate-mcqs', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      timeout: 180000, // 3 minutes for generation
    });

    console.log('Gemini MCQ generation response:', response.data);
    return response.data;
  } catch (error) {
    console.error('Error generating MCQs from Gemini file:', error);
    throw error;
  }
};

/**
 * Generate MCQs from Gemini file (simplified interface)
 * @param params Object containing gemini_file_id and question_count
 * @returns Gemini MCQ generation response
 */
export const generateMCQsFromGemini = async (params: {
  gemini_file_id: string;
  question_count: number;
  course_name?: string;
}): Promise<GeminiMCQGenerationResponse> => {
  try {
    console.log('Generating MCQs from Gemini file:', params);

    const formData = new FormData();
    formData.append('gemini_file_id', params.gemini_file_id);
    formData.append('question_count', params.question_count.toString());
    if (params.course_name) {
      formData.append('course_name', params.course_name);
    }

    const response = await apiClient.post('/student-tools/gemini/generate-mcqs', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      timeout: 180000, // 3 minutes for generation
    });

    console.log('MCQ generation response:', response.data);
    return response.data;
  } catch (error) {
    console.error('Error generating MCQs from Gemini:', error);
    throw error;
  }
};

/**
 * Generate flashcards from Gemini file using structured output
 * @param geminiFileId Gemini file ID from upload
 * @param cardCount Number of flashcards to generate (1-30)
 * @param courseName Optional course context
 * @returns Gemini flashcard generation response with saved flashcard IDs
 */
export const generateFlashcardsFromGeminiFile = async (
  geminiFileId: string,
  cardCount: number = 10,
  courseName?: string
): Promise<{
  flashcards: GeminiGeneratedFlashcard[];
  total_count: number;
  source_topics: string[];
  saved_flashcard_ids: number[];
  message: string;
}> => {
  try {
    console.log('Generating flashcards from Gemini file:', {
      geminiFileId,
      cardCount,
      courseName,
    });

    const formData = new FormData();
    formData.append('gemini_file_id', geminiFileId);
    formData.append('card_count', cardCount.toString());
    if (courseName) {
      formData.append('course_name', courseName);
    }

    const response = await apiClient.post('/student-tools/gemini/generate-flashcards', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      timeout: 180000, // 3 minutes for generation
    });

    console.log('Gemini flashcard generation response:', response.data);
    return response.data;
  } catch (error) {
    console.error('Error generating flashcards from Gemini file:', error);
    throw error;
  }
};

/**
 * Generate flashcards from Gemini file (simplified interface)
 * @param params Object containing gemini_file_id and card_count
 * @returns Gemini flashcard generation response
 */
export const generateFlashcardsFromGemini = async (params: {
  gemini_file_id: string;
  card_count: number;
  course_name?: string;
}): Promise<GeminiFlashcardGenerationResponse> => {
  try {
    console.log('Generating flashcards from Gemini file:', params);

    const formData = new FormData();
    formData.append('gemini_file_id', params.gemini_file_id);
    formData.append('card_count', params.card_count.toString());
    if (params.course_name) {
      formData.append('course_name', params.course_name);
    }

    const response = await apiClient.post('/student-tools/gemini/generate-flashcards', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      timeout: 180000, // 3 minutes for generation
    });

    console.log('Flashcard generation response:', response.data);
    return response.data;
  } catch (error) {
    console.error('Error generating flashcards from Gemini:', error);
    throw error;
  }
};

/**
 * Get generated flashcards for the current student
 * @param params Optional filtering parameters
 * @returns Array of generated flashcards
 */
export const getGeneratedFlashcards = async (params?: {
  skip?: number;
  limit?: number;
  course_name?: string;
  difficulty?: string;
  flashcard_ids?: string;
}): Promise<Array<{
  id: number;
  front_content: string;
  back_content: string;
  topic: string;
  difficulty: string;
  card_type: string;
  course_name: string;
  created_at: string;
  updated_at: string;
}>> => {
  try {
    const response = await apiClient.get('/student-tools/generated-flashcards', {
      params,
      timeout: 30000,
    });

    return response.data;
  } catch (error) {
    throw error;
  }
};

/**
 * Get MCQs generated by the current student
 * @param courseName Optional filter by course name
 * @returns List of generated MCQs
 */
export const getMyGeneratedMCQs = async (courseName?: string): Promise<Question[]> => {
  try {
    const params: Record<string, any> = {};
    if (courseName) {
      params.course_name = courseName;
    }

    const response = await apiClient.get('/student-tools/mcq/my-questions', { params });
    console.log('My generated MCQs:', response.data);
    return response.data;
  } catch (error) {
    console.error('Error fetching my generated MCQs:', error);
    throw error;
  }
};



/**
 * Delete a file from Gemini Files API
 * @param geminiFileId Gemini file ID to delete
 * @returns Promise that resolves when file is deleted
 */
export const deleteGeminiFile = async (geminiFileId: string): Promise<{ message: string; gemini_file_id: string }> => {
  try {
    console.log('Deleting Gemini file:', geminiFileId);

    // Remove 'files/' prefix if present to avoid duplicate in URL
    const fileId = geminiFileId.startsWith('files/') ? geminiFileId.substring(6) : geminiFileId;
    const response = await apiClient.delete(`/student-tools/gemini/files/${fileId}`);

    console.log('Delete Gemini file response:', response.data);
    return response.data;
  } catch (error) {
    console.error('Error deleting Gemini file:', error);
    throw error;
  }
};

/**
 * Trigger cleanup of expired Gemini files
 * @returns Cleanup statistics
 */
export const cleanupExpiredGeminiFiles = async (): Promise<{
  message: string;
  deleted_count: number;
  failed_count: number;
  total_processed: number;
}> => {
  try {
    console.log('Triggering cleanup of expired Gemini files');

    const response = await apiClient.post('/student-tools/gemini/cleanup-expired');

    console.log('Gemini file cleanup response:', response.data);
    return response.data;
  } catch (error) {
    console.error('Error cleaning up expired Gemini files:', error);
    throw error;
  }
};

/**
 * Generate note explanation from Gemini file using structured output
 * @param geminiFileId Gemini file ID from upload
 * @param courseName Optional course context
 * @returns Gemini note explanation generation response with saved explanation ID
 */
export const generateNoteExplanationFromGeminiFile = async (
  geminiFileId: string,
  courseName?: string
): Promise<{
  explanation: GeminiGeneratedNoteExplanation;
  source_info: Record<string, any>;
  saved_explanation_id: number;
  message: string;
}> => {
  try {
    console.log('Generating note explanation from Gemini file:', {
      geminiFileId,
      courseName,
    });

    const formData = new FormData();
    formData.append('gemini_file_id', geminiFileId);
    if (courseName) {
      formData.append('course_name', courseName);
    }

    const response = await apiClient.post('/student-tools/gemini/generate-note-explanation', formData, {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      timeout: 300000, // 5 minutes for generation (longer than MCQ/flashcards due to comprehensive analysis)
    });

    console.log('Gemini note explanation generation response:', response.data);
    return response.data;
  } catch (error) {
    console.error('Error generating note explanation from Gemini file:', error);
    throw error;
  }
};

/**
 * Generate note explanation from Gemini file (simplified interface)
 * @param params Object containing gemini_file_id and optional course_name
 */
export const generateNoteExplanationFromGemini = async (params: {
  gemini_file_id: string;
  course_name?: string;
}): Promise<GeminiNoteExplanationGenerationResponse> => {
  return generateNoteExplanationFromGeminiFile(params.gemini_file_id, params.course_name);
};

/**
 * Get generated note explanations for the current student
 * @param params Query parameters for filtering
 * @returns Array of generated note explanations
 */
export const getGeneratedNoteExplanations = async (params?: {
  skip?: number;
  limit?: number;
  course_name?: string;
  explanation_id?: number;
}): Promise<GeneratedNoteExplanation[]> => {
  try {
    console.log('Fetching generated note explanations:', params);

    const queryParams = new URLSearchParams();
    if (params?.skip !== undefined) queryParams.append('skip', params.skip.toString());
    if (params?.limit !== undefined) queryParams.append('limit', params.limit.toString());
    if (params?.course_name) queryParams.append('course_name', params.course_name);
    if (params?.explanation_id !== undefined) queryParams.append('explanation_id', params.explanation_id.toString());

    const response = await apiClient.get(`/student-tools/generated-note-explanations?${queryParams.toString()}`);

    console.log('Generated note explanations response:', response.data);
    return response.data;
  } catch (error) {
    console.error('Error fetching generated note explanations:', error);
    throw error;
  }
};

/**
 * Get usage statistics for the current student
 * @param month Optional month in YYYY-MM format (defaults to current month)
 * @returns Usage statistics for all usage types
 */
export const getUsageStatistics = async (month?: string): Promise<{
  student_id: number;
  month: string;
  usage_stats: {
    [key: string]: {
      current_usage: number;
      limit: number;
      remaining: number;
      is_limit_reached: boolean;
      month: string;
    };
  };
}> => {
  try {
    console.log('Fetching usage statistics:', { month });

    const params = new URLSearchParams();
    if (month) {
      params.append('month', month);
    }

    const response = await apiClient.get(`/student-tools/usage-stats?${params.toString()}`);

    console.log('Usage statistics response:', response.data);
    return response.data;
  } catch (error) {
    console.error('Error fetching usage statistics:', error);
    throw error;
  }
};

/**
 * Stream document chat responses using Gemini Files API
 * @param geminiFileId Gemini file ID to chat about
 * @param query The question to ask about the document
 * @param onEvent Callback function to handle streaming events
 * @returns A function to abort the stream
 */
export const streamDocumentChat = (
  geminiFileId: string,
  query: string,
  onEvent: (event: any) => void
): () => void => {
  // Create an AbortController to handle cancellation
  const controller = new AbortController();
  const signal = controller.signal;

  // Notify that we're starting
  onEvent({
    type: 'start',
    content: '',
  });

  // Create form data
  const formData = new FormData();
  formData.append('gemini_file_id', geminiFileId);
  formData.append('query', query);

  // Get the auth token from localStorage (same key as used in apiClient)
  const token = localStorage.getItem('token');

  // Use fetch with POST method for streaming
  fetch(`${apiClient.defaults.baseURL}/student-tools/gemini/stream/document-chat`, {
    method: 'POST',
    body: formData,
    headers: {
      'Accept': 'text/event-stream',
      'Authorization': token ? `Bearer ${token}` : '',
    },
    signal: signal,
  })
    .then(response => {
      if (!response.ok) {
        throw new Error(`HTTP error! Status: ${response.status}`);
      }

      // Get the reader from the response body stream
      const reader = response.body!.getReader();
      const decoder = new TextDecoder();
      let buffer = '';

      // Function to process the stream
      function processStream(): Promise<void> {
        return reader.read().then(({ done, value }) => {
          if (done) {
            // Handle any remaining data in the buffer
            if (buffer.trim()) {
              try {
                const lines = buffer.split('\n\n');
                for (const line of lines) {
                  if (line.startsWith('data: ')) {
                    const eventData = JSON.parse(line.substring(6));
                    onEvent(eventData);
                  }
                }
              } catch (e) {
                console.error('Error parsing final buffer:', e);
              }
            }
            return;
          }

          // Decode the chunk and add it to the buffer
          const chunk = decoder.decode(value, { stream: true });
          buffer += chunk;

          // Process complete lines
          const lines = buffer.split('\n\n');
          buffer = lines.pop() || ''; // Keep the incomplete line in the buffer

          for (const line of lines) {
            if (line.startsWith('data: ')) {
              try {
                const eventData = JSON.parse(line.substring(6));
                onEvent(eventData);
              } catch (e) {
                console.error('Error parsing event data:', e, 'Line:', line);
              }
            }
          }

          // Continue reading
          return processStream();
        });
      }

      // Start processing the stream
      return processStream();
    })
    .catch(error => {
      if (error.name !== 'AbortError') {
        console.error('Stream error:', error);
        onEvent({
          type: 'error',
          error: error.message || 'Failed to stream document chat'
        });
      }
    });

  // Return abort function
  return () => {
    controller.abort();
  };
};
