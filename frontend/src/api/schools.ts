import apiClient from './client';

export interface School {
  id: number;
  name: string;
  description: string;
  location: string;
  is_active: boolean;
  created_at: string;
  updated_at: string;
}

export interface SchoolCreate {
  name: string;
  description: string;
  location: string;
  is_active?: boolean;
}

export interface SchoolUpdate {
  name?: string;
  description?: string;
  location?: string;
  is_active?: boolean;
}

export const getSchools = async (): Promise<School[]> => {
  console.log('Fetching schools from public API...');
  console.log('API base URL:', apiClient.defaults.baseURL);
  try {
    const response = await apiClient.get<School[]>('/schools/public');
    console.log('Schools API response:', response);
    console.log('Schools fetched successfully:', response.data);
    return response.data;
  } catch (error) {
    console.error('Error fetching schools:', error);
    throw error;
  }
};

export const getSchoolsPublic = async (): Promise<School[]> => {
  console.log('Fetching public schools from API...');
  const response = await apiClient.get<School[]>('/schools/public');
  console.log('Public schools fetched successfully:', response.data);
  return response.data;
};

export const getSchool = async (id: number): Promise<School> => {
  const response = await apiClient.get<School>(`/schools/${id}`);
  return response.data;
};

export const createSchool = async (data: SchoolCreate): Promise<School> => {
  const response = await apiClient.post<School>('/schools', data);
  return response.data;
};

export const updateSchool = async (id: number, data: SchoolUpdate): Promise<School> => {
  const response = await apiClient.put<School>(`/schools/${id}`, data);
  return response.data;
};

export const deleteSchool = async (id: number): Promise<School> => {
  const response = await apiClient.delete<School>(`/schools/${id}`);
  return response.data;
};
