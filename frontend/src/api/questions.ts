import apiClient from './client';

export type QuestionType = 'multiple_choice' | 'flashcard' | 'open_ended';
export type DifficultyLevel = 'easy' | 'medium' | 'hard';

export interface Question {
  id: number;
  content: string;
  question_type: QuestionType;
  difficulty: DifficultyLevel;
  topic?: string;
  options?: Record<string, string>;
  answer: string;
  explanation?: string;
  media_url?: string;
  is_active: boolean;
  course_id: number;
  created_by_id: number;
  created_at: string;
  updated_at: string;
}

export interface QuestionCreate {
  content: string;
  question_type: QuestionType;
  difficulty: DifficultyLevel;
  topic?: string;
  options?: Record<string, string>;
  answer: string;
  explanation?: string;
  media_url?: string;
  is_active?: boolean;
  course_id: number;
}

export interface QuestionUpdate {
  content?: string;
  question_type?: QuestionType;
  difficulty?: DifficultyLevel;
  topic?: string;
  options?: Record<string, string>;
  answer?: string;
  explanation?: string;
  media_url?: string;
  is_active?: boolean;
  course_id?: number;
}

export interface AIQuestionRequest {
  course_id: number;
  question_type: QuestionType;
  difficulty: DifficultyLevel;
  topic: string;
  num_questions?: number;
}

export interface CSVUploadResponse {
  success: boolean;
  total_rows: number;
  created_questions: number;
  validation_errors: string[];
  creation_errors: string[];
  questions: Question[];
}

export const getQuestions = async (
  courseId?: number,
  difficulty?: DifficultyLevel,
  questionType?: QuestionType,
  topic?: string
): Promise<Question[]> => {
  const params: Record<string, any> = {};
  if (courseId) params.course_id = courseId;
  if (difficulty) params.difficulty = difficulty;
  if (questionType) params.question_type = questionType;
  if (topic) params.topic = topic;

  try {
    console.log('Fetching questions with params:', params);
    const response = await apiClient.get<Question[]>('/questions/', { params });
    console.log('Questions from API:', response.data);
    return response.data;
  } catch (error) {
    console.error('Error fetching questions:', error);
    throw error;
  }
};

export const getQuestion = async (id: number): Promise<Question> => {
  const response = await apiClient.get<Question>(`/questions/${id}`);
  return response.data;
};

export const getQuestionsByIds = async (questionIds: number[]): Promise<Question[]> => {
  const response = await apiClient.post<Question[]>('/questions/by-ids', questionIds);
  return response.data;
};

export const createQuestion = async (data: QuestionCreate): Promise<Question> => {
  const response = await apiClient.post<Question>('/questions', data);
  return response.data;
};

export const updateQuestion = async (id: number, data: QuestionUpdate): Promise<Question> => {
  const response = await apiClient.put<Question>(`/questions/${id}`, data);
  return response.data;
};

export const deleteQuestion = async (id: number): Promise<Question> => {
  const response = await apiClient.delete<Question>(`/questions/${id}`);
  return response.data;
};

export const generateAIQuestions = async (data: AIQuestionRequest): Promise<Question[]> => {
  const response = await apiClient.post<Question[]>('/ai/generate-questions', data);
  return response.data;
};

export const uploadCSVQuestions = async (
  file: File,
  courseId: number,
  questionType: QuestionType
): Promise<CSVUploadResponse> => {
  const formData = new FormData();
  formData.append('file', file);
  formData.append('course_id', courseId.toString());
  formData.append('question_type', questionType);

  const response = await apiClient.post<CSVUploadResponse>('/questions/upload-csv', formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
    timeout: 120000, // 2 minutes for CSV processing
  });
  return response.data;
};

export const downloadCSVTemplate = async (questionType: QuestionType): Promise<Blob> => {
  const response = await apiClient.get('/questions/csv-template', {
    params: { question_type: questionType },
    responseType: 'blob',
  });
  return response.data;
};

export interface ImageUploadResponse {
  message: string;
  question_id: number;
  image_url: string;
  image_metadata: {
    url: string;
    public_id: string;
    width?: number;
    height?: number;
    format?: string;
    bytes?: number;
    created_at?: string;
  };
}

export interface BulkImageUploadResponse {
  message: string;
  results: Array<{
    question_id: number;
    filename: string;
    success: boolean;
    image_url?: string;
    image_metadata?: any;
    error?: string;
  }>;
  total_files: number;
  successful_uploads: number;
  failed_uploads: number;
}

export const uploadQuestionImage = async (
  questionId: number,
  imageFile: File
): Promise<ImageUploadResponse> => {
  const formData = new FormData();
  formData.append('file', imageFile);

  const response = await apiClient.post<ImageUploadResponse>(
    `/questions/${questionId}/upload-image`,
    formData,
    {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    }
  );
  return response.data;
};

export const bulkUploadQuestionImages = async (
  imageFiles: File[],
  questionIds: number[]
): Promise<BulkImageUploadResponse> => {
  const formData = new FormData();

  imageFiles.forEach(file => {
    formData.append('files', file);
  });

  questionIds.forEach(id => {
    formData.append('question_ids', id.toString());
  });

  const response = await apiClient.post<BulkImageUploadResponse>(
    '/questions/bulk-upload-images',
    formData,
    {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
    }
  );
  return response.data;
};

export const uploadCSVQuestionsWithImages = async (
  csvFile: File,
  courseId: number,
  questionType: QuestionType,
  imageFiles: File[] = [],
  imageMapping: Record<string, string> = {}
): Promise<CSVUploadResponse> => {
  const formData = new FormData();
  formData.append('csv_file', csvFile);
  formData.append('course_id', courseId.toString());
  formData.append('question_type', questionType);
  formData.append('image_mapping', JSON.stringify(imageMapping));

  imageFiles.forEach(file => {
    formData.append('image_files', file);
  });

  const response = await apiClient.post<CSVUploadResponse>(
    '/questions/upload-csv-with-images',
    formData,
    {
      headers: {
        'Content-Type': 'multipart/form-data',
      },
      timeout: 180000, // 3 minutes for CSV + images processing
    }
  );
  return response.data;
};

/**
 * DEPRECATED: Use the student tools Gemini Files API instead.
 * This function has been replaced with the new Gemini AI-powered PDF processing.
 */
export const uploadPdfForQuestions = async (
  file: File,
  courseId: number,
  questionType: QuestionType,
  difficulty: DifficultyLevel,
  numQuestions: number = 5
): Promise<Question[]> => {
  console.log('DEPRECATED: uploadPdfForQuestions is deprecated. Use the student tools Gemini AI feature instead.');
  const formData = new FormData();
  formData.append('file', file);
  formData.append('course_id', courseId.toString());
  formData.append('question_type', questionType);
  formData.append('difficulty', difficulty);
  formData.append('num_questions', numQuestions.toString());

  const response = await apiClient.post<Question[]>('/questions/upload-pdf', formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });
  return response.data;
};
