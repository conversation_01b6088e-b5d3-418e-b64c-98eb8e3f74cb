import apiClient from './client';

export interface Department {
  id: number;
  name: string;
  description: string;
  is_active: boolean;
  school_id: number;
  created_at: string;
  updated_at: string;
}

export interface DepartmentCreate {
  name: string;
  description: string;
  is_active?: boolean;
  school_id: number;
}

export interface DepartmentUpdate {
  name?: string;
  description?: string;
  is_active?: boolean;
  school_id?: number;
}

export const getDepartments = async (): Promise<Department[]> => {
  const response = await apiClient.get<Department[]>('/departments');
  return response.data;
};

export const getDepartmentsBySchool = async (schoolId: number): Promise<Department[]> => {
  console.log('Fetching departments for school ID:', schoolId);
  console.log('API base URL:', apiClient.defaults.baseURL);
  try {
    const response = await apiClient.get<Department[]>(`/departments/public/school/${schoolId}`);
    console.log('Departments API response:', response);
    console.log('Departments fetched successfully:', response.data);
    return response.data;
  } catch (error) {
    console.error('Error fetching departments:', error);
    throw error;
  }
};

export const getDepartment = async (id: number): Promise<Department> => {
  const response = await apiClient.get<Department>(`/departments/${id}`);
  return response.data;
};

export const createDepartment = async (data: DepartmentCreate): Promise<Department> => {
  const response = await apiClient.post<Department>('/departments', data);
  return response.data;
};

export const updateDepartment = async (id: number, data: DepartmentUpdate): Promise<Department> => {
  const response = await apiClient.put<Department>(`/departments/${id}`, data);
  return response.data;
};

export const deleteDepartment = async (id: number): Promise<Department> => {
  const response = await apiClient.delete<Department>(`/departments/${id}`);
  return response.data;
};
