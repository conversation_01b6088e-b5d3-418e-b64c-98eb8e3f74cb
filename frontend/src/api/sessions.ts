import apiClient from './client';

export type SessionType = 'online' | 'in_person';
export type SessionStatus = 'scheduled' | 'in_progress' | 'completed' | 'cancelled';

export interface User {
  id: number;
  full_name: string;
  email: string;
  role: string;
}

export interface Course {
  id: number;
  name: string;
  code: string;
}

export interface Session {
  id: number;
  title: string;
  description: string;
  session_type: SessionType;
  status: SessionStatus;
  start_time: string;
  end_time: string;
  location?: string;
  video_link?: string;
  max_students: number;
  is_recurring: boolean;
  recurrence_pattern?: string;
  course_id?: number;
  tutor_id: number;
  student_id?: number;
  created_at: string;
  updated_at: string;
  // Relationships
  tutor?: User;
  student?: User;
  course?: Course;
}

export interface SessionCreate {
  title: string;
  description: string;
  course_name?: string;
  session_type: SessionType;
  status?: SessionStatus;
  start_time: string;
  end_time: string;
  location?: string;
  video_link?: string;
  max_students?: number;
  is_recurring?: boolean;
  recurrence_pattern?: string;
  course_id?: number;
  tutor_id: number;
}

export interface SessionUpdate {
  title?: string;
  description?: string;
  course_name?: string;
  session_type?: SessionType;
  status?: SessionStatus;
  start_time?: string;
  end_time?: string;
  location?: string;
  video_link?: string;
  max_students?: number;
  is_recurring?: boolean;
  recurrence_pattern?: string;
  course_id?: number;
}

export interface SessionBookingRequest {
  session_id: number;
  student_id: number;
}

export const getSessions = async (
  courseId?: number,
  tutorId?: number,
  status?: SessionStatus
): Promise<Session[]> => {
  const params: Record<string, any> = {};
  if (courseId) params.course_id = courseId;
  if (tutorId) params.tutor_id = tutorId;
  if (status) params.status = status;

  const response = await apiClient.get<Session[]>('/sessions', { params });
  return response.data;
};

export const getSession = async (id: number): Promise<Session> => {
  const response = await apiClient.get<Session>(`/sessions/${id}`);
  return response.data;
};

export const createSession = async (data: SessionCreate): Promise<Session> => {
  const response = await apiClient.post<Session>('/sessions', data);
  return response.data;
};

export const updateSession = async (id: number, data: SessionUpdate): Promise<Session> => {
  const response = await apiClient.put<Session>(`/sessions/${id}`, data);
  return response.data;
};

export const deleteSession = async (id: number): Promise<Session> => {
  const response = await apiClient.delete<Session>(`/sessions/${id}`);
  return response.data;
};

export const bookSession = async (sessionId: number, studentId: number): Promise<Session> => {
  const response = await apiClient.post<Session>('/sessions/book', {
    session_id: sessionId,
    student_id: studentId,
  });
  return response.data;
};
