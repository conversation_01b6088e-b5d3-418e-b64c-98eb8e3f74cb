import apiClient from './client';

export interface ChatRoom {
  id: number;
  student_id: number;
  tutor_id: number;
  is_active: boolean;
  created_at: string;
  updated_at: string;
  other_user: {
    id: number;
    name: string;
    email: string;
    profile_picture_url?: string;
    is_online: boolean;
    last_seen: string | null;
  };
  last_message: {
    content: string;
    created_at: string;
    sender_id: number;
  } | null;
  unread_count: number;
}

export interface ChatMessage {
  id: number;
  content: string;
  message_type: string;
  sender_id: number;
  chat_room_id: number;
  is_read: boolean;
  delivery_status?: string;
  created_at: string;
  updated_at: string;
  delivered_at?: string;
  read_at?: string;
  sender: {
    id: number;
    name: string;
    email: string;
    profile_picture_url?: string;
  } | null;
}

export interface ChatMessageSend {
  chat_room_id: number;
  content: string;
  message_type?: string;
}

// Get chat rooms for current user
export const getChatRooms = async (
  skip: number = 0,
  limit: number = 100
): Promise<ChatRoom[]> => {
  try {
    const params = new URLSearchParams({
      skip: skip.toString(),
      limit: limit.toString(),
    });

    const response = await apiClient.get<ChatRoom[]>(
      `/chat/rooms?${params.toString()}`
    );
    return response.data;
  } catch (error: any) {
    console.error('Failed to fetch chat rooms:', error);
    throw new Error(error.response?.data?.detail || 'Failed to fetch chat rooms');
  }
};

// Get messages for a chat room
export const getChatMessages = async (
  chatRoomId: number,
  skip: number = 0,
  limit: number = 100
): Promise<ChatMessage[]> => {
  try {
    const params = new URLSearchParams({
      skip: skip.toString(),
      limit: limit.toString(),
    });

    const response = await apiClient.get<ChatMessage[]>(
      `/chat/rooms/${chatRoomId}/messages?${params.toString()}`
    );
    return response.data;
  } catch (error: any) {
    console.error('Failed to fetch chat messages:', error);
    throw new Error(error.response?.data?.detail || 'Failed to fetch chat messages');
  }
};

// Send a message (REST API fallback)
export const sendMessage = async (
  chatRoomId: number,
  content: string,
  messageType: string = 'text'
): Promise<ChatMessage> => {
  try {
    const response = await apiClient.post<ChatMessage>(
      `/chat/rooms/${chatRoomId}/messages`,
      {
        chat_room_id: chatRoomId,
        content,
        message_type: messageType
      }
    );
    return response.data;
  } catch (error: any) {
    console.error('Failed to send message:', error);
    throw new Error(error.response?.data?.detail || 'Failed to send message');
  }
};

// Mark messages as read
export const markMessagesAsRead = async (
  chatRoomId: number
): Promise<{ marked_read: number }> => {
  try {
    const response = await apiClient.post<{ marked_read: number }>(
      `/chat/rooms/${chatRoomId}/mark-read`
    );
    return response.data;
  } catch (error: any) {
    console.error('Failed to mark messages as read:', error);
    throw new Error(error.response?.data?.detail || 'Failed to mark messages as read');
  }
};

export interface ChatParticipantProfile {
  id: number;
  full_name: string;
  email: string;
  role: string;
  profile_picture_url?: string;
  gender?: string;
  phone_number?: string;
  level?: string;
  state_of_origin?: string;
  created_at: string;
  department?: {
    id: number;
    name: string;
  };
  tutor_profile?: {
    bio?: string;
    experience_years?: number;
    hourly_rate?: number;
    is_available: boolean;
    preferred_session_type?: string;
    location?: string;
    languages?: string[];
    average_rating?: number;
    total_reviews: number;
    total_sessions_completed: number;
  };
}

// Get profile details for a chat participant
export const getChatParticipantProfile = async (
  chatRoomId: number,
  userId: number
): Promise<ChatParticipantProfile> => {
  try {
    const response = await apiClient.get<ChatParticipantProfile>(
      `/chat/rooms/${chatRoomId}/participant/${userId}/profile`
    );
    return response.data;
  } catch (error: any) {
    console.error('Failed to fetch participant profile:', error);
    throw new Error(error.response?.data?.detail || 'Failed to fetch participant profile');
  }
};
