import apiClient from './client';
import { User } from './auth';

export interface UserUpdate {
  email?: string;
  username?: string;
  full_name?: string;
  password?: string;
  is_active?: boolean;
  school_id?: number;
  department_id?: number;
  profile_picture_url?: string;
}

export const getUsers = async (): Promise<User[]> => {
  const response = await apiClient.get<User[]>('/users');
  return response.data;
};

export const getUser = async (id: number): Promise<User> => {
  const response = await apiClient.get<User>(`/users/${id}`);
  return response.data;
};

export const updateUser = async (id: number, data: UserUpdate): Promise<User> => {
  const response = await apiClient.put<User>(`/users/${id}`, data);
  return response.data;
};

export const updateCurrentUser = async (data: UserUpdate): Promise<User> => {
  const response = await apiClient.put<User>('/users/me', data);
  return response.data;
};

export const uploadProfilePicture = async (file: File): Promise<User> => {
  const formData = new FormData();
  formData.append('profile_picture', file);

  const response = await apiClient.post<User>('/users/me/profile-picture', formData, {
    headers: {
      'Content-Type': 'multipart/form-data',
    },
  });

  return response.data;
};
