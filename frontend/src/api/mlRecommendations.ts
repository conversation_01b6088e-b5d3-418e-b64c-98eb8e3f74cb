import apiClient from './client';

// Types for ML Recommendations
export interface UserFeatureVector {
  id: number;
  user_id: number;
  avg_response_time: number;
  accuracy_rate: number;
  study_frequency: number;
  session_duration_avg: number;
  preferred_difficulty?: string;
  preferred_time_of_day?: number;
  preferred_session_length?: number;
  topic_performance: Record<string, number>;
  topic_interest: Record<string, number>;
  help_seeking_rate: number;
  bookmark_rate: number;
  completion_rate: number;
  improvement_rate: number;
  consistency_score: number;
  challenge_preference: number;
  active_days_per_week: number;
  peak_performance_hour?: number;
  feature_vector: number[];
  last_updated: string;
  feature_version: number;
}

export interface MLRecommendation {
  id: number;
  user_id: number;
  content_type: string;
  content_id?: number;
  topic?: string;
  course_id?: number;
  confidence_score: number;
  predicted_performance: number;
  difficulty_match: number;
  interest_score: number;
  model_name: string;
  model_version: string;
  explanation?: string;
  is_viewed: boolean;
  is_accepted: boolean;
  is_completed: boolean;
  user_rating?: number;
  created_at: string;
  viewed_at?: string;
  accepted_at?: string;
  completed_at?: string;
  expires_at?: string;
}

export interface UserLearningSession {
  id: number;
  user_id: number;
  session_id: string;
  start_time: string;
  end_time?: string;
  device_type?: string;
  total_questions: number;
  correct_answers: number;
  total_flashcards: number;
  help_requests: number;
  bookmarks_added: number;
  avg_response_time?: number;
  accuracy_rate?: number;
  engagement_score?: number;
  primary_course_id?: number;
  topics_covered: string[];
}

export interface ModelPerformance {
  model_name: string;
  model_version: string;
  accuracy?: number;
  precision?: number;
  recall?: number;
  f1_score?: number;
  training_data_size: number;
  last_retrained?: string;
  is_active: boolean;
  recommendation_count: number;
  user_satisfaction?: number;
}

export interface LearningInsights {
  user_features?: UserFeatureVector;
  recent_recommendations: MLRecommendation[];
  performance_trends: {
    accuracy_trend: number;
    response_time_trend: number;
    study_frequency: number;
    consistency: number;
    improvement_rate: number;
  };
  learning_patterns: {
    preferred_difficulty?: string;
    preferred_time?: number;
    study_pattern: string;
    challenge_preference: number;
    help_seeking: string;
    topic_strengths: Record<string, number>;
  };
  model_confidence: number;
}

export interface RecommendationResponse {
  recommendations: MLRecommendation[];
  total_count: number;
  model_info: {
    type: string;
    version: string;
    description: string;
  };
  user_features?: UserFeatureVector;
}

// Request types
export interface BehaviorTrackingRequest {
  event_type: string;
  content_type: string;
  content_id: number;
  course_id?: number;
  topic?: string;
  difficulty?: string;
  is_correct?: boolean;
  response_time_seconds?: number;
  confidence_level?: number;
  session_id?: string;
  device_type?: string;
  event_metadata?: Record<string, any>;
}

export interface SessionStartRequest {
  session_id: string;
  device_type?: string;
  primary_course_id?: number;
}

export interface SessionEndRequest {
  total_questions?: number;
  correct_answers?: number;
  total_flashcards?: number;
  help_requests?: number;
  bookmarks_added?: number;
  topics_covered?: string[];
}

export interface RecommendationFeedback {
  is_viewed?: boolean;
  is_accepted?: boolean;
  is_completed?: boolean;
  user_rating?: number;
}

// API Functions
export const trackUserBehavior = async (request: BehaviorTrackingRequest): Promise<any> => {
  const response = await apiClient.post('/ml-recommendations/track-behavior', request);
  return response.data;
};

export const getMLRecommendations = async (
  contentType: string = 'question',
  limit: number = 10,
  courseId?: number
): Promise<RecommendationResponse> => {
  const params: Record<string, any> = {
    content_type: contentType,
    limit: limit,
  };
  
  if (courseId) {
    params.course_id = courseId;
  }
  
  const response = await apiClient.get('/ml-recommendations/recommendations', { params });
  return response.data;
};

export const getLearningInsights = async (): Promise<LearningInsights> => {
  const response = await apiClient.get('/ml-recommendations/learning-insights');
  return response.data;
};

export const startLearningSession = async (request: SessionStartRequest): Promise<UserLearningSession> => {
  const response = await apiClient.post('/ml-recommendations/session/start', request);
  return response.data;
};

export const endLearningSession = async (
  sessionId: string,
  request: SessionEndRequest
): Promise<UserLearningSession> => {
  const response = await apiClient.post(`/ml-recommendations/session/${sessionId}/end`, request);
  return response.data;
};

export const provideRecommendationFeedback = async (
  recommendationId: number,
  feedback: RecommendationFeedback
): Promise<void> => {
  await apiClient.post(`/ml-recommendations/recommendations/${recommendationId}/feedback`, feedback);
};

export const getUserFeatures = async (): Promise<UserFeatureVector> => {
  const response = await apiClient.get('/ml-recommendations/user-features');
  return response.data;
};

export const getModelPerformance = async (): Promise<ModelPerformance[]> => {
  const response = await apiClient.get('/ml-recommendations/model-performance');
  return response.data;
};

export const triggerModelRetraining = async (): Promise<void> => {
  await apiClient.post('/ml-recommendations/retrain-models');
};

export const refreshRecommendations = async (
  contentType: string = 'question'
): Promise<RecommendationResponse> => {
  const response = await apiClient.post('/ml-recommendations/recommendations/refresh', null, {
    params: { content_type: contentType }
  });
  return response.data;
};

// Utility functions for ML recommendations
export const getConfidenceColor = (confidence: number): string => {
  if (confidence >= 0.8) return '#4caf50'; // green
  if (confidence >= 0.6) return '#8bc34a'; // light green
  if (confidence >= 0.4) return '#ffeb3b'; // yellow
  if (confidence >= 0.2) return '#ff9800'; // orange
  return '#f44336'; // red
};

export const getConfidenceLabel = (confidence: number): string => {
  if (confidence >= 0.8) return 'Very High';
  if (confidence >= 0.6) return 'High';
  if (confidence >= 0.4) return 'Medium';
  if (confidence >= 0.2) return 'Low';
  return 'Very Low';
};

export const formatPerformancePrediction = (prediction: number): string => {
  const percentage = Math.round(prediction * 100);
  return `${percentage}% expected success`;
};

export const getDifficultyMatchLabel = (match: number): string => {
  if (match >= 0.8) return 'Perfect Match';
  if (match >= 0.6) return 'Good Match';
  if (match >= 0.4) return 'Fair Match';
  return 'Poor Match';
};

export const getInterestScoreLabel = (score: number): string => {
  if (score >= 0.8) return 'Very Interested';
  if (score >= 0.6) return 'Interested';
  if (score >= 0.4) return 'Somewhat Interested';
  return 'Low Interest';
};

export const formatStudyFrequency = (frequency: number): string => {
  if (frequency >= 7) return 'Daily';
  if (frequency >= 5) return 'Very Regular';
  if (frequency >= 3) return 'Regular';
  if (frequency >= 1) return 'Occasional';
  return 'Rare';
};

export const formatConsistencyScore = (score: number): string => {
  const percentage = Math.round(score * 100);
  if (percentage >= 80) return `Very Consistent (${percentage}%)`;
  if (percentage >= 60) return `Consistent (${percentage}%)`;
  if (percentage >= 40) return `Somewhat Consistent (${percentage}%)`;
  return `Inconsistent (${percentage}%)`;
};

export const getTopicStrengths = (topicPerformance: Record<string, number>): Array<{topic: string, score: number}> => {
  return Object.entries(topicPerformance)
    .map(([topic, score]) => ({ topic, score }))
    .sort((a, b) => b.score - a.score)
    .slice(0, 5);
};

export const getTopicWeaknesses = (topicPerformance: Record<string, number>): Array<{topic: string, score: number}> => {
  return Object.entries(topicPerformance)
    .map(([topic, score]) => ({ topic, score }))
    .sort((a, b) => a.score - b.score)
    .slice(0, 5);
};

export const calculateOverallProgress = (userFeatures?: UserFeatureVector): number => {
  if (!userFeatures) return 0;
  
  const factors = [
    userFeatures.accuracy_rate,
    userFeatures.consistency_score,
    userFeatures.completion_rate,
    Math.min(userFeatures.improvement_rate * 10, 1), // Scale improvement rate
    Math.min(userFeatures.study_frequency / 7, 1), // Normalize frequency
  ];
  
  return factors.reduce((sum, factor) => sum + factor, 0) / factors.length;
};

export const getRecommendationPriorityScore = (recommendation: MLRecommendation): number => {
  // Combine multiple factors to create a priority score
  return (
    recommendation.confidence_score * 0.3 +
    recommendation.predicted_performance * 0.25 +
    recommendation.difficulty_match * 0.25 +
    recommendation.interest_score * 0.2
  );
};

export const shouldShowRecommendation = (recommendation: MLRecommendation): boolean => {
  // Only show recommendations that are not expired and not completed
  const isNotExpired = !recommendation.expires_at || new Date(recommendation.expires_at) > new Date();
  const isNotCompleted = !recommendation.is_completed;
  const hasValidContent = recommendation.content_id && recommendation.content_id > 0;

  return isNotExpired && isNotCompleted && hasValidContent;
};

export const generateSessionId = (): string => {
  return `session_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`;
};
