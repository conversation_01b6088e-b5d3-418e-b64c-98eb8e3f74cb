/**
 * API client for fetching content with sharing information integrated
 */

import apiClient from './client';
import { getMyGeneratedMCQs, getGeneratedFlashcards, getGeneratedNoteExplanations } from './studentTools';
import { contentSharingApi } from './contentSharing';
import { SharedContentItem, ContentType } from '../types/contentSharing';

/**
 * Get MCQs with sharing information (own + shared)
 */
export const getMCQsWithSharing = async (): Promise<SharedContentItem[]> => {
  try {
    // Get user's own MCQs
    const ownMCQs = await getMyGeneratedMCQs();
    
    // Get shared content
    const sharedContent = await contentSharingApi.getSharedWithMe();
    const sharedMCQs = sharedContent.filter(item => item.content_type === ContentType.MCQ);
    
    // Convert own MCQs to SharedContentItem format
    const ownMCQItems: SharedContentItem[] = ownMCQs.map(mcq => ({
      content_type: ContentType.MCQ,
      content_id: mcq.id,
      content_data: mcq,
      is_shared: false,
      can_edit: true,
      can_delete: true,
      tags: []
    }));
    
    // Convert shared MCQs to SharedContentItem format
    const sharedMCQItems: SharedContentItem[] = await Promise.all(
      sharedMCQs.map(async (sharedItem) => {
        try {
          // Fetch the actual MCQ data using the shared content endpoint
          const response = await apiClient.get(`/content-sharing/content/MCQ/${sharedItem.content_id}`);
          const mcqData = response.data;

          // Validate that we got valid MCQ data
          if (!mcqData || !mcqData.id) {
            console.error(`Invalid MCQ data for ID ${sharedItem.content_id}:`, mcqData);
            return null;
          }

          return {
            content_type: ContentType.MCQ,
            content_id: sharedItem.content_id,
            content_data: mcqData,
            is_shared: true,
            shared_by_name: sharedItem.shared_by_name,
            shared_by_email: sharedItem.shared_by_email,
            share_message: sharedItem.share_message,
            shared_at: sharedItem.shared_at,
            can_edit: false,
            can_delete: false,
            tags: ['shared', 'read-only']
          };
        } catch (error) {
          console.error(`Failed to fetch MCQ ${sharedItem.content_id}:`, error);
          return null;
        }
      })
    );
    
    // Filter out failed fetches and combine
    const validSharedMCQs = sharedMCQItems.filter(item => item !== null) as SharedContentItem[];
    
    return [...ownMCQItems, ...validSharedMCQs];
  } catch (error) {
    console.error('Error fetching MCQs with sharing info:', error);
    throw error;
  }
};

/**
 * Get flashcards with sharing information (own + shared)
 */
export const getFlashcardsWithSharing = async (): Promise<SharedContentItem[]> => {
  try {
    // Get user's own flashcards
    const ownFlashcards = await getGeneratedFlashcards();
    
    // Get shared content
    const sharedContent = await contentSharingApi.getSharedWithMe();
    const sharedFlashcards = sharedContent.filter(item => item.content_type === ContentType.FLASHCARD);
    
    // Convert own flashcards to SharedContentItem format
    const ownFlashcardItems: SharedContentItem[] = ownFlashcards.map(flashcard => ({
      content_type: ContentType.FLASHCARD,
      content_id: flashcard.id,
      content_data: flashcard,
      is_shared: false,
      can_edit: true,
      can_delete: true,
      tags: []
    }));
    
    // Convert shared flashcards to SharedContentItem format
    const sharedFlashcardItems: SharedContentItem[] = await Promise.all(
      sharedFlashcards.map(async (sharedItem) => {
        try {
          // Fetch the actual flashcard data using the shared content endpoint
          const response = await apiClient.get(`/content-sharing/content/FLASHCARD/${sharedItem.content_id}`);
          const flashcardData = response.data;

          // Validate that we got valid flashcard data
          if (!flashcardData || !flashcardData.id) {
            console.error(`Invalid flashcard data for ID ${sharedItem.content_id}:`, flashcardData);
            return null;
          }

          return {
            content_type: ContentType.FLASHCARD,
            content_id: sharedItem.content_id,
            content_data: flashcardData,
            is_shared: true,
            shared_by_name: sharedItem.shared_by_name,
            shared_by_email: sharedItem.shared_by_email,
            share_message: sharedItem.share_message,
            shared_at: sharedItem.shared_at,
            can_edit: false,
            can_delete: false,
            tags: ['shared', 'read-only']
          };
        } catch (error) {
          console.error(`Failed to fetch flashcard ${sharedItem.content_id}:`, error);
          return null;
        }
      })
    );
    
    // Filter out failed fetches and combine
    const validSharedFlashcards = sharedFlashcardItems.filter(item => item !== null) as SharedContentItem[];
    
    return [...ownFlashcardItems, ...validSharedFlashcards];
  } catch (error) {
    console.error('Error fetching flashcards with sharing info:', error);
    throw error;
  }
};

/**
 * Get summaries with sharing information (own + shared)
 */
export const getSummariesWithSharing = async (): Promise<SharedContentItem[]> => {
  try {
    // Get user's own summaries
    const ownSummaries = await getGeneratedNoteExplanations();
    
    // Get shared content
    const sharedContent = await contentSharingApi.getSharedWithMe();
    const sharedSummaries = sharedContent.filter(item => item.content_type === ContentType.SUMMARY);
    
    // Convert own summaries to SharedContentItem format
    const ownSummaryItems: SharedContentItem[] = ownSummaries.map(summary => ({
      content_type: ContentType.SUMMARY,
      content_id: summary.id,
      content_data: summary,
      is_shared: false,
      can_edit: true,
      can_delete: true,
      tags: []
    }));
    
    // Convert shared summaries to SharedContentItem format
    const sharedSummaryItems: SharedContentItem[] = await Promise.all(
      sharedSummaries.map(async (sharedItem) => {
        try {
          // Fetch the actual summary data using the shared content endpoint
          const response = await apiClient.get(`/content-sharing/content/SUMMARY/${sharedItem.content_id}`);
          const summaryData = response.data;

          // Validate that we got valid summary data
          if (!summaryData || !summaryData.id) {
            console.error(`Invalid summary data for ID ${sharedItem.content_id}:`, summaryData);
            return null;
          }

          return {
            content_type: ContentType.SUMMARY,
            content_id: sharedItem.content_id,
            content_data: summaryData,
            is_shared: true,
            shared_by_name: sharedItem.shared_by_name,
            shared_by_email: sharedItem.shared_by_email,
            share_message: sharedItem.share_message,
            shared_at: sharedItem.shared_at,
            can_edit: false,
            can_delete: false,
            tags: ['shared', 'read-only']
          };
        } catch (error) {
          console.error(`Failed to fetch summary ${sharedItem.content_id}:`, error);
          return null;
        }
      })
    );
    
    // Filter out failed fetches and combine
    const validSharedSummaries = sharedSummaryItems.filter(item => item !== null) as SharedContentItem[];
    
    return [...ownSummaryItems, ...validSharedSummaries];
  } catch (error) {
    console.error('Error fetching summaries with sharing info:', error);
    throw error;
  }
};

/**
 * Get flashcards for practice mode (including shared flashcards)
 * This function can fetch flashcards by IDs regardless of ownership
 */
export const getFlashcardsForPractice = async (flashcardIds: number[]): Promise<any[]> => {
  try {
    console.log('Fetching flashcards for practice:', flashcardIds);

    const flashcards = [];
    const failedIds = [];

    // Fetch each flashcard individually using the shared content endpoint
    for (const id of flashcardIds) {
      try {
        const response = await apiClient.get(`/content-sharing/content/FLASHCARD/${id}`);
        if (response.data && response.data.id) {
          flashcards.push(response.data);
        } else {
          failedIds.push(id);
        }
      } catch (error) {
        console.error(`Failed to fetch flashcard ${id} for practice:`, error);
        failedIds.push(id);
      }
    }

    // If we have some failed IDs, try the regular endpoint as fallback for owned flashcards
    if (failedIds.length > 0) {
      try {
        console.log(`Trying fallback for failed flashcard IDs: ${failedIds.join(', ')}`);
        const { getGeneratedFlashcards } = await import('./studentTools');
        const fallbackData = await getGeneratedFlashcards({
          flashcard_ids: failedIds.join(',')
        });
        flashcards.push(...fallbackData);
        console.log(`Fallback fetched ${fallbackData.length} additional flashcards`);
      } catch (fallbackError) {
        console.error('Fallback also failed:', fallbackError);
      }
    }

    console.log(`Successfully fetched ${flashcards.length} flashcards for practice`);
    return flashcards;
  } catch (error) {
    console.error('Error fetching flashcards for practice:', error);
    throw error;
  }
};
