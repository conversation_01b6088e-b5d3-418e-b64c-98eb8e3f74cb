/**
 * API client for the new simplified content sharing system
 */

import apiClient from './client';
import {
  ShareContentRequest,
  ShareContentResponse,
  AcceptInvitationRequest,
  AcceptInvitationResponse,
  AccessPublicShareRequest,
  AccessPublicShareResponse,
  MySharedContentSummary,
  SharedWithMeSummary,
  ContentType
} from '../types/contentSharing';

const BASE_URL = '/content-sharing';

export const contentSharingApi = {
  /**
   * Share content with other users via email invitations and/or public link
   */
  shareContent: async (request: ShareContentRequest): Promise<ShareContentResponse> => {
    const response = await apiClient.post(`${BASE_URL}/share`, request);
    return response.data;
  },

  /**
   * Accept a share invitation
   */
  acceptInvitation: async (request: AcceptInvitationRequest): Promise<AcceptInvitationResponse> => {
    const response = await apiClient.post(`${BASE_URL}/accept-invitation`, request);
    return response.data;
  },

  /**
   * Access content via public share link
   */
  accessPublicShare: async (request: AccessPublicShareRequest): Promise<AccessPublicShareResponse> => {
    const response = await apiClient.post(`${BASE_URL}/access-public`, request);
    return response.data;
  },

  /**
   * Get content that I have shared with others
   */
  getMySharedContent: async (skip = 0, limit = 100): Promise<MySharedContentSummary[]> => {
    const response = await apiClient.get(`${BASE_URL}/my-shares`, {
      params: { skip, limit }
    });
    return response.data;
  },

  /**
   * Get content that has been shared with me
   */
  getSharedWithMe: async (skip = 0, limit = 100): Promise<SharedWithMeSummary[]> => {
    const response = await apiClient.get(`${BASE_URL}/shared-with-me`, {
      params: { skip, limit }
    });
    return response.data;
  },

  /**
   * Remove access to shared content (delete user's own copy)
   */
  removeSharedContentAccess: async (content_type: ContentType, content_id: number): Promise<{ message: string }> => {
    const response = await apiClient.delete(`${BASE_URL}/remove-access/${content_type}/${content_id}`);
    return response.data;
  }
};

// Helper functions for content-specific sharing
export const shareHelpers = {
  /**
   * Share MCQ questions
   */
  shareMCQ: (
    content_id: number,
    content_title: string,
    course_name?: string,
    share_message?: string,
    invited_emails?: string[],
    create_public_link = false
  ) => {
    return contentSharingApi.shareContent({
      content_type: ContentType.MCQ,
      content_id,
      share_message,
      invited_emails,
      create_public_link
    });
  },

  /**
   * Share flashcards
   */
  shareFlashcard: (
    content_id: number,
    content_title: string,
    course_name?: string,
    share_message?: string,
    invited_emails?: string[],
    create_public_link = false
  ) => {
    return contentSharingApi.shareContent({
      content_type: ContentType.FLASHCARD,
      content_id,
      share_message,
      invited_emails,
      create_public_link
    });
  },

  /**
   * Share summaries
   */
  shareSummary: (
    content_id: number,
    content_title: string,
    course_name?: string,
    share_message?: string,
    invited_emails?: string[],
    create_public_link = false
  ) => {
    return contentSharingApi.shareContent({
      content_type: ContentType.SUMMARY,
      content_id,
      share_message,
      invited_emails,
      create_public_link
    });
  }
};

// URL helpers for share links
export const shareUrlHelpers = {
  /**
   * Generate share URL from token
   */
  getShareUrl: (token: string): string => {
    return `${window.location.origin}/share/${token}`;
  },

  /**
   * Extract token from share URL
   */
  extractTokenFromUrl: (url: string): string | null => {
    const match = url.match(/\/share\/([^/?]+)/);
    return match ? match[1] : null;
  },

  /**
   * Check if current URL is a share link
   */
  isShareUrl: (url: string = window.location.href): boolean => {
    return /\/share\/[^/?]+/.test(url);
  }
};

export default contentSharingApi;
