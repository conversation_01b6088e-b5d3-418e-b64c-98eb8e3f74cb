import apiClient from './client';

export interface AdminFlashCard {
  id: number;
  course_id: number;
  front_content: string;
  back_content: string;
  topic?: string;
  difficulty?: string;
  media_url?: string;
  is_active: boolean;
  created_by: number;
  created_at: string;
  updated_at: string;
}

export interface AdminFlashCardWithCourse extends AdminFlashCard {
  course_name?: string;
  course_code?: string;
}

export interface AdminFlashCardCreate {
  course_id: number;
  front_content: string;
  back_content: string;
  topic?: string;
  difficulty?: string;
  media_url?: string;
  is_active?: boolean;
}

export interface AdminFlashCardUpdate {
  front_content?: string;
  back_content?: string;
  topic?: string;
  difficulty?: string;
  media_url?: string;
  is_active?: boolean;
  course_id?: number;
}

export interface AdminFlashCardCSVUploadResponse {
  success: boolean;
  total_rows: number;
  created_flashcards: number;
  validation_errors: string[];
  creation_errors: string[];
  flashcards: AdminFlashCard[];
}

export interface AdminFlashCardImageUploadResponse {
  success: boolean;
  image_url: string;
  message: string;
}

export interface AdminFlashCardStats {
  total: number;
  active: number;
  inactive: number;
  by_course: Array<{
    course_name: string;
    course_code: string;
    count: number;
  }>;
  by_difficulty: Array<{
    difficulty: string;
    count: number;
  }>;
  recent: number;
}

// Get all admin flashcards (admin only)
export const getAdminFlashCards = async (params?: {
  skip?: number;
  limit?: number;
  course_id?: number;
  difficulty?: string;
  topic?: string;
  search?: string;
}): Promise<AdminFlashCardWithCourse[]> => {
  try {
    const response = await apiClient.get<AdminFlashCardWithCourse[]>('/admin/flashcards/', {
      params
    });
    return response.data;
  } catch (error: any) {
    console.error('Failed to fetch admin flashcards:', error);
    throw new Error(error.response?.data?.detail || 'Failed to fetch flashcards');
  }
};

// Get public admin flashcards (for students)
export const getPublicAdminFlashCards = async (params?: {
  skip?: number;
  limit?: number;
  course_id?: number;
  difficulty?: string;
  topic?: string;
  search?: string;
}): Promise<AdminFlashCardWithCourse[]> => {
  try {
    const response = await apiClient.get<AdminFlashCardWithCourse[]>('/admin/flashcards/public', {
      params
    });
    return response.data;
  } catch (error: any) {
    console.error('Failed to fetch public admin flashcards:', error);
    throw new Error(error.response?.data?.detail || 'Failed to fetch flashcards');
  }
};

// Get single admin flashcard
export const getAdminFlashCard = async (id: number): Promise<AdminFlashCardWithCourse> => {
  try {
    const response = await apiClient.get<AdminFlashCardWithCourse>(`/admin/flashcards/${id}`);
    return response.data;
  } catch (error: any) {
    console.error('Failed to fetch admin flashcard:', error);
    throw new Error(error.response?.data?.detail || 'Failed to fetch flashcard');
  }
};

// Create admin flashcard
export const createAdminFlashCard = async (data: AdminFlashCardCreate): Promise<AdminFlashCard> => {
  try {
    const response = await apiClient.post<AdminFlashCard>('/admin/flashcards/', data);
    return response.data;
  } catch (error: any) {
    console.error('Failed to create admin flashcard:', error);
    throw new Error(error.response?.data?.detail || 'Failed to create flashcard');
  }
};

// Update admin flashcard
export const updateAdminFlashCard = async (id: number, data: AdminFlashCardUpdate): Promise<AdminFlashCard> => {
  try {
    const response = await apiClient.put<AdminFlashCard>(`/admin/flashcards/${id}`, data);
    return response.data;
  } catch (error: any) {
    console.error('Failed to update admin flashcard:', error);
    throw new Error(error.response?.data?.detail || 'Failed to update flashcard');
  }
};

// Delete admin flashcard
export const deleteAdminFlashCard = async (id: number): Promise<void> => {
  try {
    await apiClient.delete(`/admin/flashcards/${id}`);
  } catch (error: any) {
    console.error('Failed to delete admin flashcard:', error);
    throw new Error(error.response?.data?.detail || 'Failed to delete flashcard');
  }
};

// Get admin flashcard statistics
export const getAdminFlashCardStats = async (): Promise<AdminFlashCardStats> => {
  try {
    const response = await apiClient.get<AdminFlashCardStats>('/admin/flashcards/stats/overview');
    return response.data;
  } catch (error: any) {
    console.error('Failed to fetch admin flashcard stats:', error);
    throw new Error(error.response?.data?.detail || 'Failed to fetch flashcard statistics');
  }
};

// Upload CSV flashcards
export const uploadCSVFlashCards = async (
  file: File,
  courseId: number
): Promise<AdminFlashCardCSVUploadResponse> => {
  try {
    const formData = new FormData();
    formData.append('csv_file', file);
    formData.append('course_id', courseId.toString());

    const response = await apiClient.post<AdminFlashCardCSVUploadResponse>(
      '/admin/flashcards/upload-csv',
      formData,
      {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      }
    );

    return response.data;
  } catch (error: any) {
    console.error('Failed to upload CSV flashcards:', error);
    throw new Error(error.response?.data?.detail || 'Failed to upload CSV file');
  }
};

// Download CSV template for flashcards
export const downloadFlashCardCSVTemplate = async (): Promise<Blob> => {
  try {
    const response = await apiClient.get('/admin/flashcards/csv-template', {
      responseType: 'blob',
    });
    return response.data;
  } catch (error: any) {
    console.error('Failed to download flashcard CSV template:', error);
    throw new Error(error.response?.data?.detail || 'Failed to download template');
  }
};

// Upload flashcard image
export const uploadFlashCardImage = async (
  flashcardId: number,
  file: File
): Promise<AdminFlashCardImageUploadResponse> => {
  try {
    const formData = new FormData();
    formData.append('image_file', file);

    const response = await apiClient.post<AdminFlashCardImageUploadResponse>(
      `/admin/flashcards/${flashcardId}/upload-image`,
      formData,
      {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      }
    );
    return response.data;
  } catch (error: any) {
    console.error('Failed to upload flashcard image:', error);
    throw new Error(error.response?.data?.detail || 'Failed to upload image');
  }
};

// Bulk create flashcards
export const bulkCreateAdminFlashCards = async (
  flashcards: AdminFlashCardCreate[]
): Promise<AdminFlashCard[]> => {
  try {
    const response = await apiClient.post<AdminFlashCard[]>('/admin/flashcards/bulk', {
      flashcards
    });
    return response.data;
  } catch (error: any) {
    console.error('Failed to bulk create admin flashcards:', error);
    throw new Error(error.response?.data?.detail || 'Failed to create flashcards');
  }
};

// Bulk update flashcards
export const bulkUpdateAdminFlashCards = async (
  flashcardIds: number[],
  updateData: AdminFlashCardUpdate
): Promise<{ updated_count: number; message: string }> => {
  try {
    const response = await apiClient.put('/admin/flashcards/bulk', {
      flashcard_ids: flashcardIds,
      update_data: updateData
    });
    return response.data;
  } catch (error: any) {
    console.error('Failed to bulk update admin flashcards:', error);
    throw new Error(error.response?.data?.detail || 'Failed to update flashcards');
  }
};

// Bulk delete flashcards
export const bulkDeleteAdminFlashCards = async (
  flashcardIds: number[]
): Promise<{ deleted_count: number; message: string }> => {
  try {
    const response = await apiClient.delete('/admin/flashcards/bulk', {
      data: { flashcard_ids: flashcardIds }
    });
    return response.data;
  } catch (error: any) {
    console.error('Failed to bulk delete admin flashcards:', error);
    throw new Error(error.response?.data?.detail || 'Failed to delete flashcards');
  }
};
