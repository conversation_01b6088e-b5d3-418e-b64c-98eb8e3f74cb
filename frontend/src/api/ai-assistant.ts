import apiClient from './client';

export interface AIAssistantResponse {
  content: string;
  sources?: string[];
}

export interface AssistantQuery {
  query: string;
}

export enum AIAssistantEventType {
  START = 'start',
  TOKEN = 'token',
  ERROR = 'error',
  END = 'end',
}

export interface AIAssistantStreamEvent {
  type: AIAssistantEventType;
  content?: string;
  sources?: string[];
  error?: string;
}

export enum AIAssistantErrorType {
  NETWORK = 'network',
  SERVER = 'server',
  TIMEOUT = 'timeout',
  UNKNOWN = 'unknown',
}

export class AIAssistantError extends Error {
  type: AIAssistantErrorType;
  originalError?: Error;

  constructor(message: string, type: AIAssistantErrorType, originalError?: Error) {
    super(message);
    this.name = 'AIAssistantError';
    this.type = type;
    this.originalError = originalError;
  }
}

/**
 * Ask the AI assistant a question
 * @param query The question to ask
 * @returns The AI assistant's response
 * @throws AIAssistantError with detailed information about the failure
 */
export const askAssistant = async (query: string): Promise<AIAssistantResponse> => {
  try {
    const response = await apiClient.post<AIAssistantResponse>('/ai-assistant/ask', {
      query,
    });
    return response.data;
  } catch (error) {
    console.error('Error asking AI assistant:', error);

    // Create a more specific error
    if (error instanceof Error) {
      if (error.message.includes('Network error')) {
        throw new AIAssistantError(
          'Unable to connect to the AI assistant. Please check your internet connection and try again.',
          AIAssistantErrorType.NETWORK,
          error
        );
      } else if (error.message.includes('timeout')) {
        throw new AIAssistantError(
          'The AI assistant is taking too long to respond. Please try again later.',
          AIAssistantErrorType.TIMEOUT,
          error
        );
      } else if (error.message.includes('Server error')) {
        throw new AIAssistantError(
          'The AI assistant is currently unavailable. Our team has been notified and is working on a fix.',
          AIAssistantErrorType.SERVER,
          error
        );
      }
    }

    // Generic error
    throw new AIAssistantError(
      'Something went wrong while communicating with the AI assistant. Please try again later.',
      AIAssistantErrorType.UNKNOWN,
      error instanceof Error ? error : new Error(String(error))
    );
  }
};

/**
 * Get AI help for a specific question in practice mode
 * @param questionId ID of the question
 * @returns The AI assistant's explanation and help
 * @throws AIAssistantError with detailed information about the failure
 */
export const getPracticeHelp = async (questionId: number): Promise<AIAssistantResponse> => {
  try {
    const response = await apiClient.get<AIAssistantResponse>(`/ai-assistant/practice-help/${questionId}`);
    return response.data;
  } catch (error) {
    console.error('Error getting practice help:', error);

    // Create a more specific error
    if (error instanceof Error) {
      if (error.message.includes('Network error')) {
        throw new AIAssistantError(
          'Unable to connect to the AI assistant for practice help. Please check your internet connection and try again.',
          AIAssistantErrorType.NETWORK,
          error
        );
      } else if (error.message.includes('timeout')) {
        throw new AIAssistantError(
          'The AI assistant is taking too long to provide practice help. Please try again later.',
          AIAssistantErrorType.TIMEOUT,
          error
        );
      } else if (error.message.includes('Server error')) {
        throw new AIAssistantError(
          'The AI practice help is currently unavailable. Our team has been notified and is working on a fix.',
          AIAssistantErrorType.SERVER,
          error
        );
      } else if (error.message.includes('Not found')) {
        throw new AIAssistantError(
          'The question you requested help for could not be found. Please try with a different question.',
          AIAssistantErrorType.UNKNOWN,
          error
        );
      }
    }

    // Generic error
    throw new AIAssistantError(
      'Something went wrong while getting practice help. Please try again later.',
      AIAssistantErrorType.UNKNOWN,
      error instanceof Error ? error : new Error(String(error))
    );
  }
};

/**
 * Stream responses from the AI assistant
 * @param query The question to ask
 * @param onEvent Callback function to handle streaming events
 * @returns A function to abort the stream
 */
export const streamAssistant = (
  query: string,
  onEvent: (event: AIAssistantStreamEvent) => void
): () => void => {
  // Create an AbortController to handle cancellation
  const controller = new AbortController();
  const signal = controller.signal;

  // Notify that we're starting
  onEvent({
    type: AIAssistantEventType.START,
    content: '',
  });

  // Use fetch with POST method for streaming
  fetch(`${apiClient.defaults.baseURL}/ai-assistant/stream/ask`, {
    method: 'POST',
    headers: {
      'Content-Type': 'application/json',
      'Accept': 'text/event-stream',
    },
    body: JSON.stringify({ query }),
    signal: signal,
  })
    .then(response => {
      if (!response.ok) {
        throw new Error(`HTTP error! Status: ${response.status}`);
      }

      // Get the reader from the response body stream
      const reader = response.body!.getReader();
      const decoder = new TextDecoder();
      let buffer = '';

      // Function to process the stream
      function processStream(): Promise<void> {
        return reader.read().then(({ done, value }) => {
          if (done) {
            // Handle any remaining data in the buffer
            if (buffer.trim()) {
              try {
                const lines = buffer.split('\n\n');
                for (const line of lines) {
                  if (line.startsWith('data: ')) {
                    const eventData = JSON.parse(line.substring(6));
                    onEvent({
                      ...eventData,
                      type: eventData.type as unknown as AIAssistantEventType,
                    });
                  }
                }
              } catch (e) {
                console.error('Error parsing final buffer:', e);
              }
            }
            return;
          }

          // Decode the chunk and add it to our buffer
          buffer += decoder.decode(value, { stream: true });

          // Process complete events in the buffer
          const lines = buffer.split('\n\n');
          buffer = lines.pop() || ''; // Keep the last incomplete chunk in the buffer

          for (const line of lines) {
            if (line.startsWith('data: ')) {
              try {
                const eventData = JSON.parse(line.substring(6));
                onEvent({
                  ...eventData,
                  type: eventData.type as unknown as AIAssistantEventType,
                });

                // If we get an end or error event, we can stop processing
                if (eventData.type === 'end' || eventData.type === 'error') {
                  controller.abort();
                  return;
                }
              } catch (e) {
                console.error('Error parsing event:', e, 'Line:', line);
              }
            }
          }

          // Continue reading
          return processStream();
        });
      }

      // Start processing the stream
      return processStream();
    })
    .catch(error => {
      console.error('Error with stream request:', error);
      onEvent({
        type: AIAssistantEventType.ERROR,
        error: 'Failed to connect to the server. Please try again later.',
      });
    });

  // Return a function to abort the stream
  return () => {
    controller.abort();
  };
};

/**
 * Stream practice help for a specific question
 * @param questionId ID of the question
 * @param onEvent Callback function to handle streaming events
 * @returns A function to abort the stream
 */
export const streamPracticeHelp = (
  questionId: number,
  onEvent: (event: AIAssistantStreamEvent) => void
): () => void => {
  // Create an AbortController to handle cancellation
  const controller = new AbortController();
  const signal = controller.signal;

  // Notify that we're starting
  onEvent({
    type: AIAssistantEventType.START,
    content: '',
  });

  // Use fetch with GET method for streaming
  fetch(`${apiClient.defaults.baseURL}/ai-assistant/stream/practice-help/${questionId}`, {
    method: 'GET',
    headers: {
      'Accept': 'text/event-stream',
    },
    signal: signal,
  })
    .then(response => {
      if (!response.ok) {
        throw new Error(`HTTP error! Status: ${response.status}`);
      }

      // Get the reader from the response body stream
      const reader = response.body!.getReader();
      const decoder = new TextDecoder();
      let buffer = '';

      // Function to process the stream
      function processStream(): Promise<void> {
        return reader.read().then(({ done, value }) => {
          if (done) {
            // Handle any remaining data in the buffer
            if (buffer.trim()) {
              try {
                const lines = buffer.split('\n\n');
                for (const line of lines) {
                  if (line.startsWith('data: ')) {
                    const eventData = JSON.parse(line.substring(6));
                    onEvent({
                      ...eventData,
                      type: eventData.type as unknown as AIAssistantEventType,
                    });
                  }
                }
              } catch (e) {
                console.error('Error parsing final buffer:', e);
              }
            }
            return;
          }

          // Decode the chunk and add it to our buffer
          buffer += decoder.decode(value, { stream: true });

          // Process complete events in the buffer
          const lines = buffer.split('\n\n');
          buffer = lines.pop() || ''; // Keep the last incomplete chunk in the buffer

          for (const line of lines) {
            if (line.startsWith('data: ')) {
              try {
                const eventData = JSON.parse(line.substring(6));
                onEvent({
                  ...eventData,
                  type: eventData.type as unknown as AIAssistantEventType,
                });

                // If we get an end or error event, we can stop processing
                if (eventData.type === 'end' || eventData.type === 'error') {
                  controller.abort();
                  return;
                }
              } catch (e) {
                console.error('Error parsing event:', e, 'Line:', line);
              }
            }
          }

          // Continue reading
          return processStream();
        });
      }

      // Start processing the stream
      return processStream();
    })
    .catch(error => {
      console.error('Error with practice help stream request:', error);
      onEvent({
        type: AIAssistantEventType.ERROR,
        error: 'Failed to connect to the server. Please try again later.',
      });
    });

  // Return a function to abort the stream
  return () => {
    controller.abort();
  };
};
