import apiClient from './client';

export interface Badge {
  id: number;
  name: string;
  description: string;
  badge_type: string;
  icon_url: string | null;
  earned_at: string;
}

export interface UserLevel {
  id: number;
  level_number: number;
  name: string;
  min_points: number;
  max_points: number;
  icon_url: string | null;
}

export interface UserStreak {
  id: number;
  user_id: number;
  current_streak: number;
  longest_streak: number;
  last_activity_date: string;
}

export interface GamificationProfile {
  total_points: number;
  current_level: UserLevel;
  badges: Badge[];
  streak: UserStreak | null;
  next_level: UserLevel | null;
  points_to_next_level: number | null;
}

export interface LeaderboardEntry {
  user_id: number;
  full_name: string;
  profile_picture_url: string | null;
  total_points: number;
  badge_count: number;
  current_streak: number;
  level: UserLevel;
  school_name?: string;
  department_name?: string;
}

export interface Leaderboard {
  entries: LeaderboardEntry[];
}

// Get user badges
export const getUserBadges = async (userId?: number): Promise<Badge[]> => {
  const params = userId ? { user_id: userId } : {};
  const response = await apiClient.get<Badge[]>('/gamification/badges', { params });
  return response.data;
};

// Get user points
export const getUserPoints = async (userId?: number): Promise<number> => {
  const params = userId ? { user_id: userId } : {};
  const response = await apiClient.get<number>('/gamification/points', { params });
  return response.data;
};

// Get user gamification profile
export const getGamificationProfile = async (userId?: number): Promise<GamificationProfile> => {
  try {
    const params = userId ? { user_id: userId } : {};
    const response = await apiClient.get<GamificationProfile>('/gamification/profile', { params });
    return response.data;
  } catch (error) {
    console.error('API error in getGamificationProfile:', error);

    // Don't swallow authentication errors - let them bubble up
    if (error?.response?.status === 401) {
      throw error;
    }

    // Re-throw other errors as well since profile data is important
    throw error;
  }
};

export interface LeaderboardParams {
  limit?: number;
  school_id?: number;
  department_id?: number;
  course_id?: number;
  time_period?: 'daily' | 'weekly' | 'all_time';
}

// Get leaderboard
export const getLeaderboard = async (params: LeaderboardParams = {}): Promise<Leaderboard> => {
  const { limit = 10, ...otherParams } = params;

  // Clean up params - remove undefined values
  const cleanParams = Object.entries({ limit, ...otherParams })
    .filter(([_, value]) => value !== undefined)
    .reduce((acc, [key, value]) => ({ ...acc, [key]: value }), {});

  console.log('API call: getLeaderboard with params:', cleanParams);

  try {
    const response = await apiClient.get<Leaderboard>('/gamification/leaderboard', {
      params: cleanParams,
      timeout: 10000 // 10 second timeout
    });

    // Ensure we have a valid response structure
    if (!response.data || !response.data.entries) {
      console.warn('API returned invalid leaderboard data structure:', response.data);
      return { entries: [] };
    }

    console.log('API response:', response.data);
    return response.data;
  } catch (error) {
    console.error('API error in getLeaderboard:', error);

    // Don't swallow authentication errors - let them bubble up
    if (error?.response?.status === 401) {
      throw error;
    }

    // Return empty leaderboard for other errors to prevent UI errors
    return { entries: [] };
  }
};

// Update user streak
export const updateUserStreak = async (): Promise<UserStreak> => {
  const response = await apiClient.post<UserStreak>('/gamification/update-streak');
  return response.data;
};

// Recent Activity Types
export interface RecentActivity {
  id: string;
  type: 'quiz' | 'course' | 'bookmark' | 'note' | 'achievement' | 'streak';
  title: string;
  description: string;
  timestamp: string;
  points?: number;
}

export interface RecentActivityResponse {
  activities: RecentActivity[];
}

// Get recent activity
export const getRecentActivity = async (limit: number = 5): Promise<RecentActivityResponse> => {
  try {
    const response = await apiClient.get<RecentActivityResponse>('/gamification/recent-activity', {
      params: { limit },
      timeout: 5000
    });
    return response.data;
  } catch (error) {
    console.error('API error in getRecentActivity:', error);
    return { activities: [] };
  }
};
