import apiClient from './client';

export interface TutorProfile {
  id: number;
  user_id: number;
  bio?: string;
  experience_years?: number;
  hourly_rate?: number;
  is_available: boolean;
  preferred_session_type?: 'online' | 'in_person' | 'both';
  max_students_per_session: number;
  linkedin_url?: string;
  website_url?: string;
  location?: string;
  gender?: 'male' | 'female' | 'other' | 'prefer_not_to_say';
  languages?: string[];
  custom_specializations?: string[];
  average_rating?: number;
  total_reviews: number;
  total_sessions_completed: number;
  created_at: string;
  updated_at: string;
  user?: {
    id: number;
    full_name: string;
    email: string;
    profile_picture_url?: string;
  };
  specializations?: Array<{
    id: number;
    name: string;
    code: string;
  }>;
}

export interface TutorProfileCreate {
  bio?: string;
  experience_years?: number;
  hourly_rate?: number;
  is_available?: boolean;
  preferred_session_type?: 'online' | 'in_person' | 'both';
  max_students_per_session?: number;
  linkedin_url?: string;
  website_url?: string;
  location?: string;
  gender?: 'male' | 'female' | 'other' | 'prefer_not_to_say';
  languages?: string[];
  specialization_ids?: number[];
  custom_specializations?: string[];
}

export interface TutorProfileUpdate {
  bio?: string;
  experience_years?: number;
  hourly_rate?: number;
  is_available?: boolean;
  preferred_session_type?: 'online' | 'in_person' | 'both';
  max_students_per_session?: number;
  linkedin_url?: string;
  website_url?: string;
  location?: string;
  gender?: 'male' | 'female' | 'other' | 'prefer_not_to_say';
  languages?: string[];
  specialization_ids?: number[];
  custom_specializations?: string[];
}

export interface TutorReview {
  id: number;
  tutor_profile_id: number;
  student_id: number;
  session_id?: number;
  rating: number;
  comment?: string;
  created_at: string;
  updated_at: string;
  student?: {
    id: number;
    full_name: string;
    profile_picture_url?: string;
  };
}

export interface TutorReviewCreate {
  tutor_profile_id: number;
  session_id?: number;
  rating: number;
  comment?: string;
}

export interface TutorDashboardStats {
  total_sessions: number;
  completed_sessions: number;
  upcoming_sessions: number;
  total_students: number;
  average_rating?: number;
  total_reviews: number;
  total_earnings?: number;
  this_month_sessions: number;
  this_month_earnings?: number;
}

export interface TutorSearchFilters {
  course_ids?: number[];
  min_rating?: number;
  max_hourly_rate?: number;
  session_type?: 'online' | 'in_person' | 'both';
  is_available?: boolean;
  experience_years_min?: number;
}

export interface TutorListItem {
  id: number;
  user_id: number;
  full_name: string;
  bio?: string;
  experience_years?: number;
  hourly_rate?: number;
  average_rating?: number;
  total_reviews: number;
  total_sessions_completed: number;
  is_available: boolean;
  preferred_session_type?: string;
  location?: string;
  gender?: string;
  languages: string[];
  specializations: string[];
  profile_picture_url?: string;
}

// API Functions
export const getTutors = async (skip = 0, limit = 100): Promise<TutorProfile[]> => {
  const response = await apiClient.get(`/tutors/?skip=${skip}&limit=${limit}`);
  return response.data;
};

export const searchTutors = async (
  filters: TutorSearchFilters,
  skip = 0,
  limit = 100
): Promise<TutorProfile[]> => {
  const response = await apiClient.post(`/tutors/search?skip=${skip}&limit=${limit}`, filters);
  return response.data;
};

export const getMyTutorProfile = async (): Promise<TutorProfile> => {
  const response = await apiClient.get('/tutors/me');
  return response.data;
};

export const createMyTutorProfile = async (data: TutorProfileCreate): Promise<TutorProfile> => {
  const response = await apiClient.post('/tutors/me', data);
  return response.data;
};

export const updateMyTutorProfile = async (data: TutorProfileUpdate): Promise<TutorProfile> => {
  const response = await apiClient.put('/tutors/me', data);
  return response.data;
};

export const getMyTutorDashboard = async (): Promise<TutorDashboardStats> => {
  const response = await apiClient.get('/tutors/me/dashboard');
  return response.data;
};

export const getTutor = async (id: number): Promise<TutorProfile> => {
  const response = await apiClient.get(`/tutors/${id}`);
  return response.data;
};

export const createTutorReview = async (
  tutorId: number,
  data: TutorReviewCreate
): Promise<TutorReview> => {
  const response = await apiClient.post(`/tutors/${tutorId}/reviews`, data);
  return response.data;
};

export const getTutorReviews = async (
  tutorId: number,
  skip = 0,
  limit = 100
): Promise<TutorReview[]> => {
  const response = await apiClient.get(`/tutors/${tutorId}/reviews?skip=${skip}&limit=${limit}`);
  return response.data;
};

export const getMyTutorReviews = async (
  skip = 0,
  limit = 100
): Promise<TutorReview[]> => {
  const response = await apiClient.get(`/tutors/me/reviews?skip=${skip}&limit=${limit}`);
  return response.data;
};

export interface TutorEarnings {
  total_earnings: number;
  this_month_earnings: number;
  last_month_earnings: number;
  pending_payments: number;
  completed_sessions: number;
  average_session_rate: number;
  monthly_growth: number;
  recent_payments: Array<{
    id: number;
    amount: number;
    date: string;
    status: string;
    session_title: string;
    student_name: string;
  }>;
}

export const getMyTutorEarnings = async (): Promise<TutorEarnings> => {
  const response = await apiClient.get('/tutors/me/earnings');
  return response.data;
};

export interface TutorAnalytics {
  session_stats: {
    total_sessions: number;
    completed_sessions: number;
    cancelled_sessions: number;
    completion_rate: number;
  };
  student_stats: {
    total_students: number;
    active_students: number;
    retention_rate: number;
  };
  course_performance: Array<{
    course: string;
    sessions: number;
    students: number;
    avg_rating: number;
    earnings: number;
  }>;
  time_slot_analysis: Array<{
    time_slot: string;
    sessions: number;
    utilization: number;
  }>;
  monthly_trends: Array<{
    month: string;
    sessions: number;
    earnings: number;
    students: number;
  }>;
}

export const getMyTutorAnalytics = async (): Promise<TutorAnalytics> => {
  const response = await apiClient.get('/tutors/me/analytics');
  return response.data;
};

export const checkExistingReview = async (tutorId: number): Promise<boolean> => {
  try {
    const response = await apiClient.get(`/tutors/${tutorId}/reviews/check`);
    return response.data.hasReview;
  } catch (error: any) {
    if (error.response?.status === 404) {
      return false; // No review exists
    }
    throw error;
  }
};

// Admin functions
export const updateTutor = async (id: number, data: TutorProfileUpdate): Promise<TutorProfile> => {
  const response = await apiClient.put(`/tutors/${id}`, data);
  return response.data;
};

export const deleteTutor = async (id: number): Promise<TutorProfile> => {
  const response = await apiClient.delete(`/tutors/${id}`);
  return response.data;
};
