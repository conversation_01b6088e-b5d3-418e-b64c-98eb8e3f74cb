import axios, { AxiosError, AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import { handleAuthenticationError } from '../utils/authUtils';
import { refreshToken } from './auth';

// API URL
const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:8000/api/v1';

console.log('API URL:', API_URL);

// Create axios instance
const apiClient: AxiosInstance = axios.create({
  baseURL: API_URL,
  headers: {
    'Content-Type': 'application/json',
    'Accept': 'application/json',
  },
  timeout: 15000, // 15 seconds timeout
  withCredentials: false, // Important for CORS requests with credentials
});

// Maximum number of retry attempts
const MAX_RETRIES = 3;
// Delay between retries in milliseconds (starting with 1s, then 2s, then 4s)
const RETRY_DELAY = 1000;

// Add request interceptor for retry logic
apiClient.interceptors.response.use(
  (response: AxiosResponse) => response,
  async (error: AxiosError) => {
    const config = error.config as AxiosRequestConfig & { _retry?: number };

    // Only retry on network errors or 5xx server errors
    if (!config || !shouldRetry(error) || config._retry >= MAX_RETRIES) {
      // For network errors, create a detailed error message
      // For HTTP errors, preserve the original error for proper handling
      if (!error.response) {
        return Promise.reject(createDetailedError(error));
      }
      return Promise.reject(error);
    }

    // Initialize retry count
    config._retry = (config._retry || 0) + 1;

    // Exponential backoff delay
    const delay = RETRY_DELAY * Math.pow(2, config._retry - 1);

    // Wait for the delay
    await new Promise(resolve => setTimeout(resolve, delay));

    // Retry the request
    return apiClient(config);
  }
);

// Helper function to determine if we should retry the request
function shouldRetry(error: AxiosError): boolean {
  // Retry on network errors
  if (!error.response) {
    return true;
  }

  // Retry on 5xx server errors
  const status = error.response.status;
  return status >= 500 && status < 600;
}

// Helper function to create more detailed error messages for network errors only
function createDetailedError(error: AxiosError): Error {
  if (!error.response) {
    return new Error('Network error: Please check your internet connection and try again.');
  }

  // For responses with status codes, preserve the original error
  // so the calling code can handle specific status codes properly
  return error as any;
}

// Add a request interceptor to include the auth token in all requests
apiClient.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('token');
    if (token) {
      // Make sure we're setting the Authorization header correctly
      config.headers = config.headers || {};
      config.headers.Authorization = `Bearer ${token}`;
    }
    console.log(`API Request: ${config.method?.toUpperCase()} ${config.url}`, config.headers);
    console.log('Request data:', config.data);
    console.log('Full request config:', {
      baseURL: config.baseURL,
      url: config.url,
      method: config.method,
      headers: config.headers,
      data: config.data,
      params: config.params
    });
    return config;
  },
  (error) => {
    console.error('API Request Error:', error);
    return Promise.reject(error);
  }
);

// Add a response interceptor to handle common errors
apiClient.interceptors.response.use(
  (response) => {
    console.log(`API Response: ${response.status} ${response.config.method?.toUpperCase()} ${response.config.url}`);
    console.log('Response data:', response.data);
    console.log('Response headers:', response.headers);
    return response;
  },
  async (error) => {
    console.error('API Response Error:', error.message);
    if (error.response) {
      console.error('Status:', error.response.status);
      console.error('Data:', error.response.data);
      console.error('Headers:', error.response.headers);
      console.error('Request URL:', error.config?.url);
      console.error('Request method:', error.config?.method);
      console.error('Request data:', error.config?.data);

      // Enhanced logging for validation errors (422)
      if (error.response.status === 422) {
        console.error('VALIDATION ERROR DETAILS:');
        try {
          const requestData = JSON.parse(error.config?.data || '{}');
          console.error('Request Data (parsed):', requestData);
        } catch (e) {
          console.error('Could not parse request data:', error.config?.data);
        }
        console.error('Validation Errors:', error.response.data.detail || error.response.data);
      }

      // Handle 401 errors with automatic token refresh
      if (error.response.status === 401) {
        console.error('401 Unauthorized error:', error.response.data);

        // Don't try to refresh on login, refresh, or logout endpoints
        if (error.config?.url?.includes('/auth/login') ||
            error.config?.url?.includes('/auth/refresh') ||
            error.config?.url?.includes('/auth/logout')) {
          console.log('Authentication error on auth endpoint, not attempting refresh');
          handleAuthenticationError(error);
          return Promise.reject(error);
        }

        // Try to refresh the token
        const refreshTokenValue = localStorage.getItem('refresh_token');
        if (refreshTokenValue && !error.config?._retry) {
          console.log('Attempting to refresh token...');
          error.config._retry = true;

          try {
            await refreshToken();
            console.log('Token refreshed successfully, retrying original request');

            // Update the authorization header with the new token
            const newToken = localStorage.getItem('token');
            if (newToken) {
              error.config.headers = error.config.headers || {};
              error.config.headers.Authorization = `Bearer ${newToken}`;
            }

            // Retry the original request
            return apiClient.request(error.config);
          } catch (refreshError) {
            console.error('Token refresh failed:', refreshError);
            handleAuthenticationError(error);
            return Promise.reject(error);
          }
        } else {
          console.log('No refresh token available or already retried, handling as auth error');
          handleAuthenticationError(error);
        }
      }

      // Handle other authentication-related errors
      if (error.response.status === 402 || error.response.status === 403) {
        console.error(`Authentication/Authorization error (${error.response.status}):`, error.response.data);
        handleAuthenticationError(error);
      }
    } else if (error.request) {
      console.error('No response received. Request details:', {
        url: error.config?.url,
        method: error.config?.method,
        data: error.config?.data
      });
    }

    // Handle authentication errors using utility function for all other cases
    if (!error.response || !error.config?.url?.includes('/auth/login')) {
      handleAuthenticationError(error);
    }

    return Promise.reject(error);
  }
);

export default apiClient;
