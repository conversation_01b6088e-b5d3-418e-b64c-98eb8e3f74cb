import apiClient from './client';

export interface FlagReason {
  value: string;
  label: string;
  description: string;
}

export interface QuestionFlagCreate {
  question_id: number;
  reasons: string[];
  description?: string;
}

export interface StudentInfo {
  id: number;
  full_name: string;
  email: string;
}

export interface ReviewerInfo {
  id: number;
  full_name: string;
  email: string;
}

export interface QuestionInfo {
  id: number;
  content: string;
  difficulty: string;
  course_name?: string;
  options?: Record<string, string>;
  answer?: string;
  explanation?: string;
}

export interface QuestionFlag {
  id: number;
  question_id: number;
  student_id: number;
  reasons: string[];
  description?: string;
  status: 'pending' | 'under_review' | 'resolved' | 'dismissed';
  reviewed_by?: number;
  reviewed_at?: string;
  admin_notes?: string;
  created_at: string;
  updated_at: string;
  student?: StudentInfo;
  reviewer?: ReviewerInfo;
  question?: QuestionInfo;
}

export interface FlagSubmissionResponse {
  success: boolean;
  message: string;
  flag_id?: number;
}

export interface FlagActionResponse {
  success: boolean;
  message: string;
  updated_flags?: number;
}

export interface FlagStatistics {
  total_flags: number;
  pending_flags: number;
  resolved_flags: number;
  dismissed_flags: number;
  reason_statistics: Record<string, number>;
}

export interface FlagListResponse {
  flags: QuestionFlag[];
  total: number;
  page: number;
  per_page: number;
  total_pages: number;
}

export interface FlagFilters {
  status?: string;
  reason?: string;
  course_id?: number;
  student_id?: number;
  question_id?: number;
  date_from?: string;
  date_to?: string;
}

export interface AdminFlagListRequest {
  page?: number;
  per_page?: number;
  filters?: FlagFilters;
  sort_by?: string;
  sort_order?: 'asc' | 'desc';
}

/**
 * Submit a flag for a question
 */
export const submitQuestionFlag = async (flagData: QuestionFlagCreate): Promise<FlagSubmissionResponse> => {
  try {
    console.log('Submitting question flag:', flagData);

    const response = await apiClient.post('/question-flags/', flagData);

    console.log('Question flag submission response:', response.data);
    return response.data;
  } catch (error) {
    console.error('Error submitting question flag:', error);
    throw error;
  }
};

/**
 * Get available flag reasons
 */
export const getFlagReasons = async (): Promise<{ reasons: FlagReason[] }> => {
  try {
    console.log('Fetching flag reasons');

    const response = await apiClient.get('/question-flags/reasons');

    console.log('Flag reasons response:', response.data);
    return response.data;
  } catch (error) {
    console.error('Error fetching flag reasons:', error);
    throw error;
  }
};

/**
 * Get flags submitted by current student
 */
export const getMyFlags = async (page: number = 1, per_page: number = 20): Promise<FlagListResponse> => {
  try {
    console.log('Fetching my flags:', { page, per_page });

    const response = await apiClient.get('/question-flags/my-flags', {
      params: { page, per_page }
    });

    console.log('My flags response:', response.data);
    return response.data;
  } catch (error) {
    console.error('Error fetching my flags:', error);
    throw error;
  }
};

// Admin functions
/**
 * Get all flags with filters (admin only)
 */
export const getAllFlags = async (request: AdminFlagListRequest = {}): Promise<FlagListResponse> => {
  try {
    console.log('Fetching all flags (admin):', request);

    const response = await apiClient.post('/question-flags/admin/list', request);

    console.log('All flags response:', response.data);
    return response.data;
  } catch (error) {
    console.error('Error fetching all flags:', error);
    throw error;
  }
};

/**
 * Get flag statistics (admin only)
 */
export const getFlagStatistics = async (): Promise<FlagStatistics> => {
  try {
    console.log('Fetching flag statistics');

    const response = await apiClient.get('/question-flags/admin/statistics');

    console.log('Flag statistics response:', response.data);
    return response.data;
  } catch (error) {
    console.error('Error fetching flag statistics:', error);
    throw error;
  }
};

/**
 * Update flag status (admin only)
 */
export const updateFlagStatus = async (
  flagId: number,
  status: string,
  adminNotes?: string
): Promise<FlagActionResponse> => {
  try {
    console.log('Updating flag status:', { flagId, status, adminNotes });

    const response = await apiClient.put(`/question-flags/${flagId}/status`, {
      status,
      admin_notes: adminNotes
    });

    console.log('Flag status update response:', response.data);
    return response.data;
  } catch (error) {
    console.error('Error updating flag status:', error);
    throw error;
  }
};

/**
 * Bulk update flag status (admin only)
 */
export const bulkUpdateFlagStatus = async (
  flagIds: number[],
  status: string,
  adminNotes?: string
): Promise<FlagActionResponse> => {
  try {
    console.log('Bulk updating flag status:', { flagIds, status, adminNotes });

    const response = await apiClient.put('/question-flags/admin/bulk-update', {
      flag_ids: flagIds,
      status,
      admin_notes: adminNotes
    });

    console.log('Bulk flag status update response:', response.data);
    return response.data;
  } catch (error) {
    console.error('Error bulk updating flag status:', error);
    throw error;
  }
};

/**
 * Delete a flag (admin only)
 */
export const deleteFlag = async (flagId: number): Promise<FlagActionResponse> => {
  try {
    console.log('Deleting flag:', flagId);

    const response = await apiClient.delete(`/question-flags/${flagId}`);

    console.log('Flag deletion response:', response.data);
    return response.data;
  } catch (error) {
    console.error('Error deleting flag:', error);
    throw error;
  }
};

/**
 * Get flags for a specific question (admin only)
 */
export const getQuestionFlags = async (
  questionId: number,
  page: number = 1,
  per_page: number = 20
): Promise<FlagListResponse> => {
  try {
    console.log('Fetching flags for question:', { questionId, page, per_page });

    const response = await apiClient.get(`/question-flags/question/${questionId}`, {
      params: { page, per_page }
    });

    console.log('Question flags response:', response.data);
    return response.data;
  } catch (error) {
    console.error('Error fetching question flags:', error);
    throw error;
  }
};

/**
 * Update a question (admin only)
 */
export const updateQuestion = async (questionId: number, questionData: {
  content?: string;
  options?: Record<string, string>;
  answer?: string;
  explanation?: string;
}): Promise<any> => {
  try {
    console.log('Updating question:', questionId, questionData);

    const response = await apiClient.put(`/questions/${questionId}`, questionData);

    console.log('Update question response:', response.data);
    return response.data;
  } catch (error: any) {
    console.error('Error updating question:', error);
    throw new Error(error.response?.data?.detail || 'Failed to update question');
  }
};

/**
 * Update a question's content only (admin only) - for backward compatibility
 */
export const updateQuestionContent = async (questionId: number, content: string): Promise<any> => {
  return updateQuestion(questionId, { content });
};

/**
 * Update flag admin notes (admin only)
 */
export const updateFlagNotes = async (flagId: number, adminNotes: string): Promise<FlagActionResponse> => {
  try {
    console.log('Updating flag notes:', flagId, adminNotes);

    const response = await apiClient.put(`/question-flags/${flagId}/notes`, {
      admin_notes: adminNotes
    });

    console.log('Update flag notes response:', response.data);
    return response.data;
  } catch (error: any) {
    console.error('Error updating flag notes:', error);
    throw new Error(error.response?.data?.detail || 'Failed to update flag notes');
  }
};
