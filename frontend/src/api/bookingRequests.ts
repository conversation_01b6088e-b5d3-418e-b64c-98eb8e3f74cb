import apiClient from './client';

export enum BookingRequestStatus {
  PENDING = 'pending',
  ACCEPTED = 'accepted',
  DECLINED = 'declined',
  CANCELLED = 'cancelled'
}

export interface BookingRequest {
  id: number;
  subject: string;
  message: string;
  preferred_date: string;
  duration_hours: number;
  session_type: string;
  status: BookingRequestStatus;
  tutor_response?: string;
  response_date?: string;
  chat_room_id?: number;  // Link to chat room when accepted
  student_id: number;
  tutor_id: number;
  course_id?: number;
  session_id?: number;
  created_at: string;
  updated_at: string;
  // Additional fields from API
  student_name?: string;
  student_email?: string;
  tutor_name?: string;
  tutor_email?: string;
  course_name?: string;
}

export interface BookingRequestCreate {
  tutor_id: number;
  course_id?: number;
  message?: string;
}

export interface BookingRequestResponse {
  status: BookingRequestStatus;
  tutor_response?: string;
}

export interface BookingRequestFilters {
  status?: BookingRequestStatus;
  tutor_id?: number;
  student_id?: number;
  course_id?: number;
  date_from?: string;
  date_to?: string;
}

// Get booking requests for current user
export const getBookingRequests = async (
  skip: number = 0,
  limit: number = 100,
  status?: BookingRequestStatus
): Promise<BookingRequest[]> => {
  try {
    const params = new URLSearchParams({
      skip: skip.toString(),
      limit: limit.toString(),
    });
    
    if (status) {
      params.append('status', status);
    }

    const response = await apiClient.get<BookingRequest[]>(
      `/booking-requests/?${params.toString()}`
    );
    return response.data;
  } catch (error: any) {
    console.error('Failed to fetch booking requests:', error);
    throw new Error(error.response?.data?.detail || 'Failed to fetch booking requests');
  }
};

// Create a new booking request (students only)
export const createBookingRequest = async (
  requestData: BookingRequestCreate
): Promise<BookingRequest> => {
  try {
    const response = await apiClient.post<BookingRequest>(
      '/booking-requests/',
      requestData
    );
    return response.data;
  } catch (error: any) {
    console.error('Failed to create booking request:', error);
    throw new Error(error.response?.data?.detail || 'Failed to create booking request');
  }
};

// Get a specific booking request
export const getBookingRequest = async (id: number): Promise<BookingRequest> => {
  try {
    const response = await apiClient.get<BookingRequest>(`/booking-requests/${id}`);
    return response.data;
  } catch (error: any) {
    console.error('Failed to fetch booking request:', error);
    throw new Error(error.response?.data?.detail || 'Failed to fetch booking request');
  }
};

// Respond to a booking request (tutors only)
export const respondToBookingRequest = async (
  id: number,
  response: BookingRequestResponse
): Promise<BookingRequest> => {
  try {
    const apiResponse = await apiClient.post<BookingRequest>(
      `/booking-requests/${id}/respond`,
      response
    );
    return apiResponse.data;
  } catch (error: any) {
    console.error('Failed to respond to booking request:', error);
    throw new Error(error.response?.data?.detail || 'Failed to respond to booking request');
  }
};

// Get pending requests count for tutor
export const getPendingRequestsCount = async (): Promise<number> => {
  try {
    const response = await apiClient.get<{ pending_count: number }>(
      '/booking-requests/tutor/pending-count'
    );
    return response.data.pending_count;
  } catch (error: any) {
    console.error('Failed to fetch pending requests count:', error);
    throw new Error(error.response?.data?.detail || 'Failed to fetch pending requests count');
  }
};

// Accept a booking request
export const acceptBookingRequest = async (
  id: number,
  response: string,
  createSession: boolean = false,
  sessionDetails?: any
): Promise<BookingRequest> => {
  return respondToBookingRequest(id, {
    status: BookingRequestStatus.ACCEPTED,
    tutor_response: response,
    create_session: createSession,
    session_details: sessionDetails
  });
};

// Decline a booking request
export const declineBookingRequest = async (
  id: number,
  response: string
): Promise<BookingRequest> => {
  return respondToBookingRequest(id, {
    status: BookingRequestStatus.DECLINED,
    tutor_response: response
  });
};
