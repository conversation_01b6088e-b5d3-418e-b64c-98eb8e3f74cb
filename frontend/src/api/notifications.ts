import apiClient from './client';

export enum NotificationType {
  BOOKING_REQUEST = 'BOOKING_REQUEST',
  BOOKING_ACCEPTED = 'BOOKING_ACCEPTED',
  BOOKING_REJECTED = 'BOOKING_REJECTED',
  BOOKING_CANCELLED = 'BOOKING_CANCELLED',
  SYSTEM = 'SYSTEM',
  ACHIEVEMENT = 'ACHIEVEMENT',
  REMINDER = 'REMINDER',
  CONTENT_SHARED = 'CONTENT_SHARED',
  CONTENT_SHARE_ACCEPTED = 'CONTENT_SHARE_ACCEPTED',
  CONTENT_SHARE_REJECTED = 'CONTENT_SHARE_REJECTED'
}

export interface Notification {
  id: number;
  user_id: number;
  type: NotificationType;
  title: string;
  message: string;
  is_read: boolean;
  related_id?: number;
  related_type?: string;
  created_at: string;
  read_at?: string;
}

export interface NotificationCreate {
  user_id: number;
  type: NotificationType;
  title: string;
  message: string;
  related_id?: number;
  related_type?: string;
}

export interface NotificationBulkAction {
  action: 'mark_all_read' | 'delete_old';
  days_old?: number;
}

// Get notifications for current user
export const getNotifications = async (
  skip: number = 0,
  limit: number = 100,
  unreadOnly: boolean = false
): Promise<Notification[]> => {
  try {
    const params = new URLSearchParams({
      skip: skip.toString(),
      limit: limit.toString(),
      unread_only: unreadOnly.toString(),
    });

    const response = await apiClient.get<Notification[]>(
      `/notifications/?${params.toString()}`
    );
    return response.data;
  } catch (error: any) {
    console.error('Failed to fetch notifications:', error);
    throw new Error(error.response?.data?.detail || 'Failed to fetch notifications');
  }
};

// Get unread notification count
export const getUnreadNotificationCount = async (): Promise<number> => {
  try {
    const response = await apiClient.get<{ unread_count: number }>(
      '/notifications/unread-count'
    );
    return response.data.unread_count;
  } catch (error: any) {
    console.error('Failed to fetch unread count:', error);
    throw new Error(error.response?.data?.detail || 'Failed to fetch unread count');
  }
};

// Mark a specific notification as read
export const markNotificationAsRead = async (
  notificationId: number
): Promise<Notification> => {
  try {
    const response = await apiClient.post<Notification>(
      `/notifications/mark-read/${notificationId}`
    );
    return response.data;
  } catch (error: any) {
    console.error('Failed to mark notification as read:', error);
    throw new Error(error.response?.data?.detail || 'Failed to mark notification as read');
  }
};

// Mark all notifications as read
export const markAllNotificationsAsRead = async (): Promise<{ updated_count: number }> => {
  try {
    const response = await apiClient.post<{ updated_count: number }>(
      '/notifications/mark-all-read'
    );
    return response.data;
  } catch (error: any) {
    console.error('Failed to mark all notifications as read:', error);
    throw new Error(error.response?.data?.detail || 'Failed to mark all notifications as read');
  }
};

// Delete a specific notification
export const deleteNotification = async (
  notificationId: number
): Promise<{ message: string }> => {
  try {
    const response = await apiClient.delete<{ message: string }>(
      `/notifications/${notificationId}`
    );
    return response.data;
  } catch (error: any) {
    console.error('Failed to delete notification:', error);
    throw new Error(error.response?.data?.detail || 'Failed to delete notification');
  }
};

// Perform bulk actions on notifications
export const performBulkNotificationAction = async (
  action: NotificationBulkAction
): Promise<{ message: string }> => {
  try {
    const response = await apiClient.post<{ message: string }>(
      '/notifications/bulk-action',
      action
    );
    return response.data;
  } catch (error: any) {
    console.error('Failed to perform bulk action:', error);
    throw new Error(error.response?.data?.detail || 'Failed to perform bulk action');
  }
};

// Create a notification (admin only)
export const createNotification = async (
  notificationData: NotificationCreate
): Promise<Notification> => {
  try {
    const response = await apiClient.post<Notification>(
      '/notifications/',
      notificationData
    );
    return response.data;
  } catch (error: any) {
    console.error('Failed to create notification:', error);
    throw new Error(error.response?.data?.detail || 'Failed to create notification');
  }
};
