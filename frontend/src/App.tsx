import React from 'react';
import { <PERSON>rowserRouter, useLocation } from 'react-router-dom';
import { CssBaseline } from '@mui/material';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { AdapterDateFns } from '@mui/x-date-pickers/AdapterDateFns';
import { AuthProvider } from './contexts/AuthContext';
import { ThemeProvider } from './contexts/ThemeContext';
import { ToastProvider } from './contexts/ToastContext';
import AppRoutes from './routes/AppRoutes';
import { FloatingAssistantButton } from './components/AIAssistant';
import ErrorBoundary from './components/ErrorBoundary';
import { perfMonitor } from './utils/performance';
import { forceLogout, isAuthenticationError, isPublicRoute } from './utils/authUtils';
import './utils/authTestUtils'; // Import for development testing
import './App.css';

// Create a client for React Query
const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      refetchOnWindowFocus: false,
      retry: (failureCount, error: any) => {
        // Don't retry on authentication errors - redirect immediately
        if (isAuthenticationError(error)) {
          const currentPath = window.location.pathname;
          if (!isPublicRoute(currentPath)) {
            console.log('Authentication error detected in query, redirecting to login');
            forceLogout();
          }
          return false;
        }
        // Retry other errors up to 1 time
        return failureCount < 1;
      },
      staleTime: 5 * 60 * 1000, // 5 minutes
      gcTime: 10 * 60 * 1000, // 10 minutes (renamed from cacheTime)
      throwOnError: (error: any) => {
        // Never throw authentication errors - handle them silently with redirect
        if (isAuthenticationError(error)) {
          return false;
        }
        return false;
      },
    },
    mutations: {
      retry: (failureCount, error: any) => {
        // Don't retry on authentication errors - redirect immediately
        if (isAuthenticationError(error)) {
          const currentPath = window.location.pathname;
          if (!isPublicRoute(currentPath)) {
            console.log('Authentication error detected in mutation, redirecting to login');
            forceLogout();
          }
          return false;
        }
        // Retry other errors up to 1 time
        return failureCount < 1;
      },
      throwOnError: (error: any) => {
        // Never throw authentication errors - handle them silently with redirect
        if (isAuthenticationError(error)) {
          return false;
        }
        return false;
      },
    },
  },
});

// Global error handler for React Query
queryClient.setMutationDefaults(['*'], {
  onError: (error: any) => {
    const currentPath = window.location.pathname;
    if (isAuthenticationError(error) && !isPublicRoute(currentPath)) {
      console.log('React Query mutation authentication error detected, forcing logout');
      forceLogout();
    }
  },
});

// Set up global query error handler
queryClient.setQueryDefaults(['*'], {
  onError: (error: any) => {
    const currentPath = window.location.pathname;
    if (isAuthenticationError(error) && !isPublicRoute(currentPath)) {
      console.log('React Query authentication error detected, forcing logout');
      forceLogout();
    }
  },
});

function App() {
  // Mark app start for performance monitoring
  React.useEffect(() => {
    perfMonitor.markStart('app-render');
    return () => {
      perfMonitor.markEnd('app-render');
    };
  }, []);

  // Global error handler for unhandled authentication errors
  React.useEffect(() => {
    const handleUnhandledRejection = (event: PromiseRejectionEvent) => {
      const error = event.reason;
      if (error && typeof error === 'object') {
        if (isAuthenticationError(error)) {
          console.log('Unhandled authentication error detected, forcing logout');
          const currentPath = window.location.pathname;
          if (!isPublicRoute(currentPath)) {
            forceLogout();
          }
          event.preventDefault(); // Prevent the error from being logged to console
        }
      }
    };

    window.addEventListener('unhandledrejection', handleUnhandledRejection);
    return () => {
      window.removeEventListener('unhandledrejection', handleUnhandledRejection);
    };
  }, []);

  return (
    <ErrorBoundary>
      <QueryClientProvider client={queryClient}>
        <LocalizationProvider dateAdapter={AdapterDateFns}>
          <ThemeProvider>
            <CssBaseline />
            <BrowserRouter
              future={{
                v7_startTransition: true,
                v7_relativeSplatPath: true
              }}
            >
              <AuthProvider>
                <ToastProvider>
                  <AppRoutes />
                  <ConditionalAssistant />
                </ToastProvider>
              </AuthProvider>
            </BrowserRouter>
          </ThemeProvider>
        </LocalizationProvider>
      </QueryClientProvider>
    </ErrorBoundary>
  );
}

// Separate component to conditionally render the FloatingAssistantButton
const ConditionalAssistant = () => {
  const location = useLocation();

  // Only show the assistant on dashboard pages
  const isDashboardPage = location.pathname === '/dashboard' ||
                          location.pathname === '/admin/dashboard' ||
                          location.pathname === '/tutor/dashboard';

  if (!isDashboardPage) {
    return null;
  }

  return <FloatingAssistantButton />;
};

export default App;
