:root {
  font-family: '<PERSON><PERSON><PERSON>', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
  line-height: 1.5;
  font-weight: 400;

  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
  scroll-behavior: smooth;
}

* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

html, body {
  height: 100%;
  width: 100%;
  margin: 0;
  padding: 0;
  overflow-x: hidden;
}

body {
  margin: 0;
  min-width: 320px;
  min-height: 100vh;
  overflow-y: auto;
  position: relative;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 10px;
  height: 10px;
}

::-webkit-scrollbar-track {
  background: rgba(0, 0, 0, 0.05);
}

::-webkit-scrollbar-thumb {
  background: rgba(0, 118, 255, 0.5);
  border-radius: 10px;
  transition: all 0.3s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: rgba(0, 118, 255, 0.7);
}

/* Upload progress animations */
@keyframes progress-stripes {
  0% {
    background-position: 0 0;
  }
  100% {
    background-position: 20px 0;
  }
}

/* Pulse animation for upload indicators */
@keyframes upload-pulse {
  0%, 100% {
    opacity: 1;
  }
  50% {
    opacity: 0.5;
  }
}

/* Shimmer effect for loading states */
@keyframes shimmer {
  0% {
    background-position: -200px 0;
  }
  100% {
    background-position: calc(200px + 100%) 0;
  }
}

/* Selection color */
::selection {
  background-color: rgba(0, 118, 255, 0.2);
  color: inherit;
}

/* Animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    transform: translateY(20px);
    opacity: 0;
  }
  to {
    transform: translateY(0);
    opacity: 1;
  }
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(0, 118, 255, 0.4);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(0, 118, 255, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(0, 118, 255, 0);
  }
}

/* Utility classes */
.fadeIn {
  animation: fadeIn 0.5s ease forwards;
}

.slideUp {
  animation: slideUp 0.5s ease forwards;
}

.pulse {
  animation: pulse 2s infinite;
}

/* Gradient text */
.gradient-text {
  background: linear-gradient(90deg, #1976d2, #9c27b0);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  text-fill-color: transparent;
}

/* Glass effect */
.glass {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.1);
}

/* Math rendering styles */
.katex {
  font-size: 1.1em !important;
  line-height: 1.5 !important;
  font-family: 'KaTeX_Main', serif !important;
}

.katex-display {
  margin: 1.5em 0 !important;
  overflow-x: auto;
  overflow-y: hidden;
  padding: 0.5em 0;
  text-align: center;
  background-color: rgba(0, 0, 0, 0.02);
  border-radius: 4px;
}

.katex-display > .katex {
  max-width: 100%;
  overflow-x: auto;
  overflow-y: hidden;
  display: inline-block !important;
  text-align: center;
  padding: 0.5em;
}

/* Fix for inline math */
.katex-inline {
  display: inline-block;
  vertical-align: middle;
}

/* Ensure proper spacing in paragraphs */
p {
  margin-bottom: 1em;
}

/* Fix for lists with math */
li .katex {
  vertical-align: middle;
}

/* Dark mode support for math rendering */
.MuiPaper-root[data-mui-color-scheme="dark"] .katex {
  color: #f5f5f5;
}

.MuiPaper-root[data-mui-color-scheme="dark"] .katex-display {
  background-color: rgba(255, 255, 255, 0.05);
}

/* Specific styles for AI assistant responses */
.ai-response-content {
  width: 100%;
  overflow-wrap: break-word;
  word-wrap: break-word;
  word-break: break-word;
}

.practice-help-content {
  width: 100%;
  overflow-wrap: break-word;
  word-wrap: break-word;
  word-break: break-word;
}

/* Fix for long equations - SIMPLE APPROACH */
.katex-html {
  max-width: 100%;
  overflow-x: auto;
  overflow-y: hidden;
  padding-bottom: 5px;
}

/* Fix for fractions and other tall elements */
.katex .vlist-t2 {
  margin-top: 0.25em;
  margin-bottom: 0.25em;
}

/* MOBILE FIXES - SIMPLE AND EFFECTIVE */
@media (max-width: 600px) {
  /* Make math smaller on mobile */
  .katex-display {
    font-size: 0.85em !important;
    padding: 0.3em 0;
  }

  .katex {
    font-size: 0.9em !important;
  }

  /* Allow horizontal scroll for long expressions */
  .katex-display,
  .katex-html {
    overflow-x: auto !important;
    overflow-y: hidden !important;
    -webkit-overflow-scrolling: touch;
    max-width: 100% !important;
  }

  /* Force containers to respect mobile width and allow scroll */
  .MuiFormControlLabel-label {
    width: 100% !important;
    max-width: 100% !important;
    overflow-x: auto !important;
    -webkit-overflow-scrolling: touch;
  }

  /* Ensure option containers can scroll */
  .MuiFormControlLabel-root {
    width: 100% !important;
    max-width: 100% !important;
    align-items: flex-start !important;
  }

  /* Make sure the flex containers allow overflow */
  .MuiFormControlLabel-label > span {
    max-width: 100% !important;
    overflow-x: auto !important;
  }

  /* Smaller padding on mobile */
  .ai-response-content p,
  .practice-help-content p {
    margin-bottom: 0.75em;
  }

  /* Ensure all math containers can scroll horizontally */
  div[style*="overflowX: auto"] .katex,
  div[style*="overflow-x: auto"] .katex {
    white-space: nowrap !important;
  }
}

/* Modern MCQ Options Styling */
.mcq-option-card {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

.mcq-option-card:hover {
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1) !important;
}

.mcq-option-circle {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

/* Smooth selection animations */
@keyframes optionSelect {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.02);
  }
  100% {
    transform: scale(1);
  }
}

.mcq-option-selected {
  animation: optionSelect 0.3s ease-in-out;
}

/* Mobile optimizations for modern options */
@media (max-width: 600px) {
  .mcq-option-card {
    padding: 12px !important;
  }

  .mcq-option-circle {
    width: 28px !important;
    height: 28px !important;
    font-size: 0.8rem !important;
  }
}
