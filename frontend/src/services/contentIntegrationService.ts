/**
 * Service to integrate the new sharing system with existing content components
 */

import { 
  SharedContentItem, 
  ContentType, 
  MCQWithSharing, 
  FlashcardWithSharing, 
  SummaryWithSharing 
} from '../types/contentSharing';

// Helper functions to convert existing content to SharedContentItem format

export const convertMCQToSharedItem = (
  mcq: any, 
  isShared = false, 
  sharedBy?: { name: string; email: string }, 
  shareMessage?: string,
  sharedAt?: string
): MCQWithSharing => {
  return {
    content_type: ContentType.MCQ,
    content_id: mcq.id,
    content_data: {
      id: mcq.id,
      content: mcq.content,
      question_type: mcq.question_type || 'multiple_choice',
      difficulty: mcq.difficulty,
      topic: mcq.topic,
      options: mcq.options,
      answer: mcq.answer,
      explanation: mcq.explanation,
      course_name: mcq.course_name,
      created_at: mcq.created_at
    },
    is_shared: isShared,
    shared_by_name: sharedBy?.name,
    shared_by_email: sharedBy?.email,
    share_message: shareMessage,
    shared_at: sharedAt,
    can_edit: !isShared,
    can_delete: !isShared,
    tags: isShared ? ['shared', 'read-only'] : []
  };
};

export const convertFlashcardToSharedItem = (
  flashcard: any, 
  isShared = false, 
  sharedBy?: { name: string; email: string }, 
  shareMessage?: string,
  sharedAt?: string
): FlashcardWithSharing => {
  return {
    content_type: ContentType.FLASHCARD,
    content_id: flashcard.id,
    content_data: {
      id: flashcard.id,
      front_content: flashcard.front_content,
      back_content: flashcard.back_content,
      topic: flashcard.topic,
      difficulty: flashcard.difficulty,
      card_type: flashcard.card_type,
      course_name: flashcard.course_name,
      created_at: flashcard.created_at
    },
    is_shared: isShared,
    shared_by_name: sharedBy?.name,
    shared_by_email: sharedBy?.email,
    share_message: shareMessage,
    shared_at: sharedAt,
    can_edit: !isShared,
    can_delete: !isShared,
    tags: isShared ? ['shared', 'read-only'] : []
  };
};

export const convertSummaryToSharedItem = (
  summary: any, 
  isShared = false, 
  sharedBy?: { name: string; email: string }, 
  shareMessage?: string,
  sharedAt?: string
): SummaryWithSharing => {
  return {
    content_type: ContentType.SUMMARY,
    content_id: summary.id,
    content_data: {
      id: summary.id,
      title: summary.title,
      overview: summary.overview,
      detailed_explanation: summary.detailed_explanation,
      comprehensive_summary: summary.comprehensive_summary,
      key_concepts: summary.key_concepts,
      main_topics: summary.main_topics,
      course_name: summary.course_name,
      created_at: summary.created_at
    },
    is_shared: isShared,
    shared_by_name: sharedBy?.name,
    shared_by_email: sharedBy?.email,
    share_message: shareMessage,
    shared_at: sharedAt,
    can_edit: !isShared,
    can_delete: !isShared,
    tags: isShared ? ['shared', 'read-only'] : []
  };
};

// Helper functions to extract content data from SharedContentItem

export const extractMCQData = (item: MCQWithSharing) => {
  return item.content_data;
};

export const extractFlashcardData = (item: FlashcardWithSharing) => {
  return item.content_data;
};

export const extractSummaryData = (item: SummaryWithSharing) => {
  return item.content_data;
};

// Helper functions for content grouping and filtering

export const groupContentByCourse = (items: SharedContentItem[]) => {
  const grouped: { [courseName: string]: SharedContentItem[] } = {};
  
  items.forEach(item => {
    const courseName = item.content_data.course_name || 'Uncategorized';
    if (!grouped[courseName]) {
      grouped[courseName] = [];
    }
    grouped[courseName].push(item);
  });
  
  return grouped;
};

export const filterContentByType = (items: SharedContentItem[], contentType: ContentType) => {
  return items.filter(item => item.content_type === contentType);
};

export const filterSharedContent = (items: SharedContentItem[]) => {
  return items.filter(item => item.is_shared);
};

export const filterOwnContent = (items: SharedContentItem[]) => {
  return items.filter(item => !item.is_shared);
};

// Helper functions for content display

export const getContentTitle = (item: SharedContentItem): string => {
  switch (item.content_type) {
    case ContentType.MCQ:
      return item.content_data.content || `MCQ Question ${item.content_id}`;
    case ContentType.FLASHCARD:
      return item.content_data.front_content || `Flashcard ${item.content_id}`;
    case ContentType.SUMMARY:
      return item.content_data.title || `Summary ${item.content_id}`;
    default:
      return `Content ${item.content_id}`;
  }
};

export const getContentDescription = (item: SharedContentItem): string => {
  switch (item.content_type) {
    case ContentType.MCQ:
      return `${item.content_data.difficulty} difficulty • ${item.content_data.topic || 'General'}`;
    case ContentType.FLASHCARD:
      return `${item.content_data.difficulty || 'Medium'} • ${item.content_data.card_type || 'Flashcard'}`;
    case ContentType.SUMMARY:
      return item.content_data.overview?.substring(0, 100) + '...' || 'Summary content';
    default:
      return 'Content';
  }
};

export const getContentIcon = (contentType: ContentType) => {
  switch (contentType) {
    case ContentType.MCQ:
      return 'quiz';
    case ContentType.FLASHCARD:
      return 'style';
    case ContentType.SUMMARY:
      return 'description';
    default:
      return 'content_copy';
  }
};

// Helper functions for sharing permissions

export const canShareContent = (item: SharedContentItem): boolean => {
  // Users can only share content they own (not shared content)
  return !item.is_shared;
};

export const canEditContent = (item: SharedContentItem): boolean => {
  return item.can_edit;
};

export const canDeleteContent = (item: SharedContentItem): boolean => {
  return item.can_delete;
};

// Helper functions for content statistics

export const getContentStats = (items: SharedContentItem[]) => {
  const stats = {
    total: items.length,
    own: 0,
    shared: 0,
    byType: {
      [ContentType.MCQ]: 0,
      [ContentType.FLASHCARD]: 0,
      [ContentType.SUMMARY]: 0
    },
    byCourse: {} as { [courseName: string]: number }
  };

  items.forEach(item => {
    if (item.is_shared) {
      stats.shared++;
    } else {
      stats.own++;
    }

    stats.byType[item.content_type]++;

    const courseName = item.content_data.course_name || 'Uncategorized';
    stats.byCourse[courseName] = (stats.byCourse[courseName] || 0) + 1;
  });

  return stats;
};

export default {
  convertMCQToSharedItem,
  convertFlashcardToSharedItem,
  convertSummaryToSharedItem,
  extractMCQData,
  extractFlashcardData,
  extractSummaryData,
  groupContentByCourse,
  filterContentByType,
  filterSharedContent,
  filterOwnContent,
  getContentTitle,
  getContentDescription,
  getContentIcon,
  canShareContent,
  canEditContent,
  canDeleteContent,
  getContentStats
};
