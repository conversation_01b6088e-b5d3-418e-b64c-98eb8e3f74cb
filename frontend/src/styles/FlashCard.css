/* Flash Card Styles */
.card-container {
  perspective: 1000px;
  width: 100%;
  max-width: 500px;
  height: 400px;
  margin: 0 auto;
  cursor: pointer;
}

.card-inner {
  position: relative;
  width: 100%;
  height: 100%;
  transform-style: preserve-3d;
  transition: transform 0.9s;
}

.card-inner.flipped {
  transform: rotateY(180deg);
}

.card-front,
.card-back {
  position: absolute;
  width: 100%;
  height: 100%;
  backface-visibility: hidden;
  border-radius: 10px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 20px;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  border: 1px solid #ddd;
}

.card-back {
  transform: rotateY(180deg);
}

/* Mobile-friendly adjustments */
@media (max-width: 768px) {
  .card-container {
    height: 300px;
  }
}
