#root {
  width: 100%;
  margin: 0 auto;
  text-align: center;
  min-height: 100vh;
  display: flex;
  flex-direction: column;
}



/* Gamification animations */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.05);
  }
  100% {
    transform: scale(1);
  }
}

.badge-hover {
  transition: transform 0.2s ease-in-out;
}

.badge-hover:hover {
  transform: scale(1.05);
}

.leaderboard-row {
  transition: background-color 0.2s ease-in-out;
}
