import React, { useState } from 'react';
import {
  Box,
  Typography,
  Container,
  Paper,
  useTheme,
  useMediaQuery,
  Tabs,
  Tab
} from '@mui/material';
import {
  Quiz as QuizIcon,
  FolderOpen as FolderOpenIcon
} from '@mui/icons-material';
import { motion, AnimatePresence } from 'framer-motion';

import { useAuth } from '../../contexts/AuthContext';
import GeminiMCQGenerator from '../../components/Tools/GeminiMCQGenerator';
import MyGeneratedMCQsList from '../../components/Tools/MyGeneratedMCQsList';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const TabPanel: React.FC<TabPanelProps> = ({ children, value, index }) => {
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`mcq-tabpanel-${index}`}
      aria-labelledby={`mcq-tab-${index}`}
    >
      {value === index && (
        <Box sx={{ py: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
};

const MCQGenerationPage: React.FC = () => {
  const theme = useTheme();
  const { user } = useAuth();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const [tabValue, setTabValue] = useState(0);

  // Listen for tab switching events from MCQ generation
  React.useEffect(() => {
    const handleTabSwitch = () => {
      setTabValue(1); // Switch to "My Generated MCQs" tab
    };

    window.addEventListener('switchToGeneratedMCQsTab', handleTabSwitch);
    return () => {
      window.removeEventListener('switchToGeneratedMCQsTab', handleTabSwitch);
    };
  }, []);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  if (!user) {
    return (
      <Box sx={{ p: 3, textAlign: 'center' }}>
        <Typography variant="h5">Please log in to generate MCQs</Typography>
      </Box>
    );
  }

  const containerVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        type: 'spring',
        stiffness: 100,
        damping: 15
      }
    }
  };

  return (
    <Box sx={{ minHeight: '100vh', bgcolor: 'background.default' }}>

      {/* Main Content */}
      <Container maxWidth="lg" sx={{ px: { xs: 1, sm: 2, md: 3 } }}>
        <Box sx={{ py: { xs: 1, md: 3 } }}>
          {/* Page Header */}
          <Box sx={{ mb: { xs: 2, md: 4 }, px: { xs: 1, sm: 0 } }}>
            <Typography
              variant={isMobile ? "h5" : "h3"}
              component="h1"
              gutterBottom
              sx={{
                fontWeight: 600,
                color: 'text.primary',
                fontSize: { xs: '1.5rem', sm: '2rem', md: '2.5rem' }
              }}
            >
              AI MCQ Generator
            </Typography>
            <Typography
              variant="body1"
              color="text.secondary"
              sx={{
                mb: 3,
                fontSize: { xs: '0.9rem', sm: '1rem' }
              }}
            >
              Upload PDF files and generate high-quality multiple choice questions, or view your uploaded notes
            </Typography>
          </Box>

          {/* Main Content */}
          <Paper
            elevation={0}
            sx={{
              borderRadius: { xs: 1, sm: 2 },
              border: 1,
              borderColor: 'divider',
              overflow: 'hidden',
              mx: { xs: 0, sm: 0 }
            }}
          >
            {/* Tabs */}
            <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
              <Tabs
                value={tabValue}
                onChange={handleTabChange}
                aria-label="mcq tabs"
                variant={isMobile ? "fullWidth" : "standard"}
                sx={{
                  px: { xs: 2, sm: 3, md: 4 },
                  '& .MuiTab-root': {
                    textTransform: 'none',
                    fontWeight: 600,
                    fontSize: { xs: '0.875rem', sm: '1rem' }
                  }
                }}
              >
                <Tab
                  icon={<QuizIcon />}
                  iconPosition="start"
                  label="Generate MCQs"
                  id="mcq-tab-0"
                  aria-controls="mcq-tabpanel-0"
                />
                <Tab
                  icon={<FolderOpenIcon />}
                  iconPosition="start"
                  label="Generated MCQs"
                  id="mcq-tab-1"
                  aria-controls="mcq-tabpanel-1"
                />
              </Tabs>
            </Box>

            {/* Tab Content */}
            <Box sx={{ p: { xs: 2, sm: 3, md: 4 } }}>
              <AnimatePresence mode="wait">
                {tabValue === 0 && (
                  <motion.div
                    key="generate-tab"
                    variants={containerVariants}
                    initial="hidden"
                    animate="visible"
                    exit="hidden"
                  >
                    <TabPanel value={tabValue} index={0}>
                      <GeminiMCQGenerator />
                    </TabPanel>
                  </motion.div>
                )}

                {tabValue === 1 && (
                  <motion.div
                    key="notes-tab"
                    variants={containerVariants}
                    initial="hidden"
                    animate="visible"
                    exit="hidden"
                  >
                    <TabPanel value={tabValue} index={1}>
                      <MyGeneratedMCQsList />
                    </TabPanel>
                  </motion.div>
                )}
              </AnimatePresence>
            </Box>
          </Paper>
        </Box>
      </Container>
    </Box>
  );
};

export default MCQGenerationPage;
