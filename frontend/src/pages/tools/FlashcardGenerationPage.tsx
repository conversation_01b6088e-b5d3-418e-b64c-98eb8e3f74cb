import React, { useState } from 'react';
import {
  Box,
  Typography,
  Paper,
  Tabs,
  Tab,
  Container,
  useTheme,
  useMediaQuery,
  Chip
} from '@mui/material';
import {
  Style as FlashcardIcon,
  AutoAwesome as GenerateIcon,
  ViewList as ListIcon
} from '@mui/icons-material';
import { motion } from 'framer-motion';

import GeminiFlashcardGenerator from '../../components/Tools/GeminiFlashcardGenerator';
import MyGeneratedFlashcards from '../../components/Tools/MyGeneratedFlashcards';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`flashcard-tabpanel-${index}`}
      aria-labelledby={`flashcard-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ py: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

function a11yProps(index: number) {
  return {
    id: `flashcard-tab-${index}`,
    'aria-controls': `flashcard-tabpanel-${index}`,
  };
}

const FlashcardGenerationPage: React.FC = () => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const [tabValue, setTabValue] = useState(0);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  // Listen for custom events to switch tabs
  React.useEffect(() => {
    const handleSwitchToGeneratedFlashcardsTab = () => {
      setTabValue(1);
    };

    const handleSwitchToGenerateTab = () => {
      setTabValue(0);
    };

    window.addEventListener('switchToGeneratedFlashcardsTab', handleSwitchToGeneratedFlashcardsTab);
    window.addEventListener('switchToGenerateTab', handleSwitchToGenerateTab);

    return () => {
      window.removeEventListener('switchToGeneratedFlashcardsTab', handleSwitchToGeneratedFlashcardsTab);
      window.removeEventListener('switchToGenerateTab', handleSwitchToGenerateTab);
    };
  }, []);

  const containerVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 }
  };

  return (
    <Container maxWidth="lg" sx={{ py: 3 }}>
      <motion.div
        variants={containerVariants}
        initial="hidden"
        animate="visible"
      >


        {/* Header */}
        <motion.div variants={itemVariants}>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
            <FlashcardIcon sx={{ fontSize: 40, color: theme.palette.primary.main, mr: 2 }} />
            <Box>
              <Typography variant="h4" fontWeight="bold" gutterBottom>
                Flashcard Generation
              </Typography>
            </Box>
          </Box>
        </motion.div>

        {/* Main Content */}
        <motion.div variants={itemVariants}>
          <Paper elevation={3} sx={{ borderRadius: 2, overflow: 'hidden' }}>
            {/* Tabs */}
            <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
              <Tabs
                value={tabValue}
                onChange={handleTabChange}
                variant={isMobile ? 'fullWidth' : 'standard'}
                sx={{ px: 2 }}
              >
                <Tab
                  icon={<GenerateIcon />}
                  label="Generate Flashcards"
                  iconPosition="start"
                  {...a11yProps(0)}
                  sx={{ minHeight: 64 }}
                />
                <Tab
                  icon={<ListIcon />}
                  label="My Generated Flashcards"
                  iconPosition="start"
                  {...a11yProps(1)}
                  sx={{ minHeight: 64 }}
                />
              </Tabs>
            </Box>

            {/* Tab Panels */}
            <TabPanel value={tabValue} index={0}>
              <Box sx={{ px: 3 }}>
                <GeminiFlashcardGenerator />
              </Box>
            </TabPanel>

            <TabPanel value={tabValue} index={1}>
              <Box sx={{ px: 3 }}>
                <MyGeneratedFlashcards />
              </Box>
            </TabPanel>
          </Paper>
        </motion.div>


      </motion.div>
    </Container>
  );
};

export default FlashcardGenerationPage;
