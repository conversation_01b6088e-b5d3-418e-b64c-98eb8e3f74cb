import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Container,
  Paper,
  useTheme,
  useMediaQuery,
  Tabs,
  Tab
} from '@mui/material';
import {
  Description as DescriptionIcon,
  AutoAwesome as AutoAwesomeIcon,
  ViewList as ViewListIcon
} from '@mui/icons-material';
import { motion, AnimatePresence } from 'framer-motion';

import { useAuth } from '../../contexts/AuthContext';
import GeminiNoteExplanationGenerator from '../../components/Tools/GeminiNoteExplanationGenerator';
import MyGeneratedNoteExplanations from '../../components/Tools/MyGeneratedNoteExplanations';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`note-explanation-tabpanel-${index}`}
      aria-labelledby={`note-explanation-tab-${index}`}
      {...other}
    >
      {value === index && <Box>{children}</Box>}
    </div>
  );
}

function a11yProps(index: number) {
  return {
    id: `note-explanation-tab-${index}`,
    'aria-controls': `note-explanation-tabpanel-${index}`,
  };
}

const NoteExplanationPage: React.FC = () => {
  const theme = useTheme();
  const { user } = useAuth();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  const [tabValue, setTabValue] = useState(0);

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: { opacity: 1, y: 0 }
  };

  // Listen for tab switching events
  useEffect(() => {
    const handleSwitchToGeneratedExplanationsTab = () => {
      setTabValue(1);
    };

    window.addEventListener('switchToGeneratedExplanationsTab', handleSwitchToGeneratedExplanationsTab);
    return () => {
      window.removeEventListener('switchToGeneratedExplanationsTab', handleSwitchToGeneratedExplanationsTab);
    };
  }, []);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };



  return (
    <Box
      sx={{
        minHeight: '100vh',
        bgcolor: 'background.default',
        py: { xs: 2, sm: 3, md: 4 }
      }}
    >
      <Container maxWidth="xl">
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          {/* Header Section */}
          <motion.div variants={itemVariants}>
            <Box sx={{ mb: 4 }}>


              {/* Page Title */}
              <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, mb: 2 }}>
                <DescriptionIcon 
                  sx={{ 
                    fontSize: { xs: 32, sm: 40 }, 
                    color: 'primary.main' 
                  }} 
                />
                <Box>
                  <Typography 
                    variant={isMobile ? 'h4' : 'h3'} 
                    component="h1" 
                    fontWeight="bold"
                    color="text.primary"
                  >
                    Note Explanation
                  </Typography>
                  <Typography 
                    variant="subtitle1" 
                    color="text.secondary"
                    sx={{ mt: 0.5 }}
                  >
                    Generate comprehensive explanations and summaries from your notes
                  </Typography>
                </Box>
              </Box>
            </Box>
          </motion.div>

          {/* Main Content */}
          <motion.div variants={itemVariants}>
            <Paper elevation={3} sx={{ borderRadius: 2, overflow: 'hidden' }}>
              {/* Tabs */}
              <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
                <Tabs
                  value={tabValue}
                  onChange={handleTabChange}
                  variant={isMobile ? 'fullWidth' : 'standard'}
                  sx={{ px: 2 }}
                >
                  <Tab
                    icon={<AutoAwesomeIcon />}
                    label="Generate Explanation"
                    iconPosition="start"
                    {...a11yProps(0)}
                    sx={{ minHeight: 64 }}
                  />
                  <Tab
                    icon={<ViewListIcon />}
                    label="My Generated Explanations"
                    iconPosition="start"
                    {...a11yProps(1)}
                    sx={{ minHeight: 64 }}
                  />
                </Tabs>
              </Box>

              {/* Tab Content */}
              <Box sx={{ p: { xs: 2, sm: 3, md: 4 } }}>
                <AnimatePresence mode="wait">
                  {tabValue === 0 && (
                    <motion.div
                      key="generate-tab"
                      variants={containerVariants}
                      initial="hidden"
                      animate="visible"
                      exit="hidden"
                    >
                      <TabPanel value={tabValue} index={0}>
                        <GeminiNoteExplanationGenerator />
                      </TabPanel>
                    </motion.div>
                  )}

                  {tabValue === 1 && (
                    <motion.div
                      key="explanations-tab"
                      variants={containerVariants}
                      initial="hidden"
                      animate="visible"
                      exit="hidden"
                    >
                      <TabPanel value={tabValue} index={1}>
                        <MyGeneratedNoteExplanations />
                      </TabPanel>
                    </motion.div>
                  )}
                </AnimatePresence>
              </Box>
            </Paper>
          </motion.div>
        </motion.div>
      </Container>
    </Box>
  );
};

export default NoteExplanationPage;
