import React, { useState } from 'react';
import {
  Box,
  Typography,
  Container,
  Paper,
  useTheme,
  useMediaQuery,
  Tabs,
  Tab
} from '@mui/material';
import {
  FolderOpen as FolderOpenIcon,
  CloudUpload as CloudUploadIcon
} from '@mui/icons-material';
import { motion, AnimatePresence } from 'framer-motion';
import { useNavigate } from 'react-router-dom';

import { useAuth } from '../../contexts/AuthContext';
import GeminiPDFUploadTool from '../../components/Tools/GeminiPDFUploadTool';
import StudentNotesTable from '../../components/Tools/StudentNotesTable';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`notes-tabpanel-${index}`}
      aria-labelledby={`notes-tab-${index}`}
      {...other}
    >
      {value === index && <Box>{children}</Box>}
    </div>
  );
}

const MyNotesPage: React.FC = () => {
  const theme = useTheme();
  const navigate = useNavigate();
  const { user } = useAuth();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const [tabValue, setTabValue] = useState(0);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  if (!user) {
    return (
      <Box sx={{ p: 3, textAlign: 'center' }}>
        <Typography variant="h5">Please log in to view your notes</Typography>
      </Box>
    );
  }

  const containerVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        type: 'spring',
        stiffness: 100,
        damping: 15
      }
    }
  };

  return (
    <Box sx={{ minHeight: '100vh', bgcolor: 'background.default' }}>
      {/* Main Content */}
      <Container maxWidth="lg" sx={{ pt: { xs: 3, md: 4 }, px: { xs: 1, sm: 2, md: 3 } }}>
        <Box sx={{ py: { xs: 1, md: 3 } }}>
          {/* Page Header */}
          <Box sx={{ mb: { xs: 2, md: 4 }, px: { xs: 1, sm: 0 } }}>
            <Typography
              variant={isMobile ? "h5" : "h3"}
              component="h1"
              gutterBottom
              sx={{
                fontWeight: 600,
                color: 'text.primary',
                fontSize: { xs: '1.5rem', sm: '2rem', md: '2.5rem' }
              }}
            >
              My Notes
            </Typography>
            <Typography
              variant="body1"
              color="text.secondary"
              sx={{
                mb: 3,
                fontSize: { xs: '0.9rem', sm: '1rem' }
              }}
            >
              Upload and manage your PDF notes
            </Typography>
          </Box>

          {/* Main Paper Container */}
          <Paper
            elevation={0}
            sx={{
              borderRadius: 3,
              border: `1px solid ${theme.palette.divider}`,
              overflow: 'hidden',
              bgcolor: 'background.paper'
            }}
          >
            {/* Tabs */}
            <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
              <Tabs
                value={tabValue}
                onChange={handleTabChange}
                aria-label="notes tabs"
                variant={isMobile ? "fullWidth" : "standard"}
                sx={{
                  px: { xs: 2, sm: 3, md: 4 },
                  '& .MuiTab-root': {
                    textTransform: 'none',
                    fontWeight: 600,
                    fontSize: { xs: '0.875rem', sm: '1rem' }
                  }
                }}
              >
                <Tab
                  icon={<CloudUploadIcon />}
                  iconPosition="start"
                  label="Upload Notes"
                  id="notes-tab-0"
                  aria-controls="notes-tabpanel-0"
                />
                <Tab
                  icon={<FolderOpenIcon />}
                  iconPosition="start"
                  label="My Uploaded Notes"
                  id="notes-tab-1"
                  aria-controls="notes-tabpanel-1"
                />
              </Tabs>
            </Box>

            {/* Tab Content */}
            <Box sx={{ p: { xs: 2, sm: 3, md: 4 } }}>
              <AnimatePresence mode="wait">
                <TabPanel value={tabValue} index={0}>
                  <motion.div
                    key="upload-tab"
                    variants={containerVariants}
                    initial="hidden"
                    animate="visible"
                    exit="hidden"
                  >
                    <GeminiPDFUploadTool />
                  </motion.div>
                </TabPanel>

                <TabPanel value={tabValue} index={1}>
                  <motion.div
                    key="notes-tab"
                    variants={containerVariants}
                    initial="hidden"
                    animate="visible"
                    exit="hidden"
                  >
                    <StudentNotesTable />
                  </motion.div>
                </TabPanel>
              </AnimatePresence>
            </Box>
          </Paper>
        </Box>
      </Container>
    </Box>
  );
};

export default MyNotesPage;
