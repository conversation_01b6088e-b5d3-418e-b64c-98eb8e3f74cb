import React, { useState } from 'react';
import {
  Box,
  Typography,
  Container,
  Paper,
  useTheme,
  useMediaQuery,
  Tabs,
  Tab
} from '@mui/material';
import {
  CloudUpload as CloudUploadIcon,
  FolderOpen as FolderOpenIcon
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { motion, AnimatePresence } from 'framer-motion';

import { useAuth } from '../../contexts/AuthContext';
import GeminiPDFUploadTool from '../../components/Tools/GeminiPDFUploadTool';
import StudentNotesTable from '../../components/Tools/StudentNotesTable';

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const TabPanel: React.FC<TabPanelProps> = ({ children, value, index }) => {
  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`upload-tabpanel-${index}`}
      aria-labelledby={`upload-tab-${index}`}
    >
      {value === index && (
        <Box sx={{ py: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
};

const PDFUploadPage: React.FC = () => {
  const theme = useTheme();
  const navigate = useNavigate();
  const { user } = useAuth();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const [tabValue, setTabValue] = useState(0);

  // Listen for tab switching events from upload completion
  React.useEffect(() => {
    const handleTabSwitch = () => {
      setTabValue(1); // Switch to "My Notes" tab
    };

    window.addEventListener('switchToNotesTab', handleTabSwitch);
    return () => {
      window.removeEventListener('switchToNotesTab', handleTabSwitch);
    };
  }, []);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  if (!user) {
    return (
      <Box sx={{ p: 3, textAlign: 'center' }}>
        <Typography variant="h5">Please log in to upload files</Typography>
      </Box>
    );
  }

  const containerVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        type: 'spring',
        stiffness: 100,
        damping: 15
      }
    }
  };

  return (
    <Box sx={{ minHeight: '100vh', bgcolor: 'background.default' }}>
      {/* Main Content */}
      <Container maxWidth="lg" sx={{ px: { xs: 1, sm: 2, md: 3 } }}>
        <Box sx={{ py: { xs: 1, md: 3 } }}>
          {/* Page Header */}
          <Box sx={{ mb: { xs: 2, md: 4 }, px: { xs: 1, sm: 0 } }}>
            <Typography
              variant={isMobile ? "h5" : "h3"}
              component="h1"
              gutterBottom
              sx={{
                fontWeight: 600,
                color: 'text.primary',
                fontSize: { xs: '1.5rem', sm: '2rem', md: '2.5rem' }
              }}
            >
              PDF Upload & Management
            </Typography>
            <Typography
              variant="body1"
              color="text.secondary"
              sx={{
                mb: 3,
                fontSize: { xs: '0.9rem', sm: '1rem' }
              }}
            >
              Upload PDF files and manage your uploaded notes
            </Typography>
          </Box>

          {/* Main Content */}
          <Paper
            elevation={0}
            sx={{
              borderRadius: { xs: 1, sm: 2 },
              border: 1,
              borderColor: 'divider',
              overflow: 'hidden',
              mx: { xs: 0, sm: 0 }
            }}
          >
            {/* Tabs */}
            <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
              <Tabs
                value={tabValue}
                onChange={handleTabChange}
                aria-label="upload tabs"
                variant={isMobile ? "fullWidth" : "standard"}
                sx={{
                  px: { xs: 2, sm: 3, md: 4 },
                  '& .MuiTab-root': {
                    textTransform: 'none',
                    fontWeight: 600,
                    fontSize: { xs: '0.875rem', sm: '1rem' }
                  }
                }}
              >
                <Tab
                  icon={<CloudUploadIcon />}
                  iconPosition="start"
                  label="Upload New PDF"
                  id="upload-tab-0"
                  aria-controls="upload-tabpanel-0"
                />
                <Tab
                  icon={<FolderOpenIcon />}
                  iconPosition="start"
                  label="My Uploaded Notes"
                  id="upload-tab-1"
                  aria-controls="upload-tabpanel-1"
                />
              </Tabs>
            </Box>

            {/* Tab Content */}
            <Box sx={{ p: { xs: 2, sm: 3, md: 4 } }}>
              <AnimatePresence mode="wait">
                <TabPanel value={tabValue} index={0}>
                  <motion.div
                    key="upload-tab"
                    variants={containerVariants}
                    initial="hidden"
                    animate="visible"
                    exit="hidden"
                  >
                    <Typography variant="h6" gutterBottom>
                      Upload PDF Files
                    </Typography>
                    <Typography variant="body2" color="text.secondary" paragraph>
                      Upload PDF files and generate MCQs, flashcards, and summaries using Google's Gemini AI
                    </Typography>
                    <GeminiPDFUploadTool />
                  </motion.div>
                </TabPanel>

                <TabPanel value={tabValue} index={1}>
                  <motion.div
                    key="notes-tab"
                    variants={containerVariants}
                    initial="hidden"
                    animate="visible"
                    exit="hidden"
                  >
                    <Typography variant="h6" gutterBottom>
                      Your Uploaded Notes
                    </Typography>
                    <Typography variant="body2" color="text.secondary" paragraph>
                      View and manage your previously uploaded PDF files and their processing status.
                    </Typography>
                    <StudentNotesTable />
                  </motion.div>
                </TabPanel>
              </AnimatePresence>
            </Box>
          </Paper>
        </Box>
      </Container>
    </Box>
  );
};

export default PDFUploadPage;
