import React, { ErrorInfo, ReactNode } from 'react';
import {
  Box,
  Typography,
  Container,
  Grid,
  Paper,
  Tabs,
  Tab,
  useTheme,
  Alert,
  Button,
} from '@mui/material';
import {
  EmojiEvents as TrophyIcon,
  Leaderboard as LeaderboardIcon,
  Stars as StarsIcon,
  Refresh as RefreshIcon,
} from '@mui/icons-material';
import GamificationProfile from '../components/Gamification/GamificationProfile';
import Leaderboard from '../components/Gamification/Leaderboard';
import { useAuth } from '../contexts/AuthContext';

// Error boundary component to catch errors in child components
class ErrorBoundary extends React.Component<
  { children: ReactNode, fallback?: ReactNode },
  { hasError: boolean, error: Error | null }
> {
  constructor(props: { children: ReactNode, fallback?: ReactNode }) {
    super(props);
    this.state = { hasError: false, error: null };
  }

  static getDerivedStateFromError(error: Error) {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error("Error caught by ErrorBoundary:", error, errorInfo);
  }

  render() {
    if (this.state.hasError) {
      return this.props.fallback || (
        <Alert
          severity="error"
          action={
            <Button
              color="inherit"
              size="small"
              onClick={() => this.setState({ hasError: false, error: null })}
              startIcon={<RefreshIcon />}
            >
              Retry
            </Button>
          }
        >
          Something went wrong: {this.state.error?.message || 'Unknown error'}
        </Alert>
      );
    }

    return this.props.children;
  }
}

const GamificationPage: React.FC = () => {
  const theme = useTheme();
  const { user } = useAuth();
  const [tabValue, setTabValue] = React.useState(0);

  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  return (
    <Container maxWidth="lg" sx={{ mt: 4, mb: 4 }}>
      <Box sx={{ animation: 'fadeIn 0.5s ease-in-out' }}>
        <Box sx={{ mb: 4 }}>
          <Typography variant="h4" component="h1" gutterBottom fontWeight="bold">
            Achievements
          </Typography>
          <Typography variant="body1" color="text.secondary">
            Track your progress, earn badges, and compete with other students.
          </Typography>
        </Box>

        <Paper
          sx={{
            p: 0,
            mb: 4,
            borderRadius: 2,
            overflow: 'hidden',
            animation: 'slideUp 0.5s ease-in-out',
          }}
        >
          <Tabs
            value={tabValue}
            onChange={handleTabChange}
            variant="fullWidth"
            sx={{ borderBottom: 1, borderColor: 'divider' }}
          >
            <Tab icon={<StarsIcon />} label="My Achievements" iconPosition="start" />
            <Tab icon={<LeaderboardIcon />} label="Leaderboard" iconPosition="start" />
          </Tabs>

          <Box sx={{ p: 3 }}>
            {tabValue === 0 && (
              <ErrorBoundary>
                <GamificationProfile userId={user?.id} />
              </ErrorBoundary>
            )}
            {tabValue === 1 && (
              <ErrorBoundary>
                <Leaderboard limit={10} />
              </ErrorBoundary>
            )}
          </Box>
        </Paper>
      </Box>
    </Container>
  );
};

export default GamificationPage;
