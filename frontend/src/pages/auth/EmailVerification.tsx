import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Button,
  Paper,
  Alert,
  CircularProgress,
  Container,
  Stack,
  useTheme,
  alpha,
} from '@mui/material';
import {
  Email as EmailIcon,
  CheckCircle as CheckCircleIcon,
  Error as <PERSON>rrorIcon,
  Refresh as RefreshIcon,
} from '@mui/icons-material';
import { useSearchParams, useNavigate, Link as RouterLink } from 'react-router-dom';
import { motion } from 'framer-motion';
import { verifyEmail, resendVerificationEmail } from '../../api/auth';
import { useEmailResendCountdown } from '../../hooks/useEmailResendCountdown';

const MotionBox = motion(Box);
const MotionPaper = motion(Paper);

const EmailVerification: React.FC = () => {
  const [searchParams] = useSearchParams();
  const navigate = useNavigate();
  const theme = useTheme();

  const [loading, setLoading] = useState(false);
  const [verified, setVerified] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [resendLoading, setResendLoading] = useState(false);
  const [resendMessage, setResendMessage] = useState<string | null>(null);

  const token = searchParams.get('token');
  const email = searchParams.get('email');

  // Use countdown hook for resend functionality
  const { canResend, remainingTime, startCountdown } = useEmailResendCountdown({
    email: email || undefined,
    cooldownDuration: 60 // 60 seconds cooldown
  });

  useEffect(() => {
    if (token) {
      handleVerification();
    }
  }, [token]);

  const handleVerification = async () => {
    if (!token) return;
    
    setLoading(true);
    setError(null);
    
    try {
      const response = await verifyEmail(token);
      setVerified(true);
      
      // Redirect to login after 3 seconds
      setTimeout(() => {
        navigate('/login', {
          state: { message: 'Email verified successfully! You can now log in.' }
        });
      }, 3000);
    } catch (err: any) {
      console.error('Email verification error:', err);
      setError(err.response?.data?.detail || 'Failed to verify email. The link may be invalid or expired.');
    } finally {
      setLoading(false);
    }
  };

  const handleResendVerification = async () => {
    if (!email) {
      setError('Email address is required to resend verification.');
      return;
    }

    if (!canResend) {
      return; // Don't proceed if still in cooldown
    }

    setResendLoading(true);
    setResendMessage(null);
    setError(null);

    try {
      const response = await resendVerificationEmail(email);
      setResendMessage(response.message);
      // Start countdown after successful email send
      startCountdown();
    } catch (err: any) {
      console.error('Resend verification error:', err);
      setError(err.response?.data?.detail || 'Failed to resend verification email.');
    } finally {
      setResendLoading(false);
    }
  };

  return (
    <Box
      sx={{
        minHeight: '100vh',
        background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.1)} 0%, ${alpha(theme.palette.secondary.main, 0.1)} 100%)`,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        p: 3,
      }}
    >
      <Container maxWidth="sm">
        <MotionPaper
          initial={{ opacity: 0, y: 50 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
          elevation={8}
          sx={{
            p: 4,
            borderRadius: 3,
            textAlign: 'center',
          }}
        >
          <Stack spacing={3} alignItems="center">
            {/* Icon */}
            <MotionBox
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ delay: 0.2, type: 'spring', stiffness: 200 }}
            >
              {loading ? (
                <CircularProgress size={60} />
              ) : verified ? (
                <CheckCircleIcon sx={{ fontSize: 60, color: 'success.main' }} />
              ) : error ? (
                <ErrorIcon sx={{ fontSize: 60, color: 'error.main' }} />
              ) : (
                <EmailIcon sx={{ fontSize: 60, color: 'primary.main' }} />
              )}
            </MotionBox>

            {/* Title */}
            <Typography variant="h4" component="h1" fontWeight="bold">
              {loading ? 'Verifying Email...' : 
               verified ? 'Email Verified!' : 
               error ? 'Verification Failed' : 
               'Email Verification'}
            </Typography>

            {/* Content */}
            {loading && (
              <Typography variant="body1" color="text.secondary">
                Please wait while we verify your email address...
              </Typography>
            )}

            {verified && (
              <Stack spacing={2}>
                <Typography variant="body1" color="success.main" fontWeight={500}>
                  Your email has been successfully verified!
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  You will be redirected to the login page in a few seconds...
                </Typography>
              </Stack>
            )}

            {error && !token && (
              <Stack spacing={2}>
                <Typography variant="body1" color="text.secondary">
                  Please check your email for the verification link, or request a new one below.
                </Typography>
                {email && (
                  <Typography variant="body2" color="text.secondary">
                    Verification email will be sent to: <strong>{email}</strong>
                  </Typography>
                )}
              </Stack>
            )}

            {error && token && (
              <Typography variant="body1" color="error.main">
                {error}
              </Typography>
            )}

            {/* Messages */}
            {resendMessage && (
              <Alert
                severity="success"
                sx={{
                  width: '100%',
                  maxWidth: '100%',
                  minWidth: 0,
                  wordBreak: 'break-word',
                  whiteSpace: 'pre-wrap',
                  overflow: 'hidden',
                  '& .MuiAlert-message': {
                    width: '100%',
                    overflow: 'hidden',
                    textOverflow: 'ellipsis'
                  }
                }}
              >
                {resendMessage}
              </Alert>
            )}

            {/* Actions */}
            <Stack direction="row" spacing={2} sx={{ mt: 3 }}>
              {!verified && !loading && (
                <>
                  {email && (
                    <Button
                      variant="contained"
                      startIcon={resendLoading ? <CircularProgress size={20} /> : <RefreshIcon />}
                      onClick={handleResendVerification}
                      disabled={resendLoading || !canResend}
                    >
                      {resendLoading
                        ? 'Sending...'
                        : !canResend
                          ? `Resend Email (${remainingTime}s)`
                          : 'Resend Email'
                      }
                    </Button>
                  )}
                  
                  <Button
                    variant="outlined"
                    component={RouterLink}
                    to="/login"
                  >
                    Back to Login
                  </Button>
                </>
              )}

              {verified && (
                <Button
                  variant="contained"
                  component={RouterLink}
                  to="/login"
                  size="large"
                >
                  Continue to Login
                </Button>
              )}
            </Stack>

            {/* Help text */}
            <Typography variant="body2" color="text.secondary" sx={{ mt: 2 }}>
              Need help? Contact us at{' '}
              <Typography component="span" color="primary.main" fontWeight={500}>
                <EMAIL>
              </Typography>
            </Typography>
          </Stack>
        </MotionPaper>
      </Container>
    </Box>
  );
};

export default EmailVerification;
