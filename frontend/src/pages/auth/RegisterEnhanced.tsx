import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  TextField,
  Button,
  Link,
  Alert,
  CircularProgress,
  Card,
  CardContent,
  InputAdornment,
  IconButton,
  Fade,
  useTheme,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormHelperText,
  Container,
  Paper,
  FormControlLabel,
  Checkbox
} from '@mui/material';
import {
  Person as PersonIcon,
  Email as EmailIcon,
  Lock as LockIcon,
  Visibility as VisibilityIcon,
  VisibilityOff as VisibilityOffIcon,
  School as SchoolIcon,
  Badge as BadgeIcon
} from '@mui/icons-material';
import { Link as RouterLink, useNavigate, useSearchParams } from 'react-router-dom';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import { register } from '../../api/auth';
import { motion } from 'framer-motion';
import AnimatedBackground from '../../components/AnimatedBackground';
import Logo from '../../components/Logo';

// Create motion components
const MotionBox = motion(Box);
const MotionGrid = motion(Grid);

const RegisterEnhanced: React.FC = () => {
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const theme = useTheme();
  const [error, setError] = useState<string | null>(null);
  const [loading, setLoading] = useState(false);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  // No longer need schools state
  const [registrationComplete, setRegistrationComplete] = useState(false);

  // Get invitation token from URL parameters
  const invitationToken = searchParams.get('invitation_token');

  // Validation schema
  const validationSchema = Yup.object({
    full_name: Yup.string().required('Full name is required'),
    email: Yup.string().email('Invalid email address').required('Email is required'),
    password: Yup.string()
      .required('Password is required')
      .min(8, 'Password must be at least 8 characters'),
    confirmPassword: Yup.string()
      .oneOf([Yup.ref('password')], 'Passwords must match')
      .required('Confirm password is required'),
    role: Yup.string().required('Role is required').oneOf(['student', 'tutor'], 'Invalid role'),
    terms_accepted: Yup.boolean()
      .oneOf([true], 'You must accept the terms and conditions')
      .required('You must accept the terms and conditions'),
  });

  // Create formik instance
  const formik = useFormik({
    initialValues: {
      full_name: '',
      email: '',
      password: '',
      confirmPassword: '',
      role: 'student',
      terms_accepted: false,
    },
    validationSchema: validationSchema,
    onSubmit: async (values) => {
      setLoading(true);
      setError(null);

      try {
        const { confirmPassword, ...registerData } = values;

        console.log('Submitting registration data:', registerData);

        const result = await register(registerData);

        console.log('Registration successful:', result);

        // If there's an invitation token, redirect to accept share page after email verification
        if (invitationToken) {
          navigate('/verify-email-pending', {
            state: {
              email: registerData.email,
              message: 'Registration successful! Please check your email to verify your account, then you can access the shared content.',
              redirectAfterVerification: `/share/accept?token=${invitationToken}`
            }
          });
        } else {
          // Normal registration flow
          navigate('/verify-email-pending', {
            state: {
              email: registerData.email,
              message: 'Registration successful! Please check your email to verify your account.'
            }
          });
        }
      } catch (err: any) {
        console.error('Registration error:', err);

        // More detailed error handling
        if (err.response) {
          console.error('Error response:', err.response.data);
          if (err.response.data.detail) {
            setError(err.response.data.detail);
          } else if (typeof err.response.data === 'object') {
            // Handle validation errors
            const errorMessages = Object.entries(err.response.data)
              .map(([key, value]) => `${key}: ${value}`)
              .join(', ');
            setError(`Validation error: ${errorMessages}`);
          } else {
            setError(`Error: ${err.response.status} ${err.response.statusText}`);
          }
        } else if (err.request) {
          setError('No response received from server. Please check your connection.');
        } else {
          setError(err.message || 'Failed to register. Please try again.');
        }
      } finally {
        setLoading(false);
      }
    },
  });

  // No longer need to fetch schools



  const handleTogglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  const handleToggleConfirmPasswordVisibility = () => {
    setShowConfirmPassword(!showConfirmPassword);
  };

  return (
    <AnimatedBackground>
      <Container maxWidth="md">
        <Paper
          elevation={3}
          sx={{
            borderRadius: 3,
            overflow: 'hidden',
            backgroundColor: theme.palette.background.paper,
            mt: 4,
            mb: 4
          }}
        >
          <Card
            sx={{
              display: 'flex',
              flexDirection: 'column',
              justifyContent: 'center',
              boxShadow: 'none',
              border: 'none',
            }}
          >
            <CardContent sx={{ p: { xs: 3, sm: 4 } }}>
              <MotionBox
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5 }}
                sx={{ textAlign: 'center', mb: 4 }}
              >
                <Logo size={70} sx={{ mb: 2 }} />
                <Typography variant="h4" component="h1" fontWeight="bold" gutterBottom>
                  Create Your Account
                </Typography>
                <Typography variant="body1" color="text.secondary">
                  {invitationToken ? 'Complete your registration to access shared content' : 'Join CampusPQ and start learning today'}
                </Typography>
              </MotionBox>

              {/* Invitation notice */}
              {invitationToken && (
                <Alert
                  severity="info"
                  sx={{ mb: 3, borderRadius: 2 }}
                >
                  You've been invited to view shared content on CampusPQ. Complete your registration to access it!
                </Alert>
              )}

            {/* Error message */}
            {error && (
              <Fade in={true} timeout={1000}>
                <Alert
                  severity="error"
                  sx={{
                    mb: 3,
                    borderRadius: 2,
                    width: '100%',
                    maxWidth: '100%',
                    minWidth: 0,
                    wordBreak: 'break-word',
                    whiteSpace: 'pre-wrap',
                    overflow: 'hidden',
                    '& .MuiAlert-message': {
                      width: '100%',
                      overflow: 'hidden',
                      textOverflow: 'ellipsis'
                    }
                  }}
                >
                  {error}
                </Alert>
              </Fade>
            )}

            {/* Registration complete message */}
            {registrationComplete ? (
              <MotionBox
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.5 }}
                sx={{ textAlign: 'center', py: 4 }}
              >
                <Box
                  sx={{
                    width: 80,
                    height: 80,
                    borderRadius: '50%',
                    bgcolor: 'success.main',
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'center',
                    mx: 'auto',
                    mb: 3
                  }}
                >
                  <Logo size={50} sx={{ filter: 'brightness(0) invert(1)' }} />
                </Box>
                <Typography variant="h5" gutterBottom>
                  Registration Successful!
                </Typography>
                <Typography variant="body1" color="text.secondary" paragraph>
                  Your account has been created successfully. After logging in, you'll need to complete your profile.
                </Typography>

                <Button
                  variant="contained"
                  color="primary"
                  size="large"
                  onClick={() => navigate('/login', {
                    state: { message: 'Registration successful! Please login with your new account to complete your profile.' }
                  })}
                  sx={{
                    mt: 3,
                    borderRadius: 2,
                    transition: 'all 0.3s ease',
                    '&:hover': {
                      transform: 'translateY(-2px)',
                    }
                  }}
                >
                  Go to Login
                </Button>
              </MotionBox>
            ) : (
              <form onSubmit={formik.handleSubmit}>
                <Grid container spacing={3}>
                  {/* Full Name */}
                  <MotionGrid
                    item
                    xs={12}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5, delay: 0.1 }}
                  >
                    <TextField
                      fullWidth
                      id="full_name"
                      name="full_name"
                      label="Full Name"
                      variant="outlined"
                      value={formik.values.full_name}
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                      error={formik.touched.full_name && Boolean(formik.errors.full_name)}
                      helperText={formik.touched.full_name && formik.errors.full_name}
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <PersonIcon color="primary" />
                          </InputAdornment>
                        ),
                      }}
                      sx={{
                        '& .MuiOutlinedInput-root': {
                          borderRadius: 2,
                          transition: 'all 0.3s ease-in-out',
                          '&:hover': {
                            transform: 'translateY(-2px)',
                          },
                          '&.Mui-focused': {
                            transform: 'translateY(-2px)',
                          }
                        }
                      }}
                    />
                  </MotionGrid>

                  {/* Email */}
                  <MotionGrid
                    item
                    xs={12}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5, delay: 0.2 }}
                  >
                    <TextField
                      fullWidth
                      id="email"
                      name="email"
                      label="Email"
                      variant="outlined"
                      value={formik.values.email}
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                      error={formik.touched.email && Boolean(formik.errors.email)}
                      helperText={formik.touched.email && formik.errors.email}
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <EmailIcon color="primary" />
                          </InputAdornment>
                        ),
                      }}
                      sx={{
                        '& .MuiOutlinedInput-root': {
                          borderRadius: 2,
                          transition: 'all 0.3s ease-in-out',
                          '&:hover': {
                            transform: 'translateY(-2px)',
                          },
                          '&.Mui-focused': {
                            transform: 'translateY(-2px)',
                          }
                        }
                      }}
                    />
                  </MotionGrid>

                  {/* Email field takes full width now */}

                  {/* Role and School Selection Row */}
                  <Grid item xs={12}>
                    <Grid container spacing={3}>
                      {/* Account Type Section */}
                      <MotionGrid
                        item
                        xs={12}
                        initial={{ opacity: 0, y: 20 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.5, delay: 0.4 }}
                      >
                        <FormControl
                          fullWidth
                          error={formik.touched.role && Boolean(formik.errors.role)}
                        >
                          <InputLabel id="role-label">I am a</InputLabel>
                          <Select
                            labelId="role-label"
                            id="role"
                            name="role"
                            value={formik.values.role}
                            label="I am a"
                            onChange={formik.handleChange}
                            onBlur={formik.handleBlur}
                            startAdornment={
                              <InputAdornment position="start">
                                <BadgeIcon color="primary" />
                              </InputAdornment>
                            }
                            sx={{
                              borderRadius: 2,
                              transition: 'all 0.3s ease-in-out',
                              '&:hover': {
                                transform: 'translateY(-2px)',
                              }
                            }}
                          >
                            <MenuItem value="student">Student</MenuItem>
                            <MenuItem value="tutor">Tutor</MenuItem>
                          </Select>
                          {formik.touched.role && formik.errors.role && (
                            <FormHelperText>{formik.errors.role}</FormHelperText>
                          )}
                        </FormControl>
                      </MotionGrid>

                      {/* No second grid item needed */}
                    </Grid>
                  </Grid>

                  {/* Password */}
                  <MotionGrid
                    item
                    xs={12}
                    sm={6}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5, delay: 0.6 }}
                  >
                    <TextField
                      fullWidth
                      id="password"
                      name="password"
                      label="Password"
                      type={showPassword ? 'text' : 'password'}
                      variant="outlined"
                      value={formik.values.password}
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                      error={formik.touched.password && Boolean(formik.errors.password)}
                      helperText={formik.touched.password && formik.errors.password}
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <LockIcon color="primary" />
                          </InputAdornment>
                        ),
                        endAdornment: (
                          <InputAdornment position="end">
                            <IconButton
                              aria-label="toggle password visibility"
                              onClick={handleTogglePasswordVisibility}
                              edge="end"
                            >
                              {showPassword ? <VisibilityOffIcon /> : <VisibilityIcon />}
                            </IconButton>
                          </InputAdornment>
                        ),
                      }}
                      sx={{
                        '& .MuiOutlinedInput-root': {
                          borderRadius: 2,
                          transition: 'all 0.3s ease-in-out',
                          '&:hover': {
                            transform: 'translateY(-2px)',
                          },
                          '&.Mui-focused': {
                            transform: 'translateY(-2px)',
                          }
                        }
                      }}
                    />
                  </MotionGrid>

                  {/* Confirm Password */}
                  <MotionGrid
                    item
                    xs={12}
                    sm={6}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5, delay: 0.7 }}
                  >
                    <TextField
                      fullWidth
                      id="confirmPassword"
                      name="confirmPassword"
                      label="Confirm Password"
                      type={showConfirmPassword ? 'text' : 'password'}
                      variant="outlined"
                      value={formik.values.confirmPassword}
                      onChange={formik.handleChange}
                      onBlur={formik.handleBlur}
                      error={formik.touched.confirmPassword && Boolean(formik.errors.confirmPassword)}
                      helperText={formik.touched.confirmPassword && formik.errors.confirmPassword}
                      InputProps={{
                        startAdornment: (
                          <InputAdornment position="start">
                            <LockIcon color="primary" />
                          </InputAdornment>
                        ),
                        endAdornment: (
                          <InputAdornment position="end">
                            <IconButton
                              aria-label="toggle confirm password visibility"
                              onClick={handleToggleConfirmPasswordVisibility}
                              edge="end"
                            >
                              {showConfirmPassword ? <VisibilityOffIcon /> : <VisibilityIcon />}
                            </IconButton>
                          </InputAdornment>
                        ),
                      }}
                      sx={{
                        '& .MuiOutlinedInput-root': {
                          borderRadius: 2,
                          transition: 'all 0.3s ease-in-out',
                          '&:hover': {
                            transform: 'translateY(-2px)',
                          },
                          '&.Mui-focused': {
                            transform: 'translateY(-2px)',
                          }
                        }
                      }}
                    />
                  </MotionGrid>
                </Grid>

                {/* Terms and Conditions Checkbox */}
                <Grid item xs={12} sx={{ mt: 2 }}>
                  <MotionBox
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5, delay: 0.7 }}
                  >
                    <FormControlLabel
                      control={
                        <Checkbox
                          name="terms_accepted"
                          checked={formik.values.terms_accepted}
                          onChange={formik.handleChange}
                          onBlur={formik.handleBlur}
                          color="primary"
                        />
                      }
                      label={
                        <Typography variant="body2" color="text.secondary">
                          I agree to the{' '}
                          <Link
                            component={RouterLink}
                            to="/terms"
                            target="_blank"
                            rel="noopener noreferrer"
                            sx={{
                              color: 'primary.main',
                              textDecoration: 'none',
                              fontWeight: 'medium',
                              '&:hover': {
                                textDecoration: 'underline'
                              }
                            }}
                          >
                            Terms and Conditions
                          </Link>
                          {' '}and{' '}
                          <Link
                            component={RouterLink}
                            to="/privacy"
                            target="_blank"
                            rel="noopener noreferrer"
                            sx={{
                              color: 'primary.main',
                              textDecoration: 'none',
                              fontWeight: 'medium',
                              '&:hover': {
                                textDecoration: 'underline'
                              }
                            }}
                          >
                            Privacy Policy
                          </Link>
                        </Typography>
                      }
                      sx={{ alignItems: 'flex-start', mt: 1 }}
                    />
                    {formik.touched.terms_accepted && formik.errors.terms_accepted && (
                      <Typography variant="caption" color="error" sx={{ ml: 4, display: 'block' }}>
                        {formik.errors.terms_accepted}
                      </Typography>
                    )}
                  </MotionBox>
                </Grid>

                {/* Submit Button and Login Link */}
                <Grid container spacing={2} sx={{ mt: 2 }}>
                  <Grid item xs={12}>
                    <MotionBox
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      transition={{ duration: 0.5, delay: 0.8 }}
                    >
                      <Button
                        fullWidth
                        type="submit"
                        variant="contained"
                        color="primary"
                        size="large"
                        disabled={loading}
                        sx={{
                          py: 1.5,
                          borderRadius: 2,
                          boxShadow: '0 4px 14px 0 rgba(0,118,255,0.39)',
                          transition: 'all 0.3s ease',
                          '&:hover': {
                            transform: 'translateY(-2px)',
                            boxShadow: '0 6px 20px rgba(0,118,255,0.39)'
                          }
                        }}
                      >
                        {loading ? (
                          <CircularProgress size={24} color="inherit" />
                        ) : (
                          'Create Account'
                        )}
                      </Button>
                    </MotionBox>
                  </Grid>

                  <Grid item xs={12}>
                    <MotionBox
                      initial={{ opacity: 0 }}
                      animate={{ opacity: 1 }}
                      transition={{ duration: 0.5, delay: 0.9 }}
                      sx={{ textAlign: 'center' }}
                    >
                      <Typography variant="body2" color="text.secondary">
                        Already have an account?{' '}
                        <Link
                          component={RouterLink}
                          to="/login"
                          sx={{
                            fontWeight: 'bold',
                            color: 'primary.main',
                            textDecoration: 'none',
                            transition: 'all 0.2s',
                            '&:hover': {
                              color: 'secondary.main',
                              textDecoration: 'underline'
                            }
                          }}
                        >
                          Sign in
                        </Link>
                      </Typography>
                    </MotionBox>
                  </Grid>
                </Grid>
              </form>
            )}
          </CardContent>
        </Card>
      </Paper>
    </Container>
  </AnimatedBackground>
  );
};

export default RegisterEnhanced;
