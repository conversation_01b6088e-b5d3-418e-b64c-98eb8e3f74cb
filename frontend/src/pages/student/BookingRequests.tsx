import React, { useState } from 'react';
import {
  Box,
  Card,
  CardContent,
  <PERSON><PERSON>graphy,
  Chip,
  Button,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  CircularProgress,
  Avatar,
  Divider,
  useTheme,
  useMediaQuery
} from '@mui/material';
import {
  Schedule as ScheduleIcon,
  Person as PersonIcon,
  School as SchoolIcon,
  AccessTime as TimeIcon,
  Message as MessageIcon,
  Chat as ChatIcon
} from '@mui/icons-material';
import { useQuery } from '@tanstack/react-query';
import { format } from 'date-fns';
import { useNavigate } from 'react-router-dom';
import { getBookingRequests, BookingRequest, BookingRequestStatus } from '../../api/bookingRequests';

const BookingRequests: React.FC = () => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const navigate = useNavigate();
  const [statusFilter, setStatusFilter] = useState<BookingRequestStatus | ''>('');

  const {
    data: requests = [],
    isLoading,
    error
  } = useQuery<BookingRequest[]>({
    queryKey: ['booking-requests', statusFilter],
    queryFn: () => getBookingRequests(0, 100, statusFilter || undefined)
  });

  const getStatusColor = (status: BookingRequestStatus) => {
    switch (status) {
      case BookingRequestStatus.PENDING:
        return 'warning';
      case BookingRequestStatus.ACCEPTED:
        return 'success';
      case BookingRequestStatus.DECLINED:
        return 'error';
      case BookingRequestStatus.CANCELLED:
        return 'default';
      default:
        return 'default';
    }
  };

  const getStatusText = (status: BookingRequestStatus) => {
    switch (status) {
      case BookingRequestStatus.PENDING:
        return 'Pending';
      case BookingRequestStatus.ACCEPTED:
        return 'Accepted';
      case BookingRequestStatus.DECLINED:
        return 'Declined';
      case BookingRequestStatus.CANCELLED:
        return 'Cancelled';
      default:
        return status;
    }
  };

  if (error) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="error">
          Failed to load booking requests. Please try again later.
        </Alert>
      </Box>
    );
  }

  return (
    <Box sx={{ p: { xs: 2, md: 3 } }}>
      <Typography variant="h4" component="h1" gutterBottom>
        My Booking Requests
      </Typography>
      <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
        Track your tutoring session requests and responses
      </Typography>

      {/* Filter */}
      <Box sx={{ mb: 3 }}>
        <FormControl size="small" sx={{ minWidth: 200 }}>
          <InputLabel>Filter by Status</InputLabel>
          <Select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value as BookingRequestStatus | '')}
            label="Filter by Status"
          >
            <MenuItem value="">All Requests</MenuItem>
            <MenuItem value={BookingRequestStatus.PENDING}>Pending</MenuItem>
            <MenuItem value={BookingRequestStatus.ACCEPTED}>Accepted</MenuItem>
            <MenuItem value={BookingRequestStatus.DECLINED}>Declined</MenuItem>
            <MenuItem value={BookingRequestStatus.CANCELLED}>Cancelled</MenuItem>
          </Select>
        </FormControl>
      </Box>

      {/* Loading State */}
      {isLoading && (
        <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
          <CircularProgress />
        </Box>
      )}

      {/* Requests List */}
      {!isLoading && (
        <Grid container spacing={3}>
          {requests.map((request) => (
            <Grid item xs={12} key={request.id}>
              <Card>
                <CardContent>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                      <Avatar>
                        {request.tutor_name?.charAt(0) || 'T'}
                      </Avatar>
                      <Box>
                        <Typography variant="h6" component="h3">
                          {request.subject}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          Tutor: {request.tutor_name}
                        </Typography>
                      </Box>
                    </Box>
                    <Chip
                      label={getStatusText(request.status)}
                      color={getStatusColor(request.status) as any}
                      size="small"
                    />
                  </Box>

                  <Grid container spacing={2} sx={{ mb: 2 }}>
                    <Grid item xs={12} sm={6} md={3}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <ScheduleIcon fontSize="small" color="action" />
                        <Box>
                          <Typography variant="body2" color="text.secondary">
                            Preferred Date
                          </Typography>
                          <Typography variant="body2">
                            {format(new Date(request.preferred_date), 'MMM dd, yyyy')}
                          </Typography>
                          <Typography variant="body2">
                            {format(new Date(request.preferred_date), 'h:mm a')}
                          </Typography>
                        </Box>
                      </Box>
                    </Grid>

                    <Grid item xs={12} sm={6} md={3}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <TimeIcon fontSize="small" color="action" />
                        <Box>
                          <Typography variant="body2" color="text.secondary">
                            Duration
                          </Typography>
                          <Typography variant="body2">
                            {request.duration_hours} hour{request.duration_hours > 1 ? 's' : ''}
                          </Typography>
                        </Box>
                      </Box>
                    </Grid>

                    <Grid item xs={12} sm={6} md={3}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <PersonIcon fontSize="small" color="action" />
                        <Box>
                          <Typography variant="body2" color="text.secondary">
                            Session Type
                          </Typography>
                          <Typography variant="body2" sx={{ textTransform: 'capitalize' }}>
                            {request.session_type.replace('_', ' ')}
                          </Typography>
                        </Box>
                      </Box>
                    </Grid>

                    {request.course_name && (
                      <Grid item xs={12} sm={6} md={3}>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <SchoolIcon fontSize="small" color="action" />
                          <Box>
                            <Typography variant="body2" color="text.secondary">
                              Course
                            </Typography>
                            <Typography variant="body2">
                              {request.course_name}
                            </Typography>
                          </Box>
                        </Box>
                      </Grid>
                    )}
                  </Grid>

                  <Divider sx={{ my: 2 }} />

                  <Box sx={{ mb: 2 }}>
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                      Your Message:
                    </Typography>
                    <Typography variant="body2">
                      {request.message}
                    </Typography>
                  </Box>

                  {request.tutor_response && (
                    <Box sx={{ bgcolor: 'grey.50', p: 2, borderRadius: 1 }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                        <MessageIcon fontSize="small" color="action" />
                        <Typography variant="body2" color="text.secondary" fontWeight="medium">
                          Tutor Response:
                        </Typography>
                      </Box>
                      <Typography variant="body2">
                        {request.tutor_response}
                      </Typography>
                      {request.response_date && (
                        <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
                          Responded on {format(new Date(request.response_date), 'MMM dd, yyyy h:mm a')}
                        </Typography>
                      )}
                    </Box>
                  )}

                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mt: 2 }}>
                    <Typography variant="caption" color="text.secondary">
                      Requested on {format(new Date(request.created_at), 'MMM dd, yyyy h:mm a')}
                    </Typography>

                    {request.status === BookingRequestStatus.ACCEPTED && request.chat_room_id && (
                      <Button
                        variant="contained"
                        size="small"
                        startIcon={<ChatIcon />}
                        onClick={() => navigate(`/chat/${request.chat_room_id}`)}
                        color="primary"
                      >
                        Start Chat
                      </Button>
                    )}
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
      )}

      {/* No Results */}
      {!isLoading && requests.length === 0 && (
        <Box sx={{ textAlign: 'center', py: 4 }}>
          <Typography variant="h6" color="text.secondary">
            No booking requests found
          </Typography>
          <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
            {statusFilter ? 'Try changing the filter or' : ''} Start by finding a tutor and sending a request
          </Typography>
          <Button variant="contained" href="/tutors">
            Find Tutors
          </Button>
        </Box>
      )}
    </Box>
  );
};

export default BookingRequests;
