import React, { useState } from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Button,
  Chip,
  Divider,
  CircularProgress,
  Alert,
  Card,
  CardContent,
  CardHeader,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Tab,
  Tabs,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  Snackbar
} from '@mui/material';
import {
  Edit as EditIcon,
  ArrowBack as ArrowBackIcon,
  School as SchoolIcon,
  Book as BookIcon,
  CalendarToday as CalendarIcon,
  QuestionAnswer as QuestionIcon,
  Event as EventIcon,
  Person as PersonIcon,
  Add as AddIcon
} from '@mui/icons-material';
import { Link as RouterLink, useParams, useNavigate } from 'react-router-dom';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { getCourse, enrollInCourse } from '../../api/courses';
import { getSchool } from '../../api/schools';
import { getDepartment } from '../../api/departments';
import { getQuestions } from '../../api/questions';
import { getSessions } from '../../api/sessions';
import { useAuth } from '../../contexts/AuthContext';

// Tab panel component
interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

const TabPanel = (props: TabPanelProps) => {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`course-tabpanel-${index}`}
      aria-labelledby={`course-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ pt: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
};

const CourseDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const courseId = parseInt(id || '0');
  const navigate = useNavigate();
  const { user } = useAuth();
  const queryClient = useQueryClient();

  const isAdmin = user?.role === 'admin';
  const isTutor = user?.role === 'tutor';
  const isStudent = user?.role === 'student';
  const canManageCourse = isAdmin || isTutor;

  // State for tabs
  const [tabValue, setTabValue] = useState(0);

  // State for enrollment dialog
  const [enrollDialogOpen, setEnrollDialogOpen] = useState(false);

  // State for success message
  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  // Fetch course data
  const {
    data: course,
    isLoading: isLoadingCourse,
    error: courseError
  } = useQuery({
    queryKey: ['course', courseId],
    queryFn: () => getCourse(courseId),
    enabled: !!courseId
  });

  // Fetch school data
  const {
    data: school,
    isLoading: isLoadingSchool
  } = useQuery({
    queryKey: ['school', course?.school_id],
    queryFn: () => getSchool(course!.school_id),
    enabled: !!course?.school_id
  });

  // Fetch department data
  const {
    data: department,
    isLoading: isLoadingDepartment
  } = useQuery({
    queryKey: ['department', course?.department_id],
    queryFn: () => getDepartment(course!.department_id!),
    enabled: !!course?.department_id
  });

  // Fetch questions for this course
  const {
    data: questions = [],
    isLoading: isLoadingQuestions
  } = useQuery({
    queryKey: ['questions', { courseId }],
    queryFn: () => getQuestions(courseId),
    enabled: !!courseId
  });

  // Fetch sessions for this course
  const {
    data: sessions = [],
    isLoading: isLoadingSessions
  } = useQuery({
    queryKey: ['sessions', { courseId }],
    queryFn: () => getSessions(courseId),
    enabled: !!courseId
  });

  // Enroll in course mutation
  const enrollMutation = useMutation({
    mutationFn: () => enrollInCourse(courseId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['course', courseId] });
      setEnrollDialogOpen(false);
      setSuccessMessage('Successfully enrolled in course');
    }
  });

  // Handle tab change
  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  // Check if user is enrolled
  const isEnrolled = () => {
    if (!course || !user) return false;
    return course.users?.some(u => u.id === user.id) || false;
  };

  // Format date
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  if (isLoadingCourse) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
        <CircularProgress />
      </Box>
    );
  }

  if (courseError) {
    return (
      <Alert severity="error" sx={{ mt: 2 }}>
        Error loading course: {(courseError as Error).message}
      </Alert>
    );
  }

  if (!course) {
    return (
      <Alert severity="error" sx={{ mt: 2 }}>
        Course not found
      </Alert>
    );
  }

  return (
    <Box>
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
        <Button
          startIcon={<ArrowBackIcon />}
          onClick={() => navigate('/courses')}
          sx={{ mr: 2 }}
        >
          Back to Courses
        </Button>

        <Typography variant="h4" component="h1" sx={{ flexGrow: 1 }}>
          {course.name} <Typography component="span" variant="h6" color="text.secondary">({course.code})</Typography>
        </Typography>

        {canManageCourse && (
          <Button
            variant="contained"
            color="primary"
            startIcon={<EditIcon />}
            component={RouterLink}
            to={`/courses/${course.id}/edit`}
            sx={{ mr: 1 }}
          >
            Edit Course
          </Button>
        )}

        {isStudent && !isEnrolled() && (
          <Button
            variant="contained"
            color="secondary"
            onClick={() => setEnrollDialogOpen(true)}
          >
            Enroll
          </Button>
        )}
      </Box>

      <Grid container spacing={4}>
        <Grid item xs={12} md={8}>
          <Paper sx={{ mb: 4 }}>
            <Box sx={{ borderBottom: 1, borderColor: 'divider' }}>
              <Tabs
                value={tabValue}
                onChange={handleTabChange}
                aria-label="course tabs"
                variant="scrollable"
                scrollButtons="auto"
              >
                <Tab label="Overview" id="course-tab-0" aria-controls="course-tabpanel-0" />
                <Tab label="Questions" id="course-tab-1" aria-controls="course-tabpanel-1" />
                <Tab label="Sessions" id="course-tab-2" aria-controls="course-tabpanel-2" />
                {(isAdmin || isTutor) && (
                  <Tab label="Students" id="course-tab-3" aria-controls="course-tabpanel-3" />
                )}
              </Tabs>
            </Box>

            {/* Overview Tab */}
            <TabPanel value={tabValue} index={0}>
              <Box sx={{ p: 3 }}>
                <Typography variant="h6" gutterBottom>
                  About this Course
                </Typography>
                <Typography variant="body1" paragraph>
                  {course.description}
                </Typography>

                <Divider sx={{ my: 3 }} />

                <Typography variant="h6" gutterBottom>
                  School
                </Typography>

                {isLoadingSchool ? (
                  <CircularProgress size={24} />
                ) : school ? (
                  <Box sx={{ display: 'flex', alignItems: 'center' }}>
                    <SchoolIcon color="action" sx={{ mr: 1 }} />
                    <Typography variant="body1">
                      {school.name}
                    </Typography>
                  </Box>
                ) : (
                  <Typography variant="body2" color="text.secondary">
                    School information not available
                  </Typography>
                )}

                {course.department_id && (
                  <>
                    <Typography variant="h6" gutterBottom sx={{ mt: 3 }}>
                      Department
                    </Typography>

                    {isLoadingDepartment ? (
                      <CircularProgress size={24} />
                    ) : department ? (
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <BookIcon color="action" sx={{ mr: 1 }} />
                        <Typography variant="body1">
                          {department.name}
                        </Typography>
                      </Box>
                    ) : (
                      <Typography variant="body2" color="text.secondary">
                        Department information not available
                      </Typography>
                    )}
                  </>
                )}

                <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                  <CalendarIcon color="action" sx={{ mr: 1 }} />
                  <Typography variant="body2" color="text.secondary">
                    Added on {formatDate(course.created_at)}
                  </Typography>
                </Box>
              </Box>
            </TabPanel>

            {/* Questions Tab */}
            <TabPanel value={tabValue} index={1}>
              <Box sx={{ p: 3 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                  <Typography variant="h6">
                    Questions
                  </Typography>

                  {canManageCourse && (
                    <Button
                      variant="outlined"
                      size="small"
                      startIcon={<AddIcon />}
                      component={RouterLink}
                      to="/questions/new"
                      state={{ courseId: course.id }}
                    >
                      Add Question
                    </Button>
                  )}
                </Box>

                {isLoadingQuestions ? (
                  <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
                    <CircularProgress />
                  </Box>
                ) : questions.length === 0 ? (
                  <Box sx={{ textAlign: 'center', py: 4 }}>
                    <QuestionIcon sx={{ fontSize: 48, color: 'text.secondary', mb: 2 }} />
                    <Typography variant="body1" color="text.secondary">
                      No questions available for this course yet.
                    </Typography>
                    {canManageCourse && (
                      <Button
                        variant="contained"
                        size="small"
                        startIcon={<AddIcon />}
                        component={RouterLink}
                        to="/questions/new"
                        state={{ courseId: course.id }}
                        sx={{ mt: 2 }}
                      >
                        Add First Question
                      </Button>
                    )}
                  </Box>
                ) : (
                  <List>
                    {questions.slice(0, 5).map((question) => (
                      <React.Fragment key={question.id}>
                        <ListItem
                          button
                          component={RouterLink}
                          to={`/questions/${question.id}`}
                        >
                          <ListItemIcon>
                            <QuestionIcon />
                          </ListItemIcon>
                          <ListItemText
                            primary={question.content.length > 100
                              ? `${question.content.substring(0, 100)}...`
                              : question.content
                            }
                            secondary={
                              <Box sx={{ display: 'flex', gap: 1, mt: 0.5 }}>
                                <Chip
                                  label={question.question_type}
                                  size="small"
                                  variant="outlined"
                                />
                                <Chip
                                  label={question.difficulty}
                                  size="small"
                                  variant="outlined"
                                  color={
                                    question.difficulty === 'easy'
                                      ? 'success'
                                      : question.difficulty === 'medium'
                                        ? 'warning'
                                        : 'error'
                                  }
                                />
                              </Box>
                            }
                          />
                        </ListItem>
                        <Divider variant="inset" component="li" />
                      </React.Fragment>
                    ))}

                    {questions.length > 5 && (
                      <Box sx={{ textAlign: 'center', mt: 2 }}>
                        <Button
                          component={RouterLink}
                          to="/questions"
                          state={{ courseId: course.id }}
                        >
                          View All Questions
                        </Button>
                      </Box>
                    )}
                  </List>
                )}
              </Box>
            </TabPanel>

            {/* Sessions Tab */}
            <TabPanel value={tabValue} index={2}>
              <Box sx={{ p: 3 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                  <Typography variant="h6">
                    Sessions
                  </Typography>

                  {isTutor && (
                    <Button
                      variant="outlined"
                      size="small"
                      startIcon={<AddIcon />}
                      component={RouterLink}
                      to="/sessions/new"
                      state={{ courseId: course.id }}
                    >
                      Create Session
                    </Button>
                  )}
                </Box>

                {isLoadingSessions ? (
                  <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
                    <CircularProgress />
                  </Box>
                ) : sessions.length === 0 ? (
                  <Box sx={{ textAlign: 'center', py: 4 }}>
                    <EventIcon sx={{ fontSize: 48, color: 'text.secondary', mb: 2 }} />
                    <Typography variant="body1" color="text.secondary">
                      No sessions available for this course yet.
                    </Typography>
                    {isTutor && (
                      <Button
                        variant="contained"
                        size="small"
                        startIcon={<AddIcon />}
                        component={RouterLink}
                        to="/sessions/new"
                        state={{ courseId: course.id }}
                        sx={{ mt: 2 }}
                      >
                        Create First Session
                      </Button>
                    )}
                  </Box>
                ) : (
                  <List>
                    {sessions.slice(0, 5).map((session) => (
                      <React.Fragment key={session.id}>
                        <ListItem
                          button
                          component={RouterLink}
                          to={`/sessions/${session.id}`}
                        >
                          <ListItemIcon>
                            <EventIcon />
                          </ListItemIcon>
                          <ListItemText
                            primary={session.title}
                            secondary={
                              <>
                                {new Date(session.start_time).toLocaleString()} - {new Date(session.end_time).toLocaleTimeString()}
                                <br />
                                {session.session_type === 'online' ? 'Online' : `In-person: ${session.location}`}
                                <Box sx={{ display: 'flex', gap: 1, mt: 0.5 }}>
                                  <Chip
                                    label={session.status}
                                    size="small"
                                    variant="outlined"
                                    color={
                                      session.status === 'scheduled'
                                        ? 'primary'
                                        : session.status === 'in_progress'
                                          ? 'warning'
                                          : session.status === 'completed'
                                            ? 'success'
                                            : 'error'
                                    }
                                  />
                                </Box>
                              </>
                            }
                          />
                        </ListItem>
                        <Divider variant="inset" component="li" />
                      </React.Fragment>
                    ))}

                    {sessions.length > 5 && (
                      <Box sx={{ textAlign: 'center', mt: 2 }}>
                        <Button
                          component={RouterLink}
                          to="/sessions"
                          state={{ courseId: course.id }}
                        >
                          View All Sessions
                        </Button>
                      </Box>
                    )}
                  </List>
                )}
              </Box>
            </TabPanel>

            {/* Students Tab (Admin/Tutor only) */}
            {(isAdmin || isTutor) && (
              <TabPanel value={tabValue} index={3}>
                <Box sx={{ p: 3 }}>
                  <Typography variant="h6" gutterBottom>
                    Enrolled Students
                  </Typography>

                  {course.users && course.users.length > 0 ? (
                    <List>
                      {course.users.map((user) => (
                        <React.Fragment key={user.id}>
                          <ListItem>
                            <ListItemIcon>
                              <PersonIcon />
                            </ListItemIcon>
                            <ListItemText
                              primary={user.full_name}
                              secondary={user.email}
                            />
                          </ListItem>
                          <Divider variant="inset" component="li" />
                        </React.Fragment>
                      ))}
                    </List>
                  ) : (
                    <Box sx={{ textAlign: 'center', py: 4 }}>
                      <PersonIcon sx={{ fontSize: 48, color: 'text.secondary', mb: 2 }} />
                      <Typography variant="body1" color="text.secondary">
                        No students enrolled in this course yet.
                      </Typography>
                    </Box>
                  )}
                </Box>
              </TabPanel>
            )}
          </Paper>
        </Grid>

        <Grid item xs={12} md={4}>
          <Paper sx={{ p: 3, mb: 4 }}>
            <Typography variant="h6" gutterBottom>
              Course Information
            </Typography>

            <Box sx={{ mt: 2 }}>
              <Typography variant="subtitle2" color="text.secondary">
                Course Code
              </Typography>
              <Typography variant="body1">
                {course.code}
              </Typography>
            </Box>

            <Box sx={{ mt: 3 }}>
              <Typography variant="subtitle2" color="text.secondary">
                School
              </Typography>
              <Typography variant="body1">
                {isLoadingSchool ? (
                  <CircularProgress size={16} />
                ) : school ? (
                  <Button
                    component={RouterLink}
                    to={`/schools/${school.id}`}
                    size="small"
                    sx={{ p: 0, minWidth: 0, textTransform: 'none' }}
                  >
                    {school.name}
                  </Button>
                ) : (
                  'Unknown School'
                )}
              </Typography>
            </Box>

            {course.department_id && (
              <Box sx={{ mt: 3 }}>
                <Typography variant="subtitle2" color="text.secondary">
                  Department
                </Typography>
                <Typography variant="body1">
                  {isLoadingDepartment ? (
                    <CircularProgress size={16} />
                  ) : department ? (
                    department.name
                  ) : (
                    'Unknown Department'
                  )}
                </Typography>
              </Box>
            )}

            <Box sx={{ mt: 3 }}>
              <Typography variant="subtitle2" color="text.secondary">
                Status
              </Typography>
              <Chip
                label={course.is_active ? 'Active' : 'Inactive'}
                color={course.is_active ? 'success' : 'default'}
                sx={{ mt: 1 }}
              />
            </Box>

            <Box sx={{ mt: 3 }}>
              <Typography variant="subtitle2" color="text.secondary">
                Created
              </Typography>
              <Typography variant="body2">
                {formatDate(course.created_at)}
              </Typography>
            </Box>

            <Box sx={{ mt: 3 }}>
              <Typography variant="subtitle2" color="text.secondary">
                Last Updated
              </Typography>
              <Typography variant="body2">
                {formatDate(course.updated_at)}
              </Typography>
            </Box>

            <Box sx={{ mt: 3 }}>
              <Typography variant="subtitle2" color="text.secondary">
                Questions
              </Typography>
              <Typography variant="body2">
                {isLoadingQuestions ? (
                  <CircularProgress size={16} sx={{ ml: 1 }} />
                ) : (
                  questions.length
                )}
              </Typography>
            </Box>

            <Box sx={{ mt: 3 }}>
              <Typography variant="subtitle2" color="text.secondary">
                Sessions
              </Typography>
              <Typography variant="body2">
                {isLoadingSessions ? (
                  <CircularProgress size={16} sx={{ ml: 1 }} />
                ) : (
                  sessions.length
                )}
              </Typography>
            </Box>

            {course.users && (
              <Box sx={{ mt: 3 }}>
                <Typography variant="subtitle2" color="text.secondary">
                  Enrolled Students
                </Typography>
                <Typography variant="body2">
                  {course.users.length}
                </Typography>
              </Box>
            )}
          </Paper>

          {isStudent && (
            <Paper sx={{ p: 3, mb: 4 }}>
              <Typography variant="h6" gutterBottom>
                Student Actions
              </Typography>

              {isEnrolled() ? (
                <>
                  <Alert severity="success" sx={{ mb: 2 }}>
                    You are enrolled in this course
                  </Alert>

                  <Button
                    fullWidth
                    variant="contained"
                    component={RouterLink}
                    to="/questions"
                    state={{ courseId: course.id }}
                    sx={{ mb: 2 }}
                  >
                    Practice Questions
                  </Button>

                  <Button
                    fullWidth
                    variant="outlined"
                    component={RouterLink}
                    to="/sessions"
                    state={{ courseId: course.id }}
                  >
                    Browse Sessions
                  </Button>
                </>
              ) : (
                <>
                  <Alert severity="info" sx={{ mb: 2 }}>
                    Enroll to access course materials and sessions
                  </Alert>

                  <Button
                    fullWidth
                    variant="contained"
                    color="secondary"
                    onClick={() => setEnrollDialogOpen(true)}
                  >
                    Enroll in Course
                  </Button>
                </>
              )}
            </Paper>
          )}

          {canManageCourse && (
            <Paper sx={{ p: 3 }}>
              <Typography variant="h6" gutterBottom>
                Instructor Actions
              </Typography>

              <Button
                fullWidth
                variant="outlined"
                color="primary"
                component={RouterLink}
                to={`/courses/${course.id}/edit`}
                sx={{ mb: 2 }}
              >
                Edit Course
              </Button>

              <Button
                fullWidth
                variant="outlined"
                component={RouterLink}
                to="/questions/new"
                state={{ courseId: course.id }}
                sx={{ mb: 2 }}
              >
                Add Question
              </Button>

              {isTutor && (
                <Button
                  fullWidth
                  variant="outlined"
                  component={RouterLink}
                  to="/sessions/new"
                  state={{ courseId: course.id }}
                >
                  Create Session
                </Button>
              )}
            </Paper>
          )}
        </Grid>
      </Grid>

      {/* Enrollment Confirmation Dialog */}
      <Dialog
        open={enrollDialogOpen}
        onClose={() => setEnrollDialogOpen(false)}
      >
        <DialogTitle>Enroll in Course</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Are you sure you want to enroll in "{course.name}" ({course.code})?
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setEnrollDialogOpen(false)}>Cancel</Button>
          <Button
            onClick={() => enrollMutation.mutate()}
            color="primary"
            variant="contained"
            disabled={enrollMutation.isPending}
          >
            {enrollMutation.isPending ? <CircularProgress size={24} /> : 'Enroll'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Success message */}
      <Snackbar
        open={!!successMessage}
        autoHideDuration={3000}
        onClose={() => setSuccessMessage(null)}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert severity="success" onClose={() => setSuccessMessage(null)}>
          {successMessage}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default CourseDetail;
