import React, { useState } from 'react';
import { 
  Box, 
  Typography, 
  Button, 
  Paper, 
  Table, 
  TableBody, 
  TableCell, 
  TableContainer, 
  TableHead, 
  TableRow,
  TablePagination,
  IconButton,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  CircularProgress,
  Alert,
  TextField,
  InputAdornment,
  MenuItem,
  FormControl,
  InputLabel,
  Select,
  SelectChangeEvent
} from '@mui/material';
import { 
  Add as AddIcon, 
  Edit as EditIcon, 
  Delete as DeleteIcon,
  Visibility as ViewIcon,
  Search as SearchIcon
} from '@mui/icons-material';
import { Link as RouterLink, useNavigate } from 'react-router-dom';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { getCourses, deleteCourse, Course } from '../../api/courses';
import { getSchools, School } from '../../api/schools';
import { useAuth } from '../../contexts/AuthContext';
import { useContextualNavigation } from '../../hooks/useAdminContext';

const CoursesList: React.FC = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const { getPath } = useContextualNavigation();
  const isAdmin = user?.role === 'admin';
  const isTutor = user?.role === 'tutor';
  const canManageCourses = isAdmin || isTutor;
  
  // State for pagination
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  
  // State for filtering
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedSchool, setSelectedSchool] = useState<string>('');
  const [activeOnly, setActiveOnly] = useState<boolean>(false);
  
  // State for delete confirmation dialog
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [courseToDelete, setCourseToDelete] = useState<Course | null>(null);

  // Fetch courses data
  const { 
    data: courses = [], 
    isLoading: isLoadingCourses, 
    error: coursesError 
  } = useQuery({
    queryKey: ['courses'],
    queryFn: getCourses
  });

  // Fetch schools data for filtering
  const { 
    data: schools = [], 
    isLoading: isLoadingSchools 
  } = useQuery({
    queryKey: ['schools'],
    queryFn: getSchools
  });

  // Delete course mutation
  const deleteMutation = useMutation({
    mutationFn: (id: number) => deleteCourse(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['courses'] });
      setDeleteDialogOpen(false);
      setCourseToDelete(null);
    }
  });

  // Handle page change
  const handleChangePage = (event: unknown, newPage: number) => {
    setPage(newPage);
  };

  // Handle rows per page change
  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  // Handle school filter change
  const handleSchoolChange = (event: SelectChangeEvent) => {
    setSelectedSchool(event.target.value);
    setPage(0);
  };

  // Handle active filter change
  const handleActiveFilterChange = (event: SelectChangeEvent) => {
    setActiveOnly(event.target.value === 'active');
    setPage(0);
  };

  // Open delete confirmation dialog
  const handleDeleteClick = (course: Course) => {
    setCourseToDelete(course);
    setDeleteDialogOpen(true);
  };

  // Close delete confirmation dialog
  const handleDeleteCancel = () => {
    setDeleteDialogOpen(false);
    setCourseToDelete(null);
  };

  // Confirm delete
  const handleDeleteConfirm = () => {
    if (courseToDelete) {
      deleteMutation.mutate(courseToDelete.id);
    }
  };

  // Filter courses based on search term and filters
  const filteredCourses = courses.filter((course) => {
    const matchesSearch = 
      course.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      course.code.toLowerCase().includes(searchTerm.toLowerCase()) ||
      course.description.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesSchool = selectedSchool ? course.school_id === parseInt(selectedSchool) : true;
    const matchesActive = activeOnly ? course.is_active : true;
    
    return matchesSearch && matchesSchool && matchesActive;
  });

  // Get school name by ID
  const getSchoolName = (schoolId: number): string => {
    const school = schools.find(s => s.id === schoolId);
    return school ? school.name : 'Unknown School';
  };

  // Calculate pagination
  const paginatedCourses = filteredCourses.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage);

  if (isLoadingCourses) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
        <CircularProgress />
      </Box>
    );
  }

  if (coursesError) {
    return (
      <Alert severity="error" sx={{ mt: 2 }}>
        Error loading courses: {(coursesError as Error).message}
      </Alert>
    );
  }

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" component="h1">
          Courses
        </Typography>
        
        {canManageCourses && (
          <Button
            variant="contained"
            color="primary"
            startIcon={<AddIcon />}
            component={RouterLink}
            to={getPath("/courses/new")}
          >
            Add Course
          </Button>
        )}
      </Box>
      
      {/* Filters */}
      <Paper sx={{ p: 2, mb: 3 }}>
        <Box sx={{ display: 'flex', flexDirection: { xs: 'column', md: 'row' }, gap: 2 }}>
          <TextField
            label="Search Courses"
            variant="outlined"
            size="small"
            fullWidth
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon />
                </InputAdornment>
              ),
            }}
          />
          
          <FormControl size="small" sx={{ minWidth: 200 }}>
            <InputLabel id="school-filter-label">School</InputLabel>
            <Select
              labelId="school-filter-label"
              id="school-filter"
              value={selectedSchool}
              label="School"
              onChange={handleSchoolChange}
              disabled={isLoadingSchools}
            >
              <MenuItem value="">All Schools</MenuItem>
              {schools.map((school) => (
                <MenuItem key={school.id} value={school.id.toString()}>
                  {school.name}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
          
          <FormControl size="small" sx={{ minWidth: 150 }}>
            <InputLabel id="status-filter-label">Status</InputLabel>
            <Select
              labelId="status-filter-label"
              id="status-filter"
              value={activeOnly ? 'active' : 'all'}
              label="Status"
              onChange={handleActiveFilterChange}
            >
              <MenuItem value="all">All</MenuItem>
              <MenuItem value="active">Active Only</MenuItem>
            </Select>
          </FormControl>
        </Box>
      </Paper>
      
      {filteredCourses.length === 0 ? (
        <Paper sx={{ p: 3, textAlign: 'center' }}>
          <Typography variant="body1" color="text.secondary">
            No courses found.
          </Typography>
          {canManageCourses && (
            <Button
              variant="contained"
              color="primary"
              startIcon={<AddIcon />}
              component={RouterLink}
              to={getPath("/courses/new")}
              sx={{ mt: 2 }}
            >
              Add Course
            </Button>
          )}
        </Paper>
      ) : (
        <Paper sx={{ width: '100%', overflow: 'hidden' }}>
          <TableContainer>
            <Table stickyHeader aria-label="courses table">
              <TableHead>
                <TableRow>
                  <TableCell>Code</TableCell>
                  <TableCell>Name</TableCell>
                  <TableCell>School</TableCell>
                  <TableCell>Status</TableCell>
                  <TableCell align="right">Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {paginatedCourses.map((course) => (
                  <TableRow key={course.id} hover>
                    <TableCell>{course.code}</TableCell>
                    <TableCell component="th" scope="row">
                      {course.name}
                    </TableCell>
                    <TableCell>{getSchoolName(course.school_id)}</TableCell>
                    <TableCell>
                      <Chip 
                        label={course.is_active ? 'Active' : 'Inactive'} 
                        color={course.is_active ? 'success' : 'default'}
                        size="small"
                      />
                    </TableCell>
                    <TableCell align="right">
                      <IconButton
                        aria-label="view"
                        onClick={() => navigate(getPath(`/courses/${course.id}`))}
                      >
                        <ViewIcon />
                      </IconButton>

                      {canManageCourses && (
                        <>
                          <IconButton
                            aria-label="edit"
                            onClick={() => navigate(getPath(`/courses/${course.id}/edit`))}
                          >
                            <EditIcon />
                          </IconButton>
                          
                          {isAdmin && (
                            <IconButton 
                              aria-label="delete"
                              onClick={() => handleDeleteClick(course)}
                              color="error"
                            >
                              <DeleteIcon />
                            </IconButton>
                          )}
                        </>
                      )}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
          
          <TablePagination
            rowsPerPageOptions={[5, 10, 25]}
            component="div"
            count={filteredCourses.length}
            rowsPerPage={rowsPerPage}
            page={page}
            onPageChange={handleChangePage}
            onRowsPerPageChange={handleChangeRowsPerPage}
          />
        </Paper>
      )}
      
      {/* Delete Confirmation Dialog */}
      <Dialog
        open={deleteDialogOpen}
        onClose={handleDeleteCancel}
      >
        <DialogTitle>Delete Course</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Are you sure you want to delete the course "{courseToDelete?.name}" ({courseToDelete?.code})? 
            This action cannot be undone.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleDeleteCancel}>Cancel</Button>
          <Button 
            onClick={handleDeleteConfirm} 
            color="error" 
            disabled={deleteMutation.isPending}
          >
            {deleteMutation.isPending ? <CircularProgress size={24} /> : 'Delete'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default CoursesList;
