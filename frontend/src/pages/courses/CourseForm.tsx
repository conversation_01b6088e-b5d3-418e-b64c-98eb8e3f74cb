import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  TextField,
  Button,
  Grid,
  FormControlLabel,
  Switch,
  CircularProgress,
  Alert,
  Snackbar,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormHelperText
} from '@mui/material';
import {
  Save as SaveIcon,
  ArrowBack as ArrowBackIcon
} from '@mui/icons-material';
import { useNavigate, useParams, useLocation } from 'react-router-dom';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { getCourse, createCourse, updateCourse, CourseCreate, CourseUpdate } from '../../api/courses';
import { getSchools } from '../../api/schools';
import { getDepartmentsBySchool, Department } from '../../api/departments';
import { useContextualNavigation } from '../../hooks/useAdminContext';

// Validation schema
const validationSchema = Yup.object({
  name: Yup.string()
    .required('Name is required')
    .max(100, 'Name must be at most 100 characters'),
  description: Yup.string()
    .required('Description is required')
    .max(1000, 'Description must be at most 1000 characters'),
  code: Yup.string()
    .required('Course code is required')
    .max(20, 'Course code must be at most 20 characters'),
  school_id: Yup.number()
    .required('School is required'),
  department_id: Yup.number()
    .nullable(),
  is_active: Yup.boolean()
});

const CourseForm: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const location = useLocation();
  const isEditMode = !!id;
  const courseId = parseInt(id || '0');
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const { getPath } = useContextualNavigation();

  // Get schoolId from location state if available (for creating a course from school page)
  const initialSchoolId = location.state?.schoolId;

  // State for success message
  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  // State for departments
  const [departments, setDepartments] = useState<Department[]>([]);
  const [selectedSchoolId, setSelectedSchoolId] = useState<number | null>(initialSchoolId ? Number(initialSchoolId) : null);

  // Fetch course data if in edit mode
  const {
    data: course,
    isLoading: isLoadingCourse,
    error: courseError
  } = useQuery({
    queryKey: ['course', courseId],
    queryFn: () => getCourse(courseId),
    enabled: isEditMode
  });

  // Fetch schools for dropdown
  const {
    data: schools = [],
    isLoading: isLoadingSchools
  } = useQuery({
    queryKey: ['schools'],
    queryFn: getSchools
  });

  // Fetch departments for selected school
  const {
    data: departmentsData = [],
    isLoading: isLoadingDepartments
  } = useQuery({
    queryKey: ['departments', selectedSchoolId],
    queryFn: () => selectedSchoolId ? getDepartmentsBySchool(selectedSchoolId) : Promise.resolve([]),
    enabled: !!selectedSchoolId
  });

  // Update departments when departmentsData changes
  useEffect(() => {
    if (departmentsData.length > 0) {
      setDepartments(departmentsData);
    }
  }, [departmentsData]);

  // Create course mutation
  const createMutation = useMutation({
    mutationFn: (data: CourseCreate) => createCourse(data),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['courses'] });
      setSuccessMessage('Course created successfully');
      setTimeout(() => {
        navigate(getPath(`/courses/${data.id}`));
      }, 1500);
    }
  });

  // Update course mutation
  const updateMutation = useMutation({
    mutationFn: ({ id, data }: { id: number; data: CourseUpdate }) =>
      updateCourse(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['courses'] });
      queryClient.invalidateQueries({ queryKey: ['course', courseId] });
      setSuccessMessage('Course updated successfully');
      setTimeout(() => {
        navigate(getPath(`/courses/${courseId}`));
      }, 1500);
    }
  });

  // Handle school selection change
  const handleSchoolChange = (event: React.ChangeEvent<{ value: unknown }>) => {
    const schoolId = event.target.value as string;
    setSelectedSchoolId(schoolId ? Number(schoolId) : null);
    formik.setFieldValue('school_id', schoolId);
    formik.setFieldValue('department_id', ''); // Reset department when school changes
  };

  // Setup formik
  const formik = useFormik({
    initialValues: {
      name: '',
      description: '',
      code: '',
      school_id: initialSchoolId || '',
      department_id: '',
      is_active: true
    },
    validationSchema,
    onSubmit: (values) => {
      const courseData = {
        ...values,
        school_id: Number(values.school_id),
        department_id: values.department_id ? Number(values.department_id) : undefined
      };

      if (isEditMode) {
        updateMutation.mutate({ id: courseId, data: courseData });
      } else {
        createMutation.mutate(courseData);
      }
    },
    enableReinitialize: true
  });

  // Update form values when course data is loaded
  useEffect(() => {
    if (course) {
      setSelectedSchoolId(course.school_id);
      formik.setValues({
        name: course.name,
        description: course.description,
        code: course.code,
        school_id: course.school_id.toString(),
        department_id: course.department_id ? course.department_id.toString() : '',
        is_active: course.is_active
      });
    }
  }, [course]);

  // Handle loading and error states
  if (isEditMode && isLoadingCourse) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
        <CircularProgress />
      </Box>
    );
  }

  if (isEditMode && courseError) {
    return (
      <Alert severity="error" sx={{ mt: 2 }}>
        Error loading course: {(courseError as Error).message}
      </Alert>
    );
  }

  const isSubmitting = createMutation.isPending || updateMutation.isPending;
  const error = createMutation.error || updateMutation.error;

  return (
    <Box>
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
        <Button
          startIcon={<ArrowBackIcon />}
          onClick={() => navigate(isEditMode ? getPath(`/courses/${courseId}`) : getPath('/courses'))}
          sx={{ mr: 2 }}
        >
          {isEditMode ? 'Back to Course' : 'Back to Courses'}
        </Button>

        <Typography variant="h4" component="h1">
          {isEditMode ? 'Edit Course' : 'Add New Course'}
        </Typography>
      </Box>

      <Paper sx={{ p: 3 }}>
        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {(error as Error).message || 'An error occurred. Please try again.'}
          </Alert>
        )}

        <form onSubmit={formik.handleSubmit}>
          <Grid container spacing={3}>
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                id="name"
                name="name"
                label="Course Name"
                variant="outlined"
                value={formik.values.name}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                error={formik.touched.name && Boolean(formik.errors.name)}
                helperText={formik.touched.name && formik.errors.name}
                disabled={isSubmitting}
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                id="code"
                name="code"
                label="Course Code"
                variant="outlined"
                value={formik.values.code}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                error={formik.touched.code && Boolean(formik.errors.code)}
                helperText={formik.touched.code && formik.errors.code}
                disabled={isSubmitting}
              />
            </Grid>

            <Grid item xs={12} md={6}>
              <FormControl
                fullWidth
                error={formik.touched.school_id && Boolean(formik.errors.school_id)}
                disabled={isLoadingSchools || isSubmitting}
              >
                <InputLabel id="school-label">School</InputLabel>
                <Select
                  labelId="school-label"
                  id="school_id"
                  name="school_id"
                  value={formik.values.school_id}
                  label="School"
                  onChange={(e) => handleSchoolChange(e as any)}
                  onBlur={formik.handleBlur}
                >
                  {schools.map((school) => (
                    <MenuItem key={school.id} value={school.id.toString()}>
                      {school.name}
                    </MenuItem>
                  ))}
                </Select>
                {formik.touched.school_id && formik.errors.school_id && (
                  <FormHelperText>{formik.errors.school_id as string}</FormHelperText>
                )}
              </FormControl>
            </Grid>

            <Grid item xs={12} md={6}>
              <FormControl
                fullWidth
                error={formik.touched.department_id && Boolean(formik.errors.department_id)}
                disabled={isLoadingDepartments || isSubmitting || !selectedSchoolId}
              >
                <InputLabel id="department-label">Department (Optional)</InputLabel>
                <Select
                  labelId="department-label"
                  id="department_id"
                  name="department_id"
                  value={formik.values.department_id}
                  label="Department (Optional)"
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                >
                  <MenuItem value="">
                    <em>None</em>
                  </MenuItem>
                  {departments.map((department) => (
                    <MenuItem key={department.id} value={department.id.toString()}>
                      {department.name}
                    </MenuItem>
                  ))}
                </Select>
                {formik.touched.department_id && formik.errors.department_id && (
                  <FormHelperText>{formik.errors.department_id as string}</FormHelperText>
                )}
                {isLoadingDepartments && (
                  <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
                    <CircularProgress size={16} sx={{ mr: 1 }} />
                    <Typography variant="caption">Loading departments...</Typography>
                  </Box>
                )}
              </FormControl>
            </Grid>

            <Grid item xs={12}>
              <TextField
                fullWidth
                id="description"
                name="description"
                label="Description"
                variant="outlined"
                multiline
                rows={4}
                value={formik.values.description}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                error={formik.touched.description && Boolean(formik.errors.description)}
                helperText={formik.touched.description && formik.errors.description}
                disabled={isSubmitting}
              />
            </Grid>

            <Grid item xs={12}>
              <FormControlLabel
                control={
                  <Switch
                    id="is_active"
                    name="is_active"
                    checked={formik.values.is_active}
                    onChange={formik.handleChange}
                    disabled={isSubmitting}
                  />
                }
                label="Active"
              />
            </Grid>

            <Grid item xs={12} sx={{ display: 'flex', justifyContent: 'flex-end', gap: 2 }}>
              <Button
                variant="outlined"
                onClick={() => navigate(isEditMode ? getPath(`/courses/${courseId}`) : getPath('/courses'))}
                disabled={isSubmitting}
              >
                Cancel
              </Button>

              <Button
                type="submit"
                variant="contained"
                color="primary"
                startIcon={isSubmitting ? <CircularProgress size={24} /> : <SaveIcon />}
                disabled={isSubmitting}
              >
                {isEditMode ? 'Update Course' : 'Create Course'}
              </Button>
            </Grid>
          </Grid>
        </form>
      </Paper>

      {/* Success message */}
      <Snackbar
        open={!!successMessage}
        autoHideDuration={3000}
        onClose={() => setSuccessMessage(null)}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert severity="success" onClose={() => setSuccessMessage(null)}>
          {successMessage}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default CourseForm;
