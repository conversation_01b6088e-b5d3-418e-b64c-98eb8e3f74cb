import React, { useState } from 'react';
import { 
  Box, 
  Typography, 
  Button, 
  Paper, 
  Table, 
  TableBody, 
  TableCell, 
  TableContainer, 
  TableHead, 
  TableRow,
  TablePagination,
  IconButton,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  CircularProgress,
  Alert,
  TextField,
  InputAdornment,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  SelectChangeEvent,
  Avatar
} from '@mui/material';
import { 
  Edit as EditIcon, 
  Visibility as ViewIcon,
  Search as SearchIcon,
  Person as PersonIcon,
  School as SchoolIcon
} from '@mui/icons-material';
import { Link as RouterLink, useNavigate } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';
import { getUsers, User } from '../../api/users';
import { getSchools } from '../../api/schools';
import { useAuth } from '../../contexts/AuthContext';
import { useContextualNavigation } from '../../hooks/useAdminContext';

// Helper function to get role color
const getRoleColor = (role: string) => {
  switch (role) {
    case 'admin':
      return 'error';
    case 'tutor':
      return 'primary';
    case 'student':
      return 'success';
    default:
      return 'default';
  }
};

// Helper function to get avatar color
const getAvatarColor = (role: string) => {
  switch (role) {
    case 'admin':
      return '#f44336';
    case 'tutor':
      return '#2196f3';
    case 'student':
      return '#4caf50';
    default:
      return '#9e9e9e';
  }
};

// Helper function to get initials from full name
const getInitials = (name: string) => {
  return name
    .split(' ')
    .map(part => part[0])
    .join('')
    .toUpperCase()
    .substring(0, 2);
};

const UsersList: React.FC = () => {
  const navigate = useNavigate();
  const { getPath } = useContextualNavigation();
  
  // State for pagination
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  
  // State for filtering
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedRole, setSelectedRole] = useState<string>('');
  const [selectedSchool, setSelectedSchool] = useState<string>('');
  const [activeOnly, setActiveOnly] = useState<boolean>(true);

  // Fetch users data
  const { 
    data: users = [], 
    isLoading: isLoadingUsers, 
    error: usersError 
  } = useQuery({
    queryKey: ['users'],
    queryFn: getUsers
  });

  // Fetch schools data for filtering
  const { 
    data: schools = [], 
    isLoading: isLoadingSchools 
  } = useQuery({
    queryKey: ['schools'],
    queryFn: getSchools
  });

  // Handle page change
  const handleChangePage = (event: unknown, newPage: number) => {
    setPage(newPage);
  };

  // Handle rows per page change
  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  // Handle role filter change
  const handleRoleChange = (event: SelectChangeEvent) => {
    setSelectedRole(event.target.value);
    setPage(0);
  };

  // Handle school filter change
  const handleSchoolChange = (event: SelectChangeEvent) => {
    setSelectedSchool(event.target.value);
    setPage(0);
  };

  // Handle active filter change
  const handleActiveFilterChange = (event: SelectChangeEvent) => {
    setActiveOnly(event.target.value === 'active');
    setPage(0);
  };

  // Filter users based on search term and filters
  const filteredUsers = users.filter((user) => {
    // Text search
    const matchesSearch = 
      user.full_name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.username.toLowerCase().includes(searchTerm.toLowerCase()) ||
      user.email.toLowerCase().includes(searchTerm.toLowerCase());
    
    // Role filter
    const matchesRole = selectedRole ? user.role === selectedRole : true;
    
    // School filter
    const matchesSchool = selectedSchool 
      ? user.school_id === parseInt(selectedSchool)
      : true;
    
    // Active filter
    const matchesActive = activeOnly ? user.is_active : true;
    
    return matchesSearch && matchesRole && matchesSchool && matchesActive;
  });

  // Get school name by ID
  const getSchoolName = (schoolId?: number): string => {
    if (!schoolId) return 'N/A';
    const school = schools.find(s => s.id === schoolId);
    return school ? school.name : 'Unknown School';
  };

  // Calculate pagination
  const paginatedUsers = filteredUsers.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage);

  if (isLoadingUsers) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
        <CircularProgress />
      </Box>
    );
  }

  if (usersError) {
    return (
      <Alert severity="error" sx={{ mt: 2 }}>
        Error loading users: {(usersError as Error).message}
      </Alert>
    );
  }

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" component="h1">
          Users
        </Typography>
      </Box>
      
      {/* Filters */}
      <Paper sx={{ p: 2, mb: 3 }}>
        <Box sx={{ display: 'flex', flexDirection: { xs: 'column', md: 'row' }, gap: 2 }}>
          <TextField
            label="Search Users"
            variant="outlined"
            size="small"
            fullWidth
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon />
                </InputAdornment>
              ),
            }}
          />
          
          <FormControl size="small" sx={{ minWidth: 150 }}>
            <InputLabel id="role-filter-label">Role</InputLabel>
            <Select
              labelId="role-filter-label"
              id="role-filter"
              value={selectedRole}
              label="Role"
              onChange={handleRoleChange}
            >
              <MenuItem value="">All Roles</MenuItem>
              <MenuItem value="admin">Admin</MenuItem>
              <MenuItem value="tutor">Tutor</MenuItem>
              <MenuItem value="student">Student</MenuItem>
            </Select>
          </FormControl>
          
          <FormControl size="small" sx={{ minWidth: 200 }}>
            <InputLabel id="school-filter-label">School</InputLabel>
            <Select
              labelId="school-filter-label"
              id="school-filter"
              value={selectedSchool}
              label="School"
              onChange={handleSchoolChange}
              disabled={isLoadingSchools}
            >
              <MenuItem value="">All Schools</MenuItem>
              {schools.map((school) => (
                <MenuItem key={school.id} value={school.id.toString()}>
                  {school.name}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
          
          <FormControl size="small" sx={{ minWidth: 150 }}>
            <InputLabel id="status-filter-label">Status</InputLabel>
            <Select
              labelId="status-filter-label"
              id="status-filter"
              value={activeOnly ? 'active' : 'all'}
              label="Status"
              onChange={handleActiveFilterChange}
            >
              <MenuItem value="all">All</MenuItem>
              <MenuItem value="active">Active Only</MenuItem>
            </Select>
          </FormControl>
        </Box>
      </Paper>
      
      {filteredUsers.length === 0 ? (
        <Paper sx={{ p: 3, textAlign: 'center' }}>
          <Typography variant="body1" color="text.secondary">
            No users found.
          </Typography>
        </Paper>
      ) : (
        <Paper sx={{ width: '100%', overflow: 'hidden' }}>
          <TableContainer>
            <Table stickyHeader aria-label="users table">
              <TableHead>
                <TableRow>
                  <TableCell>User</TableCell>
                  <TableCell>Email</TableCell>
                  <TableCell>Role</TableCell>
                  <TableCell>School</TableCell>
                  <TableCell>Status</TableCell>
                  <TableCell align="right">Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {paginatedUsers.map((user) => (
                  <TableRow key={user.id} hover>
                    <TableCell>
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <Avatar 
                          sx={{ 
                            bgcolor: getAvatarColor(user.role),
                            mr: 2
                          }}
                        >
                          {getInitials(user.full_name)}
                        </Avatar>
                        <Box>
                          <Typography variant="body1">
                            {user.full_name}
                          </Typography>
                          <Typography variant="body2" color="text.secondary">
                            @{user.username}
                          </Typography>
                        </Box>
                      </Box>
                    </TableCell>
                    <TableCell>{user.email}</TableCell>
                    <TableCell>
                      <Chip 
                        label={user.role} 
                        color={getRoleColor(user.role)}
                        size="small"
                      />
                    </TableCell>
                    <TableCell>
                      {user.school_id ? (
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          <SchoolIcon fontSize="small" sx={{ mr: 1 }} />
                          {getSchoolName(user.school_id)}
                        </Box>
                      ) : (
                        'N/A'
                      )}
                    </TableCell>
                    <TableCell>
                      <Chip 
                        label={user.is_active ? 'Active' : 'Inactive'} 
                        color={user.is_active ? 'success' : 'default'}
                        size="small"
                      />
                    </TableCell>
                    <TableCell align="right">
                      <IconButton
                        aria-label="view"
                        onClick={() => navigate(getPath(`/users/${user.id}`))}
                      >
                        <ViewIcon />
                      </IconButton>

                      <IconButton
                        aria-label="edit"
                        onClick={() => navigate(getPath(`/users/${user.id}/edit`))}
                      >
                        <EditIcon />
                      </IconButton>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
          
          <TablePagination
            rowsPerPageOptions={[5, 10, 25]}
            component="div"
            count={filteredUsers.length}
            rowsPerPage={rowsPerPage}
            page={page}
            onPageChange={handleChangePage}
            onRowsPerPageChange={handleChangeRowsPerPage}
          />
        </Paper>
      )}
    </Box>
  );
};

export default UsersList;
