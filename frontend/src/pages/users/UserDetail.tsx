import React, { useState } from 'react';
import { 
  Box, 
  Typography, 
  Paper, 
  Grid, 
  Button, 
  Chip,
  Divider,
  CircularProgress,
  Alert,
  Card,
  CardContent,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Avatar,
  Switch,
  FormControlLabel,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  Snackbar
} from '@mui/material';
import { 
  Edit as EditIcon, 
  ArrowBack as ArrowBackIcon,
  School as SchoolIcon,
  Email as EmailIcon,
  Person as PersonIcon,
  CalendarToday as CalendarIcon,
  Book as BookIcon,
  Event as EventIcon,
  QuestionAnswer as QuestionIcon
} from '@mui/icons-material';
import { Link as RouterLink, useParams, useNavigate } from 'react-router-dom';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { getUser, updateUser } from '../../api/users';
import { getSchool } from '../../api/schools';
import { useAuth } from '../../contexts/AuthContext';

// Helper function to format date
const formatDate = (dateString: string) => {
  const date = new Date(dateString);
  return date.toLocaleDateString('en-US', {
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
};

// Helper function to get role color
const getRoleColor = (role: string) => {
  switch (role) {
    case 'admin':
      return 'error';
    case 'tutor':
      return 'primary';
    case 'student':
      return 'success';
    default:
      return 'default';
  }
};

// Helper function to get avatar color
const getAvatarColor = (role: string) => {
  switch (role) {
    case 'admin':
      return '#f44336';
    case 'tutor':
      return '#2196f3';
    case 'student':
      return '#4caf50';
    default:
      return '#9e9e9e';
  }
};

// Helper function to get initials from full name
const getInitials = (name: string) => {
  return name
    .split(' ')
    .map(part => part[0])
    .join('')
    .toUpperCase()
    .substring(0, 2);
};

const UserDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const userId = parseInt(id || '0');
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const { user: currentUser } = useAuth();
  
  // State for status toggle
  const [statusDialogOpen, setStatusDialogOpen] = useState(false);
  const [newStatus, setNewStatus] = useState(false);
  
  // State for success message
  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  // Fetch user data
  const { 
    data: user, 
    isLoading: isLoadingUser, 
    error: userError 
  } = useQuery({
    queryKey: ['user', userId],
    queryFn: () => getUser(userId),
    enabled: !!userId
  });

  // Fetch school data if user is a student
  const { 
    data: school, 
    isLoading: isLoadingSchool 
  } = useQuery({
    queryKey: ['school', user?.school_id],
    queryFn: () => getSchool(user!.school_id!),
    enabled: !!user?.school_id
  });

  // Update user status mutation
  const updateStatusMutation = useMutation({
    mutationFn: (isActive: boolean) => updateUser(userId, { is_active: isActive }),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['users'] });
      queryClient.invalidateQueries({ queryKey: ['user', userId] });
      setStatusDialogOpen(false);
      setSuccessMessage(`User ${newStatus ? 'activated' : 'deactivated'} successfully`);
    }
  });

  // Handle status toggle
  const handleStatusToggle = (event: React.ChangeEvent<HTMLInputElement>) => {
    setNewStatus(event.target.checked);
    setStatusDialogOpen(true);
  };

  // Confirm status change
  const handleStatusConfirm = () => {
    updateStatusMutation.mutate(newStatus);
  };

  if (isLoadingUser) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
        <CircularProgress />
      </Box>
    );
  }

  if (userError) {
    return (
      <Alert severity="error" sx={{ mt: 2 }}>
        Error loading user: {(userError as Error).message}
      </Alert>
    );
  }

  if (!user) {
    return (
      <Alert severity="error" sx={{ mt: 2 }}>
        User not found
      </Alert>
    );
  }

  return (
    <Box>
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
        <Button
          startIcon={<ArrowBackIcon />}
          onClick={() => navigate('/users')}
          sx={{ mr: 2 }}
        >
          Back to Users
        </Button>
        
        <Typography variant="h4" component="h1" sx={{ flexGrow: 1 }}>
          User Profile
        </Typography>
        
        <Button
          variant="contained"
          color="primary"
          startIcon={<EditIcon />}
          component={RouterLink}
          to={`/users/${user.id}/edit`}
        >
          Edit User
        </Button>
      </Box>
      
      <Grid container spacing={4}>
        <Grid item xs={12} md={4}>
          <Paper sx={{ p: 3, textAlign: 'center', mb: 4 }}>
            <Avatar 
              sx={{ 
                width: 100, 
                height: 100, 
                mx: 'auto', 
                mb: 2,
                bgcolor: getAvatarColor(user.role),
                fontSize: '2.5rem'
              }}
            >
              {getInitials(user.full_name)}
            </Avatar>
            
            <Typography variant="h5" gutterBottom>
              {user.full_name}
            </Typography>
            
            <Typography variant="body1" color="text.secondary" gutterBottom>
              @{user.username}
            </Typography>
            
            <Box sx={{ display: 'flex', justifyContent: 'center', mt: 2 }}>
              <Chip 
                label={user.role} 
                color={getRoleColor(user.role)}
                sx={{ mr: 1 }}
              />
              <Chip 
                label={user.is_active ? 'Active' : 'Inactive'} 
                color={user.is_active ? 'success' : 'default'}
              />
            </Box>
            
            <Box sx={{ mt: 3 }}>
              <FormControlLabel
                control={
                  <Switch
                    checked={user.is_active}
                    onChange={handleStatusToggle}
                    disabled={updateStatusMutation.isPending}
                  />
                }
                label={user.is_active ? 'Active' : 'Inactive'}
              />
            </Box>
          </Paper>
          
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Contact Information
            </Typography>
            
            <List>
              <ListItem>
                <ListItemIcon>
                  <EmailIcon />
                </ListItemIcon>
                <ListItemText 
                  primary="Email"
                  secondary={user.email}
                />
              </ListItem>
              
              {user.role === 'student' && user.school_id && (
                <ListItem>
                  <ListItemIcon>
                    <SchoolIcon />
                  </ListItemIcon>
                  <ListItemText 
                    primary="School"
                    secondary={
                      isLoadingSchool ? (
                        <CircularProgress size={16} />
                      ) : school ? (
                        <Button 
                          component={RouterLink} 
                          to={`/schools/${school.id}`}
                          size="small"
                          sx={{ p: 0, minWidth: 0, textTransform: 'none' }}
                        >
                          {school.name}
                        </Button>
                      ) : (
                        'Unknown School'
                      )
                    }
                  />
                </ListItem>
              )}
              
              <ListItem>
                <ListItemIcon>
                  <CalendarIcon />
                </ListItemIcon>
                <ListItemText 
                  primary="Joined"
                  secondary={formatDate(user.created_at)}
                />
              </ListItem>
            </List>
          </Paper>
        </Grid>
        
        <Grid item xs={12} md={8}>
          <Paper sx={{ p: 3, mb: 4 }}>
            <Typography variant="h6" gutterBottom>
              User Information
            </Typography>
            
            <Grid container spacing={3}>
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2" color="text.secondary">
                  Full Name
                </Typography>
                <Typography variant="body1">
                  {user.full_name}
                </Typography>
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2" color="text.secondary">
                  Username
                </Typography>
                <Typography variant="body1">
                  {user.username}
                </Typography>
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2" color="text.secondary">
                  Email
                </Typography>
                <Typography variant="body1">
                  {user.email}
                </Typography>
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2" color="text.secondary">
                  Role
                </Typography>
                <Chip 
                  label={user.role} 
                  color={getRoleColor(user.role)}
                  size="small"
                />
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2" color="text.secondary">
                  Status
                </Typography>
                <Chip 
                  label={user.is_active ? 'Active' : 'Inactive'} 
                  color={user.is_active ? 'success' : 'default'}
                  size="small"
                />
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2" color="text.secondary">
                  Created
                </Typography>
                <Typography variant="body1">
                  {formatDate(user.created_at)}
                </Typography>
              </Grid>
              
              <Grid item xs={12} sm={6}>
                <Typography variant="subtitle2" color="text.secondary">
                  Last Updated
                </Typography>
                <Typography variant="body1">
                  {formatDate(user.updated_at)}
                </Typography>
              </Grid>
            </Grid>
          </Paper>
          
          {user.role === 'student' && (
            <Paper sx={{ p: 3, mb: 4 }}>
              <Typography variant="h6" gutterBottom>
                Enrolled Courses
              </Typography>
              
              {user.courses && user.courses.length > 0 ? (
                <List>
                  {user.courses.map((course) => (
                    <ListItem 
                      key={course.id}
                      button
                      component={RouterLink}
                      to={`/courses/${course.id}`}
                    >
                      <ListItemIcon>
                        <BookIcon />
                      </ListItemIcon>
                      <ListItemText 
                        primary={course.name}
                        secondary={course.code}
                      />
                    </ListItem>
                  ))}
                </List>
              ) : (
                <Typography variant="body1" color="text.secondary">
                  Not enrolled in any courses yet.
                </Typography>
              )}
            </Paper>
          )}
          
          {user.role === 'tutor' && (
            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <Paper sx={{ p: 3, mb: { xs: 3, md: 0 } }}>
                  <Typography variant="h6" gutterBottom>
                    Sessions
                  </Typography>
                  
                  {user.sessions && user.sessions.length > 0 ? (
                    <List>
                      {user.sessions.slice(0, 5).map((session) => (
                        <ListItem 
                          key={session.id}
                          button
                          component={RouterLink}
                          to={`/sessions/${session.id}`}
                        >
                          <ListItemIcon>
                            <EventIcon />
                          </ListItemIcon>
                          <ListItemText 
                            primary={session.title}
                            secondary={new Date(session.start_time).toLocaleString()}
                          />
                        </ListItem>
                      ))}
                      
                      {user.sessions.length > 5 && (
                        <Box sx={{ textAlign: 'center', mt: 1 }}>
                          <Button 
                            component={RouterLink} 
                            to="/sessions"
                            state={{ tutorId: user.id }}
                          >
                            View All Sessions
                          </Button>
                        </Box>
                      )}
                    </List>
                  ) : (
                    <Typography variant="body1" color="text.secondary">
                      No sessions created yet.
                    </Typography>
                  )}
                </Paper>
              </Grid>
              
              <Grid item xs={12} md={6}>
                <Paper sx={{ p: 3 }}>
                  <Typography variant="h6" gutterBottom>
                    Questions
                  </Typography>
                  
                  {user.questions && user.questions.length > 0 ? (
                    <List>
                      {user.questions.slice(0, 5).map((question) => (
                        <ListItem 
                          key={question.id}
                          button
                          component={RouterLink}
                          to={`/questions/${question.id}`}
                        >
                          <ListItemIcon>
                            <QuestionIcon />
                          </ListItemIcon>
                          <ListItemText 
                            primary={question.content.substring(0, 50) + (question.content.length > 50 ? '...' : '')}
                            secondary={`${question.question_type} - ${question.difficulty}`}
                          />
                        </ListItem>
                      ))}
                      
                      {user.questions.length > 5 && (
                        <Box sx={{ textAlign: 'center', mt: 1 }}>
                          <Button 
                            component={RouterLink} 
                            to="/questions"
                            state={{ createdById: user.id }}
                          >
                            View All Questions
                          </Button>
                        </Box>
                      )}
                    </List>
                  ) : (
                    <Typography variant="body1" color="text.secondary">
                      No questions created yet.
                    </Typography>
                  )}
                </Paper>
              </Grid>
            </Grid>
          )}
        </Grid>
      </Grid>
      
      {/* Status Change Confirmation Dialog */}
      <Dialog
        open={statusDialogOpen}
        onClose={() => setStatusDialogOpen(false)}
      >
        <DialogTitle>
          {newStatus ? 'Activate User' : 'Deactivate User'}
        </DialogTitle>
        <DialogContent>
          <DialogContentText>
            Are you sure you want to {newStatus ? 'activate' : 'deactivate'} this user?
            {!newStatus && ' Deactivated users will not be able to log in to the system.'}
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setStatusDialogOpen(false)}>Cancel</Button>
          <Button 
            onClick={handleStatusConfirm} 
            color={newStatus ? 'success' : 'error'} 
            variant="contained"
            disabled={updateStatusMutation.isPending}
          >
            {updateStatusMutation.isPending ? (
              <CircularProgress size={24} />
            ) : (
              newStatus ? 'Activate' : 'Deactivate'
            )}
          </Button>
        </DialogActions>
      </Dialog>
      
      {/* Success message */}
      <Snackbar
        open={!!successMessage}
        autoHideDuration={3000}
        onClose={() => setSuccessMessage(null)}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert severity="success" onClose={() => setSuccessMessage(null)}>
          {successMessage}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default UserDetail;
