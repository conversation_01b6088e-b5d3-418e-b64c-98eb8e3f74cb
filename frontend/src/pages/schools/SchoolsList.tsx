import React, { useState } from 'react';
import { 
  Box, 
  Typography, 
  Button, 
  Paper, 
  Table, 
  TableBody, 
  TableCell, 
  TableContainer, 
  TableHead, 
  TableRow,
  TablePagination,
  IconButton,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  CircularProgress,
  Alert
} from '@mui/material';
import { 
  Add as AddIcon, 
  Edit as EditIcon, 
  Delete as DeleteIcon,
  Visibility as ViewIcon
} from '@mui/icons-material';
import { Link as RouterLink, useNavigate } from 'react-router-dom';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { getSchools, deleteSchool, School } from '../../api/schools';
import { useAuth } from '../../contexts/AuthContext';
import { useContextualNavigation } from '../../hooks/useAdminContext';

const SchoolsList: React.FC = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const { getPath } = useContextualNavigation();
  const isAdmin = user?.role === 'admin';
  
  // State for pagination
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  
  // State for delete confirmation dialog
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [schoolToDelete, setSchoolToDelete] = useState<School | null>(null);

  // Fetch schools data
  const { 
    data: schools = [], 
    isLoading, 
    error 
  } = useQuery({
    queryKey: ['schools'],
    queryFn: getSchools
  });

  // Delete school mutation
  const deleteMutation = useMutation({
    mutationFn: (id: number) => deleteSchool(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['schools'] });
      setDeleteDialogOpen(false);
      setSchoolToDelete(null);
    }
  });

  // Handle page change
  const handleChangePage = (event: unknown, newPage: number) => {
    setPage(newPage);
  };

  // Handle rows per page change
  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  // Open delete confirmation dialog
  const handleDeleteClick = (school: School) => {
    setSchoolToDelete(school);
    setDeleteDialogOpen(true);
  };

  // Close delete confirmation dialog
  const handleDeleteCancel = () => {
    setDeleteDialogOpen(false);
    setSchoolToDelete(null);
  };

  // Confirm delete
  const handleDeleteConfirm = () => {
    if (schoolToDelete) {
      deleteMutation.mutate(schoolToDelete.id);
    }
  };

  // Calculate pagination
  const paginatedSchools = schools.slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage);

  if (isLoading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ mt: 2 }}>
        Error loading schools: {(error as Error).message}
      </Alert>
    );
  }

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" component="h1">
          Schools
        </Typography>
        
        {isAdmin && (
          <Button
            variant="contained"
            color="primary"
            startIcon={<AddIcon />}
            component={RouterLink}
            to={getPath("/schools/new")}
          >
            Add School
          </Button>
        )}
      </Box>
      
      {schools.length === 0 ? (
        <Paper sx={{ p: 3, textAlign: 'center' }}>
          <Typography variant="body1" color="text.secondary">
            No schools found.
          </Typography>
          {isAdmin && (
            <Button
              variant="contained"
              color="primary"
              startIcon={<AddIcon />}
              component={RouterLink}
              to={getPath("/schools/new")}
              sx={{ mt: 2 }}
            >
              Add School
            </Button>
          )}
        </Paper>
      ) : (
        <Paper sx={{ width: '100%', overflow: 'hidden' }}>
          <TableContainer>
            <Table stickyHeader aria-label="schools table">
              <TableHead>
                <TableRow>
                  <TableCell>Name</TableCell>
                  <TableCell>Location</TableCell>
                  <TableCell>Status</TableCell>
                  <TableCell align="right">Actions</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {paginatedSchools.map((school) => (
                  <TableRow key={school.id} hover>
                    <TableCell component="th" scope="row">
                      {school.name}
                    </TableCell>
                    <TableCell>{school.location}</TableCell>
                    <TableCell>
                      <Chip 
                        label={school.is_active ? 'Active' : 'Inactive'} 
                        color={school.is_active ? 'success' : 'default'}
                        size="small"
                      />
                    </TableCell>
                    <TableCell align="right">
                      <IconButton
                        aria-label="view"
                        onClick={() => navigate(getPath(`/schools/${school.id}`))}
                      >
                        <ViewIcon />
                      </IconButton>

                      {isAdmin && (
                        <>
                          <IconButton
                            aria-label="edit"
                            onClick={() => navigate(getPath(`/schools/${school.id}/edit`))}
                          >
                            <EditIcon />
                          </IconButton>
                          
                          <IconButton 
                            aria-label="delete"
                            onClick={() => handleDeleteClick(school)}
                            color="error"
                          >
                            <DeleteIcon />
                          </IconButton>
                        </>
                      )}
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </TableContainer>
          
          <TablePagination
            rowsPerPageOptions={[5, 10, 25]}
            component="div"
            count={schools.length}
            rowsPerPage={rowsPerPage}
            page={page}
            onPageChange={handleChangePage}
            onRowsPerPageChange={handleChangeRowsPerPage}
          />
        </Paper>
      )}
      
      {/* Delete Confirmation Dialog */}
      <Dialog
        open={deleteDialogOpen}
        onClose={handleDeleteCancel}
      >
        <DialogTitle>Delete School</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Are you sure you want to delete the school "{schoolToDelete?.name}"? 
            This action cannot be undone.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleDeleteCancel}>Cancel</Button>
          <Button 
            onClick={handleDeleteConfirm} 
            color="error" 
            disabled={deleteMutation.isPending}
          >
            {deleteMutation.isPending ? <CircularProgress size={24} /> : 'Delete'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default SchoolsList;
