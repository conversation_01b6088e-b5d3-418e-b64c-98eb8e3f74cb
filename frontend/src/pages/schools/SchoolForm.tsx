import React, { useState, useEffect } from 'react';
import { 
  Box, 
  Typography, 
  Paper, 
  TextField, 
  Button, 
  Grid,
  FormControlLabel,
  Switch,
  CircularProgress,
  Alert,
  Snackbar
} from '@mui/material';
import { 
  Save as SaveIcon, 
  ArrowBack as ArrowBackIcon 
} from '@mui/icons-material';
import { useNavigate, useParams } from 'react-router-dom';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { getSchool, createSchool, updateSchool, SchoolCreate, SchoolUpdate } from '../../api/schools';
import { useContextualNavigation } from '../../hooks/useAdminContext';

// Validation schema
const validationSchema = Yup.object({
  name: Yup.string()
    .required('Name is required')
    .max(100, 'Name must be at most 100 characters'),
  description: Yup.string()
    .required('Description is required')
    .max(1000, 'Description must be at most 1000 characters'),
  location: Yup.string()
    .required('Location is required')
    .max(200, 'Location must be at most 200 characters'),
  is_active: Yup.boolean()
});

const SchoolForm: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const isEditMode = !!id;
  const schoolId = parseInt(id || '0');
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const { getPath } = useContextualNavigation();
  
  // State for success message
  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  // Fetch school data if in edit mode
  const { 
    data: school, 
    isLoading: isLoadingSchool, 
    error: schoolError 
  } = useQuery({
    queryKey: ['school', schoolId],
    queryFn: () => getSchool(schoolId),
    enabled: isEditMode
  });

  // Create school mutation
  const createMutation = useMutation({
    mutationFn: (data: SchoolCreate) => createSchool(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['schools'] });
      setSuccessMessage('School created successfully');
      setTimeout(() => {
        navigate(getPath('/schools'));
      }, 1500);
    }
  });

  // Update school mutation
  const updateMutation = useMutation({
    mutationFn: ({ id, data }: { id: number; data: SchoolUpdate }) => 
      updateSchool(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['schools'] });
      queryClient.invalidateQueries({ queryKey: ['school', schoolId] });
      setSuccessMessage('School updated successfully');
      setTimeout(() => {
        navigate(getPath(`/schools/${schoolId}`));
      }, 1500);
    }
  });

  // Setup formik
  const formik = useFormik({
    initialValues: {
      name: '',
      description: '',
      location: '',
      is_active: true
    },
    validationSchema,
    onSubmit: (values) => {
      if (isEditMode) {
        updateMutation.mutate({ id: schoolId, data: values });
      } else {
        createMutation.mutate(values);
      }
    },
    enableReinitialize: true
  });

  // Update form values when school data is loaded
  useEffect(() => {
    if (school) {
      formik.setValues({
        name: school.name,
        description: school.description,
        location: school.location,
        is_active: school.is_active
      });
    }
  }, [school]);

  // Handle loading and error states
  if (isEditMode && isLoadingSchool) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
        <CircularProgress />
      </Box>
    );
  }

  if (isEditMode && schoolError) {
    return (
      <Alert severity="error" sx={{ mt: 2 }}>
        Error loading school: {(schoolError as Error).message}
      </Alert>
    );
  }

  const isSubmitting = createMutation.isPending || updateMutation.isPending;
  const error = createMutation.error || updateMutation.error;

  return (
    <Box>
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
        <Button
          startIcon={<ArrowBackIcon />}
          onClick={() => navigate(isEditMode ? getPath(`/schools/${schoolId}`) : getPath('/schools'))}
          sx={{ mr: 2 }}
        >
          {isEditMode ? 'Back to School' : 'Back to Schools'}
        </Button>
        
        <Typography variant="h4" component="h1">
          {isEditMode ? 'Edit School' : 'Add New School'}
        </Typography>
      </Box>
      
      <Paper sx={{ p: 3 }}>
        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {(error as Error).message || 'An error occurred. Please try again.'}
          </Alert>
        )}
        
        <form onSubmit={formik.handleSubmit}>
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <TextField
                fullWidth
                id="name"
                name="name"
                label="School Name"
                variant="outlined"
                value={formik.values.name}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                error={formik.touched.name && Boolean(formik.errors.name)}
                helperText={formik.touched.name && formik.errors.name}
                disabled={isSubmitting}
              />
            </Grid>
            
            <Grid item xs={12}>
              <TextField
                fullWidth
                id="location"
                name="location"
                label="Location"
                variant="outlined"
                value={formik.values.location}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                error={formik.touched.location && Boolean(formik.errors.location)}
                helperText={formik.touched.location && formik.errors.location}
                disabled={isSubmitting}
              />
            </Grid>
            
            <Grid item xs={12}>
              <TextField
                fullWidth
                id="description"
                name="description"
                label="Description"
                variant="outlined"
                multiline
                rows={4}
                value={formik.values.description}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                error={formik.touched.description && Boolean(formik.errors.description)}
                helperText={formik.touched.description && formik.errors.description}
                disabled={isSubmitting}
              />
            </Grid>
            
            <Grid item xs={12}>
              <FormControlLabel
                control={
                  <Switch
                    id="is_active"
                    name="is_active"
                    checked={formik.values.is_active}
                    onChange={formik.handleChange}
                    disabled={isSubmitting}
                  />
                }
                label="Active"
              />
            </Grid>
            
            <Grid item xs={12} sx={{ display: 'flex', justifyContent: 'flex-end', gap: 2 }}>
              <Button
                variant="outlined"
                onClick={() => navigate(isEditMode ? getPath(`/schools/${schoolId}`) : getPath('/schools'))}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
              
              <Button
                type="submit"
                variant="contained"
                color="primary"
                startIcon={isSubmitting ? <CircularProgress size={24} /> : <SaveIcon />}
                disabled={isSubmitting}
              >
                {isEditMode ? 'Update School' : 'Create School'}
              </Button>
            </Grid>
          </Grid>
        </form>
      </Paper>
      
      {/* Success message */}
      <Snackbar
        open={!!successMessage}
        autoHideDuration={3000}
        onClose={() => setSuccessMessage(null)}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert severity="success" onClose={() => setSuccessMessage(null)}>
          {successMessage}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default SchoolForm;
