import React from 'react';
import { 
  Box, 
  Typography, 
  Paper, 
  Grid, 
  Button, 
  Chip,
  Divider,
  CircularProgress,
  Alert,
  Card,
  CardContent,
  CardHeader
} from '@mui/material';
import { 
  Edit as EditIcon, 
  ArrowBack as ArrowBackIcon,
  School as SchoolIcon,
  LocationOn as LocationIcon,
  CalendarToday as CalendarIcon
} from '@mui/icons-material';
import { Link as RouterLink, useParams, useNavigate } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';
import { getSchool } from '../../api/schools';
import { getCourses } from '../../api/courses';
import { useAuth } from '../../contexts/AuthContext';

const SchoolDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const schoolId = parseInt(id || '0');
  const navigate = useNavigate();
  const { user } = useAuth();
  const isAdmin = user?.role === 'admin';

  // Fetch school data
  const { 
    data: school, 
    isLoading: isLoadingSchool, 
    error: schoolError 
  } = useQuery({
    queryKey: ['school', schoolId],
    queryFn: () => getSchool(schoolId),
    enabled: !!schoolId
  });

  // Fetch courses for this school
  const { 
    data: courses = [], 
    isLoading: isLoadingCourses 
  } = useQuery({
    queryKey: ['courses', { schoolId }],
    queryFn: () => getCourses(),
    select: (data) => data.filter(course => course.school_id === schoolId),
    enabled: !!schoolId
  });

  if (isLoadingSchool) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
        <CircularProgress />
      </Box>
    );
  }

  if (schoolError) {
    return (
      <Alert severity="error" sx={{ mt: 2 }}>
        Error loading school: {(schoolError as Error).message}
      </Alert>
    );
  }

  if (!school) {
    return (
      <Alert severity="error" sx={{ mt: 2 }}>
        School not found
      </Alert>
    );
  }

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  return (
    <Box>
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
        <Button
          startIcon={<ArrowBackIcon />}
          onClick={() => navigate('/schools')}
          sx={{ mr: 2 }}
        >
          Back to Schools
        </Button>
        
        <Typography variant="h4" component="h1" sx={{ flexGrow: 1 }}>
          {school.name}
        </Typography>
        
        {isAdmin && (
          <Button
            variant="contained"
            color="primary"
            startIcon={<EditIcon />}
            component={RouterLink}
            to={`/schools/${school.id}/edit`}
          >
            Edit School
          </Button>
        )}
      </Box>
      
      <Grid container spacing={4}>
        <Grid item xs={12} md={8}>
          <Paper sx={{ p: 3, mb: 4 }}>
            <Typography variant="h6" gutterBottom>
              About
            </Typography>
            <Typography variant="body1" paragraph>
              {school.description}
            </Typography>
            
            <Box sx={{ display: 'flex', alignItems: 'center', mt: 2 }}>
              <LocationIcon color="action" sx={{ mr: 1 }} />
              <Typography variant="body1">
                {school.location}
              </Typography>
            </Box>
            
            <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
              <CalendarIcon color="action" sx={{ mr: 1 }} />
              <Typography variant="body2" color="text.secondary">
                Added on {formatDate(school.created_at)}
              </Typography>
            </Box>
          </Paper>
          
          <Paper sx={{ p: 3 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
              <Typography variant="h6">
                Courses
              </Typography>
              
              {isAdmin && (
                <Button
                  variant="outlined"
                  size="small"
                  component={RouterLink}
                  to="/courses/new"
                  state={{ schoolId: school.id }}
                >
                  Add Course
                </Button>
              )}
            </Box>
            
            {isLoadingCourses ? (
              <Box sx={{ display: 'flex', justifyContent: 'center', my: 4 }}>
                <CircularProgress />
              </Box>
            ) : courses.length === 0 ? (
              <Box sx={{ textAlign: 'center', py: 4 }}>
                <SchoolIcon sx={{ fontSize: 48, color: 'text.secondary', mb: 2 }} />
                <Typography variant="body1" color="text.secondary">
                  No courses available for this school yet.
                </Typography>
                {isAdmin && (
                  <Button
                    variant="contained"
                    size="small"
                    component={RouterLink}
                    to="/courses/new"
                    state={{ schoolId: school.id }}
                    sx={{ mt: 2 }}
                  >
                    Add First Course
                  </Button>
                )}
              </Box>
            ) : (
              <Grid container spacing={2}>
                {courses.map((course) => (
                  <Grid item xs={12} sm={6} key={course.id}>
                    <Card variant="outlined">
                      <CardHeader
                        title={course.name}
                        subheader={course.code}
                        action={
                          <Chip 
                            label={course.is_active ? 'Active' : 'Inactive'} 
                            color={course.is_active ? 'success' : 'default'}
                            size="small"
                          />
                        }
                      />
                      <Divider />
                      <CardContent>
                        <Typography variant="body2" color="text.secondary">
                          {course.description.substring(0, 100)}
                          {course.description.length > 100 ? '...' : ''}
                        </Typography>
                        <Button 
                          size="small" 
                          sx={{ mt: 1 }}
                          component={RouterLink}
                          to={`/courses/${course.id}`}
                        >
                          View Course
                        </Button>
                      </CardContent>
                    </Card>
                  </Grid>
                ))}
              </Grid>
            )}
          </Paper>
        </Grid>
        
        <Grid item xs={12} md={4}>
          <Paper sx={{ p: 3, mb: 4 }}>
            <Typography variant="h6" gutterBottom>
              School Information
            </Typography>
            
            <Box sx={{ mt: 2 }}>
              <Typography variant="subtitle2" color="text.secondary">
                Status
              </Typography>
              <Chip 
                label={school.is_active ? 'Active' : 'Inactive'} 
                color={school.is_active ? 'success' : 'default'}
                sx={{ mt: 1 }}
              />
            </Box>
            
            <Box sx={{ mt: 3 }}>
              <Typography variant="subtitle2" color="text.secondary">
                Created
              </Typography>
              <Typography variant="body2">
                {formatDate(school.created_at)}
              </Typography>
            </Box>
            
            <Box sx={{ mt: 3 }}>
              <Typography variant="subtitle2" color="text.secondary">
                Last Updated
              </Typography>
              <Typography variant="body2">
                {formatDate(school.updated_at)}
              </Typography>
            </Box>
            
            <Box sx={{ mt: 3 }}>
              <Typography variant="subtitle2" color="text.secondary">
                Courses
              </Typography>
              <Typography variant="body2">
                {isLoadingCourses ? (
                  <CircularProgress size={16} sx={{ ml: 1 }} />
                ) : (
                  courses.length
                )}
              </Typography>
            </Box>
          </Paper>
          
          {isAdmin && (
            <Paper sx={{ p: 3 }}>
              <Typography variant="h6" gutterBottom>
                Admin Actions
              </Typography>
              
              <Button
                fullWidth
                variant="outlined"
                color="primary"
                component={RouterLink}
                to={`/schools/${school.id}/edit`}
                sx={{ mb: 2 }}
              >
                Edit School
              </Button>
              
              <Button
                fullWidth
                variant="outlined"
                component={RouterLink}
                to="/courses/new"
                state={{ schoolId: school.id }}
              >
                Add Course
              </Button>
            </Paper>
          )}
        </Grid>
      </Grid>
    </Box>
  );
};

export default SchoolDetail;
