import React, { useState } from 'react';
import { 
  Box, 
  Typography, 
  Button, 
  Paper, 
  Table, 
  TableBody, 
  TableCell, 
  TableContainer, 
  TableHead, 
  TableRow,
  TablePagination,
  IconButton,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  CircularProgress,
  Alert,
  TextField,
  InputAdornment,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  SelectChangeEvent
} from '@mui/material';
import { 
  Add as AddIcon, 
  Edit as EditIcon, 
  Delete as DeleteIcon,
  Visibility as ViewIcon,
  Search as SearchIcon,
  School as SchoolIcon
} from '@mui/icons-material';
import { Link as RouterLink, useNavigate } from 'react-router-dom';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { getDepartments, deleteDepartment, Department } from '../../api/departments';
import { getSchools } from '../../api/schools';
import { useAuth } from '../../contexts/AuthContext';
import { useContextualNavigation } from '../../hooks/useAdminContext';

const DepartmentsList: React.FC = () => {
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const { user } = useAuth();
  const { getPath } = useContextualNavigation();
  
  const isAdmin = user?.role === 'admin';
  const canManageDepartments = isAdmin;
  
  // State for pagination
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(10);
  
  // State for filtering
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedSchool, setSelectedSchool] = useState<string>('');
  const [activeOnly, setActiveOnly] = useState<boolean>(true);
  
  // State for delete confirmation dialog
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [departmentToDelete, setDepartmentToDelete] = useState<Department | null>(null);

  // Fetch departments data
  const { 
    data: departments = [], 
    isLoading: isLoadingDepartments, 
    error: departmentsError 
  } = useQuery({
    queryKey: ['departments'],
    queryFn: getDepartments
  });

  // Fetch schools data for filtering
  const { 
    data: schools = [], 
    isLoading: isLoadingSchools 
  } = useQuery({
    queryKey: ['schools'],
    queryFn: getSchools
  });

  // Delete department mutation
  const deleteMutation = useMutation({
    mutationFn: (id: number) => deleteDepartment(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['departments'] });
      setDeleteDialogOpen(false);
      setDepartmentToDelete(null);
    }
  });

  // Handle page change
  const handleChangePage = (event: unknown, newPage: number) => {
    setPage(newPage);
  };

  // Handle rows per page change
  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  // Handle school filter change
  const handleSchoolChange = (event: SelectChangeEvent) => {
    setSelectedSchool(event.target.value);
    setPage(0);
  };

  // Handle active only filter change
  const handleActiveOnlyChange = (event: SelectChangeEvent) => {
    setActiveOnly(event.target.value === 'true');
    setPage(0);
  };

  // Handle delete dialog open
  const handleDeleteClick = (department: Department) => {
    setDepartmentToDelete(department);
    setDeleteDialogOpen(true);
  };

  // Handle delete confirmation
  const handleDeleteConfirm = () => {
    if (departmentToDelete) {
      deleteMutation.mutate(departmentToDelete.id);
    }
  };

  // Filter departments based on search term and filters
  const filteredDepartments = departments.filter(department => {
    const matchesSearch = department.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         department.description.toLowerCase().includes(searchTerm.toLowerCase());
    
    const matchesSchool = selectedSchool ? department.school_id.toString() === selectedSchool : true;
    
    const matchesActive = activeOnly ? department.is_active : true;
    
    return matchesSearch && matchesSchool && matchesActive;
  });

  // Get school name by ID
  const getSchoolName = (schoolId: number) => {
    const school = schools.find(s => s.id === schoolId);
    return school ? school.name : 'Unknown School';
  };

  // Format date
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString();
  };

  if (departmentsError) {
    return (
      <Alert severity="error" sx={{ mt: 2 }}>
        Error loading departments: {(departmentsError as Error).message}
      </Alert>
    );
  }

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" component="h1">
          Departments
        </Typography>
        
        {canManageDepartments && (
          <Button
            variant="contained"
            color="primary"
            startIcon={<AddIcon />}
            component={RouterLink}
            to={getPath("/departments/new")}
          >
            Add Department
          </Button>
        )}
      </Box>
      
      {/* Filters */}
      <Paper sx={{ p: 2, mb: 3 }}>
        <Box sx={{ display: 'flex', flexDirection: { xs: 'column', md: 'row' }, gap: 2 }}>
          <TextField
            label="Search Departments"
            variant="outlined"
            size="small"
            fullWidth
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon />
                </InputAdornment>
              ),
            }}
          />
          
          <FormControl variant="outlined" size="small" sx={{ minWidth: 200 }}>
            <InputLabel id="school-filter-label">School</InputLabel>
            <Select
              labelId="school-filter-label"
              id="school-filter"
              value={selectedSchool}
              onChange={handleSchoolChange}
              label="School"
            >
              <MenuItem value="">
                <em>All Schools</em>
              </MenuItem>
              {schools.map((school) => (
                <MenuItem key={school.id} value={school.id.toString()}>
                  {school.name}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
          
          <FormControl variant="outlined" size="small" sx={{ minWidth: 150 }}>
            <InputLabel id="active-filter-label">Status</InputLabel>
            <Select
              labelId="active-filter-label"
              id="active-filter"
              value={activeOnly.toString()}
              onChange={handleActiveOnlyChange}
              label="Status"
            >
              <MenuItem value="true">Active Only</MenuItem>
              <MenuItem value="false">All</MenuItem>
            </Select>
          </FormControl>
        </Box>
      </Paper>
      
      {/* Departments Table */}
      <Paper>
        <TableContainer>
          <Table>
            <TableHead>
              <TableRow>
                <TableCell>Name</TableCell>
                <TableCell>School</TableCell>
                <TableCell>Status</TableCell>
                <TableCell>Created</TableCell>
                <TableCell align="right">Actions</TableCell>
              </TableRow>
            </TableHead>
            <TableBody>
              {isLoadingDepartments ? (
                <TableRow>
                  <TableCell colSpan={5} align="center" sx={{ py: 3 }}>
                    <CircularProgress />
                  </TableCell>
                </TableRow>
              ) : filteredDepartments.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={5} align="center" sx={{ py: 3 }}>
                    No departments found
                  </TableCell>
                </TableRow>
              ) : (
                filteredDepartments
                  .slice(page * rowsPerPage, page * rowsPerPage + rowsPerPage)
                  .map((department) => (
                    <TableRow key={department.id}>
                      <TableCell>
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          <Typography variant="body1" component="div">
                            {department.name}
                          </Typography>
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Box sx={{ display: 'flex', alignItems: 'center' }}>
                          <SchoolIcon fontSize="small" sx={{ mr: 1, color: 'text.secondary' }} />
                          <Typography variant="body2">
                            {getSchoolName(department.school_id)}
                          </Typography>
                        </Box>
                      </TableCell>
                      <TableCell>
                        <Chip 
                          label={department.is_active ? 'Active' : 'Inactive'} 
                          color={department.is_active ? 'success' : 'default'}
                          size="small"
                        />
                      </TableCell>
                      <TableCell>
                        {formatDate(department.created_at)}
                      </TableCell>
                      <TableCell align="right">
                        <IconButton
                          component={RouterLink}
                          to={getPath(`/departments/${department.id}`)}
                          color="primary"
                          size="small"
                        >
                          <ViewIcon />
                        </IconButton>
                        
                        {canManageDepartments && (
                          <>
                            <IconButton
                              component={RouterLink}
                              to={getPath(`/departments/${department.id}/edit`)}
                              color="primary"
                              size="small"
                            >
                              <EditIcon />
                            </IconButton>
                            
                            <IconButton
                              onClick={() => handleDeleteClick(department)}
                              color="error"
                              size="small"
                            >
                              <DeleteIcon />
                            </IconButton>
                          </>
                        )}
                      </TableCell>
                    </TableRow>
                  ))
              )}
            </TableBody>
          </Table>
        </TableContainer>
        
        <TablePagination
          rowsPerPageOptions={[5, 10, 25]}
          component="div"
          count={filteredDepartments.length}
          rowsPerPage={rowsPerPage}
          page={page}
          onPageChange={handleChangePage}
          onRowsPerPageChange={handleChangeRowsPerPage}
        />
      </Paper>
      
      {/* Delete Confirmation Dialog */}
      <Dialog
        open={deleteDialogOpen}
        onClose={() => setDeleteDialogOpen(false)}
      >
        <DialogTitle>Confirm Delete</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Are you sure you want to delete the department "{departmentToDelete?.name}"? 
            This action cannot be undone.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialogOpen(false)}>Cancel</Button>
          <Button 
            onClick={handleDeleteConfirm} 
            color="error"
            disabled={deleteMutation.isPending}
          >
            {deleteMutation.isPending ? <CircularProgress size={24} /> : 'Delete'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default DepartmentsList;
