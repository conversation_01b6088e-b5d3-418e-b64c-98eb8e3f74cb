import React from 'react';
import { 
  Box, 
  Typography, 
  Paper, 
  Grid, 
  Button, 
  Chip,
  Divider,
  CircularProgress,
  Alert,
  List,
  ListItem,
  ListItemText,
  ListItemIcon
} from '@mui/material';
import { 
  Edit as EditIcon, 
  ArrowBack as ArrowBackIcon,
  School as SchoolIcon,
  Book as BookIcon,
  CalendarToday as CalendarIcon,
  Add as AddIcon
} from '@mui/icons-material';
import { Link as RouterLink, useParams, useNavigate } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';
import { getDepartment } from '../../api/departments';
import { getSchool } from '../../api/schools';
import { getCourses } from '../../api/courses';
import { useAuth } from '../../contexts/AuthContext';

const DepartmentDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const departmentId = parseInt(id || '0');
  const navigate = useNavigate();
  const { user } = useAuth();
  
  const isAdmin = user?.role === 'admin';
  const canManageDepartment = isAdmin;

  // Fetch department data
  const { 
    data: department, 
    isLoading: isLoadingDepartment, 
    error: departmentError 
  } = useQuery({
    queryKey: ['department', departmentId],
    queryFn: () => getDepartment(departmentId),
    enabled: !!departmentId
  });

  // Fetch school data
  const { 
    data: school, 
    isLoading: isLoadingSchool 
  } = useQuery({
    queryKey: ['school', department?.school_id],
    queryFn: () => getSchool(department!.school_id),
    enabled: !!department?.school_id
  });

  // Fetch courses for this department
  const { 
    data: courses = [], 
    isLoading: isLoadingCourses 
  } = useQuery({
    queryKey: ['courses', { departmentId }],
    queryFn: () => getCourses(undefined, departmentId),
    enabled: !!departmentId
  });

  // Format date
  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  if (isLoadingDepartment) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
        <CircularProgress />
      </Box>
    );
  }

  if (departmentError) {
    return (
      <Alert severity="error" sx={{ mt: 2 }}>
        Error loading department: {(departmentError as Error).message}
      </Alert>
    );
  }

  if (!department) {
    return (
      <Alert severity="error" sx={{ mt: 2 }}>
        Department not found
      </Alert>
    );
  }

  return (
    <Box>
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
        <Button
          startIcon={<ArrowBackIcon />}
          onClick={() => navigate('/departments')}
          sx={{ mr: 2 }}
        >
          Back to Departments
        </Button>
        
        <Typography variant="h4" component="h1" sx={{ flexGrow: 1 }}>
          {department.name}
        </Typography>
        
        {canManageDepartment && (
          <Button
            variant="contained"
            color="primary"
            startIcon={<EditIcon />}
            component={RouterLink}
            to={`/departments/${department.id}/edit`}
          >
            Edit Department
          </Button>
        )}
      </Box>
      
      <Grid container spacing={4}>
        <Grid item xs={12} md={8}>
          <Paper sx={{ p: 3, mb: 4 }}>
            <Typography variant="h6" gutterBottom>
              About
            </Typography>
            <Typography variant="body1" paragraph>
              {department.description}
            </Typography>
            
            <Divider sx={{ my: 3 }} />
            
            <Typography variant="h6" gutterBottom>
              School
            </Typography>
            
            {isLoadingSchool ? (
              <CircularProgress size={24} />
            ) : school ? (
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <SchoolIcon color="action" sx={{ mr: 1 }} />
                <Typography variant="body1">
                  {school.name}
                </Typography>
              </Box>
            ) : (
              <Typography variant="body2" color="text.secondary">
                School information not available
              </Typography>
            )}
            
            <Box sx={{ display: 'flex', alignItems: 'center', mt: 1 }}>
              <CalendarIcon color="action" sx={{ mr: 1 }} />
              <Typography variant="body2" color="text.secondary">
                Added on {formatDate(department.created_at)}
              </Typography>
            </Box>
          </Paper>
          
          <Paper sx={{ p: 3 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
              <Typography variant="h6">
                Courses
              </Typography>
              
              {canManageDepartment && (
                <Button
                  variant="outlined"
                  size="small"
                  startIcon={<AddIcon />}
                  component={RouterLink}
                  to="/courses/new"
                  state={{ departmentId: department.id, schoolId: department.school_id }}
                >
                  Add Course
                </Button>
              )}
            </Box>
            
            {isLoadingCourses ? (
              <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
                <CircularProgress />
              </Box>
            ) : courses.length === 0 ? (
              <Box sx={{ textAlign: 'center', py: 4 }}>
                <BookIcon sx={{ fontSize: 48, color: 'text.secondary', mb: 2 }} />
                <Typography variant="body1" color="text.secondary">
                  No courses in this department yet.
                </Typography>
                {canManageDepartment && (
                  <Button
                    variant="contained"
                    startIcon={<AddIcon />}
                    component={RouterLink}
                    to="/courses/new"
                    state={{ departmentId: department.id, schoolId: department.school_id }}
                    sx={{ mt: 2 }}
                  >
                    Add First Course
                  </Button>
                )}
              </Box>
            ) : (
              <List>
                {courses.map((course) => (
                  <React.Fragment key={course.id}>
                    <ListItem 
                      button 
                      component={RouterLink} 
                      to={`/courses/${course.id}`}
                    >
                      <ListItemIcon>
                        <BookIcon />
                      </ListItemIcon>
                      <ListItemText 
                        primary={
                          <Box sx={{ display: 'flex', alignItems: 'center' }}>
                            <Typography variant="body1">{course.name}</Typography>
                            <Typography variant="body2" color="text.secondary" sx={{ ml: 1 }}>
                              ({course.code})
                            </Typography>
                          </Box>
                        }
                        secondary={
                          <Typography variant="body2" color="text.secondary" sx={{ 
                            overflow: 'hidden',
                            textOverflow: 'ellipsis',
                            display: '-webkit-box',
                            WebkitLineClamp: 2,
                            WebkitBoxOrient: 'vertical',
                          }}>
                            {course.description}
                          </Typography>
                        }
                      />
                    </ListItem>
                    <Divider variant="inset" component="li" />
                  </React.Fragment>
                ))}
              </List>
            )}
          </Paper>
        </Grid>
        
        <Grid item xs={12} md={4}>
          <Paper sx={{ p: 3, mb: 4 }}>
            <Typography variant="h6" gutterBottom>
              Department Information
            </Typography>
            
            <Box sx={{ mt: 3 }}>
              <Typography variant="subtitle2" color="text.secondary">
                School
              </Typography>
              <Typography variant="body1">
                {isLoadingSchool ? (
                  <CircularProgress size={16} />
                ) : school ? (
                  <Button 
                    component={RouterLink} 
                    to={`/schools/${school.id}`}
                    size="small"
                    sx={{ p: 0, minWidth: 0, textTransform: 'none' }}
                  >
                    {school.name}
                  </Button>
                ) : (
                  'Unknown School'
                )}
              </Typography>
            </Box>
            
            <Box sx={{ mt: 3 }}>
              <Typography variant="subtitle2" color="text.secondary">
                Status
              </Typography>
              <Chip 
                label={department.is_active ? 'Active' : 'Inactive'} 
                color={department.is_active ? 'success' : 'default'}
                sx={{ mt: 1 }}
              />
            </Box>
            
            <Box sx={{ mt: 3 }}>
              <Typography variant="subtitle2" color="text.secondary">
                Created
              </Typography>
              <Typography variant="body2">
                {formatDate(department.created_at)}
              </Typography>
            </Box>
            
            <Box sx={{ mt: 3 }}>
              <Typography variant="subtitle2" color="text.secondary">
                Last Updated
              </Typography>
              <Typography variant="body2">
                {formatDate(department.updated_at)}
              </Typography>
            </Box>
            
            <Box sx={{ mt: 3 }}>
              <Typography variant="subtitle2" color="text.secondary">
                Courses
              </Typography>
              <Typography variant="body2">
                {isLoadingCourses ? (
                  <CircularProgress size={16} sx={{ ml: 1 }} />
                ) : (
                  courses.length
                )}
              </Typography>
            </Box>
          </Paper>
          
          {canManageDepartment && (
            <Paper sx={{ p: 3 }}>
              <Typography variant="h6" gutterBottom>
                Admin Actions
              </Typography>
              
              <Button
                fullWidth
                variant="outlined"
                color="primary"
                component={RouterLink}
                to={`/departments/${department.id}/edit`}
                sx={{ mb: 2 }}
              >
                Edit Department
              </Button>
              
              <Button
                fullWidth
                variant="outlined"
                component={RouterLink}
                to="/courses/new"
                state={{ departmentId: department.id, schoolId: department.school_id }}
              >
                Add Course
              </Button>
            </Paper>
          )}
        </Grid>
      </Grid>
    </Box>
  );
};

export default DepartmentDetail;
