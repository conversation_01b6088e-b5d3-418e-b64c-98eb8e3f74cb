import React, { useState, useEffect } from 'react';
import { 
  Box, 
  Typography, 
  Paper, 
  TextField, 
  Button, 
  Grid,
  FormControlLabel,
  Switch,
  CircularProgress,
  Alert,
  Snackbar,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  FormHelperText
} from '@mui/material';
import { 
  Save as SaveIcon, 
  ArrowBack as ArrowBackIcon 
} from '@mui/icons-material';
import { useNavigate, useParams, useLocation } from 'react-router-dom';
import { useFormik } from 'formik';
import * as Yup from 'yup';
import { useMutation, useQuery, useQueryClient } from '@tanstack/react-query';
import { getDepartment, createDepartment, updateDepartment, DepartmentCreate, DepartmentUpdate } from '../../api/departments';
import { getSchools } from '../../api/schools';
import { useContextualNavigation } from '../../hooks/useAdminContext';

// Validation schema
const validationSchema = Yup.object({
  name: Yup.string()
    .required('Name is required')
    .max(100, 'Name must be at most 100 characters'),
  description: Yup.string()
    .required('Description is required')
    .max(1000, 'Description must be at most 1000 characters'),
  school_id: Yup.number()
    .required('School is required'),
  is_active: Yup.boolean()
});

const DepartmentForm: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const location = useLocation();
  const isEditMode = !!id;
  const departmentId = parseInt(id || '0');
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const { getPath } = useContextualNavigation();
  
  // Get schoolId from location state if available (for creating a department from school page)
  const initialSchoolId = location.state?.schoolId;
  
  // State for success message
  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  // Fetch department data if in edit mode
  const { 
    data: department, 
    isLoading: isLoadingDepartment, 
    error: departmentError 
  } = useQuery({
    queryKey: ['department', departmentId],
    queryFn: () => getDepartment(departmentId),
    enabled: isEditMode
  });

  // Fetch schools for dropdown
  const { 
    data: schools = [], 
    isLoading: isLoadingSchools 
  } = useQuery({
    queryKey: ['schools'],
    queryFn: getSchools
  });

  // Create department mutation
  const createMutation = useMutation({
    mutationFn: (data: DepartmentCreate) => createDepartment(data),
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['departments'] });
      setSuccessMessage('Department created successfully');
      setTimeout(() => {
        navigate(getPath(`/departments/${data.id}`));
      }, 1500);
    }
  });

  // Update department mutation
  const updateMutation = useMutation({
    mutationFn: ({ id, data }: { id: number; data: DepartmentUpdate }) => 
      updateDepartment(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['departments'] });
      queryClient.invalidateQueries({ queryKey: ['department', departmentId] });
      setSuccessMessage('Department updated successfully');
      setTimeout(() => {
        navigate(getPath(`/departments/${departmentId}`));
      }, 1500);
    }
  });

  // Setup formik
  const formik = useFormik({
    initialValues: {
      name: '',
      description: '',
      school_id: initialSchoolId || '',
      is_active: true
    },
    validationSchema,
    onSubmit: (values) => {
      const departmentData = {
        ...values,
        school_id: Number(values.school_id)
      };
      
      if (isEditMode) {
        updateMutation.mutate({ id: departmentId, data: departmentData });
      } else {
        createMutation.mutate(departmentData);
      }
    },
    enableReinitialize: true
  });

  // Update form values when department data is loaded
  useEffect(() => {
    if (department) {
      formik.setValues({
        name: department.name,
        description: department.description,
        school_id: department.school_id.toString(),
        is_active: department.is_active
      });
    }
  }, [department]);

  // Handle loading and error states
  if (isEditMode && isLoadingDepartment) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
        <CircularProgress />
      </Box>
    );
  }

  if (isEditMode && departmentError) {
    return (
      <Alert severity="error" sx={{ mt: 2 }}>
        Error loading department: {(departmentError as Error).message}
      </Alert>
    );
  }

  const isSubmitting = createMutation.isPending || updateMutation.isPending;
  const error = createMutation.error || updateMutation.error;

  return (
    <Box>
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
        <Button
          startIcon={<ArrowBackIcon />}
          onClick={() => navigate(isEditMode ? getPath(`/departments/${departmentId}`) : getPath('/departments'))}
          sx={{ mr: 2 }}
        >
          {isEditMode ? 'Back to Department' : 'Back to Departments'}
        </Button>
        
        <Typography variant="h4" component="h1">
          {isEditMode ? 'Edit Department' : 'Add New Department'}
        </Typography>
      </Box>
      
      <Paper sx={{ p: 3 }}>
        {error && (
          <Alert severity="error" sx={{ mb: 3 }}>
            {(error as Error).message || 'An error occurred. Please try again.'}
          </Alert>
        )}
        
        <form onSubmit={formik.handleSubmit}>
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <TextField
                fullWidth
                id="name"
                name="name"
                label="Department Name"
                variant="outlined"
                value={formik.values.name}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                error={formik.touched.name && Boolean(formik.errors.name)}
                helperText={formik.touched.name && formik.errors.name}
                disabled={isSubmitting}
              />
            </Grid>
            
            <Grid item xs={12}>
              <FormControl 
                fullWidth 
                error={formik.touched.school_id && Boolean(formik.errors.school_id)}
                disabled={isLoadingSchools || isSubmitting}
              >
                <InputLabel id="school-label">School</InputLabel>
                <Select
                  labelId="school-label"
                  id="school_id"
                  name="school_id"
                  value={formik.values.school_id}
                  label="School"
                  onChange={formik.handleChange}
                  onBlur={formik.handleBlur}
                >
                  {schools.map((school) => (
                    <MenuItem key={school.id} value={school.id.toString()}>
                      {school.name}
                    </MenuItem>
                  ))}
                </Select>
                {formik.touched.school_id && formik.errors.school_id && (
                  <FormHelperText>{formik.errors.school_id as string}</FormHelperText>
                )}
              </FormControl>
            </Grid>
            
            <Grid item xs={12}>
              <TextField
                fullWidth
                id="description"
                name="description"
                label="Description"
                variant="outlined"
                multiline
                rows={4}
                value={formik.values.description}
                onChange={formik.handleChange}
                onBlur={formik.handleBlur}
                error={formik.touched.description && Boolean(formik.errors.description)}
                helperText={formik.touched.description && formik.errors.description}
                disabled={isSubmitting}
              />
            </Grid>
            
            <Grid item xs={12}>
              <FormControlLabel
                control={
                  <Switch
                    id="is_active"
                    name="is_active"
                    checked={formik.values.is_active}
                    onChange={formik.handleChange}
                    disabled={isSubmitting}
                  />
                }
                label="Active"
              />
            </Grid>
            
            <Grid item xs={12} sx={{ display: 'flex', justifyContent: 'flex-end', gap: 2 }}>
              <Button
                variant="outlined"
                onClick={() => navigate(isEditMode ? getPath(`/departments/${departmentId}`) : getPath('/departments'))}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
              
              <Button
                type="submit"
                variant="contained"
                color="primary"
                startIcon={isSubmitting ? <CircularProgress size={24} /> : <SaveIcon />}
                disabled={isSubmitting}
              >
                {isEditMode ? 'Update Department' : 'Create Department'}
              </Button>
            </Grid>
          </Grid>
        </form>
      </Paper>
      
      {/* Success message */}
      <Snackbar
        open={!!successMessage}
        autoHideDuration={3000}
        onClose={() => setSuccessMessage(null)}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert severity="success" onClose={() => setSuccessMessage(null)}>
          {successMessage}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default DepartmentForm;
