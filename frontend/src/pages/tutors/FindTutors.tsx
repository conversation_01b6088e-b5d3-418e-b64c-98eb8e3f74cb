import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Button,
  Grid,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  Avatar,
  Rating,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  useTheme,
  useMediaQuery,
  InputAdornment,
  CircularProgress,
  Alert
} from '@mui/material';
import {
  Search as SearchIcon,
  LocationOn as LocationIcon,
  Language as LanguageIcon,
  School as SchoolIcon,
  Send as SendIcon,
  Star as StarIcon,
  Visibility as ViewIcon
} from '@mui/icons-material';
import { useQuery } from '@tanstack/react-query';
import { getTutors, searchTutors, TutorProfile, TutorSearchFilters } from '../../api/tutors';
import { getCourses, Course } from '../../api/courses';
import SimpleBookingButton from '../../components/SimpleBookingButton';

const FindTutors: React.FC = () => {
  const theme = useTheme();
  const navigate = useNavigate();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  
  const [searchFilters, setSearchFilters] = useState<TutorSearchFilters>({
    is_available: true
  });
  const [searchTerm, setSearchTerm] = useState('');
  // Removed selectedTutor and bookingDialogOpen state - no longer needed with SimpleBookingButton

  // Fetch courses for filter
  const { data: courses = [] } = useQuery<Course[]>({
    queryKey: ['courses'],
    queryFn: getCourses
  });

  // Fetch tutors
  const { 
    data: tutors = [], 
    isLoading, 
    error,
    refetch 
  } = useQuery<TutorProfile[]>({
    queryKey: ['tutors', searchFilters],
    queryFn: () => {
      if (Object.keys(searchFilters).length > 1 || !searchFilters.is_available) {
        return searchTutors(searchFilters);
      }
      return getTutors();
    }
  });

  const handleSearch = () => {
    refetch();
  };

  // Removed handleBookingRequest - no longer needed with SimpleBookingButton

  const handleFilterChange = (field: keyof TutorSearchFilters, value: any) => {
    setSearchFilters(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const clearFilters = () => {
    setSearchFilters({ is_available: true });
    setSearchTerm('');
  };

  if (error) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="error">
          Failed to load tutors. Please try again later.
        </Alert>
      </Box>
    );
  }

  return (
    <Box sx={{ p: { xs: 2, md: 3 } }}>
      <Typography variant="h4" component="h1" gutterBottom>
        Find Tutors
      </Typography>
      <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
        Connect with experienced tutors for personalized learning
      </Typography>

      {/* Search and Filters */}
      <Card sx={{ mb: 3 }}>
        <CardContent>
          <Grid container spacing={2} alignItems="center">
            <Grid item xs={12} md={4}>
              <TextField
                fullWidth
                placeholder="Search tutors..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                InputProps={{
                  startAdornment: (
                    <InputAdornment position="start">
                      <SearchIcon />
                    </InputAdornment>
                  ),
                }}
                size="small"
              />
            </Grid>
            
            <Grid item xs={12} sm={6} md={2}>
              <FormControl fullWidth size="small">
                <InputLabel>Subject</InputLabel>
                <Select
                  value={searchFilters.course_id || ''}
                  onChange={(e) => handleFilterChange('course_id', e.target.value || undefined)}
                  label="Subject"
                >
                  <MenuItem value="">All Subjects</MenuItem>
                  {courses.map((course) => (
                    <MenuItem key={course.id} value={course.id}>
                      {course.name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} sm={6} md={2}>
              <FormControl fullWidth size="small">
                <InputLabel>Min Rating</InputLabel>
                <Select
                  value={searchFilters.min_rating || ''}
                  onChange={(e) => handleFilterChange('min_rating', e.target.value || undefined)}
                  label="Min Rating"
                >
                  <MenuItem value="">Any Rating</MenuItem>
                  <MenuItem value={4}>4+ Stars</MenuItem>
                  <MenuItem value={4.5}>4.5+ Stars</MenuItem>
                  <MenuItem value={5}>5 Stars</MenuItem>
                </Select>
              </FormControl>
            </Grid>

            <Grid item xs={12} sm={6} md={2}>
              <TextField
                fullWidth
                type="number"
                label="Max Rate ($/hr)"
                value={searchFilters.max_hourly_rate || ''}
                onChange={(e) => handleFilterChange('max_hourly_rate', e.target.value ? Number(e.target.value) : undefined)}
                size="small"
              />
            </Grid>

            <Grid item xs={12} sm={6} md={2}>
              <Box sx={{ display: 'flex', gap: 1 }}>
                <Button
                  variant="contained"
                  onClick={handleSearch}
                  disabled={isLoading}
                  fullWidth={isMobile}
                >
                  Search
                </Button>
                <Button
                  variant="outlined"
                  onClick={clearFilters}
                  fullWidth={isMobile}
                >
                  Clear
                </Button>
              </Box>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Loading State */}
      {isLoading && (
        <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
          <CircularProgress />
        </Box>
      )}

      {/* Tutors Grid */}
      {!isLoading && (
        <Grid container spacing={3}>
          {tutors.map((tutor) => (
            <Grid item xs={12} sm={6} md={4} key={tutor.id}>
              <Card
                sx={{
                  height: '100%',
                  display: 'flex',
                  flexDirection: 'column',
                  cursor: 'pointer',
                  transition: 'all 0.2s ease-in-out',
                  '&:hover': {
                    transform: 'translateY(-4px)',
                    boxShadow: theme.shadows[8]
                  }
                }}
                onClick={() => navigate(`/tutors/${tutor.id}`)}
              >
                <CardContent sx={{ flexGrow: 1 }}>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <Avatar
                      src={tutor.user?.profile_picture_url}
                      sx={{ mr: 2, bgcolor: theme.palette.primary.main }}
                    >
                      {tutor.user?.full_name?.charAt(0) || 'T'}
                    </Avatar>
                    <Box>
                      <Typography variant="h6" component="h3">
                        {tutor.user?.full_name || 'Tutor'}
                      </Typography>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                        <Rating value={tutor.average_rating || 0} readOnly size="small" />
                        <Typography variant="body2" color="text.secondary">
                          ({tutor.total_reviews || 0})
                        </Typography>
                      </Box>
                    </Box>
                  </Box>

                  {tutor.bio && (
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                      {tutor.bio.length > 100 ? `${tutor.bio.substring(0, 100)}...` : tutor.bio}
                    </Typography>
                  )}

                  <Box sx={{ mb: 2 }}>
                    {tutor.specializations?.slice(0, 3).map((course) => (
                      <Chip
                        key={course.id}
                        label={course.name}
                        size="small"
                        sx={{ mr: 0.5, mb: 0.5 }}
                      />
                    ))}
                    {tutor.specializations && tutor.specializations.length > 3 && (
                      <Chip
                        label={`+${tutor.specializations.length - 3} more`}
                        size="small"
                        variant="outlined"
                        sx={{ mr: 0.5, mb: 0.5 }}
                      />
                    )}
                  </Box>

                  <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                    {tutor.location && (
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                        <LocationIcon fontSize="small" color="action" />
                        <Typography variant="body2" color="text.secondary">
                          {tutor.location}
                        </Typography>
                      </Box>
                    )}
                    
                    {tutor.experience_years && (
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                        <SchoolIcon fontSize="small" color="action" />
                        <Typography variant="body2" color="text.secondary">
                          {tutor.experience_years} years experience
                        </Typography>
                      </Box>
                    )}

                    {tutor.hourly_rate && (
                      <Typography variant="h6" color="primary" sx={{ mt: 1 }}>
                        ${tutor.hourly_rate}/hr
                      </Typography>
                    )}
                  </Box>
                </CardContent>

                <Box
                  sx={{ p: 2, pt: 0, display: 'flex', flexDirection: 'column', gap: 1 }}
                  onClick={(e) => e.stopPropagation()}
                >
                  <Button
                    variant="outlined"
                    fullWidth
                    startIcon={<ViewIcon />}
                    onClick={() => navigate(`/tutors/${tutor.id}`)}
                    sx={{ mb: 1 }}
                  >
                    View Profile
                  </Button>
                  <SimpleBookingButton
                    tutor={tutor}
                    disabled={!tutor.is_available}
                    fullWidth
                  />
                </Box>
              </Card>
            </Grid>
          ))}
        </Grid>
      )}

      {/* No Results */}
      {!isLoading && tutors.length === 0 && (
        <Box sx={{ textAlign: 'center', py: 4 }}>
          <Typography variant="h6" color="text.secondary">
            No tutors found
          </Typography>
          <Typography variant="body2" color="text.secondary">
            Try adjusting your search filters
          </Typography>
        </Box>
      )}

      {/* No longer need the booking dialog - SimpleBookingButton handles everything */}
    </Box>
  );
};

export default FindTutors;
