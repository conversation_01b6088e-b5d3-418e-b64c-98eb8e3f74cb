import React, { useState } from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  CardHeader,
  Button,
  Divider,
  Chip,
  CircularProgress,
  useTheme,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  IconButton,
  Tooltip,
  Alert,
} from '@mui/material';
import {
  CheckCircle as CorrectIcon,
  Cancel as IncorrectIcon,
  ArrowBack as ArrowBackIcon,
  School as SchoolIcon,
  Timer as TimerIcon,
} from '@mui/icons-material';
import { useParams, useNavigate } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';
import { getExamWithAttempts } from '../../api/studentProgress';
import { getCourse } from '../../api/courses';
import { getQuestion } from '../../api/questions';
import {
  Pie<PERSON><PERSON>,
  Pie,
  Cell,
  ResponsiveContainer,
  Tooltip as RechartsTooltip,
  Legend,
} from 'recharts';

const ExamDetails: React.FC = () => {
  const theme = useTheme();
  const navigate = useNavigate();
  const { examId } = useParams<{ examId: string }>();
  const [loadingQuestions, setLoadingQuestions] = useState<boolean>(false);
  const [questions, setQuestions] = useState<Record<number, any>>({});

  // Fetch exam details with attempts
  const {
    data: examWithAttempts,
    isLoading: isLoadingExam,
    error: examError,
  } = useQuery({
    queryKey: ['exam', examId],
    queryFn: () => getExamWithAttempts(parseInt(examId!)),
    enabled: !!examId,
  });

  // Fetch course details
  const {
    data: course,
    isLoading: isLoadingCourse,
    error: courseError,
  } = useQuery({
    queryKey: ['course', examWithAttempts?.course_id],
    queryFn: () => getCourse(examWithAttempts!.course_id),
    enabled: !!examWithAttempts?.course_id,
  });

  // Load question details for an attempt
  const loadQuestionDetails = async (questionId: number) => {
    if (questions[questionId]) return;

    try {
      setLoadingQuestions(true);
      const questionData = await getQuestion(questionId);
      setQuestions(prev => ({
        ...prev,
        [questionId]: questionData,
      }));
    } catch (error) {
      console.error('Error loading question details:', error);
    } finally {
      setLoadingQuestions(false);
    }
  };

  // Get grade letter and color based on score
  const getGrade = (score: number | null) => {
    if (score === null) return { grade: 'N/A', color: theme.palette.grey[500] };
    if (score >= 90) return { grade: 'A', color: theme.palette.success.dark };
    if (score >= 80) return { grade: 'B', color: theme.palette.success.main };
    if (score >= 70) return { grade: 'C', color: theme.palette.warning.main };
    if (score >= 60) return { grade: 'D', color: theme.palette.warning.dark };
    return { grade: 'F', color: theme.palette.error.main };
  };

  // Calculate exam duration
  const calculateDuration = () => {
    if (!examWithAttempts?.start_time || !examWithAttempts?.end_time) return 'N/A';
    
    const start = new Date(examWithAttempts.start_time);
    const end = new Date(examWithAttempts.end_time);
    const durationMs = end.getTime() - start.getTime();
    
    const minutes = Math.floor(durationMs / (1000 * 60));
    const seconds = Math.floor((durationMs % (1000 * 60)) / 1000);
    
    return `${minutes}m ${seconds}s`;
  };

  // Prepare data for pie chart
  const getPieChartData = () => {
    if (!examWithAttempts) return [];
    
    const correctCount = examWithAttempts.correct_answers || 0;
    const incorrectCount = examWithAttempts.total_questions - correctCount;
    
    return [
      { name: 'Correct', value: correctCount, color: theme.palette.success.main },
      { name: 'Incorrect', value: incorrectCount, color: theme.palette.error.main },
    ];
  };

  // Loading state
  if (isLoadingExam || isLoadingCourse) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  // Error state
  if (examError || courseError) {
    return (
      <Box>
        <Typography variant="h5" color="error" gutterBottom>
          Error loading exam details
        </Typography>
        <Button 
          variant="contained" 
          startIcon={<ArrowBackIcon />}
          onClick={() => navigate('/mcq/history')}
        >
          Back to History
        </Button>
      </Box>
    );
  }

  // If exam not found
  if (!examWithAttempts) {
    return (
      <Box>
        <Alert severity="error" sx={{ mb: 3 }}>
          Exam not found
        </Alert>
        <Button 
          variant="contained" 
          startIcon={<ArrowBackIcon />}
          onClick={() => navigate('/mcq/history')}
        >
          Back to History
        </Button>
      </Box>
    );
  }

  const grade = getGrade(examWithAttempts.score);
  const pieData = getPieChartData();

  return (
    <Box>
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 4 }}>
        <IconButton onClick={() => navigate('/mcq/history')} sx={{ mr: 2 }}>
          <ArrowBackIcon />
        </IconButton>
        <Typography variant="h4" component="h1" fontWeight="bold">
          Exam Details
        </Typography>
      </Box>

      {/* Exam Summary Card */}
      <Card sx={{ mb: 4, boxShadow: theme.shadows[3] }}>
        <CardHeader 
          title={`${course?.name || 'Course'} Exam`}
          titleTypographyProps={{ variant: 'h5', fontWeight: 'bold' }}
          avatar={<SchoolIcon color="primary" fontSize="large" />}
        />
        <CardContent>
          <Grid container spacing={3}>
            <Grid item xs={12} md={4}>
              <Box sx={{ textAlign: 'center' }}>
                <Box sx={{ position: 'relative', display: 'inline-flex' }}>
                  <CircularProgress
                    variant="determinate"
                    value={examWithAttempts.score || 0}
                    size={120}
                    thickness={5}
                    sx={{ 
                      color: grade.color,
                      circle: {
                        strokeLinecap: 'round',
                      }
                    }}
                  />
                  <Box
                    sx={{
                      top: 0,
                      left: 0,
                      bottom: 0,
                      right: 0,
                      position: 'absolute',
                      display: 'flex',
                      alignItems: 'center',
                      justifyContent: 'center',
                    }}
                  >
                    <Typography variant="h4" fontWeight="bold" color={grade.color}>
                      {Math.round(examWithAttempts.score || 0)}%
                    </Typography>
                  </Box>
                </Box>
                <Typography variant="h5" sx={{ mt: 2 }}>
                  Grade: <span style={{ color: grade.color, fontWeight: 'bold' }}>{grade.grade}</span>
                </Typography>
              </Box>
            </Grid>
            <Grid item xs={12} md={4}>
              <Box>
                <Typography variant="h6" gutterBottom>
                  Exam Details
                </Typography>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography variant="body1">Date:</Typography>
                  <Typography variant="body1" fontWeight="bold">
                    {new Date(examWithAttempts.start_time).toLocaleDateString()}
                  </Typography>
                </Box>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography variant="body1">Time:</Typography>
                  <Typography variant="body1" fontWeight="bold">
                    {new Date(examWithAttempts.start_time).toLocaleTimeString()}
                  </Typography>
                </Box>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography variant="body1">Duration:</Typography>
                  <Typography variant="body1" fontWeight="bold">
                    {calculateDuration()}
                  </Typography>
                </Box>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography variant="body1">Questions:</Typography>
                  <Typography variant="body1" fontWeight="bold">
                    {examWithAttempts.total_questions}
                  </Typography>
                </Box>
              </Box>
            </Grid>
            <Grid item xs={12} md={4}>
              <Box sx={{ height: 200 }}>
                <Typography variant="h6" gutterBottom textAlign="center">
                  Answer Distribution
                </Typography>
                <ResponsiveContainer width="100%" height="100%">
                  <PieChart>
                    <Pie
                      data={pieData}
                      cx="50%"
                      cy="50%"
                      labelLine={false}
                      outerRadius={70}
                      fill="#8884d8"
                      dataKey="value"
                      label={({ name, percent }) => `${name}: ${(percent * 100).toFixed(0)}%`}
                    >
                      {pieData.map((entry, index) => (
                        <Cell key={`cell-${index}`} fill={entry.color} />
                      ))}
                    </Pie>
                    <RechartsTooltip formatter={(value) => [`${value} questions`, '']} />
                    <Legend />
                  </PieChart>
                </ResponsiveContainer>
              </Box>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Question Analysis */}
      <Card sx={{ boxShadow: theme.shadows[3] }}>
        <CardHeader title="Question Analysis" />
        <CardContent>
          <TableContainer component={Paper} variant="outlined" sx={{ maxHeight: 500, overflow: 'auto' }}>
            <Table stickyHeader>
              <TableHead>
                <TableRow>
                  <TableCell>Question</TableCell>
                  <TableCell>Your Answer</TableCell>
                  <TableCell>Correct Answer</TableCell>
                  <TableCell>Result</TableCell>
                </TableRow>
              </TableHead>
              <TableBody>
                {examWithAttempts.attempts.map((attempt) => {
                  const questionDetails = questions[attempt.question_id];
                  
                  return (
                    <TableRow 
                      key={attempt.id}
                      sx={{
                        cursor: 'pointer',
                        '&:hover': {
                          bgcolor: theme.palette.action.hover,
                        },
                        bgcolor: attempt.is_correct 
                          ? alpha(theme.palette.success.light, 0.1)
                          : alpha(theme.palette.error.light, 0.1),
                      }}
                      onClick={() => loadQuestionDetails(attempt.question_id)}
                    >
                      <TableCell>
                        {questionDetails ? (
                          <Typography variant="body2">
                            {questionDetails.content}
                          </Typography>
                        ) : (
                          <Box sx={{ display: 'flex', alignItems: 'center' }}>
                            <Typography variant="body2">
                              Question {attempt.question_id}
                            </Typography>
                            {loadingQuestions ? (
                              <CircularProgress size={16} sx={{ ml: 1 }} />
                            ) : (
                              <Tooltip title="Click to load question details">
                                <Typography variant="caption" color="primary" sx={{ ml: 1 }}>
                                  (Click to view)
                                </Typography>
                              </Tooltip>
                            )}
                          </Box>
                        )}
                      </TableCell>
                      <TableCell>
                        {attempt.selected_answer || 'No answer'}
                      </TableCell>
                      <TableCell>
                        {questionDetails?.answer || 'Click to view'}
                      </TableCell>
                      <TableCell>
                        {attempt.is_correct ? (
                          <Chip 
                            icon={<CorrectIcon />} 
                            label="Correct" 
                            color="success" 
                            size="small" 
                          />
                        ) : (
                          <Chip 
                            icon={<IncorrectIcon />} 
                            label="Incorrect" 
                            color="error" 
                            size="small" 
                          />
                        )}
                      </TableCell>
                    </TableRow>
                  );
                })}
              </TableBody>
            </Table>
          </TableContainer>
        </CardContent>
      </Card>

      <Box sx={{ display: 'flex', justifyContent: 'space-between', mt: 4 }}>
        <Button
          variant="outlined"
          startIcon={<ArrowBackIcon />}
          onClick={() => navigate('/mcq/history')}
        >
          Back to History
        </Button>
        <Button
          variant="contained"
          color="primary"
          onClick={() => navigate(`/mcq/practice/${examWithAttempts.course_id}`)}
        >
          Practice This Course
        </Button>
      </Box>
    </Box>
  );
};

// Helper function to create alpha colors
function alpha(color: string, opacity: number): string {
  // Simple alpha function for demo purposes
  return color;
}

export default ExamDetails;
