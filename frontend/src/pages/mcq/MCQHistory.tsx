import React, { useState } from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Button,
  Card,
  CardContent,
  CardActions,
  IconButton,
  Tabs,
  Tab,
  CircularProgress,
  Alert,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  SelectChangeEvent,
  useTheme,
  Divider,
  Chip,
  LinearProgress,
} from '@mui/material';
import {
  ArrowBack as ArrowBackIcon,
  History as HistoryIcon,
  Assessment as AssessmentIcon,
  PlayArrow as PlayArrowIcon,
  School as SchoolIcon,
  CheckCircle as CheckCircleIcon,
  Cancel as CancelIcon,
  TrendingUp as TrendingUpIcon,
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';
import { motion } from 'framer-motion';

import { useAuth } from '../../contexts/AuthContext';
import { getCourses } from '../../api/courses';
import {
  getQuestionAttempts,
  getCourseAttempts,
  getExams,
  getExamWithAttempts,
  QuestionAttempt,
  Exam,
  ExamWithAttempts
} from '../../api/studentProgress';
import Calculator from '../../components/Calculator';

// Create motion components
const MotionPaper = motion.create(Paper);
const MotionCard = motion.create(Card);

interface TabPanelProps {
  children?: React.ReactNode;
  index: number;
  value: number;
}

function TabPanel(props: TabPanelProps) {
  const { children, value, index, ...other } = props;

  return (
    <div
      role="tabpanel"
      hidden={value !== index}
      id={`history-tabpanel-${index}`}
      aria-labelledby={`history-tab-${index}`}
      {...other}
    >
      {value === index && (
        <Box sx={{ p: 3 }}>
          {children}
        </Box>
      )}
    </div>
  );
}

const MCQHistory: React.FC = () => {
  const theme = useTheme();
  const navigate = useNavigate();
  const { user } = useAuth();

  const [tabValue, setTabValue] = useState(0);
  const [selectedCourse, setSelectedCourse] = useState<string>('');
  const [selectedExam, setSelectedExam] = useState<number | null>(null);
  const [examDetails, setExamDetails] = useState<ExamWithAttempts | null>(null);

  // Fetch courses with enrolled courses prioritized
  const {
    data: courses = [],
    isLoading: isLoadingCourses
  } = useQuery({
    queryKey: ['courses', 'prioritizeEnrolled'],
    queryFn: () => getCourses(undefined, undefined, false, true)
  });

  // Fetch practice attempts
  const {
    data: attempts = [],
    isLoading: isLoadingAttempts
  } = useQuery({
    queryKey: ['attempts', user?.id, selectedCourse],
    queryFn: () => selectedCourse
      ? getCourseAttempts(user!.id, parseInt(selectedCourse))
      : getQuestionAttempts(user!.id),
    enabled: !!user
  });

  // Fetch exams
  const {
    data: exams = [],
    isLoading: isLoadingExams
  } = useQuery({
    queryKey: ['exams', user?.id, selectedCourse],
    queryFn: () => getExams(
      user!.id,
      selectedCourse ? parseInt(selectedCourse) : undefined
    ),
    enabled: !!user
  });

  // Handle tab change
  const handleTabChange = (_event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
    setSelectedExam(null);
    setExamDetails(null);
  };

  // Handle course selection change
  const handleCourseChange = (event: SelectChangeEvent) => {
    setSelectedCourse(event.target.value);
    setSelectedExam(null);
    setExamDetails(null);
  };

  // Load exam details
  const loadExamDetails = async (examId: number) => {
    try {
      const details = await getExamWithAttempts(examId);
      setExamDetails(details);
      setSelectedExam(examId);
    } catch (error) {
      console.error('Error loading exam details:', error);
    }
  };

  // Get course name by ID
  const getCourseName = (courseId: number): string => {
    const course = courses.find(c => c.id === courseId);
    return course ? course.name : 'Unknown Course';
  };

  // Group attempts by date
  const groupAttemptsByDate = (attempts: QuestionAttempt[]) => {
    const grouped: Record<string, QuestionAttempt[]> = {};

    attempts.forEach(attempt => {
      const date = new Date(attempt.attempt_time).toLocaleDateString();
      if (!grouped[date]) {
        grouped[date] = [];
      }
      grouped[date].push(attempt);
    });

    return Object.entries(grouped).sort((a, b) => {
      return new Date(b[0]).getTime() - new Date(a[0]).getTime();
    });
  };

  // Calculate success rate
  const calculateSuccessRate = (attempts: QuestionAttempt[]) => {
    if (attempts.length === 0) return 0;

    const correctCount = attempts.filter(a => a.is_correct).length;
    return (correctCount / attempts.length) * 100;
  };

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        type: 'spring',
        stiffness: 100
      }
    }
  };

  // Format date and time
  const formatDateTime = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleString();
  };

  // Calculate time spent on exam
  const calculateTimeSpent = (startTime: string, endTime: string | null) => {
    if (!endTime) return 'In progress';

    const start = new Date(startTime).getTime();
    const end = new Date(endTime).getTime();
    const diffMs = end - start;

    const minutes = Math.floor(diffMs / 60000);
    const seconds = Math.floor((diffMs % 60000) / 1000);

    return `${minutes}m ${seconds}s`;
  };

  return (
    <Box>
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 4 }}>
        <IconButton onClick={() => navigate('/mcq')} sx={{ mr: 2 }}>
          <ArrowBackIcon />
        </IconButton>
        <Typography variant="h4" component="h1" fontWeight="bold">
          MCQ History
        </Typography>
      </Box>

      <Paper sx={{ borderRadius: 2, mb: 4 }}>
        <Tabs
          value={tabValue}
          onChange={handleTabChange}
          variant="fullWidth"
          sx={{ borderBottom: 1, borderColor: 'divider' }}
        >
          <Tab label="Practice History" icon={<PlayArrowIcon />} iconPosition="start" />
          <Tab label="Exam History" icon={<AssessmentIcon />} iconPosition="start" />
          <Tab label="Performance" icon={<TrendingUpIcon />} iconPosition="start" />
        </Tabs>

        <Box sx={{ p: 3 }}>
          <FormControl fullWidth>
            <InputLabel id="course-filter-label">Filter by Course</InputLabel>
            <Select
              labelId="course-filter-label"
              id="course-filter"
              value={selectedCourse}
              label="Filter by Course"
              onChange={handleCourseChange}
              disabled={isLoadingCourses}
            >
              <MenuItem value="">
                <em>All Courses</em>
              </MenuItem>
              {courses.map((course) => (
                <MenuItem key={course.id} value={course.id.toString()}>
                  {course.name}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
        </Box>

        <TabPanel value={tabValue} index={0}>
          {isLoadingAttempts ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
              <CircularProgress />
            </Box>
          ) : attempts.length === 0 ? (
            <Alert severity="info">
              No practice history found. Start practicing to see your history here.
            </Alert>
          ) : (
            <motion.div
              variants={containerVariants}
              initial="hidden"
              animate="visible"
            >
              {groupAttemptsByDate(attempts).map(([date, dateAttempts]) => (
                <MotionPaper
                  key={date}
                  variants={itemVariants}
                  sx={{ p: 3, mb: 3, borderRadius: 2 }}
                >
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
                    <Typography variant="h6" fontWeight="bold">
                      {date}
                    </Typography>
                    <Chip
                      label={`${dateAttempts.length} attempt${dateAttempts.length !== 1 ? 's' : ''}`}
                      color="primary"
                    />
                  </Box>

                  <Box sx={{ mb: 2 }}>
                    <Typography variant="body2" color="text.secondary" gutterBottom>
                      Success Rate:
                    </Typography>
                    <Box sx={{ display: 'flex', alignItems: 'center' }}>
                      <Box sx={{ width: '100%', mr: 1 }}>
                        <LinearProgress
                          variant="determinate"
                          value={calculateSuccessRate(dateAttempts)}
                          color={calculateSuccessRate(dateAttempts) >= 70 ? "success" : "primary"}
                          sx={{ height: 10, borderRadius: 5 }}
                        />
                      </Box>
                      <Box sx={{ minWidth: 35 }}>
                        <Typography variant="body2" color="text.secondary">
                          {Math.round(calculateSuccessRate(dateAttempts))}%
                        </Typography>
                      </Box>
                    </Box>
                  </Box>

                  <Divider sx={{ my: 2 }} />

                  <Grid container spacing={2}>
                    {dateAttempts.slice(0, 5).map((attempt) => (
                      <Grid item xs={12} key={attempt.id}>
                        <Box
                          sx={{
                            p: 2,
                            borderRadius: 2,
                            bgcolor: theme.palette.mode === 'light' ? 'rgba(0, 0, 0, 0.02)' : 'rgba(255, 255, 255, 0.02)',
                            border: `1px solid ${theme.palette.divider}`,
                            display: 'flex',
                            alignItems: 'center',
                          }}
                        >
                          {attempt.is_correct ? (
                            <CheckCircleIcon color="success" sx={{ mr: 2 }} />
                          ) : (
                            <CancelIcon color="error" sx={{ mr: 2 }} />
                          )}

                          <Box sx={{ flexGrow: 1 }}>
                            <Typography variant="body2">
                              Question #{attempt.question_id}
                            </Typography>
                            <Typography variant="caption" color="text.secondary">
                              {new Date(attempt.attempt_time).toLocaleTimeString()}
                            </Typography>
                          </Box>

                          <Button
                            size="small"
                            variant="outlined"
                            onClick={() => navigate(`/questions/${attempt.question_id}`)}
                          >
                            View Question
                          </Button>
                        </Box>
                      </Grid>
                    ))}
                  </Grid>

                  {dateAttempts.length > 5 && (
                    <Box sx={{ textAlign: 'center', mt: 2 }}>
                      <Typography variant="body2" color="text.secondary">
                        {dateAttempts.length - 5} more attempts on this day
                      </Typography>
                    </Box>
                  )}
                </MotionPaper>
              ))}
            </motion.div>
          )}
        </TabPanel>

        <TabPanel value={tabValue} index={1}>
          {selectedExam ? (
            <Box>
              <Button
                startIcon={<ArrowBackIcon />}
                onClick={() => {
                  setSelectedExam(null);
                  setExamDetails(null);
                }}
                sx={{ mb: 3 }}
              >
                Back to Exam List
              </Button>

              {!examDetails ? (
                <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
                  <CircularProgress />
                </Box>
              ) : (
                <motion.div
                  variants={containerVariants}
                  initial="hidden"
                  animate="visible"
                >
                  <MotionCard
                    variants={itemVariants}
                    sx={{ mb: 4, borderRadius: 2 }}
                  >
                    <CardContent>
                      <Typography variant="h5" gutterBottom>
                        {getCourseName(examDetails.course_id)} Exam
                      </Typography>

                      <Grid container spacing={3} sx={{ mt: 1 }}>
                        <Grid item xs={12} md={6}>
                          <Box sx={{ mb: 3 }}>
                            <Typography variant="subtitle2" color="text.secondary">
                              Date & Time:
                            </Typography>
                            <Typography variant="body1">
                              {formatDateTime(examDetails.start_time)}
                            </Typography>
                          </Box>

                          <Box sx={{ mb: 3 }}>
                            <Typography variant="subtitle2" color="text.secondary">
                              Duration:
                            </Typography>
                            <Typography variant="body1">
                              {calculateTimeSpent(examDetails.start_time, examDetails.end_time)}
                            </Typography>
                          </Box>

                          <Box>
                            <Typography variant="subtitle2" color="text.secondary">
                              Questions:
                            </Typography>
                            <Typography variant="body1">
                              {examDetails.total_questions}
                            </Typography>
                          </Box>
                        </Grid>

                        <Grid item xs={12} md={6}>
                          <Box sx={{ textAlign: 'center' }}>
                            <Box
                              sx={{
                                position: 'relative',
                                display: 'inline-flex',
                                width: 150,
                                height: 150,
                              }}
                            >
                              <CircularProgress
                                variant="determinate"
                                value={examDetails.score || 0}
                                size={150}
                                thickness={5}
                                sx={{
                                  color: (examDetails.score || 0) >= 70
                                    ? 'success.main'
                                    : (examDetails.score || 0) >= 50
                                    ? 'warning.main'
                                    : 'error.main',
                                }}
                              />
                              <Box
                                sx={{
                                  top: 0,
                                  left: 0,
                                  bottom: 0,
                                  right: 0,
                                  position: 'absolute',
                                  display: 'flex',
                                  alignItems: 'center',
                                  justifyContent: 'center',
                                  flexDirection: 'column',
                                }}
                              >
                                <Typography variant="h4" component="div" fontWeight="bold">
                                  {Math.round(examDetails.score || 0)}%
                                </Typography>
                                <Typography variant="body2" color="text.secondary">
                                  Score
                                </Typography>
                              </Box>
                            </Box>

                            <Typography variant="h6" sx={{ mt: 2 }}>
                              {examDetails.correct_answers} / {examDetails.total_questions} correct
                            </Typography>
                          </Box>
                        </Grid>
                      </Grid>

                      <Divider sx={{ my: 3 }} />

                      <Typography variant="h6" gutterBottom>
                        Question Attempts
                      </Typography>

                      <Grid container spacing={2}>
                        {examDetails.attempts.map((attempt, index) => (
                          <Grid item xs={12} key={attempt.id}>
                            <Box
                              sx={{
                                p: 2,
                                borderRadius: 2,
                                bgcolor: theme.palette.mode === 'light' ? 'rgba(0, 0, 0, 0.02)' : 'rgba(255, 255, 255, 0.02)',
                                border: `1px solid ${theme.palette.divider}`,
                                display: 'flex',
                                alignItems: 'center',
                              }}
                            >
                              <Typography variant="body2" sx={{ width: 30, mr: 2 }}>
                                #{index + 1}
                              </Typography>

                              {attempt.is_correct ? (
                                <CheckCircleIcon color="success" sx={{ mr: 2 }} />
                              ) : (
                                <CancelIcon color="error" sx={{ mr: 2 }} />
                              )}

                              <Box sx={{ flexGrow: 1 }}>
                                <Typography variant="body2">
                                  Question #{attempt.question_id}
                                </Typography>
                                {attempt.selected_answer && (
                                  <Typography variant="caption" color="text.secondary">
                                    Your answer: {attempt.selected_answer}
                                  </Typography>
                                )}
                              </Box>

                              <Button
                                size="small"
                                variant="outlined"
                                onClick={() => navigate(`/questions/${attempt.question_id}`)}
                              >
                                View Question
                              </Button>
                            </Box>
                          </Grid>
                        ))}
                      </Grid>
                    </CardContent>
                    <CardActions>
                      <Button
                        variant="contained"
                        color="primary"
                        onClick={() => navigate(`/mcq/practice/${examDetails.course_id}`)}
                      >
                        Practice This Course Again
                      </Button>
                    </CardActions>
                  </MotionCard>
                </motion.div>
              )}
            </Box>
          ) : isLoadingExams ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
              <CircularProgress />
            </Box>
          ) : exams.length === 0 ? (
            <Alert severity="info">
              No exam history found. Take an exam to see your history here.
            </Alert>
          ) : (
            <motion.div
              variants={containerVariants}
              initial="hidden"
              animate="visible"
            >
              <Grid container spacing={3}>
                {exams.map((exam) => (
                  <Grid item xs={12} md={6} key={exam.id}>
                    <MotionCard
                      variants={itemVariants}
                      sx={{
                        height: '100%',
                        display: 'flex',
                        flexDirection: 'column',
                        transition: 'transform 0.3s ease',
                        '&:hover': {
                          transform: 'translateY(-4px)',
                        },
                      }}
                    >
                      <CardContent sx={{ flexGrow: 1 }}>
                        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                          <SchoolIcon sx={{ mr: 1, color: 'primary.main' }} />
                          <Typography variant="h6">
                            {getCourseName(exam.course_id)}
                          </Typography>
                        </Box>

                        <Divider sx={{ mb: 2 }} />

                        <Grid container spacing={2}>
                          <Grid item xs={6}>
                            <Typography variant="body2" color="text.secondary">
                              Date:
                            </Typography>
                            <Typography variant="body1">
                              {new Date(exam.start_time).toLocaleDateString()}
                            </Typography>
                          </Grid>

                          <Grid item xs={6}>
                            <Typography variant="body2" color="text.secondary">
                              Time:
                            </Typography>
                            <Typography variant="body1">
                              {new Date(exam.start_time).toLocaleTimeString()}
                            </Typography>
                          </Grid>

                          <Grid item xs={6}>
                            <Typography variant="body2" color="text.secondary">
                              Questions:
                            </Typography>
                            <Typography variant="body1">
                              {exam.total_questions}
                            </Typography>
                          </Grid>

                          <Grid item xs={6}>
                            <Typography variant="body2" color="text.secondary">
                              Score:
                            </Typography>
                            <Typography
                              variant="body1"
                              color={
                                (exam.score || 0) >= 70
                                  ? 'success.main'
                                  : (exam.score || 0) >= 50
                                  ? 'warning.main'
                                  : 'error.main'
                              }
                              fontWeight="bold"
                            >
                              {exam.score !== null ? `${Math.round(exam.score)}%` : 'In Progress'}
                            </Typography>
                          </Grid>
                        </Grid>
                      </CardContent>

                      <CardActions>
                        <Button
                          fullWidth
                          variant="contained"
                          onClick={() => loadExamDetails(exam.id)}
                        >
                          View Details
                        </Button>
                      </CardActions>
                    </MotionCard>
                  </Grid>
                ))}
              </Grid>
            </motion.div>
          )}
        </TabPanel>

        <TabPanel value={tabValue} index={2}>
          {isLoadingAttempts || isLoadingExams ? (
            <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
              <CircularProgress />
            </Box>
          ) : attempts.length === 0 && exams.length === 0 ? (
            <Alert severity="info">
              No practice or exam history found. Start practicing or take an exam to see your performance statistics.
            </Alert>
          ) : (
            <motion.div
              variants={containerVariants}
              initial="hidden"
              animate="visible"
            >
              <Grid container spacing={4}>
                {/* Practice Statistics */}
                <Grid item xs={12} md={6}>
                  <MotionPaper
                    variants={itemVariants}
                    sx={{ p: 3, borderRadius: 2, height: '100%' }}
                  >
                    <Typography variant="h6" fontWeight="bold" gutterBottom>
                      Practice Statistics
                    </Typography>

                    <Box sx={{ mt: 3 }}>
                      <Typography variant="body2" color="text.secondary" gutterBottom>
                        Total Attempts:
                      </Typography>
                      <Typography variant="h4" color="primary.main">
                        {attempts.length}
                      </Typography>
                    </Box>

                    <Box sx={{ mt: 3 }}>
                      <Typography variant="body2" color="text.secondary" gutterBottom>
                        Success Rate:
                      </Typography>
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <Box sx={{ width: '100%', mr: 1 }}>
                          <LinearProgress
                            variant="determinate"
                            value={calculateSuccessRate(attempts)}
                            color={calculateSuccessRate(attempts) >= 70 ? "success" : "primary"}
                            sx={{ height: 10, borderRadius: 5 }}
                          />
                        </Box>
                        <Box sx={{ minWidth: 35 }}>
                          <Typography variant="body2" color="text.secondary">
                            {Math.round(calculateSuccessRate(attempts))}%
                          </Typography>
                        </Box>
                      </Box>
                    </Box>

                    <Box sx={{ mt: 3 }}>
                      <Typography variant="body2" color="text.secondary" gutterBottom>
                        Correct Answers:
                      </Typography>
                      <Typography variant="h5" color="success.main">
                        {attempts.filter(a => a.is_correct).length}
                      </Typography>
                    </Box>

                    <Box sx={{ mt: 3 }}>
                      <Typography variant="body2" color="text.secondary" gutterBottom>
                        Incorrect Answers:
                      </Typography>
                      <Typography variant="h5" color="error.main">
                        {attempts.filter(a => !a.is_correct).length}
                      </Typography>
                    </Box>

                    <Button
                      variant="outlined"
                      fullWidth
                      sx={{ mt: 3 }}
                      onClick={() => setTabValue(0)}
                    >
                      View Practice History
                    </Button>
                  </MotionPaper>
                </Grid>

                {/* Exam Statistics */}
                <Grid item xs={12} md={6}>
                  <MotionPaper
                    variants={itemVariants}
                    sx={{ p: 3, borderRadius: 2, height: '100%' }}
                  >
                    <Typography variant="h6" fontWeight="bold" gutterBottom>
                      Exam Statistics
                    </Typography>

                    <Box sx={{ mt: 3 }}>
                      <Typography variant="body2" color="text.secondary" gutterBottom>
                        Total Exams:
                      </Typography>
                      <Typography variant="h4" color="secondary.main">
                        {exams.length}
                      </Typography>
                    </Box>

                    <Box sx={{ mt: 3 }}>
                      <Typography variant="body2" color="text.secondary" gutterBottom>
                        Average Score:
                      </Typography>
                      <Box sx={{ display: 'flex', alignItems: 'center' }}>
                        <Box sx={{ width: '100%', mr: 1 }}>
                          <LinearProgress
                            variant="determinate"
                            value={exams.length > 0
                              ? exams.reduce((sum, exam) => sum + (exam.score || 0), 0) / exams.length
                              : 0
                            }
                            color={
                              exams.length > 0 && (exams.reduce((sum, exam) => sum + (exam.score || 0), 0) / exams.length) >= 70
                                ? "success"
                                : "secondary"
                            }
                            sx={{ height: 10, borderRadius: 5 }}
                          />
                        </Box>
                        <Box sx={{ minWidth: 35 }}>
                          <Typography variant="body2" color="text.secondary">
                            {exams.length > 0
                              ? Math.round(exams.reduce((sum, exam) => sum + (exam.score || 0), 0) / exams.length)
                              : 0
                            }%
                          </Typography>
                        </Box>
                      </Box>
                    </Box>

                    <Box sx={{ mt: 3 }}>
                      <Typography variant="body2" color="text.secondary" gutterBottom>
                        Best Score:
                      </Typography>
                      <Typography variant="h5" color="success.main">
                        {exams.length > 0
                          ? `${Math.round(Math.max(...exams.map(e => e.score || 0)))}%`
                          : 'N/A'
                        }
                      </Typography>
                    </Box>

                    <Box sx={{ mt: 3 }}>
                      <Typography variant="body2" color="text.secondary" gutterBottom>
                        Completed Exams:
                      </Typography>
                      <Typography variant="h5">
                        {exams.filter(e => e.end_time).length}
                      </Typography>
                    </Box>

                    <Button
                      variant="outlined"
                      fullWidth
                      sx={{ mt: 3 }}
                      onClick={() => setTabValue(1)}
                    >
                      View Exam History
                    </Button>
                  </MotionPaper>
                </Grid>
              </Grid>
            </motion.div>
          )}
        </TabPanel>
      </Paper>

      {/* Calculator */}
      <Calculator position="right" />
    </Box>
  );
};

export default MCQHistory;
