import React, { useEffect, useState } from 'react';
import {
  Box,
  Typography,
  Button,
  Paper,
  Grid,
  Divider,
  useTheme,
  CircularProgress,
  Alert,
  Tabs,
  Tab,
  useMediaQuery,
  Card,
  CardContent,
  Fade,
  Zoom,
  Chip,
  IconButton,
  SwipeableDrawer,
} from '@mui/material';
import {
  Share as ShareIcon,
  EmojiEvents as TrophyIcon,
  ArrowBack as ArrowBackIcon,
  School as SchoolIcon,
  BarChart as BarChartIcon,
  AccessTime as TimeIcon,
  CheckCircle as CheckCircleIcon,
  Cancel as CancelIcon,
  Groups as GroupsIcon,
} from '@mui/icons-material';
import { useNavigate, useParams } from 'react-router-dom';
import { useQuery, useQueryClient } from '@tanstack/react-query';
import { getExamWithAttempts } from '../../api/studentProgress';
import { getCourse } from '../../api/courses';
import TopicAnalysis from '../../components/ExamResults/TopicAnalysis';
import ComparativePerformance from '../../components/ExamResults/ComparativePerformance';
import { motion } from 'framer-motion';

const ExamResults: React.FC = () => {
  const { examId } = useParams<{ examId: string }>();
  const navigate = useNavigate();
  const theme = useTheme();
  const queryClient = useQueryClient();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const isTablet = useMediaQuery(theme.breakpoints.between('sm', 'md'));
  const [loadingError, setLoadingError] = useState<string | null>(null);
  const [tabValue, setTabValue] = useState(0);
  const [shareDrawerOpen, setShareDrawerOpen] = useState(false);
  const [animationComplete, setAnimationComplete] = useState(false);

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1,
        delayChildren: 0.3
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        type: 'spring',
        stiffness: 300,
        damping: 24
      }
    }
  };

  const scoreCircleVariants = {
    hidden: { scale: 0.8, opacity: 0 },
    visible: {
      scale: 1,
      opacity: 1,
      transition: {
        type: 'spring',
        stiffness: 200,
        damping: 20,
        delay: 0.5
      }
    }
  };

  // Handle tab change
  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  // Default to Summary tab
  useEffect(() => {
    setTabValue(0);
  }, []);

  // Get exam results from React Query cache first for immediate display
  const cachedResults = queryClient.getQueryData(['examResults', Number(examId)]);

  // Fallback to localStorage if not in cache
  const storedResults = localStorage.getItem('currentExamResults');
  const initialResults = cachedResults || (storedResults ? JSON.parse(storedResults) : null);

  console.log("Initial results:", initialResults, "From cache:", !!cachedResults, "From localStorage:", !!storedResults);

  // If examId is not in the URL but is in localStorage, use it
  const effectiveExamId = examId || (initialResults?.id?.toString() || initialResults?.examId?.toString());

  // Fetch exam details from backend
  const {
    data: examData,
    isLoading: isLoadingExam,
    error: examError,
    refetch: refetchExam
  } = useQuery({
    queryKey: ['exam', effectiveExamId],
    queryFn: () => getExamWithAttempts(Number(effectiveExamId)),
    enabled: !!effectiveExamId,
    retry: 3,
    retryDelay: 1000,
    staleTime: 5 * 60 * 1000, // 5 minutes
    onError: (error: any) => {
      console.error('Error fetching exam data:', error);
      setLoadingError('Failed to load exam results from the server. Using locally stored results instead.');
    }
  });

  // Get course ID from either API data or localStorage
  const resultCourseId = examData?.course_id || initialResults?.course_id || initialResults?.courseId || 0;

  // Fetch course details
  const {
    data: courseData,
    isLoading: isLoadingCourse
  } = useQuery({
    queryKey: ['course', resultCourseId],
    queryFn: () => getCourse(resultCourseId),
    enabled: !!resultCourseId,
    staleTime: 10 * 60 * 1000, // 10 minutes
    onError: (error: any) => {
      console.error('Error fetching course data:', error);
    }
  });

  // Retry loading exam data if it fails initially
  useEffect(() => {
    if (examError && examId) {
      // Wait a bit longer and try again
      const timer = setTimeout(() => {
        console.log('Retrying exam data fetch...');
        refetchExam();
      }, 2000);

      return () => clearTimeout(timer);
    }
  }, [examError, examId, refetchExam]);

  // Use either backend data or localStorage data
  const examResults = examData || initialResults;
  const courseName = courseData?.name || initialResults?.courseName || 'Course';

  // Debug logging
  console.log("ExamResults component - Debug info:");
  console.log("examId:", examId);
  console.log("initialResults from localStorage:", initialResults);
  console.log("examData from API:", examData);
  console.log("courseData:", courseData);
  console.log("Final examResults being used:", examResults);

  // Animation complete handler
  useEffect(() => {
    const timer = setTimeout(() => {
      setAnimationComplete(true);
    }, 1000);
    return () => clearTimeout(timer);
  }, []);

  // Show loading state only if we don't have any results yet
  if (isLoadingExam && !initialResults) {
    console.log("Showing loading state - no initial results available");
    return (
      <Box
        sx={{
          p: { xs: 2, sm: 4 },
          textAlign: 'center',
          display: 'flex',
          flexDirection: 'column',
          alignItems: 'center',
          minHeight: '50vh',
          justifyContent: 'center'
        }}
      >
        <motion.div
          initial={{ scale: 0.8, opacity: 0 }}
          animate={{ scale: 1, opacity: 1 }}
          transition={{ duration: 0.5 }}
        >
          <CircularProgress size={isMobile ? 50 : 60} sx={{ mb: 3 }} />
        </motion.div>
        <motion.div
          initial={{ y: 20, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ delay: 0.3, duration: 0.5 }}
        >
          <Typography variant={isMobile ? "h6" : "h5"} gutterBottom>
            Loading exam results...
          </Typography>
          <Typography variant="body2" color="text.secondary">
            This should only take a moment...
          </Typography>
        </motion.div>
      </Box>
    );
  }

  // If no results are available, show a message
  if (!examResults && !initialResults) {
    console.log("No exam results found - showing error message");
    return (
      <Box
        sx={{
          p: { xs: 2, sm: 4 },
          textAlign: 'center',
          minHeight: '60vh',
          display: 'flex',
          flexDirection: 'column',
          justifyContent: 'center'
        }}
      >
        <motion.div
          initial={{ opacity: 0, y: -20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <Typography variant={isMobile ? "h6" : "h5"} gutterBottom>
            No exam results found
          </Typography>
        </motion.div>

        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.2, duration: 0.5 }}
        >
          <Typography variant="body1" color="error" paragraph>
            We couldn't find any results for this exam. This might be because the exam was not properly submitted.
          </Typography>
          <Typography variant="body2" color="text.secondary" paragraph>
            If you just completed an exam, please try refreshing the page or checking your exam history.
          </Typography>
        </motion.div>

        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ delay: 0.4, duration: 0.5 }}
        >
          <Box
            sx={{
              display: 'flex',
              justifyContent: 'center',
              gap: 2,
              mt: 3,
              flexDirection: isMobile ? 'column' : 'row',
              width: isMobile ? '100%' : 'auto',
              maxWidth: isMobile ? '300px' : 'none',
              mx: 'auto'
            }}
          >
            <Button
              variant="outlined"
              onClick={() => navigate(-1)}
              fullWidth={isMobile}
              startIcon={<ArrowBackIcon />}
              size={isMobile ? "large" : "medium"}
              sx={{ py: isMobile ? 1.5 : 1 }}
            >
              Go Back
            </Button>
            <Button
              variant="contained"
              onClick={() => navigate('/mcq')}
              fullWidth={isMobile}
              startIcon={<SchoolIcon />}
              size={isMobile ? "large" : "medium"}
              sx={{ py: isMobile ? 1.5 : 1 }}
            >
              Back to Dashboard
            </Button>
          </Box>
        </motion.div>
      </Box>
    );
  }

  // Calculate values for display
  // Handle both API response format and localStorage format
  const score = examResults?.score || 0;
  // API returns correct_answers, localStorage might have correctAnswers
  const correctAnswers = examResults?.correct_answers || examResults?.correctAnswers || 0;
  // API returns total_questions, localStorage might have totalQuestions
  const totalQuestions = examResults?.total_questions || examResults?.totalQuestions || 0;
  // API returns end_time, localStorage might have timestamp
  const timestamp = examResults?.end_time || examResults?.timestamp || new Date().toISOString();

  console.log("Calculated display values:", {
    score,
    correctAnswers,
    totalQuestions,
    resultCourseId,
    timestamp
  });

  // Get grade based on score
  const getGrade = (score: number) => {
    if (score >= 90) return 'A';
    if (score >= 80) return 'B';
    if (score >= 70) return 'C';
    if (score >= 60) return 'D';
    return 'F';
  };

  // Get color based on score
  const getScoreColor = (score: number) => {
    if (score >= 70) return theme.palette.success.light;
    if (score >= 50) return theme.palette.warning.light;
    return theme.palette.error.light;
  };

  // Get feedback based on score
  const getFeedback = (score: number) => {
    if (score >= 70) return 'Excellent work! You have a good understanding of this subject.';
    if (score >= 50) return 'Good effort! Keep practicing to improve your score.';
    return 'You need more practice with this subject. Don\'t give up!';
  };

  // Share drawer toggle
  const toggleShareDrawer = (open: boolean) => () => {
    setShareDrawerOpen(open);
  };

  // Share exam results
  const handleShare = () => {
    // This would be implemented with actual sharing functionality
    console.log('Sharing exam results');
    setShareDrawerOpen(false);
  };

  return (
    <motion.div
      initial="hidden"
      animate="visible"
      variants={containerVariants}
    >
      <Box sx={{
        p: { xs: 2, sm: 3 },
        maxWidth: '1200px',
        mx: 'auto',
        overflow: 'hidden'
      }}>
        {loadingError && (
          <Alert
            severity="warning"
            sx={{
              mb: 3,
              borderRadius: 2,
              animation: 'slideUp 0.5s ease-out'
            }}
          >
            {loadingError}
          </Alert>
        )}

        {/* Back button for mobile */}
        {isMobile && (
          <Box sx={{ mb: 2, display: 'flex', alignItems: 'center' }}>
            <IconButton
              onClick={() => navigate('/mcq')}
              sx={{ mr: 1 }}
              color="primary"
            >
              <ArrowBackIcon />
            </IconButton>
            <Typography variant="subtitle1" fontWeight="medium">
              Back to Dashboard
            </Typography>
          </Box>
        )}

        <Paper
          elevation={isMobile ? 2 : 3}
          sx={{
            p: { xs: 2, sm: 3, md: 4 },
            borderRadius: { xs: 3, sm: 4 },
            overflow: 'hidden',
            position: 'relative',
            boxShadow: isMobile
              ? '0 4px 20px rgba(0,0,0,0.05)'
              : '0 8px 30px rgba(0,0,0,0.12)',
            transition: 'all 0.3s ease'
          }}
          component={motion.div}
          variants={itemVariants}
        >
          {/* Header with share button */}
          <Box sx={{
            display: 'flex',
            justifyContent: 'space-between',
            alignItems: 'center',
            mb: 2
          }}>
            <Typography
              variant={isMobile ? "h5" : "h4"}
              component="h1"
              fontWeight="bold"
              gutterBottom
            >
              Exam Results
            </Typography>

            {!isMobile && (
              <IconButton
                onClick={toggleShareDrawer(true)}
                color="primary"
                sx={{
                  bgcolor: 'rgba(0,0,0,0.03)',
                  '&:hover': { bgcolor: 'rgba(0,0,0,0.08)' }
                }}
              >
                <ShareIcon />
              </IconButton>
            )}
          </Box>

          <Divider sx={{ my: { xs: 1.5, sm: 2 } }} />

          {/* Score Circle and Course Info */}
          <Box
            sx={{
              my: { xs: 2, sm: 3, md: 4 },
              textAlign: 'center',
              display: 'flex',
              flexDirection: 'column',
              alignItems: 'center'
            }}
          >
            <motion.div variants={itemVariants}>
              <Chip
                label={courseName + " Exam"}
                color="primary"
                sx={{
                  mb: 2,
                  fontSize: { xs: '0.9rem', sm: '1rem' },
                  py: 0.5,
                  px: 1,
                  height: 'auto',
                  '& .MuiChip-label': { px: 1 }
                }}
                icon={<SchoolIcon />}
              />
            </motion.div>

            <motion.div
              variants={scoreCircleVariants}
              style={{ display: 'inline-block' }}
            >
              <Box sx={{
                display: 'inline-flex',
                position: 'relative',
                borderRadius: '50%',
                width: { xs: 150, sm: 180, md: 200 },
                height: { xs: 150, sm: 180, md: 200 },
                bgcolor: getScoreColor(score),
                justifyContent: 'center',
                alignItems: 'center',
                my: { xs: 2, sm: 3 },
                boxShadow: '0 8px 32px rgba(0,0,0,0.15)',
                border: '4px solid white',
                transition: 'all 0.3s ease',
                '&::after': {
                  content: '""',
                  position: 'absolute',
                  top: -4,
                  left: -4,
                  right: -4,
                  bottom: -4,
                  borderRadius: '50%',
                  border: '2px solid',
                  borderColor: getScoreColor(score),
                  opacity: 0.5
                }
              }}>
                <Box sx={{ textAlign: 'center' }}>
                  <Typography
                    variant={isMobile ? "h3" : "h2"}
                    fontWeight="bold"
                    color="white"
                    sx={{ mb: 0.5 }}
                  >
                    {Math.round(score)}%
                  </Typography>
                  <Typography
                    variant="subtitle1"
                    color="white"
                    sx={{
                      opacity: 0.9,
                      fontSize: { xs: '0.8rem', sm: '0.9rem' }
                    }}
                  >
                    Grade: {getGrade(score)}
                  </Typography>
                </Box>
              </Box>
            </motion.div>

            <motion.div variants={itemVariants}>
              <Typography
                variant={isMobile ? "subtitle1" : "h6"}
                gutterBottom
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  justifyContent: 'center',
                  gap: 1
                }}
              >
                <CheckCircleIcon color="success" fontSize="small" />
                {correctAnswers} out of {totalQuestions} correct
              </Typography>
            </motion.div>

            <motion.div variants={itemVariants}>
              <Typography
                variant="body1"
                paragraph
                sx={{
                  maxWidth: '600px',
                  mx: 'auto',
                  mt: 1,
                  px: { xs: 1, sm: 2 },
                  py: 1,
                  bgcolor: 'rgba(0,0,0,0.02)',
                  borderRadius: 2,
                  border: '1px solid',
                  borderColor: 'divider'
                }}
              >
                {getFeedback(score)}
              </Typography>
            </motion.div>
          </Box>

          <Divider sx={{ my: { xs: 1.5, sm: 2 } }} />

          {/* Summary Cards */}
          <motion.div variants={itemVariants}>
            <Box sx={{ my: { xs: 2, sm: 3 } }}>
              <Typography
                variant={isMobile ? "h6" : "h5"}
                gutterBottom
                sx={{
                  display: 'flex',
                  alignItems: 'center',
                  gap: 1
                }}
              >
                <BarChartIcon color="primary" />
                Summary
              </Typography>

              <Grid container spacing={isMobile ? 1.5 : 2}>
                <Grid item xs={12} sm={6}>
                  <Card
                    variant="outlined"
                    sx={{
                      height: '100%',
                      transition: 'transform 0.2s ease',
                      '&:hover': {
                        transform: 'translateY(-2px)'
                      }
                    }}
                  >
                    <CardContent sx={{ p: { xs: 1.5, sm: 2 } }}>
                      <Typography
                        variant="subtitle1"
                        gutterBottom
                        sx={{
                          display: 'flex',
                          alignItems: 'center',
                          gap: 1,
                          mb: 1.5
                        }}
                      >
                        <TrophyIcon fontSize="small" color="primary" />
                        Score Details
                      </Typography>

                      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                        <Typography variant="body2">Score:</Typography>
                        <Typography fontWeight="bold">{Math.round(score)}%</Typography>
                      </Box>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                        <Typography variant="body2">Grade:</Typography>
                        <Chip
                          label={getGrade(score)}
                          size="small"
                          sx={{
                            bgcolor: getScoreColor(score),
                            color: 'white',
                            fontWeight: 'bold',
                            minWidth: '32px'
                          }}
                        />
                      </Box>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                        <Typography variant="body2">Correct:</Typography>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                          <CheckCircleIcon color="success" fontSize="small" />
                          <Typography fontWeight="bold" color="success.main">
                            {correctAnswers}
                          </Typography>
                        </Box>
                      </Box>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                        <Typography variant="body2">Incorrect:</Typography>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 0.5 }}>
                          <CancelIcon color="error" fontSize="small" />
                          <Typography fontWeight="bold" color="error.main">
                            {totalQuestions - correctAnswers}
                          </Typography>
                        </Box>
                      </Box>
                    </CardContent>
                  </Card>
                </Grid>

                <Grid item xs={12} sm={6}>
                  <Card
                    variant="outlined"
                    sx={{
                      height: '100%',
                      transition: 'transform 0.2s ease',
                      '&:hover': {
                        transform: 'translateY(-2px)'
                      }
                    }}
                  >
                    <CardContent sx={{ p: { xs: 1.5, sm: 2 } }}>
                      <Typography
                        variant="subtitle1"
                        gutterBottom
                        sx={{
                          display: 'flex',
                          alignItems: 'center',
                          gap: 1,
                          mb: 1.5
                        }}
                      >
                        <TimeIcon fontSize="small" color="primary" />
                        Exam Details
                      </Typography>

                      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                        <Typography variant="body2">Exam ID:</Typography>
                        <Typography fontWeight="bold">{examId}</Typography>
                      </Box>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                        <Typography variant="body2">Course:</Typography>
                        <Typography fontWeight="bold">{courseName}</Typography>
                      </Box>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                        <Typography variant="body2">Date:</Typography>
                        <Typography fontWeight="bold">
                          {new Date(timestamp).toLocaleDateString()}
                        </Typography>
                      </Box>
                      <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                        <Typography variant="body2">Time:</Typography>
                        <Typography fontWeight="bold">
                          {new Date(timestamp).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                        </Typography>
                      </Box>
                    </CardContent>
                  </Card>
                </Grid>
              </Grid>
            </Box>
          </motion.div>

          {/* Tabs for different analysis views */}
          <motion.div variants={itemVariants}>
            <Box
              sx={{
                mt: { xs: 3, sm: 4 },
                borderBottom: 1,
                borderColor: 'divider',
                borderRadius: '4px 4px 0 0',
                overflow: 'hidden',
                bgcolor: 'rgba(0,0,0,0.02)'
              }}
            >
              <Tabs
                value={tabValue}
                onChange={handleTabChange}
                variant="fullWidth"
                aria-label="Exam results tabs"
                sx={{
                  '& .MuiTab-root': {
                    py: { xs: 1.5, sm: 2 },
                    fontSize: { xs: '0.85rem', sm: '0.9rem' },
                    fontWeight: 'medium'
                  },
                  '& .Mui-selected': {
                    fontWeight: 'bold'
                  }
                }}
              >
                <Tab label="Summary" />
                <Tab label="Topic Analysis" />
                <Tab label="Class Comparison" />
              </Tabs>
            </Box>
          </motion.div>

          {/* Tab content */}
          <motion.div variants={itemVariants}>
            <Box sx={{ py: { xs: 2, sm: 3 } }}>
              {tabValue === 0 ? (
                <Box>
                  {/* Summary content is shown in the parent component */}
                  <Typography
                    variant={isMobile ? "h6" : "h5"}
                    gutterBottom
                    sx={{
                      display: 'flex',
                      alignItems: 'center',
                      gap: 1
                    }}
                  >
                    <CheckCircleIcon color="primary" />
                    Exam Summary
                  </Typography>

                  <Card
                    variant="outlined"
                    sx={{
                      p: { xs: 1.5, sm: 2 },
                      mb: 2,
                      borderRadius: 2
                    }}
                  >
                    <Typography variant="body1" paragraph>
                      You answered <strong>{examResults?.correct_answers || 0}</strong> out of <strong>{examResults?.total_questions || 0}</strong> questions correctly.
                    </Typography>
                    <Typography variant="body1">
                      Your score: <Chip
                        label={`${examResults?.score?.toFixed(1) || 0}%`}
                        size="small"
                        sx={{
                          bgcolor: getScoreColor(score),
                          color: 'white',
                          fontWeight: 'bold',
                          ml: 1
                        }}
                      />
                    </Typography>
                  </Card>

                  <Typography variant="body2" color="text.secondary" sx={{ mt: 2, mb: 3 }}>
                    Continue practicing to improve your understanding of this subject. You can take more practice exams or review specific topics.
                  </Typography>

                  {/* Preview of class comparison */}
                  <Card
                    variant="outlined"
                    sx={{
                      p: 2,
                      mt: 3,
                      borderRadius: 2,
                      borderStyle: 'dashed',
                      bgcolor: 'rgba(0,0,0,0.01)',
                      cursor: 'pointer',
                      transition: 'all 0.2s ease',
                      '&:hover': {
                        bgcolor: 'rgba(0,0,0,0.03)',
                        transform: 'translateY(-2px)'
                      }
                    }}
                    onClick={() => setTabValue(2)}
                  >
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                      <GroupsIcon color="primary" fontSize={isMobile ? "medium" : "large"} />
                      <Box>
                        <Typography variant="subtitle1" fontWeight="medium" gutterBottom>
                          See how you compare to your classmates
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          Check your performance against others who took this exam. Click to view the detailed comparison.
                        </Typography>
                      </Box>
                    </Box>
                  </Card>
                </Box>
              ) : tabValue === 1 ? (
                <TopicAnalysis
                  examId={Number(examId)}
                  studentId={examResults?.student_id || 0}
                  showTimeData={!isMobile}
                />
              ) : (
                <ComparativePerformance
                  examId={Number(examId)}
                  courseId={resultCourseId}
                  studentScore={score}
                  isLoading={isLoadingExam && !initialResults}
                />
              )}
            </Box>
          </motion.div>

          {/* Action Buttons */}
          <motion.div variants={itemVariants}>
            <Box
              sx={{
                display: 'flex',
                justifyContent: 'center',
                gap: { xs: 1.5, sm: 2 },
                mt: { xs: 3, sm: 4 },
                flexDirection: isMobile ? 'column' : 'row'
              }}
            >
              <Button
                variant="outlined"
                onClick={() => navigate('/mcq')}
                size={isMobile ? "large" : "medium"}
                fullWidth={isMobile}
                startIcon={<ArrowBackIcon />}
                sx={{
                  py: isMobile ? 1.5 : 1,
                  borderRadius: isMobile ? 2 : 1
                }}
              >
                Back to Dashboard
              </Button>
              <Button
                variant="contained"
                color="primary"
                onClick={() => navigate(`/mcq/practice/${resultCourseId}`)}
                size={isMobile ? "large" : "medium"}
                fullWidth={isMobile}
                startIcon={<SchoolIcon />}
                sx={{
                  py: isMobile ? 1.5 : 1,
                  borderRadius: isMobile ? 2 : 1
                }}
              >
                Practice More
              </Button>

              {isMobile && (
                <Button
                  variant="outlined"
                  color="secondary"
                  onClick={toggleShareDrawer(true)}
                  size="large"
                  fullWidth
                  startIcon={<ShareIcon />}
                  sx={{
                    py: 1.5,
                    borderRadius: 2,
                    mt: 1
                  }}
                >
                  Share Results
                </Button>
              )}
            </Box>
          </motion.div>
        </Paper>

        {/* Share Drawer (Mobile) */}
        <SwipeableDrawer
          anchor="bottom"
          open={shareDrawerOpen}
          onClose={toggleShareDrawer(false)}
          onOpen={toggleShareDrawer(true)}
          sx={{
            '& .MuiDrawer-paper': {
              borderTopLeftRadius: 16,
              borderTopRightRadius: 16,
              pb: 2
            }
          }}
        >
          <Box sx={{ p: 2 }}>
            <Typography variant="h6" gutterBottom>
              Share Your Results
            </Typography>
            <Divider sx={{ mb: 2 }} />
            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1.5 }}>
              <Button
                variant="outlined"
                fullWidth
                onClick={handleShare}
                startIcon={<ShareIcon />}
              >
                Share via Message
              </Button>
              <Button
                variant="outlined"
                fullWidth
                onClick={handleShare}
                startIcon={<ShareIcon />}
              >
                Copy Link
              </Button>
              <Button
                variant="outlined"
                color="error"
                fullWidth
                onClick={toggleShareDrawer(false)}
                sx={{ mt: 1 }}
              >
                Cancel
              </Button>
            </Box>
          </Box>
        </SwipeableDrawer>
      </Box>
    </motion.div>
  );
};

export default ExamResults;
