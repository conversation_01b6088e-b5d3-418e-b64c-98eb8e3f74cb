import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Grid,
  Card,
  CardContent,
  CardHeader,
  Button,
  Tabs,
  Tab,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Chip,
  CircularProgress,
  useTheme,
  Divider,
  IconButton,
  Tooltip,
} from '@mui/material';
import {
  Timeline as TimelineIcon,
  TrendingUp as TrendingUpIcon,
  School as SchoolIcon,
  Visibility as ViewIcon,
  FilterList as FilterIcon,
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';
import { useAuth } from '../../contexts/AuthContext';
import { getExams, Exam } from '../../api/studentProgress';
import { getCourses } from '../../api/courses';
import {
  BarChart,
  Bar,
  LineChart,
  Line,
  XAxis,
  YAxis,
  CartesianGrid,
  Tooltip as Recharts<PERSON>ool<PERSON>,
  Legend,
  ResponsiveContainer,
} from 'recharts';

const ExamHistory: React.FC = () => {
  const theme = useTheme();
  const navigate = useNavigate();
  const { user } = useAuth();
  const [tabValue, setTabValue] = useState(0);
  const [selectedCourseId, setSelectedCourseId] = useState<number | null>(null);

  // Fetch all exams for the current user
  const {
    data: exams = [],
    isLoading: isLoadingExams,
    error: examsError,
  } = useQuery({
    queryKey: ['exams', user?.id, selectedCourseId],
    queryFn: () => getExams(user?.id || 0, selectedCourseId || undefined),
    enabled: !!user?.id,
  });

  // Fetch all courses with enrolled courses prioritized
  const {
    data: courses = [],
    isLoading: isLoadingCourses,
    error: coursesError,
  } = useQuery({
    queryKey: ['courses', 'prioritizeEnrolled'],
    queryFn: () => getCourses(undefined, undefined, false, true),
  });

  // Process exam data for charts
  const [chartData, setChartData] = useState<any[]>([]);
  const [coursePerformance, setCoursePerformance] = useState<any[]>([]);

  useEffect(() => {
    if (exams.length > 0) {
      // Sort exams by date
      const sortedExams = [...exams].sort(
        (a, b) => new Date(a.start_time).getTime() - new Date(b.start_time).getTime()
      );

      // Format data for timeline chart
      const timelineData = sortedExams.map((exam, index) => ({
        id: exam.id,
        name: `Exam ${index + 1}`,
        score: exam.score || 0,
        date: new Date(exam.start_time).toLocaleDateString(),
        formattedDate: new Date(exam.start_time).toLocaleDateString('en-US', {
          month: 'short',
          day: 'numeric',
        }),
        courseId: exam.course_id,
        courseName: courses.find(c => c.id === exam.course_id)?.name || `Course ${exam.course_id}`,
      }));

      setChartData(timelineData);

      // Group exams by course for course performance chart
      const courseMap = new Map();
      sortedExams.forEach(exam => {
        if (!courseMap.has(exam.course_id)) {
          courseMap.set(exam.course_id, {
            courseId: exam.course_id,
            courseName: courses.find(c => c.id === exam.course_id)?.name || `Course ${exam.course_id}`,
            exams: [],
            avgScore: 0,
            totalExams: 0,
          });
        }

        if (exam.score !== null) {
          const courseData = courseMap.get(exam.course_id);
          courseData.exams.push(exam);
          courseData.totalExams += 1;
          courseData.avgScore = courseData.exams.reduce((sum: number, e: Exam) => sum + (e.score || 0), 0) / courseData.totalExams;
        }
      });

      setCoursePerformance(Array.from(courseMap.values()));
    }
  }, [exams, courses]);

  // Handle tab change
  const handleTabChange = (event: React.SyntheticEvent, newValue: number) => {
    setTabValue(newValue);
  };

  // Get grade letter and color based on score
  const getGrade = (score: number | null) => {
    if (score === null) return { grade: 'N/A', color: theme.palette.grey[500] };
    if (score >= 90) return { grade: 'A', color: theme.palette.success.dark };
    if (score >= 80) return { grade: 'B', color: theme.palette.success.main };
    if (score >= 70) return { grade: 'C', color: theme.palette.warning.main };
    if (score >= 60) return { grade: 'D', color: theme.palette.warning.dark };
    return { grade: 'F', color: theme.palette.error.main };
  };

  // Filter exams by course
  const handleCourseFilter = (courseId: number | null) => {
    setSelectedCourseId(courseId === selectedCourseId ? null : courseId);
  };

  // Loading state
  if (isLoadingExams || isLoadingCourses) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  // Error state
  if (examsError || coursesError) {
    return (
      <Box>
        <Typography variant="h5" color="error" gutterBottom>
          Error loading exam history
        </Typography>
        <Button variant="contained" onClick={() => navigate('/mcq')}>
          Back to Dashboard
        </Button>
      </Box>
    );
  }

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 4 }}>
        <Typography variant="h4" component="h1" fontWeight="bold">
          Exam History
        </Typography>
        <Button variant="outlined" onClick={() => navigate('/mcq')}>
          Back to Dashboard
        </Button>
      </Box>

      {exams.length === 0 ? (
        <Paper sx={{ p: 4, textAlign: 'center' }}>
          <Typography variant="h6" gutterBottom>
            No exam history found
          </Typography>
          <Typography variant="body1" color="text.secondary" paragraph>
            You haven't taken any exams yet. Start practicing and take exams to see your performance history.
          </Typography>
          <Button
            variant="contained"
            color="primary"
            onClick={() => navigate('/mcq')}
            sx={{ mt: 2 }}
          >
            Go to MCQ Dashboard
          </Button>
        </Paper>
      ) : (
        <>
          {/* Performance Summary Card */}
          <Card sx={{ mb: 4, boxShadow: theme.shadows[3] }}>
            <CardHeader
              title="Performance Overview"
              titleTypographyProps={{ variant: 'h5', fontWeight: 'bold' }}
              avatar={<TrendingUpIcon color="primary" fontSize="large" />}
            />
            <CardContent>
              <Grid container spacing={3}>
                <Grid item xs={12} md={4}>
                  <Box sx={{ textAlign: 'center' }}>
                    <Typography variant="h6" gutterBottom>
                      Total Exams Taken
                    </Typography>
                    <Typography variant="h3" fontWeight="bold" color="primary">
                      {exams.length}
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={12} md={4}>
                  <Box sx={{ textAlign: 'center' }}>
                    <Typography variant="h6" gutterBottom>
                      Average Score
                    </Typography>
                    <Typography
                      variant="h3"
                      fontWeight="bold"
                      color={getGrade(exams.reduce((sum, exam) => sum + (exam.score || 0), 0) / exams.length).color}
                    >
                      {(exams.reduce((sum, exam) => sum + (exam.score || 0), 0) / exams.length).toFixed(1)}%
                    </Typography>
                  </Box>
                </Grid>
                <Grid item xs={12} md={4}>
                  <Box sx={{ textAlign: 'center' }}>
                    <Typography variant="h6" gutterBottom>
                      Courses
                    </Typography>
                    <Typography variant="h3" fontWeight="bold" color="primary">
                      {new Set(exams.map(exam => exam.course_id)).size}
                    </Typography>
                  </Box>
                </Grid>
              </Grid>
            </CardContent>
          </Card>

          {/* Course Filter */}
          <Box sx={{ mb: 3, display: 'flex', flexWrap: 'wrap', gap: 1 }}>
            <Tooltip title="Show all courses">
              <Chip
                label="All Courses"
                color={selectedCourseId === null ? 'primary' : 'default'}
                onClick={() => handleCourseFilter(null)}
                icon={<FilterIcon />}
                sx={{ fontWeight: selectedCourseId === null ? 'bold' : 'normal' }}
              />
            </Tooltip>
            {courses.map(course => (
              <Tooltip key={course.id} title={`Filter by ${course.name}`}>
                <Chip
                  label={course.name}
                  color={selectedCourseId === course.id ? 'primary' : 'default'}
                  onClick={() => handleCourseFilter(course.id)}
                  sx={{ fontWeight: selectedCourseId === course.id ? 'bold' : 'normal' }}
                />
              </Tooltip>
            ))}
          </Box>

          {/* Tabs for different views */}
          <Box sx={{ mb: 3 }}>
            <Tabs
              value={tabValue}
              onChange={handleTabChange}
              variant="fullWidth"
              textColor="primary"
              indicatorColor="primary"
            >
              <Tab label="Timeline" icon={<TimelineIcon />} iconPosition="start" />
              <Tab label="Course Performance" icon={<SchoolIcon />} iconPosition="start" />
              <Tab label="Exam List" icon={<FilterIcon />} iconPosition="start" />
            </Tabs>
          </Box>

          {/* Timeline Tab */}
          {tabValue === 0 && (
            <Card sx={{ boxShadow: theme.shadows[3] }}>
              <CardHeader title="Score Timeline" />
              <CardContent>
                <Box sx={{ height: 400 }}>
                  {chartData.length > 0 ? (
                    <ResponsiveContainer width="100%" height="100%">
                      <LineChart
                        data={chartData}
                        margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                      >
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="formattedDate" />
                        <YAxis domain={[0, 100]} />
                        <RechartsTooltip
                          formatter={(value: any, name: any, props: any) => {
                            const { payload } = props;
                            return [
                              `${value}%`,
                              `${payload.courseName} - ${payload.date}`
                            ];
                          }}
                        />
                        <Legend />
                        <Line
                          type="monotone"
                          dataKey="score"
                          stroke={theme.palette.primary.main}
                          activeDot={{ r: 8 }}
                          strokeWidth={2}
                        />
                      </LineChart>
                    </ResponsiveContainer>
                  ) : (
                    <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
                      <Typography variant="body1">
                        No data available for the selected filter.
                      </Typography>
                    </Box>
                  )}
                </Box>
              </CardContent>
            </Card>
          )}

          {/* Course Performance Tab */}
          {tabValue === 1 && (
            <Card sx={{ boxShadow: theme.shadows[3] }}>
              <CardHeader title="Course Performance" />
              <CardContent>
                <Box sx={{ height: 400 }}>
                  {coursePerformance.length > 0 ? (
                    <ResponsiveContainer width="100%" height="100%">
                      <BarChart
                        data={coursePerformance}
                        margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                      >
                        <CartesianGrid strokeDasharray="3 3" />
                        <XAxis dataKey="courseName" />
                        <YAxis domain={[0, 100]} />
                        <RechartsTooltip
                          formatter={(value: any) => [`${value.toFixed(1)}%`, 'Average Score']}
                        />
                        <Legend />
                        <Bar
                          dataKey="avgScore"
                          name="Average Score"
                          fill={theme.palette.primary.main}
                          radius={[4, 4, 0, 0]}
                        />
                      </BarChart>
                    </ResponsiveContainer>
                  ) : (
                    <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100%' }}>
                      <Typography variant="body1">
                        No data available for the selected filter.
                      </Typography>
                    </Box>
                  )}
                </Box>
              </CardContent>
            </Card>
          )}

          {/* Exam List Tab */}
          {tabValue === 2 && (
            <Card sx={{ boxShadow: theme.shadows[3] }}>
              <CardHeader title="Exam List" />
              <CardContent>
                <TableContainer component={Paper} variant="outlined" sx={{ maxHeight: 400, overflow: 'auto' }}>
                  <Table stickyHeader>
                    <TableHead>
                      <TableRow>
                        <TableCell>Date</TableCell>
                        <TableCell>Course</TableCell>
                        <TableCell>Score</TableCell>
                        <TableCell>Grade</TableCell>
                        <TableCell>Questions</TableCell>
                        <TableCell>Actions</TableCell>
                      </TableRow>
                    </TableHead>
                    <TableBody>
                      {exams.map((exam) => {
                        const examGrade = getGrade(exam.score);
                        const courseName = courses.find(c => c.id === exam.course_id)?.name || `Course ${exam.course_id}`;

                        return (
                          <TableRow key={exam.id}>
                            <TableCell>
                              {new Date(exam.start_time).toLocaleDateString()}
                              <Typography variant="caption" display="block" color="text.secondary">
                                {new Date(exam.start_time).toLocaleTimeString()}
                              </Typography>
                            </TableCell>
                            <TableCell>{courseName}</TableCell>
                            <TableCell>{exam.score !== null ? `${exam.score.toFixed(1)}%` : 'N/A'}</TableCell>
                            <TableCell>
                              <Chip
                                label={examGrade.grade}
                                size="small"
                                sx={{
                                  bgcolor: examGrade.color,
                                  color: 'white',
                                  fontWeight: 'bold'
                                }}
                              />
                            </TableCell>
                            <TableCell>
                              {exam.correct_answers !== null ? `${exam.correct_answers}/${exam.total_questions}` : `${exam.total_questions} questions`}
                            </TableCell>
                            <TableCell>
                              <Tooltip title="View Details">
                                <IconButton
                                  size="small"
                                  color="primary"
                                  onClick={() => navigate(`/mcq/exam-details/${exam.id}`)}
                                >
                                  <ViewIcon />
                                </IconButton>
                              </Tooltip>
                            </TableCell>
                          </TableRow>
                        );
                      })}
                    </TableBody>
                  </Table>
                </TableContainer>
              </CardContent>
            </Card>
          )}
        </>
      )}
    </Box>
  );
};

export default ExamHistory;
