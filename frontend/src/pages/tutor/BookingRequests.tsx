import React, { useState } from 'react';
import {
  <PERSON>,
  Card,
  CardContent,
  <PERSON><PERSON><PERSON>,
  Chip,
  Button,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Alert,
  CircularProgress,
  Avatar,
  Divider,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  useTheme,
  useMediaQuery
} from '@mui/material';
import {
  Schedule as ScheduleIcon,
  Person as PersonIcon,
  School as SchoolIcon,
  AccessTime as TimeIcon,
  Message as MessageIcon,
  Check as CheckIcon,
  Close as CloseIcon,
  Chat as ChatIcon
} from '@mui/icons-material';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { format } from 'date-fns';
import { useNavigate } from 'react-router-dom';
import {
  getBookingRequests,
  BookingRequest,
  BookingRequestStatus,
  acceptBookingRequest,
  declineBookingRequest
} from '../../api/bookingRequests';

const TutorBookingRequests: React.FC = () => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('sm'));
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  
  const [statusFilter, setStatusFilter] = useState<BookingRequestStatus | ''>('');
  const [responseDialog, setResponseDialog] = useState<{
    open: boolean;
    request: BookingRequest | null;
    type: 'accept' | 'decline' | null;
  }>({
    open: false,
    request: null,
    type: null
  });
  const [responseMessage, setResponseMessage] = useState('');

  const { 
    data: requests = [], 
    isLoading, 
    error 
  } = useQuery<BookingRequest[]>({
    queryKey: ['tutor-booking-requests', statusFilter],
    queryFn: () => getBookingRequests(0, 100, statusFilter || undefined)
  });

  const acceptMutation = useMutation({
    mutationFn: ({ id, response }: { id: number; response: string }) => 
      acceptBookingRequest(id, response),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['tutor-booking-requests'] });
      handleCloseDialog();
    }
  });

  const declineMutation = useMutation({
    mutationFn: ({ id, response }: { id: number; response: string }) => 
      declineBookingRequest(id, response),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['tutor-booking-requests'] });
      handleCloseDialog();
    }
  });

  const handleOpenDialog = (request: BookingRequest, type: 'accept' | 'decline') => {
    setResponseDialog({ open: true, request, type });
    setResponseMessage('');
  };

  const handleCloseDialog = () => {
    setResponseDialog({ open: false, request: null, type: null });
    setResponseMessage('');
  };

  const handleSubmitResponse = () => {
    if (!responseDialog.request || !responseMessage.trim()) return;

    const { request, type } = responseDialog;
    
    if (type === 'accept') {
      acceptMutation.mutate({ id: request.id, response: responseMessage });
    } else {
      declineMutation.mutate({ id: request.id, response: responseMessage });
    }
  };

  const getStatusColor = (status: BookingRequestStatus) => {
    switch (status) {
      case BookingRequestStatus.PENDING:
        return 'warning';
      case BookingRequestStatus.ACCEPTED:
        return 'success';
      case BookingRequestStatus.DECLINED:
        return 'error';
      case BookingRequestStatus.CANCELLED:
        return 'default';
      default:
        return 'default';
    }
  };

  const getStatusText = (status: BookingRequestStatus) => {
    switch (status) {
      case BookingRequestStatus.PENDING:
        return 'Pending';
      case BookingRequestStatus.ACCEPTED:
        return 'Accepted';
      case BookingRequestStatus.DECLINED:
        return 'Declined';
      case BookingRequestStatus.CANCELLED:
        return 'Cancelled';
      default:
        return status;
    }
  };

  if (error) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="error">
          Failed to load booking requests. Please try again later.
        </Alert>
      </Box>
    );
  }

  return (
    <Box sx={{ p: { xs: 2, md: 3 } }}>
      <Typography variant="h4" component="h1" gutterBottom>
        Booking Requests
      </Typography>
      <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
        Manage student requests for tutoring sessions
      </Typography>

      {/* Filter */}
      <Box sx={{ mb: 3 }}>
        <FormControl size="small" sx={{ minWidth: 200 }}>
          <InputLabel>Filter by Status</InputLabel>
          <Select
            value={statusFilter}
            onChange={(e) => setStatusFilter(e.target.value as BookingRequestStatus | '')}
            label="Filter by Status"
          >
            <MenuItem value="">All Requests</MenuItem>
            <MenuItem value={BookingRequestStatus.PENDING}>Pending</MenuItem>
            <MenuItem value={BookingRequestStatus.ACCEPTED}>Accepted</MenuItem>
            <MenuItem value={BookingRequestStatus.DECLINED}>Declined</MenuItem>
          </Select>
        </FormControl>
      </Box>

      {/* Loading State */}
      {isLoading && (
        <Box sx={{ display: 'flex', justifyContent: 'center', py: 4 }}>
          <CircularProgress />
        </Box>
      )}

      {/* Requests List */}
      {!isLoading && (
        <Grid container spacing={3}>
          {requests.map((request) => (
            <Grid item xs={12} key={request.id}>
              <Card>
                <CardContent>
                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'flex-start', mb: 2 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
                      <Avatar>
                        {request.student_name?.charAt(0) || 'S'}
                      </Avatar>
                      <Box>
                        <Typography variant="h6" component="h3">
                          Tutoring Session Request
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          Student: {request.student_name}
                        </Typography>
                      </Box>
                    </Box>
                    <Chip
                      label={getStatusText(request.status)}
                      color={getStatusColor(request.status) as any}
                      size="small"
                    />
                  </Box>

                  <Grid container spacing={2} sx={{ mb: 2 }}>
                    <Grid item xs={12} sm={6}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <ScheduleIcon fontSize="small" color="action" />
                        <Box>
                          <Typography variant="body2" color="text.secondary">
                            Request Date
                          </Typography>
                          <Typography variant="body2">
                            {format(new Date(request.created_at), 'MMM dd, yyyy h:mm a')}
                          </Typography>
                        </Box>
                      </Box>
                    </Grid>

                    {request.course_name && (
                      <Grid item xs={12} sm={6}>
                        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                          <SchoolIcon fontSize="small" color="action" />
                          <Box>
                            <Typography variant="body2" color="text.secondary">
                              Course
                            </Typography>
                            <Typography variant="body2">
                              {request.course_name}
                            </Typography>
                          </Box>
                        </Box>
                      </Grid>
                    )}
                  </Grid>

                  <Divider sx={{ my: 2 }} />

                  <Box sx={{ mb: 2 }}>
                    <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                      Student's Message:
                    </Typography>
                    <Typography variant="body2">
                      {request.message || "I would like to request a tutoring session."}
                    </Typography>
                  </Box>

                  {request.tutor_response && (
                    <Box sx={{ bgcolor: 'grey.50', p: 2, borderRadius: 1, mb: 2 }}>
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1, mb: 1 }}>
                        <MessageIcon fontSize="small" color="action" />
                        <Typography variant="body2" color="text.secondary" fontWeight="medium">
                          Your Response:
                        </Typography>
                      </Box>
                      <Typography variant="body2">
                        {request.tutor_response}
                      </Typography>
                      {request.response_date && (
                        <Typography variant="caption" color="text.secondary" sx={{ mt: 1, display: 'block' }}>
                          Responded on {format(new Date(request.response_date), 'MMM dd, yyyy h:mm a')}
                        </Typography>
                      )}
                    </Box>
                  )}

                  <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', flexWrap: 'wrap', gap: 2 }}>
                    <Typography variant="caption" color="text.secondary">
                      Requested on {format(new Date(request.created_at), 'MMM dd, yyyy h:mm a')}
                    </Typography>
                    
                    {request.status === BookingRequestStatus.PENDING && (
                      <Box sx={{ display: 'flex', gap: 1 }}>
                        <Button
                          variant="outlined"
                          color="error"
                          size="small"
                          startIcon={<CloseIcon />}
                          onClick={() => handleOpenDialog(request, 'decline')}
                        >
                          Decline
                        </Button>
                        <Button
                          variant="contained"
                          color="success"
                          size="small"
                          startIcon={<CheckIcon />}
                          onClick={() => handleOpenDialog(request, 'accept')}
                        >
                          Accept
                        </Button>
                      </Box>
                    )}

                    {request.status === BookingRequestStatus.ACCEPTED && request.chat_room_id && (
                      <Button
                        variant="contained"
                        size="small"
                        startIcon={<ChatIcon />}
                        onClick={() => navigate(`/chat/${request.chat_room_id}`)}
                        color="primary"
                      >
                        Start Chat
                      </Button>
                    )}
                  </Box>
                </CardContent>
              </Card>
            </Grid>
          ))}
        </Grid>
      )}

      {/* No Results */}
      {!isLoading && requests.length === 0 && (
        <Box sx={{ textAlign: 'center', py: 4 }}>
          <Typography variant="h6" color="text.secondary">
            No booking requests found
          </Typography>
          <Typography variant="body2" color="text.secondary">
            {statusFilter ? 'Try changing the filter' : 'Students will be able to send you requests once you have an active tutor profile'}
          </Typography>
        </Box>
      )}

      {/* Response Dialog */}
      <Dialog open={responseDialog.open} onClose={handleCloseDialog} maxWidth="sm" fullWidth>
        <DialogTitle>
          {responseDialog.type === 'accept' ? 'Accept' : 'Decline'} Booking Request
        </DialogTitle>
        <DialogContent>
          {responseDialog.request && (
            <Box sx={{ mb: 2 }}>
              <Typography variant="body2" color="text.secondary">
                Request from: <strong>{responseDialog.request.student_name}</strong>
              </Typography>
              <Typography variant="body2" color="text.secondary">
                Request: <strong>Tutoring Session</strong>
              </Typography>
            </Box>
          )}
          <TextField
            fullWidth
            multiline
            rows={4}
            label={`Response to student`}
            value={responseMessage}
            onChange={(e) => setResponseMessage(e.target.value)}
            placeholder={
              responseDialog.type === 'accept' 
                ? "Great! I'd be happy to help you with this. Let me know if you have any questions about scheduling..."
                : "Thank you for your interest. Unfortunately, I'm not available at this time..."
            }
          />
        </DialogContent>
        <DialogActions>
          <Button onClick={handleCloseDialog}>Cancel</Button>
          <Button
            onClick={handleSubmitResponse}
            variant="contained"
            color={responseDialog.type === 'accept' ? 'success' : 'error'}
            disabled={!responseMessage.trim() || acceptMutation.isPending || declineMutation.isPending}
          >
            {acceptMutation.isPending || declineMutation.isPending ? 'Sending...' : 
             responseDialog.type === 'accept' ? 'Accept Request' : 'Decline Request'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default TutorBookingRequests;
