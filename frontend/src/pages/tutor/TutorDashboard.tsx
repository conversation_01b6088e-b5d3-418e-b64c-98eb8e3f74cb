import React from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Button,
  Paper,
  Chip,
  Avatar,
  CircularProgress,
  Alert,
  useTheme,
  useMediaQuery,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Divider,
} from '@mui/material';
import {
  School,
  Event,
  TrendingUp,
  Add,
  Edit,
  CalendarToday,
  AttachMoney,
  Notifications,
  AccessTime,
  CheckCircle,
  Schedule,
  Analytics,
  Person,
  People,
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';
import { motion } from 'framer-motion';
import { format, isToday, isTomorrow, parseISO } from 'date-fns';
import { useAuth } from '../../contexts/AuthContext';
import { getMyTutorProfile, getMyTutorDashboard, TutorProfile, TutorDashboardStats } from '../../api/tutors';
import { getSessions, Session } from '../../api/sessions';

interface StatCardProps {
  title: string;
  value: number | string;
  icon: React.ReactNode;
  color: string;
  subtitle?: string;
}

const StatCard: React.FC<StatCardProps> = ({ title, value, icon, color, subtitle }) => {
  const theme = useTheme();
  
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.3 }}
    >
      <Card
        sx={{
          height: '100%',
          background: theme.palette.mode === 'dark'
            ? `linear-gradient(135deg, ${theme.palette.grey[800]} 0%, ${theme.palette.grey[900]} 100%)`
            : `linear-gradient(135deg, ${theme.palette.background.paper} 0%, ${theme.palette.grey[50]} 100%)`,
          border: `1px solid ${theme.palette.divider}`,
          '&:hover': {
            transform: 'translateY(-2px)',
            boxShadow: theme.shadows[8],
          },
          transition: 'all 0.3s ease-in-out',
        }}
      >
        <CardContent>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <Box
              sx={{
                p: 1,
                borderRadius: 2,
                backgroundColor: color,
                color: 'white',
                mr: 2,
              }}
            >
              {icon}
            </Box>
            <Box>
              <Typography variant="h4" component="div" fontWeight="bold">
                {value}
              </Typography>
              <Typography variant="body2" color="text.secondary">
                {title}
              </Typography>
              {subtitle && (
                <Typography variant="caption" color="text.secondary">
                  {subtitle}
                </Typography>
              )}
            </Box>
          </Box>
        </CardContent>
      </Card>
    </motion.div>
  );
};

const TutorDashboard: React.FC = () => {
  const { user } = useAuth();
  const theme = useTheme();
  const navigate = useNavigate();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  // Fetch tutor profile
  const {
    data: tutorProfile,
    isLoading: isLoadingProfile,
    error: profileError,
  } = useQuery({
    queryKey: ['tutorProfile'],
    queryFn: getMyTutorProfile,
    retry: false,
  });

  // Fetch dashboard stats
  const {
    data: dashboardStats,
    isLoading: isLoadingStats,
    error: statsError,
  } = useQuery({
    queryKey: ['tutorDashboard'],
    queryFn: getMyTutorDashboard,
    enabled: !!tutorProfile,
  });

  // Fetch tutor's sessions
  const {
    data: sessions,
    isLoading: isLoadingSessions,
  } = useQuery({
    queryKey: ['tutorSessions', user?.id],
    queryFn: () => getSessions(undefined, user?.id),
    enabled: !!user?.id,
  });

  const isLoading = isLoadingProfile || isLoadingStats;

  if (isLoading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  if (profileError && profileError.message.includes('404')) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="info" sx={{ mb: 3 }}>
          You haven't set up your tutor profile yet. Create one to start offering tutoring services.
        </Alert>
        <Button
          variant="contained"
          startIcon={<Add />}
          onClick={() => navigate('/complete-tutor-profile')}
          size="large"
        >
          Complete Tutor Profile
        </Button>
      </Box>
    );
  }

  if (profileError || statsError) {
    return (
      <Alert severity="error" sx={{ mt: 2 }}>
        Error loading dashboard: {(profileError || statsError)?.message}
      </Alert>
    );
  }

  return (
    <Box sx={{ maxWidth: '100%' }}>
      {/* Header */}
      <Box
        sx={{
          mb: { xs: 2, sm: 4 },
          display: 'flex',
          flexDirection: isMobile ? 'column' : 'row',
          justifyContent: 'space-between',
          alignItems: isMobile ? 'flex-start' : 'center',
          gap: 2,
        }}
      >
        <Box>
          <Typography
            variant={isMobile ? 'h5' : 'h4'}
            component="h1"
            fontWeight="bold"
            gutterBottom
          >
            Tutor Dashboard
          </Typography>
          <Typography
            variant={isMobile ? 'body1' : 'h6'}
            color="text.secondary"
            gutterBottom
          >
            Welcome back, {user?.full_name}!
          </Typography>
        </Box>

        <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
          <Button
            variant="outlined"
            startIcon={<Analytics />}
            onClick={() => navigate('/tutor/analytics')}
          >
            Analytics
          </Button>
          <Button
            variant="contained"
            startIcon={<Add />}
            onClick={() => navigate('/tutor/tutorials/new')}
          >
            Create Tutorial
          </Button>
        </Box>
      </Box>

      {/* Profile Summary */}
      {tutorProfile && (
        <Paper sx={{ p: 3, mb: 3 }}>
          <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
            <Avatar
              src={tutorProfile.user?.profile_picture_url}
              sx={{ width: 64, height: 64, mr: 2 }}
            >
              {tutorProfile.user?.full_name?.charAt(0)}
            </Avatar>
            <Box sx={{ flex: 1 }}>
              <Typography variant="h6" gutterBottom>
                {tutorProfile.user?.full_name}
              </Typography>
              <Typography variant="body2" color="text.secondary" gutterBottom>
                {tutorProfile.experience_years ? `${tutorProfile.experience_years} years experience` : 'New tutor'}
                {tutorProfile.hourly_rate && ` • $${tutorProfile.hourly_rate}/hour`}
              </Typography>
              <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                {tutorProfile.specializations?.map((course) => (
                  <Chip key={course.id} label={course.name} size="small" />
                ))}
              </Box>
            </Box>
            <Chip
              label={tutorProfile.is_available ? 'Available' : 'Unavailable'}
              color={tutorProfile.is_available ? 'success' : 'default'}
            />
          </Box>
          {tutorProfile.bio && (
            <Typography variant="body2" color="text.secondary">
              {tutorProfile.bio}
            </Typography>
          )}
        </Paper>
      )}

      {/* Statistics Grid */}
      {dashboardStats && (
        <Grid container spacing={3} sx={{ mb: 3 }}>
          <Grid item xs={12} sm={6} md={3}>
            <StatCard
              title="Total Sessions"
              value={dashboardStats.total_sessions}
              icon={<Event sx={{ fontSize: 24 }} />}
              color="#2196f3"
            />
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <StatCard
              title="Completed Sessions"
              value={dashboardStats.completed_sessions}
              icon={<School sx={{ fontSize: 24 }} />}
              color="#4caf50"
            />
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <StatCard
              title="Upcoming Sessions"
              value={dashboardStats.upcoming_sessions}
              icon={<CalendarToday sx={{ fontSize: 24 }} />}
              color="#ff9800"
            />
          </Grid>

          <Grid item xs={12} sm={6} md={3}>
            <StatCard
              title="Total Students"
              value={dashboardStats.total_students}
              icon={<People sx={{ fontSize: 24 }} />}
              color="#9c27b0"
            />
          </Grid>



          <Grid item xs={12} sm={6} md={3}>
            <StatCard
              title="This Month"
              value={dashboardStats.this_month_sessions}
              icon={<TrendingUp sx={{ fontSize: 24 }} />}
              color="#00bcd4"
              subtitle="Sessions"
            />
          </Grid>

          {dashboardStats.total_earnings && (
            <Grid item xs={12} sm={6} md={3}>
              <StatCard
                title="Total Earnings"
                value={`$${Number(dashboardStats.total_earnings).toFixed(2)}`}
                icon={<AttachMoney sx={{ fontSize: 24 }} />}
                color="#795548"
              />
            </Grid>
          )}

          {dashboardStats.this_month_earnings && (
            <Grid item xs={12} sm={6} md={3}>
              <StatCard
                title="This Month Earnings"
                value={`$${Number(dashboardStats.this_month_earnings).toFixed(2)}`}
                icon={<AttachMoney sx={{ fontSize: 24 }} />}
                color="#607d8b"
              />
            </Grid>
          )}
        </Grid>
      )}

      {/* Sessions Overview */}
      {sessions && (
        <Grid container spacing={3} sx={{ mb: 3 }}>
          {/* Upcoming Sessions */}
          <Grid item xs={12} md={6}>
            <Paper sx={{ p: 3, height: '100%' }}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Schedule sx={{ mr: 1, color: 'primary.main' }} />
                <Typography variant="h6">Upcoming Sessions</Typography>
              </Box>
              {sessions.filter(session =>
                session.status === 'scheduled' &&
                new Date(session.start_time) > new Date()
              ).slice(0, 3).length > 0 ? (
                <List dense>
                  {sessions
                    .filter(session =>
                      session.status === 'scheduled' &&
                      new Date(session.start_time) > new Date()
                    )
                    .slice(0, 3)
                    .map((session) => (
                      <ListItem key={session.id} sx={{ px: 0 }}>
                        <ListItemIcon>
                          <Event color="primary" />
                        </ListItemIcon>
                        <ListItemText
                          primary={session.title}
                          secondary={
                            <Box>
                              <Typography variant="caption" display="block">
                                {format(parseISO(session.start_time), 'MMM dd, yyyy • h:mm a')}
                              </Typography>
                              {session.student?.full_name && (
                                <Typography variant="caption" color="text.secondary">
                                  Student: {session.student.full_name}
                                </Typography>
                              )}
                            </Box>
                          }
                        />
                      </ListItem>
                    ))}
                </List>
              ) : (
                <Typography variant="body2" color="text.secondary">
                  No upcoming sessions scheduled
                </Typography>
              )}
              <Button
                size="small"
                onClick={() => navigate('/tutor/tutorials')}
                sx={{ mt: 1 }}
              >
                View All Sessions
              </Button>
            </Paper>
          </Grid>

          {/* Recent Sessions */}
          <Grid item xs={12} md={6}>
            <Paper sx={{ p: 3, height: '100%' }}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <CheckCircle sx={{ mr: 1, color: 'success.main' }} />
                <Typography variant="h6">Recent Sessions</Typography>
              </Box>
              {sessions.filter(session => session.status === 'completed').slice(0, 3).length > 0 ? (
                <List dense>
                  {sessions
                    .filter(session => session.status === 'completed')
                    .slice(0, 3)
                    .map((session) => (
                      <ListItem key={session.id} sx={{ px: 0 }}>
                        <ListItemIcon>
                          <CheckCircle color="success" />
                        </ListItemIcon>
                        <ListItemText
                          primary={session.title}
                          secondary={
                            <Box>
                              <Typography variant="caption" display="block">
                                {format(parseISO(session.end_time), 'MMM dd, yyyy')}
                              </Typography>
                              {session.student?.full_name && (
                                <Typography variant="caption" color="text.secondary">
                                  Student: {session.student.full_name}
                                </Typography>
                              )}
                            </Box>
                          }
                        />
                      </ListItem>
                    ))}
                </List>
              ) : (
                <Typography variant="body2" color="text.secondary">
                  No completed sessions yet
                </Typography>
              )}
              <Button
                size="small"
                onClick={() => navigate('/tutor/tutorials')}
                sx={{ mt: 1 }}
              >
                View Session History
              </Button>
            </Paper>
          </Grid>
        </Grid>
      )}

      {/* Quick Actions */}
      <Paper sx={{ p: 3 }}>
        <Typography variant="h6" gutterBottom>
          Quick Actions
        </Typography>
        <Grid container spacing={2}>
          <Grid item xs={12} sm={6} md={3}>
            <Button
              fullWidth
              variant="outlined"
              startIcon={<Add />}
              onClick={() => navigate('/tutor/tutorials/new')}
            >
              Create Tutorial
            </Button>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Button
              fullWidth
              variant="outlined"
              startIcon={<Event />}
              onClick={() => navigate('/tutor/tutorials')}
            >
              View Tutorials
            </Button>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Button
              fullWidth
              variant="outlined"
              startIcon={<Notifications />}
              onClick={() => navigate('/tutor/booking-requests')}
            >
              Booking Requests
            </Button>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Button
              fullWidth
              variant="outlined"
              startIcon={<Analytics />}
              onClick={() => navigate('/tutor/analytics')}
            >
              View Analytics
            </Button>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Button
              fullWidth
              variant="outlined"
              startIcon={<AttachMoney />}
              onClick={() => navigate('/tutor/earnings')}
            >
              View Earnings
            </Button>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Button
              fullWidth
              variant="outlined"
              startIcon={<Person />}
              onClick={() => navigate('/tutor/profile')}
            >
              Edit Profile
            </Button>
          </Grid>
        </Grid>
      </Paper>
    </Box>
  );
};

export default TutorDashboard;
