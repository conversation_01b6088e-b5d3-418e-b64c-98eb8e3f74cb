import React, { useState } from 'react';
import {
  Box,
  Typography,
  Paper,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  TextField,
  InputAdornment,
  CircularProgress,
  Alert,
} from '@mui/material';
import {
  Star,
  Search,
} from '@mui/icons-material';

import { useQuery } from '@tanstack/react-query';
import { getMyTutorReviews, getMyTutorProfile } from '../../api/tutors';
import ReviewStats from '../../components/ReviewStats';
import ReviewCard from '../../components/ReviewCard';

const TutorReviews: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [ratingFilter, setRatingFilter] = useState('all');

  // Fetch tutor profile
  const {
    data: tutorProfile,
    isLoading: profileLoading,
    error: profileError,
  } = useQuery({
    queryKey: ['my-tutor-profile'],
    queryFn: getMyTutorProfile,
  });

  // Fetch reviews
  const {
    data: reviews = [],
    isLoading: reviewsLoading,
    error: reviewsError,
  } = useQuery({
    queryKey: ['my-tutor-reviews'],
    queryFn: () => getMyTutorReviews(),
  });

  // Filter reviews
  const filteredReviews = reviews.filter((review: any) => {
    const matchesSearch =
      (review.comment?.toLowerCase().includes(searchTerm.toLowerCase()) || false) ||
      (review.student?.full_name?.toLowerCase().includes(searchTerm.toLowerCase()) || false);

    const matchesRating = ratingFilter === 'all' || review.rating.toString() === ratingFilter;

    return matchesSearch && matchesRating;
  });

  if (profileLoading || reviewsLoading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  if (profileError || reviewsError) {
    return (
      <Alert severity="error" sx={{ m: 2 }}>
        Error loading reviews. Please try again.
      </Alert>
    );
  }

  return (
    <Box>
      <Typography variant="h4" gutterBottom>
        Reviews & Ratings
      </Typography>
      <Typography variant="body1" color="text.secondary" sx={{ mb: 3 }}>
        See what your students are saying about your tutoring
      </Typography>

      {/* Review Stats */}
      <ReviewStats
        reviews={reviews || []}
        averageRating={tutorProfile?.average_rating}
        totalReviews={tutorProfile?.total_reviews}
      />

      {/* Filters */}
      <Paper sx={{ p: 2, mb: 3 }}>
        <Box sx={{ display: 'flex', gap: 2, flexWrap: 'wrap', alignItems: 'center' }}>
          <Box sx={{ flex: 1, minWidth: 200 }}>
            <TextField
              fullWidth
              size="small"
              placeholder="Search reviews..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              slotProps={{
                input: {
                  startAdornment: (
                    <InputAdornment position="start">
                      <Search />
                    </InputAdornment>
                  ),
                }
              }}
            />
          </Box>
          <Box sx={{ minWidth: 150 }}>
            <FormControl fullWidth size="small">
              <InputLabel>Rating</InputLabel>
              <Select
                value={ratingFilter}
                label="Rating"
                onChange={(e) => setRatingFilter(e.target.value)}
              >
                <MenuItem value="all">All Ratings</MenuItem>
                <MenuItem value="5">5 Stars</MenuItem>
                <MenuItem value="4">4 Stars</MenuItem>
                <MenuItem value="3">3 Stars</MenuItem>
                <MenuItem value="2">2 Stars</MenuItem>
                <MenuItem value="1">1 Star</MenuItem>
              </Select>
            </FormControl>
          </Box>
        </Box>
      </Paper>

      {/* Reviews List */}
      <Box>
        <Typography variant="h6" gutterBottom>
          Student Reviews ({filteredReviews.length})
        </Typography>

        {filteredReviews.length > 0 ? (
          filteredReviews.map((review) => (
            <ReviewCard key={review.id} review={review} showActions={true} />
          ))
        ) : (
          <Paper sx={{ p: 4, textAlign: 'center', borderRadius: 2 }}>
            <Star sx={{ fontSize: 48, color: 'text.secondary', mb: 2 }} />
            <Typography variant="h6" color="text.secondary" gutterBottom>
              {reviews.length === 0 ? 'No Reviews Yet' : 'No Reviews Found'}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {reviews.length === 0
                ? 'Your students will be able to leave reviews after tutoring sessions.'
                : 'Try adjusting your search criteria.'
              }
            </Typography>
          </Paper>
        )}
      </Box>
    </Box>
  );
};

export default TutorReviews;
