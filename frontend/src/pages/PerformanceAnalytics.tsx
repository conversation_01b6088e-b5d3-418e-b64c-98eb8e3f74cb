import React, { useState, useEffect } from 'react';
import {
  Box,
  Container,
  Typography,
  Grid,
  Card,
  CardContent,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  ToggleButton,
  ToggleButtonGroup,
  Chip,
  CircularProgress,
  Alert,
  useTheme,
  useMediaQuery,
  Fab,
  Zoom
} from '@mui/material';
import {
  Analytics as AnalyticsIcon,
  Refresh as RefreshIcon,
  FilterList as FilterIcon
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import { useQuery } from '@tanstack/react-query';
import { useAuth } from '../contexts/AuthContext';
import { getCourses } from '../api/courses';
import { 
  getComprehensiveAnalytics, 
  getCourseComparisonAnalytics,
  getPerformanceTrends,
  ComprehensiveAnalytics 
} from '../api/studentProgress';
import PerformanceOverviewCard from '../components/Analytics/PerformanceOverviewCard';
import PerformanceTrendsChart from '../components/Analytics/PerformanceTrendsChart';
import CourseComparisonChart from '../components/Analytics/CourseComparisonChart';
import ChallengeMetricsCard from '../components/Analytics/ChallengeMetricsCard';

const PerformanceAnalytics: React.FC = () => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const { user } = useAuth();
  
  const [selectedCourse, setSelectedCourse] = useState<number | 'all'>('all');
  const [timePeriod, setTimePeriod] = useState<'week' | 'month' | 'quarter'>('month');
  const [viewMode, setViewMode] = useState<'overview' | 'detailed'>('overview');

  // Fetch courses for filtering
  const { data: courses = [] } = useQuery({
    queryKey: ['courses'],
    queryFn: getCourses,
    staleTime: 5 * 60 * 1000
  });

  // Fetch comprehensive analytics
  const { 
    data: analytics, 
    isLoading: analyticsLoading, 
    error: analyticsError,
    refetch: refetchAnalytics 
  } = useQuery({
    queryKey: ['comprehensiveAnalytics', selectedCourse, timePeriod],
    queryFn: () => {
      const days = timePeriod === 'week' ? 7 : timePeriod === 'month' ? 30 : 90;
      const courseId = selectedCourse === 'all' ? undefined : selectedCourse as number;
      return getComprehensiveAnalytics(courseId, days);
    },
    enabled: !!user,
    staleTime: 2 * 60 * 1000
  });

  // Fetch course comparison data (only when viewing all courses)
  const { 
    data: courseComparison, 
    isLoading: comparisonLoading 
  } = useQuery({
    queryKey: ['courseComparison'],
    queryFn: getCourseComparisonAnalytics,
    enabled: !!user && selectedCourse === 'all',
    staleTime: 5 * 60 * 1000
  });

  const handleCourseChange = (event: any) => {
    setSelectedCourse(event.target.value);
  };

  const handleTimePeriodChange = (event: any, newPeriod: string | null) => {
    if (newPeriod) {
      setTimePeriod(newPeriod as 'week' | 'month' | 'quarter');
    }
  };

  const handleRefresh = () => {
    refetchAnalytics();
  };

  const getTimePeriodLabel = () => {
    switch (timePeriod) {
      case 'week': return 'Last 7 days';
      case 'month': return 'Last 30 days';
      case 'quarter': return 'Last 90 days';
      default: return 'Last 30 days';
    }
  };

  const selectedCourseName = selectedCourse === 'all' ? 'All Courses' : 
    courses.find(c => c.id === selectedCourse)?.name || 'Unknown Course';

  if (analyticsError) {
    return (
      <Container maxWidth="lg" sx={{ py: 4 }}>
        <Alert severity="error" sx={{ mb: 3 }}>
          Failed to load performance analytics. Please try again.
        </Alert>
      </Container>
    );
  }

  return (
    <Container maxWidth="xl" sx={{ py: isMobile ? 2 : 4 }}>
      {/* Header */}
      <Box
        component={motion.div}
        initial={{ opacity: 0, y: -20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
        sx={{ mb: 4 }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <AnalyticsIcon sx={{ fontSize: 32, mr: 2, color: theme.palette.primary.main }} />
          <Typography variant="h4" fontWeight="bold">
            Performance Analytics
          </Typography>
        </Box>
        <Typography variant="body1" color="text.secondary">
          Track your learning progress, identify strengths, and discover areas for improvement
        </Typography>
      </Box>

      {/* Filters */}
      <Card
        component={motion.div}
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5, delay: 0.1 }}
        sx={{ mb: 3, borderRadius: 2 }}
      >
        <CardContent>
          <Grid container spacing={3} alignItems="center">
            <Grid item xs={12} sm={6} md={3}>
              <FormControl fullWidth size="small">
                <InputLabel>Course</InputLabel>
                <Select
                  value={selectedCourse}
                  label="Course"
                  onChange={handleCourseChange}
                >
                  <MenuItem value="all">All Courses</MenuItem>
                  {courses.map((course) => (
                    <MenuItem key={course.id} value={course.id}>
                      {course.name}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>
            
            <Grid item xs={12} sm={6} md={4}>
              <ToggleButtonGroup
                value={timePeriod}
                exclusive
                onChange={handleTimePeriodChange}
                size="small"
                fullWidth
              >
                <ToggleButton value="week">Week</ToggleButton>
                <ToggleButton value="month">Month</ToggleButton>
                <ToggleButton value="quarter">Quarter</ToggleButton>
              </ToggleButtonGroup>
            </Grid>

            <Grid item xs={12} md={5}>
              <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
                <Chip
                  icon={<FilterIcon />}
                  label={selectedCourseName}
                  color="primary"
                  variant="outlined"
                />
                <Chip
                  label={getTimePeriodLabel()}
                  color="secondary"
                  variant="outlined"
                />
              </Box>
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Loading State */}
      {analyticsLoading && (
        <Box sx={{ display: 'flex', justifyContent: 'center', py: 8 }}>
          <CircularProgress size={48} />
        </Box>
      )}

      {/* Analytics Content */}
      {analytics && !analyticsLoading && (
        <Grid container spacing={3}>
          {/* Performance Overview */}
          <Grid item xs={12}>
            <PerformanceOverviewCard 
              overview={analytics.overview}
              isLoading={analyticsLoading}
            />
          </Grid>

          {/* Performance Trends */}
          <Grid item xs={12} lg={8}>
            <PerformanceTrendsChart
              trendData={analytics.performance_trend}
              insights={analytics.insights}
              isLoading={analyticsLoading}
            />
          </Grid>

          {/* Challenge Metrics */}
          <Grid item xs={12} lg={4}>
            <ChallengeMetricsCard
              challengeMetrics={analytics.challenge_metrics}
              isLoading={analyticsLoading}
            />
          </Grid>

          {/* Course Comparison (only when viewing all courses) */}
          {selectedCourse === 'all' && courseComparison && (
            <Grid item xs={12}>
              <CourseComparisonChart
                courseData={courseComparison.course_performance}
                isLoading={comparisonLoading}
              />
            </Grid>
          )}
        </Grid>
      )}

      {/* Floating Action Button for Refresh */}
      <Zoom in={!analyticsLoading}>
        <Fab
          color="primary"
          onClick={handleRefresh}
          sx={{
            position: 'fixed',
            bottom: isMobile ? 100 : 32,
            right: 32,
            zIndex: 1000
          }}
        >
          <RefreshIcon />
        </Fab>
      </Zoom>
    </Container>
  );
};

export default PerformanceAnalytics;
