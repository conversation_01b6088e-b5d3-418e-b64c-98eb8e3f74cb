import React, { useState } from 'react';
import { 
  Box, 
  Typography, 
  Button, 
  Paper, 
  Grid,
  Card,
  CardContent,
  CardActions,
  IconButton,
  Chip,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  CircularProgress,
  Alert,
  TextField,
  InputAdornment,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  SelectChangeEvent,
  Pagination,
  Divider,
  Avatar,
  Badge,
  FormControlLabel,
  Switch
} from '@mui/material';
import { 
  Add as AddIcon, 
  Edit as EditIcon, 
  Delete as DeleteIcon,
  Visibility as ViewIcon,
  Search as SearchIcon,
  Event as EventIcon,
  Person as PersonIcon,
  School as SchoolIcon,
  Book as BookIcon,
  CalendarMonth as CalendarIcon,
  LocationOn as LocationIcon,
  Videocam as VideocamIcon
} from '@mui/icons-material';
import { Link as RouterLink, useNavigate, useLocation } from 'react-router-dom';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { 
  getSessions, 
  deleteSession, 
  Session, 
  SessionType, 
  SessionStatus 
} from '../../api/sessions';
import { getCourses } from '../../api/courses';
import { useAuth } from '../../contexts/AuthContext';

// Helper function to format date
const formatDate = (dateString: string) => {
  const date = new Date(dateString);
  return date.toLocaleDateString('en-US', {
    weekday: 'short',
    month: 'short',
    day: 'numeric',
    hour: 'numeric',
    minute: '2-digit'
  });
};

// Helper function to get status color
const getStatusColor = (status: SessionStatus) => {
  switch (status) {
    case 'scheduled':
      return 'primary';
    case 'in_progress':
      return 'warning';
    case 'completed':
      return 'success';
    case 'cancelled':
      return 'error';
    default:
      return 'default';
  }
};

const SessionsList: React.FC = () => {
  const { user } = useAuth();
  const navigate = useNavigate();
  const location = useLocation();
  const queryClient = useQueryClient();
  
  const isAdmin = user?.role === 'admin';
  const isTutor = user?.role === 'tutor';
  const isStudent = user?.role === 'student';
  const canCreateSession = isAdmin || isTutor;
  
  // Get courseId from location state if available
  const initialCourseId = location.state?.courseId;
  
  // State for pagination
  const [page, setPage] = useState(1);
  const [itemsPerPage] = useState(6);
  
  // State for filtering
  const [searchTerm, setSearchTerm] = useState('');
  const [selectedCourse, setSelectedCourse] = useState<string>(initialCourseId?.toString() || '');
  const [selectedStatus, setSelectedStatus] = useState<string>('');
  const [selectedType, setSelectedType] = useState<string>('');
  const [showMySessionsOnly, setShowMySessionsOnly] = useState(false);
  
  // State for delete confirmation dialog
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  const [sessionToDelete, setSessionToDelete] = useState<Session | null>(null);

  // Fetch sessions data
  const {
    data: sessions = [],
    isLoading: isLoadingSessions,
    error: sessionsError
  } = useQuery({
    queryKey: ['sessions', selectedCourse ? parseInt(selectedCourse) : undefined],
    queryFn: () => getSessions(
      selectedCourse ? parseInt(selectedCourse) : undefined,
      undefined,
      selectedStatus as SessionStatus || undefined
    )
  });

  // Fetch courses data for filtering
  const { 
    data: courses = [], 
    isLoading: isLoadingCourses 
  } = useQuery({
    queryKey: ['courses'],
    queryFn: getCourses
  });

  // Delete session mutation
  const deleteMutation = useMutation({
    mutationFn: (id: number) => deleteSession(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['sessions'] });
      setDeleteDialogOpen(false);
      setSessionToDelete(null);
    }
  });

  // Handle page change
  const handlePageChange = (event: React.ChangeEvent<unknown>, value: number) => {
    setPage(value);
    window.scrollTo(0, 0);
  };

  // Handle course filter change
  const handleCourseChange = (event: SelectChangeEvent) => {
    setSelectedCourse(event.target.value);
    setPage(1);
  };

  // Handle status filter change
  const handleStatusChange = (event: SelectChangeEvent) => {
    setSelectedStatus(event.target.value);
    setPage(1);
  };

  // Handle type filter change
  const handleTypeChange = (event: SelectChangeEvent) => {
    setSelectedType(event.target.value);
    setPage(1);
  };

  // Handle my sessions toggle
  const handleMySessionsToggle = (event: React.ChangeEvent<HTMLInputElement>) => {
    setShowMySessionsOnly(event.target.checked);
    setPage(1);
  };

  // Open delete confirmation dialog
  const handleDeleteClick = (session: Session) => {
    setSessionToDelete(session);
    setDeleteDialogOpen(true);
  };

  // Close delete confirmation dialog
  const handleDeleteCancel = () => {
    setDeleteDialogOpen(false);
    setSessionToDelete(null);
  };

  // Confirm delete
  const handleDeleteConfirm = () => {
    if (sessionToDelete) {
      deleteMutation.mutate(sessionToDelete.id);
    }
  };

  // Filter sessions based on search term and filters
  const filteredSessions = sessions.filter((session) => {
    // Text search
    const matchesSearch = 
      session.title.toLowerCase().includes(searchTerm.toLowerCase()) ||
      session.description.toLowerCase().includes(searchTerm.toLowerCase()) ||
      (session.location && session.location.toLowerCase().includes(searchTerm.toLowerCase()));
    
    // Type filter
    const matchesType = selectedType ? session.session_type === selectedType : true;
    
    // My sessions filter
    const matchesMySessions = showMySessionsOnly
      ? (isStudent && session.student_id === user?.id) ||
        (isTutor && session.tutor_id === user?.id)
      : true;
    
    return matchesSearch && matchesType && matchesMySessions;
  });



  // Get course name by ID
  const getCourseName = (courseId: number): string => {
    const course = courses.find(c => c.id === courseId);
    return course ? course.name : 'Unknown Course';
  };

  // Calculate pagination
  const indexOfLastItem = page * itemsPerPage;
  const indexOfFirstItem = indexOfLastItem - itemsPerPage;
  const currentItems = filteredSessions.slice(indexOfFirstItem, indexOfLastItem);
  const totalPages = Math.ceil(filteredSessions.length / itemsPerPage);

  // Check if session is upcoming
  const isUpcoming = (session: Session) => {
    return new Date(session.start_time) > new Date();
  };

  // Check if session is happening now
  const isHappeningNow = (session: Session) => {
    const now = new Date();
    const start = new Date(session.start_time);
    const end = new Date(session.end_time);
    return now >= start && now <= end;
  };

  // Check if user can join session
  const canJoinSession = (session: Session) => {
    if (session.status !== 'scheduled' && session.status !== 'in_progress') {
      return false;
    }
    
    if (isStudent) {
      return session.student_id === user?.id;
    }
    
    if (isTutor) {
      return session.tutor_id === user?.id;
    }
    
    return isAdmin;
  };

  if (isLoadingSessions) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
        <CircularProgress />
      </Box>
    );
  }

  if (sessionsError) {
    return (
      <Alert severity="error" sx={{ mt: 2 }}>
        Error loading sessions: {(sessionsError as Error).message}
      </Alert>
    );
  }

  return (
    <Box>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" component="h1">
          Tutorials
        </Typography>
        
        {canCreateSession && (
          <Button
            variant="contained"
            color="primary"
            startIcon={<AddIcon />}
            component={RouterLink}
            to="/tutorials/new"
            state={{ courseId: selectedCourse ? parseInt(selectedCourse) : undefined }}
          >
            Create Tutorial
          </Button>
        )}
      </Box>
      
      {/* Filters */}
      <Paper sx={{ p: 2, mb: 3 }}>
        <Box sx={{ display: 'flex', flexDirection: { xs: 'column', md: 'row' }, gap: 2 }}>
          <TextField
            label="Search Tutorials"
            variant="outlined"
            size="small"
            fullWidth
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            InputProps={{
              startAdornment: (
                <InputAdornment position="start">
                  <SearchIcon />
                </InputAdornment>
              ),
            }}
          />
          
          <FormControl size="small" sx={{ minWidth: 200 }}>
            <InputLabel id="course-filter-label">Course</InputLabel>
            <Select
              labelId="course-filter-label"
              id="course-filter"
              value={selectedCourse}
              label="Course"
              onChange={handleCourseChange}
              disabled={isLoadingCourses}
            >
              <MenuItem value="">All Courses</MenuItem>
              {courses.map((course) => (
                <MenuItem key={course.id} value={course.id.toString()}>
                  {course.name}
                </MenuItem>
              ))}
            </Select>
          </FormControl>
          
          <FormControl size="small" sx={{ minWidth: 150 }}>
            <InputLabel id="status-filter-label">Status</InputLabel>
            <Select
              labelId="status-filter-label"
              id="status-filter"
              value={selectedStatus}
              label="Status"
              onChange={handleStatusChange}
            >
              <MenuItem value="">All Statuses</MenuItem>
              <MenuItem value="scheduled">Scheduled</MenuItem>
              <MenuItem value="in_progress">In Progress</MenuItem>
              <MenuItem value="completed">Completed</MenuItem>
              <MenuItem value="cancelled">Cancelled</MenuItem>
            </Select>
          </FormControl>
          
          <FormControl size="small" sx={{ minWidth: 150 }}>
            <InputLabel id="type-filter-label">Type</InputLabel>
            <Select
              labelId="type-filter-label"
              id="type-filter"
              value={selectedType}
              label="Type"
              onChange={handleTypeChange}
            >
              <MenuItem value="">All Types</MenuItem>
              <MenuItem value="online">Online</MenuItem>
              <MenuItem value="in_person">In-Person</MenuItem>
            </Select>
          </FormControl>
          
          {(isStudent || isTutor) && (
            <FormControlLabel
              control={
                <Switch
                  checked={showMySessionsOnly}
                  onChange={handleMySessionsToggle}
                />
              }
              label="My Tutorials Only"
              sx={{ ml: 1 }}
            />
          )}
        </Box>
      </Paper>
      
      {filteredSessions.length === 0 ? (
        <Paper sx={{ p: 3, textAlign: 'center' }}>
          <EventIcon sx={{ fontSize: 60, color: 'text.secondary', mb: 2 }} />
          <Typography variant="h6" color="text.secondary" gutterBottom>
            No tutorials found
          </Typography>
          <Typography variant="body1" color="text.secondary" paragraph>
            {searchTerm || selectedCourse || selectedStatus || selectedType || showMySessionsOnly
              ? 'Try adjusting your filters'
              : 'No tutorials have been scheduled yet'}
          </Typography>
          {canCreateSession && (
            <Button
              variant="contained"
              color="primary"
              startIcon={<AddIcon />}
              component={RouterLink}
              to="/tutorials/new"
              state={{ courseId: selectedCourse ? parseInt(selectedCourse) : undefined }}
              sx={{ mt: 2 }}
            >
              Create Tutorial
            </Button>
          )}
        </Paper>
      ) : (
        <>
          <Grid container spacing={3}>
            {currentItems.map((session) => (
              <Grid item xs={12} sm={6} md={4} key={session.id}>
                <Card 
                  variant="outlined" 
                  sx={{ 
                    height: '100%', 
                    display: 'flex', 
                    flexDirection: 'column',
                    position: 'relative',
                    '&:hover': {
                      boxShadow: 3
                    }
                  }}
                >
                  {isHappeningNow(session) && (
                    <Badge
                      color="error"
                      badgeContent="LIVE"
                      sx={{
                        position: 'absolute',
                        top: 12,
                        right: 12,
                        '& .MuiBadge-badge': {
                          fontSize: '0.7rem',
                          height: '20px',
                          minWidth: '40px',
                        }
                      }}
                    />
                  )}
                  
                  <CardContent sx={{ flexGrow: 1, pt: 3 }}>
                    <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                      <Chip 
                        label={session.session_type === 'online' ? 'Online' : 'In-Person'}
                        size="small"
                        color={session.session_type === 'online' ? 'info' : 'success'}
                        variant="outlined"
                        icon={session.session_type === 'online' ? <VideocamIcon /> : <LocationIcon />}
                      />
                      <Chip 
                        label={session.status}
                        size="small"
                        color={getStatusColor(session.status)}
                        variant="outlined"
                      />
                    </Box>
                    
                    <Typography 
                      variant="h6" 
                      component="div" 
                      sx={{ 
                        mb: 1,
                        display: '-webkit-box',
                        WebkitLineClamp: 2,
                        WebkitBoxOrient: 'vertical',
                        overflow: 'hidden',
                        textOverflow: 'ellipsis'
                      }}
                    >
                      {session.title}
                    </Typography>
                    
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                      <BookIcon fontSize="small" color="action" sx={{ mr: 1 }} />
                      <Typography variant="body2" color="text.secondary">
                        {session.course_id ? getCourseName(session.course_id) : 'Custom Course'}
                      </Typography>
                    </Box>
                    
                    <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                      <CalendarIcon fontSize="small" color="action" sx={{ mr: 1 }} />
                      <Typography variant="body2">
                        {formatDate(session.start_time)}
                      </Typography>
                    </Box>
                    
                    {session.session_type === 'in_person' && session.location && (
                      <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                        <LocationIcon fontSize="small" color="action" sx={{ mr: 1 }} />
                        <Typography variant="body2" noWrap>
                          {session.location}
                        </Typography>
                      </Box>
                    )}
                    
                    <Typography 
                      variant="body2" 
                      color="text.secondary" 
                      sx={{ 
                        mt: 1,
                        display: '-webkit-box',
                        WebkitLineClamp: 2,
                        WebkitBoxOrient: 'vertical',
                        overflow: 'hidden',
                        textOverflow: 'ellipsis'
                      }}
                    >
                      {session.description}
                    </Typography>
                  </CardContent>
                  
                  <Divider />
                  
                  <CardActions>
                    <Button 
                      size="small" 
                      component={RouterLink}
                      to={`/tutorials/${session.id}`}
                      startIcon={<ViewIcon />}
                    >
                      View
                    </Button>
                    
                    {canJoinSession(session) && session.session_type === 'online' && (
                      <Button 
                        size="small" 
                        color="primary"
                        variant="contained"
                        startIcon={<VideocamIcon />}
                        component="a"
                        href={session.video_link || '#'}
                        target="_blank"
                        disabled={!session.video_link}
                      >
                        Join
                      </Button>
                    )}
                    
                    {(isAdmin || (isTutor && session.tutor_id === user?.id)) && (
                      <>
                        <Button 
                          size="small" 
                          component={RouterLink}
                          to={`/tutorials/${session.id}/edit`}
                          startIcon={<EditIcon />}
                        >
                          Edit
                        </Button>
                        
                        <Button 
                          size="small" 
                          color="error"
                          startIcon={<DeleteIcon />}
                          onClick={() => handleDeleteClick(session)}
                        >
                          Delete
                        </Button>
                      </>
                    )}
                  </CardActions>
                </Card>
              </Grid>
            ))}
          </Grid>
          
          {totalPages > 1 && (
            <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
              <Pagination 
                count={totalPages} 
                page={page} 
                onChange={handlePageChange} 
                color="primary" 
                size="large"
              />
            </Box>
          )}
        </>
      )}
      
      {/* Delete Confirmation Dialog */}
      <Dialog
        open={deleteDialogOpen}
        onClose={handleDeleteCancel}
      >
        <DialogTitle>Delete Session</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Are you sure you want to delete the session "{sessionToDelete?.title}"? 
            This action cannot be undone.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={handleDeleteCancel}>Cancel</Button>
          <Button 
            onClick={handleDeleteConfirm} 
            color="error" 
            disabled={deleteMutation.isPending}
          >
            {deleteMutation.isPending ? <CircularProgress size={24} /> : 'Delete'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default SessionsList;
