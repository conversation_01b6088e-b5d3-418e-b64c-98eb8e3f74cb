import React, { useState } from 'react';
import { 
  Box, 
  Typography, 
  Paper, 
  Grid, 
  Button, 
  Chip,
  Divider,
  CircularProgress,
  Alert,
  Card,
  CardContent,
  List,
  ListItem,
  ListItemText,
  ListItemIcon,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogContentText,
  DialogActions,
  Snackbar,
  Avatar
} from '@mui/material';
import { 
  Edit as EditIcon, 
  ArrowBack as ArrowBackIcon,
  School as SchoolIcon,
  Book as BookIcon,
  CalendarToday as CalendarIcon,
  Person as PersonIcon,
  Delete as DeleteIcon,
  Videocam as VideocamIcon,
  LocationOn as LocationIcon,
  AccessTime as AccessTimeIcon,
  Group as GroupIcon,
  Repeat as RepeatIcon
} from '@mui/icons-material';
import { Link as RouterLink, useParams, useNavigate } from 'react-router-dom';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { getSession, deleteSession, bookSession, SessionStatus } from '../../api/sessions';
import { getCourse } from '../../api/courses';
import { useAuth } from '../../contexts/AuthContext';

// Helper function to format date
const formatDate = (dateString: string) => {
  const date = new Date(dateString);
  return date.toLocaleDateString('en-US', {
    weekday: 'long',
    year: 'numeric',
    month: 'long',
    day: 'numeric'
  });
};

// Helper function to format time
const formatTime = (dateString: string) => {
  const date = new Date(dateString);
  return date.toLocaleTimeString('en-US', {
    hour: 'numeric',
    minute: '2-digit'
  });
};

// Helper function to get status color
const getStatusColor = (status: SessionStatus) => {
  switch (status) {
    case 'scheduled':
      return 'primary';
    case 'in_progress':
      return 'warning';
    case 'completed':
      return 'success';
    case 'cancelled':
      return 'error';
    default:
      return 'default';
  }
};

const SessionDetail: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const sessionId = parseInt(id || '0');
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const { user } = useAuth();
  
  const isAdmin = user?.role === 'admin';
  const isTutor = user?.role === 'tutor';
  const isStudent = user?.role === 'student';
  
  // State for delete confirmation dialog
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
  
  // State for booking confirmation dialog
  const [bookDialogOpen, setBookDialogOpen] = useState(false);
  
  // State for success message
  const [successMessage, setSuccessMessage] = useState<string | null>(null);

  // Fetch session data
  const { 
    data: session, 
    isLoading: isLoadingSession, 
    error: sessionError 
  } = useQuery({
    queryKey: ['session', sessionId],
    queryFn: () => getSession(sessionId),
    enabled: !!sessionId
  });



  // Fetch course data
  const {
    data: course,
    isLoading: isLoadingCourse
  } = useQuery({
    queryKey: ['course', session?.course_id],
    queryFn: () => getCourse(session!.course_id!),
    enabled: !!session?.course_id
  });

  // Delete session mutation
  const deleteMutation = useMutation({
    mutationFn: () => deleteSession(sessionId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['sessions'] });
      setDeleteDialogOpen(false);
      setSuccessMessage('Session deleted successfully');
      setTimeout(() => {
        navigate('/sessions');
      }, 1500);
    }
  });

  // Book session mutation
  const bookMutation = useMutation({
    mutationFn: () => bookSession(sessionId, user!.id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['sessions'] });
      queryClient.invalidateQueries({ queryKey: ['session', sessionId] });
      setBookDialogOpen(false);
      setSuccessMessage('Tutorial joined successfully');
    }
  });

  // Check if session is upcoming
  const isUpcoming = () => {
    if (!session) return false;
    return new Date(session.start_time) > new Date();
  };

  // Check if session is happening now
  const isHappeningNow = () => {
    if (!session) return false;
    const now = new Date();
    const start = new Date(session.start_time);
    const end = new Date(session.end_time);
    return now >= start && now <= end;
  };

  // Check if user can join session
  const canJoinSession = () => {
    if (!session || !user) return false;

    if (session.status !== 'scheduled' && session.status !== 'in_progress') {
      return false;
    }

    if (isStudent) {
      // Students can join if they're enrolled OR if the session is available (no student assigned)
      return session.student_id === user.id || session.student_id === null;
    }

    if (isTutor) {
      return session.tutor_id === user.id;
    }

    return isAdmin;
  };

  // Check if user can book session
  const canBookSession = () => {
    if (!session || !user || !isStudent) return false;

    return (
      session.status === 'scheduled' &&
      isUpcoming() &&
      !session.student_id
    );
  };

  // Check if user can manage session
  const canManageSession = () => {
    if (!session || !user) return false;
    
    if (isAdmin) return true;
    
    if (isTutor) {
      return session.tutor_id === user.id;
    }
    
    return false;
  };

  if (isLoadingSession) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', mt: 4 }}>
        <CircularProgress />
      </Box>
    );
  }

  if (sessionError) {
    return (
      <Alert severity="error" sx={{ mt: 2 }}>
        Error loading session: {(sessionError as Error).message}
      </Alert>
    );
  }

  if (!session) {
    return (
      <Alert severity="error" sx={{ mt: 2 }}>
        Session not found
      </Alert>
    );
  }

  return (
    <Box>
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 3 }}>
        <Button
          startIcon={<ArrowBackIcon />}
          onClick={() => {
            const basePath = user?.role === 'tutor' ? '/tutor/tutorials' : '/tutorials';
            navigate(basePath);
          }}
          sx={{ mr: 2 }}
        >
          Back to Tutorials
        </Button>
        
        <Typography variant="h4" component="h1" sx={{ flexGrow: 1 }}>
          {session.title}
        </Typography>
        
        {canManageSession() && (
          <>
            <Button
              variant="contained"
              color="primary"
              startIcon={<EditIcon />}
              component={RouterLink}
              to={`/sessions/${session.id}/edit`}
              sx={{ mr: 1 }}
            >
              Edit
            </Button>
            
            <Button
              variant="outlined"
              color="error"
              startIcon={<DeleteIcon />}
              onClick={() => setDeleteDialogOpen(true)}
            >
              Delete
            </Button>
          </>
        )}
      </Box>
      
      <Grid container spacing={4}>
        <Grid item xs={12} md={8}>
          <Paper sx={{ p: 3, mb: 4 }}>
            <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 2 }}>
              <Box sx={{ display: 'flex', gap: 1 }}>
                <Chip 
                  label={session.session_type === 'online' ? 'Online' : 'In-Person'}
                  color={session.session_type === 'online' ? 'info' : 'success'}
                  icon={session.session_type === 'online' ? <VideocamIcon /> : <LocationIcon />}
                />
                <Chip 
                  label={session.status}
                  color={getStatusColor(session.status)}
                />
                {isHappeningNow() && (
                  <Chip 
                    label="LIVE NOW"
                    color="error"
                  />
                )}
              </Box>
            </Box>
            
            <Typography variant="h6" gutterBottom>
              Description
            </Typography>
            <Typography variant="body1" paragraph>
              {session.description}
            </Typography>
            
            <Divider sx={{ my: 3 }} />
            
            <Grid container spacing={3}>
              <Grid item xs={12} sm={6}>
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <CalendarIcon color="action" sx={{ mr: 1 }} />
                  <Typography variant="body1">
                    {formatDate(session.start_time)}
                  </Typography>
                </Box>
                
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <AccessTimeIcon color="action" sx={{ mr: 1 }} />
                  <Typography variant="body1">
                    {formatTime(session.start_time)} - {formatTime(session.end_time)}
                  </Typography>
                </Box>
                
                {session.is_recurring && (
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <RepeatIcon color="action" sx={{ mr: 1 }} />
                    <Typography variant="body1">
                      Recurring: {session.recurrence_pattern || 'Weekly'}
                    </Typography>
                  </Box>
                )}
              </Grid>
              
              <Grid item xs={12} sm={6}>
                {session.session_type === 'in_person' && session.location && (
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <LocationIcon color="action" sx={{ mr: 1 }} />
                    <Typography variant="body1">
                      {session.location}
                    </Typography>
                  </Box>
                )}
                
                {session.session_type === 'online' && session.video_link && (
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                    <VideocamIcon color="action" sx={{ mr: 1 }} />
                    <Typography variant="body1">
                      <Button
                        component="a"
                        href={session.video_link}
                        target="_blank"
                        rel="noopener noreferrer"
                        disabled={!canJoinSession()}
                      >
                        Video Link
                      </Button>
                    </Typography>
                  </Box>
                )}
                
                <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                  <GroupIcon color="action" sx={{ mr: 1 }} />
                  <Typography variant="body1">
                    Max Students: {session.max_students}
                  </Typography>
                </Box>
              </Grid>
            </Grid>
            
            <Divider sx={{ my: 3 }} />
            
            <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
              <BookIcon color="action" sx={{ mr: 1 }} />
              <Typography variant="body1">
                Course: {session.course_id ? (
                  isLoadingCourse ? (
                    <CircularProgress size={16} sx={{ ml: 1 }} />
                  ) : course ? (
                    <Button
                      component={RouterLink}
                      to={`/courses/${course.id}`}
                      sx={{ textTransform: 'none' }}
                    >
                      {course.name}
                    </Button>
                  ) : (
                    'Unknown Course'
                  )
                ) : (
                  'Custom Course'
                )}
              </Typography>
            </Box>
            
            <Box sx={{ display: 'flex', alignItems: 'center' }}>
              <PersonIcon color="action" sx={{ mr: 1 }} />
              <Typography variant="body1">
                Tutor: {session.tutor?.full_name || 'Unknown Tutor'}
              </Typography>
            </Box>
          </Paper>
          
          {canJoinSession() && session.session_type === 'online' && session.video_link && (
            <Paper sx={{ p: 3, mb: 4, textAlign: 'center' }}>
              <Typography variant="h6" gutterBottom>
                {isHappeningNow() ? 'This tutorial is happening now!' : 'Join this tutorial'}
              </Typography>
              
              <Button
                variant="contained"
                color="primary"
                size="large"
                startIcon={<VideocamIcon />}
                component="a"
                href={session.video_link}
                target="_blank"
                rel="noopener noreferrer"
                sx={{ mt: 1 }}
              >
                Join Video Tutorial
              </Button>
            </Paper>
          )}
        </Grid>
        
        <Grid item xs={12} md={4}>
          <Paper sx={{ p: 3, mb: 4 }}>
            <Typography variant="h6" gutterBottom>
              Session Information
            </Typography>
            
            <Box sx={{ mt: 2 }}>
              <Typography variant="subtitle2" color="text.secondary">
                Status
              </Typography>
              <Chip 
                label={session.status}
                color={getStatusColor(session.status)}
                sx={{ mt: 1 }}
              />
            </Box>
            
            <Box sx={{ mt: 3 }}>
              <Typography variant="subtitle2" color="text.secondary">
                Type
              </Typography>
              <Chip 
                label={session.session_type === 'online' ? 'Online' : 'In-Person'}
                color={session.session_type === 'online' ? 'info' : 'success'}
                size="small"
                sx={{ mt: 1 }}
              />
            </Box>
            
            <Box sx={{ mt: 3 }}>
              <Typography variant="subtitle2" color="text.secondary">
                Date
              </Typography>
              <Typography variant="body2">
                {formatDate(session.start_time)}
              </Typography>
            </Box>
            
            <Box sx={{ mt: 3 }}>
              <Typography variant="subtitle2" color="text.secondary">
                Time
              </Typography>
              <Typography variant="body2">
                {formatTime(session.start_time)} - {formatTime(session.end_time)}
              </Typography>
            </Box>
            
            {session.session_type === 'in_person' && session.location && (
              <Box sx={{ mt: 3 }}>
                <Typography variant="subtitle2" color="text.secondary">
                  Location
                </Typography>
                <Typography variant="body2">
                  {session.location}
                </Typography>
              </Box>
            )}
            
            <Box sx={{ mt: 3 }}>
              <Typography variant="subtitle2" color="text.secondary">
                Created
              </Typography>
              <Typography variant="body2">
                {formatDate(session.created_at)}
              </Typography>
            </Box>
          </Paper>
          
          {isStudent && (
            <Paper sx={{ p: 3, mb: 4 }}>
              <Typography variant="h6" gutterBottom>
                Student Actions
              </Typography>
              
              {session.student_id === user?.id ? (
                <>
                  <Alert severity="success" sx={{ mb: 2 }}>
                    You are enrolled in this tutorial
                  </Alert>
                  
                  {session.session_type === 'online' && session.video_link && canJoinSession() && (
                    <Button
                      fullWidth
                      variant="contained"
                      color="primary"
                      startIcon={<VideocamIcon />}
                      component="a"
                      href={session.video_link}
                      target="_blank"
                      rel="noopener noreferrer"
                      sx={{ mb: 2 }}
                    >
                      Join Tutorial
                    </Button>
                  )}
                </>
              ) : canBookSession() ? (
                <>
                  <Alert severity="info" sx={{ mb: 2 }}>
                    This tutorial is available to join
                  </Alert>
                  
                  <Button
                    fullWidth
                    variant="contained"
                    color="secondary"
                    onClick={() => setBookDialogOpen(true)}
                  >
                    Join Tutorial
                  </Button>
                </>
              ) : (
                <Alert severity="warning">
                  {session.student_id
                    ? 'This tutorial has already been joined'
                    : session.status !== 'scheduled'
                      ? 'This tutorial is not available to join'
                      : 'You cannot join this tutorial'}
                </Alert>
              )}
            </Paper>
          )}
          
          {canManageSession() && (
            <Paper sx={{ p: 3 }}>
              <Typography variant="h6" gutterBottom>
                Tutor Actions
              </Typography>
              
              <Button
                fullWidth
                variant="outlined"
                color="primary"
                component={RouterLink}
                to={`/sessions/${session.id}/edit`}
                sx={{ mb: 2 }}
              >
                Edit Tutorial
              </Button>
              
              {session.session_type === 'online' && canJoinSession() && (
                <Button
                  fullWidth
                  variant="contained"
                  color="primary"
                  startIcon={<VideocamIcon />}
                  component="a"
                  href={session.video_link || '#'}
                  target="_blank"
                  rel="noopener noreferrer"
                  disabled={!session.video_link}
                  sx={{ mb: 2 }}
                >
                  Start/Join Tutorial
                </Button>
              )}
              
              <Button
                fullWidth
                variant="outlined"
                color="error"
                onClick={() => setDeleteDialogOpen(true)}
              >
                Delete Session
              </Button>
            </Paper>
          )}
        </Grid>
      </Grid>
      
      {/* Delete Confirmation Dialog */}
      <Dialog
        open={deleteDialogOpen}
        onClose={() => setDeleteDialogOpen(false)}
      >
        <DialogTitle>Delete Session</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Are you sure you want to delete this session? This action cannot be undone.
          </DialogContentText>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialogOpen(false)}>Cancel</Button>
          <Button 
            onClick={() => deleteMutation.mutate()}
            color="error" 
            disabled={deleteMutation.isPending}
          >
            {deleteMutation.isPending ? <CircularProgress size={24} /> : 'Delete'}
          </Button>
        </DialogActions>
      </Dialog>
      
      {/* Book Confirmation Dialog */}
      <Dialog
        open={bookDialogOpen}
        onClose={() => setBookDialogOpen(false)}
      >
        <DialogTitle>Join Tutorial</DialogTitle>
        <DialogContent>
          <DialogContentText>
            Are you sure you want to join this tutorial?
          </DialogContentText>
          <Box sx={{ mt: 2 }}>
            <Typography variant="subtitle2" gutterBottom>
              {session.title}
            </Typography>
            <Typography variant="body2">
              {formatDate(session.start_time)} at {formatTime(session.start_time)}
            </Typography>
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setBookDialogOpen(false)}>Cancel</Button>
          <Button 
            onClick={() => bookMutation.mutate()}
            color="primary" 
            variant="contained"
            disabled={bookMutation.isPending}
          >
            {bookMutation.isPending ? <CircularProgress size={24} /> : 'Join Tutorial'}
          </Button>
        </DialogActions>
      </Dialog>
      
      {/* Success message */}
      <Snackbar
        open={!!successMessage}
        autoHideDuration={3000}
        onClose={() => setSuccessMessage(null)}
        anchorOrigin={{ vertical: 'bottom', horizontal: 'center' }}
      >
        <Alert severity="success" onClose={() => setSuccessMessage(null)}>
          {successMessage}
        </Alert>
      </Snackbar>
    </Box>
  );
};

export default SessionDetail;
