import React, { useState, useEffect } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import {
  Box,
  Typography,
  Paper,
  List,
  ListItem,
  ListItemAvatar,
  ListItemText,
  Avatar,
  Chip,
  IconButton,
  useTheme,
  useMediaQuery,
  CircularProgress,
  Alert,
  Divider
} from '@mui/material';
import {
  ArrowBack as ArrowBackIcon,
  Circle as CircleIcon
} from '@mui/icons-material';
import { useQuery } from '@tanstack/react-query';
import { getChatRooms, getChatMessages } from '../../api/chat';
import { ChatRoom, useWebSocketChat } from '../../hooks/useWebSocketChat';
import ChatComponent from '../../components/ChatComponent';
import { formatDistanceToNow } from 'date-fns';

const ChatPage: React.FC = () => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const navigate = useNavigate();
  const { roomId } = useParams<{ roomId?: string }>();

  const [selectedRoom, setSelectedRoom] = useState<ChatRoom | null>(null);
  const [showChatList, setShowChatList] = useState(true);

  // Get real-time online status from WebSocket
  const { onlineUsers } = useWebSocketChat();

  // Helper function to get real-time online status
  const getRealtimeOnlineStatus = (userId: number, fallbackStatus: boolean) => {
    // Use real-time status if available, otherwise fall back to API status
    return onlineUsers.hasOwnProperty(userId) ? onlineUsers[userId] : fallbackStatus;
  };

  // Fetch chat rooms
  const {
    data: chatRooms = [],
    isLoading: roomsLoading,
    error: roomsError
  } = useQuery({
    queryKey: ['chat-rooms'],
    queryFn: () => getChatRooms(0, 100),
    staleTime: 30000, // 30 seconds
  });

  // Set selected room based on URL parameter or default to first room
  useEffect(() => {
    if (roomId && chatRooms.length > 0) {
      const room = chatRooms.find(r => r.id === parseInt(roomId));
      if (room) {
        setSelectedRoom(room);
      } else {
        // Room not found, redirect to chat list
        navigate('/chat', { replace: true });
      }
    } else if (!roomId && chatRooms.length > 0 && !selectedRoom) {
      // No room selected, show the first room on desktop
      if (!isMobile) {
        setSelectedRoom(chatRooms[0]);
        navigate(`/chat/${chatRooms[0].id}`, { replace: true });
      }
    }
  }, [roomId, chatRooms, navigate, isMobile, selectedRoom]);

  const handleRoomSelect = (room: ChatRoom) => {
    setSelectedRoom(room);
    if (isMobile) {
      setShowChatList(false);
    }
    navigate(`/chat/${room.id}`);
  };

  const handleBackToList = () => {
    if (isMobile) {
      setShowChatList(true);
      setSelectedRoom(null);
    }
    navigate('/chat');
  };

  if (roomsLoading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '400px' }}>
        <CircularProgress />
      </Box>
    );
  }

  if (roomsError) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="error">
          Failed to load chat rooms. Please try again later.
        </Alert>
      </Box>
    );
  }

  if (chatRooms.length === 0) {
    return (
      <Box sx={{ p: 3, textAlign: 'center' }}>
        <Typography variant="h6" color="text.secondary" gutterBottom>
          No Chat Rooms
        </Typography>
        <Typography variant="body2" color="text.secondary">
          You don't have any active chat rooms yet. Chat rooms are created when tutors accept your booking requests.
        </Typography>
      </Box>
    );
  }

  // Mobile view: show either room list or selected room
  if (isMobile) {
    if (selectedRoom && !showChatList) {
      return (
        <Box sx={{
          height: '100vh',
          display: 'flex',
          flexDirection: 'column',
          bgcolor: 'background.default'
        }}>
          <ChatComponent chatRoom={selectedRoom} onClose={handleBackToList} />
        </Box>
      );
    }

    // Show room list on mobile
    return (
      <Box sx={{
        height: '100vh',
        display: 'flex',
        flexDirection: 'column',
        bgcolor: 'background.default'
      }}>
        <Paper
          elevation={1}
          sx={{
            p: 2,
            borderRadius: 0,
            borderBottom: 1,
            borderColor: 'divider'
          }}
        >
          <Typography variant="h5" sx={{ fontWeight: 600 }}>
            Your Chats
          </Typography>
        </Paper>

        <Box sx={{ flex: 1, overflow: 'auto' }}>
          <List sx={{ p: 0 }}>
            {chatRooms.map((room) => (
              <ListItem
                key={room.id}
                button
                onClick={() => handleRoomSelect(room)}
                sx={{
                  py: 2,
                  px: 2,
                  borderBottom: 1,
                  borderColor: 'divider',
                  '&:hover': {
                    bgcolor: 'action.hover'
                  }
                }}
              >
              <ListItemAvatar>
                <Avatar>
                  {room.other_user.name.charAt(0)}
                </Avatar>
              </ListItemAvatar>
              <ListItemText
                primary={
                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                    <Typography variant="subtitle1">
                      {room.other_user.name}
                    </Typography>
                    <CircleIcon
                      sx={{
                        fontSize: 8,
                        color: getRealtimeOnlineStatus(room.other_user.id, room.other_user.is_online) ? 'success.main' : 'grey.400'
                      }}
                    />
                    {room.unread_count > 0 && (
                      <Chip 
                        label={room.unread_count} 
                        size="small" 
                        color="primary"
                        sx={{ ml: 'auto' }}
                      />
                    )}
                  </Box>
                }
                secondary={
                  room.last_message ? (
                    <Box>
                      <Typography variant="body2" color="text.secondary" noWrap>
                        {room.last_message.content}
                      </Typography>
                      <Typography variant="caption" color="text.secondary">
                        {formatDistanceToNow(new Date(room.last_message.created_at), { addSuffix: true })}
                      </Typography>
                    </Box>
                  ) : (
                    <Typography variant="body2" color="text.secondary">
                      No messages yet
                    </Typography>
                  )
                }
              />
            </ListItem>
          ))}
        </List>
        </Box>
      </Box>
    );
  }

  // Desktop view: split layout
  return (
    <Box sx={{
      height: { xs: '100vh', md: '85vh' },
      display: 'flex',
      bgcolor: 'background.default'
    }}>
      {/* Chat rooms list */}
      <Paper
        elevation={1}
        sx={{
          width: { md: 350, lg: 400 },
          display: 'flex',
          flexDirection: 'column',
          borderRadius: 0,
          borderRight: 1,
          borderColor: 'divider'
        }}
      >
        <Box sx={{
          p: 2,
          borderBottom: 1,
          borderColor: 'divider',
          bgcolor: 'background.paper'
        }}>
          <Typography variant="h6" sx={{ fontWeight: 600 }}>
            Your Chats
          </Typography>
        </Box>
        <List sx={{ flex: 1, overflow: 'auto' }}>
          {chatRooms.map((room) => (
            <React.Fragment key={room.id}>
              <ListItem
                button
                selected={selectedRoom?.id === room.id}
                onClick={() => handleRoomSelect(room)}
              >
                <ListItemAvatar>
                  <Avatar>
                    {room.other_user.name.charAt(0)}
                  </Avatar>
                </ListItemAvatar>
                <ListItemText
                  primary={
                    <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                      <Typography variant="subtitle2">
                        {room.other_user.name}
                      </Typography>
                      <CircleIcon
                        sx={{
                          fontSize: 8,
                          color: getRealtimeOnlineStatus(room.other_user.id, room.other_user.is_online) ? 'success.main' : 'grey.400'
                        }}
                      />
                      {room.unread_count > 0 && (
                        <Chip 
                          label={room.unread_count} 
                          size="small" 
                          color="primary"
                          sx={{ ml: 'auto' }}
                        />
                      )}
                    </Box>
                  }
                  secondary={
                    room.last_message ? (
                      <Box>
                        <Typography variant="body2" color="text.secondary" noWrap>
                          {room.last_message.content}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {formatDistanceToNow(new Date(room.last_message.created_at), { addSuffix: true })}
                        </Typography>
                      </Box>
                    ) : (
                      <Typography variant="body2" color="text.secondary">
                        No messages yet
                      </Typography>
                    )
                  }
                />
              </ListItem>
              <Divider />
            </React.Fragment>
          ))}
        </List>
      </Paper>

      {/* Chat area */}
      <Box sx={{ flex: 1, display: 'flex', flexDirection: 'column' }}>
        {selectedRoom ? (
          <ChatComponent chatRoom={selectedRoom} />
        ) : (
          <Box sx={{
            flex: 1,
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            bgcolor: (theme) => theme.palette.mode === 'dark' ? 'grey.900' : 'grey.50'
          }}>
            <Typography variant="h6" color="text.secondary">
              Select a chat to start messaging
            </Typography>
          </Box>
        )}
      </Box>
    </Box>
  );
};

export default ChatPage;
