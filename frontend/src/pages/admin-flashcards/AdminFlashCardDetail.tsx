import React, { useState } from 'react';
import {
  Box,
  Typography,
  Button,
  Paper,
  Grid,
  Chip,
  Card,
  CardContent,
  CardMedia,
  Alert,
  CircularProgress,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Divider,
  useTheme,
  useMediaQuery,
} from '@mui/material';
import {
  Edit as EditIcon,
  Delete as DeleteIcon,
  ArrowBack as BackIcon,
  Image as ImageIcon,
  School as SchoolIcon,
  Topic as TopicIcon,
  CalendarToday as DateIcon,
} from '@mui/icons-material';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useNavigate, useParams } from 'react-router-dom';
import { format } from 'date-fns';

import {
  getAdminFlashCard,
  deleteAdminFlashCard,
} from '../../api/adminFlashcards';
import PageHeader from '../../components/Layout/PageHeader';
import FlashCardPreview from '../../components/AdminFlashCards/FlashCardPreview';

const AdminFlashCardDetail: React.FC = () => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const { id } = useParams<{ id: string }>();

  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);

  // Fetch flashcard
  const {
    data: flashcard,
    isLoading,
    error,
  } = useQuery({
    queryKey: ['adminFlashcard', id],
    queryFn: () => getAdminFlashCard(Number(id)),
    enabled: Boolean(id),
  });

  // Delete mutation
  const deleteMutation = useMutation({
    mutationFn: deleteAdminFlashCard,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['adminFlashcards'] });
      navigate('/admin/flashcards');
    },
  });

  const handleEdit = () => {
    navigate(`/admin/flashcards/${id}/edit`);
  };

  const handleDeleteClick = () => {
    setDeleteDialogOpen(true);
  };

  const handleDeleteConfirm = () => {
    if (id) {
      deleteMutation.mutate(Number(id));
    }
  };

  const handleBack = () => {
    navigate('/admin/flashcards');
  };

  const getDifficultyColor = (difficulty?: string) => {
    switch (difficulty?.toLowerCase()) {
      case 'easy':
        return 'success';
      case 'medium':
        return 'warning';
      case 'hard':
        return 'error';
      default:
        return 'default';
    }
  };

  if (isLoading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
        <CircularProgress />
      </Box>
    );
  }

  if (error || !flashcard) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="error">
          Failed to load flashcard. Please try again.
        </Alert>
      </Box>
    );
  }

  return (
    <Box sx={{ p: { xs: 2, md: 3 } }}>
      <PageHeader
        title="Flashcard Details"
        subtitle={`ID: ${flashcard.id}`}
        action={
          <Box sx={{ display: 'flex', gap: 1, flexWrap: 'wrap' }}>
            <Button
              variant="outlined"
              startIcon={<BackIcon />}
              onClick={handleBack}
              size={isMobile ? 'small' : 'medium'}
            >
              Back to List
            </Button>
            <Button
              variant="outlined"
              startIcon={<EditIcon />}
              onClick={handleEdit}
              size={isMobile ? 'small' : 'medium'}
            >
              Edit
            </Button>
            <Button
              variant="outlined"
              color="error"
              startIcon={<DeleteIcon />}
              onClick={handleDeleteClick}
              size={isMobile ? 'small' : 'medium'}
            >
              Delete
            </Button>
          </Box>
        }
      />

      <Grid container spacing={3}>
        {/* Flashcard Preview */}
        <Grid item xs={12} md={8}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Flashcard Preview
            </Typography>
            <FlashCardPreview
              frontContent={flashcard.front_content}
              backContent={flashcard.back_content}
              topic={flashcard.topic}
              difficulty={flashcard.difficulty}
              imageUrl={flashcard.media_url}
            />
          </Paper>
        </Grid>

        {/* Details */}
        <Grid item xs={12} md={4}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Details
            </Typography>

            <Box sx={{ display: 'flex', flexDirection: 'column', gap: 2 }}>
              {/* Course */}
              <Box>
                <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                  Course
                </Typography>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <SchoolIcon fontSize="small" color="action" />
                  <Box>
                    <Typography variant="body2">
                      {flashcard.course_name || 'Unknown Course'}
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      {flashcard.course_code}
                    </Typography>
                  </Box>
                </Box>
              </Box>

              {/* Topic */}
              {flashcard.topic && (
                <Box>
                  <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                    Topic
                  </Typography>
                  <Chip
                    icon={<TopicIcon />}
                    label={flashcard.topic}
                    variant="outlined"
                    size="small"
                  />
                </Box>
              )}

              {/* Difficulty */}
              {flashcard.difficulty && (
                <Box>
                  <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                    Difficulty
                  </Typography>
                  <Chip
                    label={flashcard.difficulty}
                    color={getDifficultyColor(flashcard.difficulty) as any}
                    size="small"
                  />
                </Box>
              )}

              {/* Status */}
              <Box>
                <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                  Status
                </Typography>
                <Chip
                  label={flashcard.is_active ? 'Active' : 'Inactive'}
                  color={flashcard.is_active ? 'success' : 'default'}
                  size="small"
                />
              </Box>

              {/* Image */}
              {flashcard.media_url && (
                <Box>
                  <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                    Image
                  </Typography>
                  <Card sx={{ maxWidth: 200 }}>
                    <CardMedia
                      component="img"
                      height="120"
                      image={flashcard.media_url}
                      alt="Flashcard image"
                      sx={{ objectFit: 'contain', bgcolor: 'grey.100' }}
                    />
                  </Card>
                </Box>
              )}

              <Divider />

              {/* Metadata */}
              <Box>
                <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                  Created
                </Typography>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <DateIcon fontSize="small" color="action" />
                  <Typography variant="body2">
                    {format(new Date(flashcard.created_at), 'PPP')}
                  </Typography>
                </Box>
              </Box>

              <Box>
                <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                  Last Updated
                </Typography>
                <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                  <DateIcon fontSize="small" color="action" />
                  <Typography variant="body2">
                    {format(new Date(flashcard.updated_at), 'PPP')}
                  </Typography>
                </Box>
              </Box>
            </Box>
          </Paper>
        </Grid>

        {/* Content Details */}
        <Grid item xs={12}>
          <Paper sx={{ p: 3 }}>
            <Typography variant="h6" gutterBottom>
              Content
            </Typography>

            <Grid container spacing={3}>
              <Grid item xs={12} md={6}>
                <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                  Front Content (Question)
                </Typography>
                <Paper variant="outlined" sx={{ p: 2, minHeight: 100 }}>
                  <Typography variant="body2" sx={{ whiteSpace: 'pre-wrap' }}>
                    {flashcard.front_content}
                  </Typography>
                </Paper>
              </Grid>

              <Grid item xs={12} md={6}>
                <Typography variant="subtitle2" color="text.secondary" gutterBottom>
                  Back Content (Answer)
                </Typography>
                <Paper variant="outlined" sx={{ p: 2, minHeight: 100 }}>
                  <Typography variant="body2" sx={{ whiteSpace: 'pre-wrap' }}>
                    {flashcard.back_content}
                  </Typography>
                </Paper>
              </Grid>
            </Grid>
          </Paper>
        </Grid>
      </Grid>

      {/* Delete Confirmation Dialog */}
      <Dialog open={deleteDialogOpen} onClose={() => setDeleteDialogOpen(false)}>
        <DialogTitle>Confirm Delete</DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to delete this flashcard? This action cannot be undone.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setDeleteDialogOpen(false)}>Cancel</Button>
          <Button
            onClick={handleDeleteConfirm}
            color="error"
            disabled={deleteMutation.isPending}
          >
            {deleteMutation.isPending ? 'Deleting...' : 'Delete'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Error Display */}
      {deleteMutation.error && (
        <Alert severity="error" sx={{ mt: 2 }}>
          {deleteMutation.error.message}
        </Alert>
      )}
    </Box>
  );
};

export default AdminFlashCardDetail;
