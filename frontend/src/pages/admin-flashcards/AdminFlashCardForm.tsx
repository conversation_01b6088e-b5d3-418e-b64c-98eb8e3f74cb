import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Button,
  Paper,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Switch,
  FormControlLabel,
  Alert,
  CircularProgress,
  Divider,
  Grid,
  Card,
  CardContent,
  useTheme,
  useMediaQuery,
} from '@mui/material';
import {
  Save as SaveIcon,
  Cancel as CancelIcon,
  Preview as PreviewIcon,
  Image as ImageIcon,
} from '@mui/icons-material';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useNavigate, useParams } from 'react-router-dom';

import {
  getAdminFlashCard,
  createAdminFlashCard,
  updateAdminFlashCard,
  uploadFlashCardImage,
  AdminFlashCardCreate,
  AdminFlashCardUpdate,
} from '../../api/adminFlashcards';
import { getCourses } from '../../api/courses';
import PageHeader from '../../components/Layout/PageHeader';
import FlashCardImageUpload from '../../components/AdminFlashCards/FlashCardImageUpload';
import FlashCardPreview from '../../components/AdminFlashCards/FlashCardPreview';

const AdminFlashCardForm: React.FC = () => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const navigate = useNavigate();
  const queryClient = useQueryClient();
  const { id } = useParams<{ id: string }>();
  const isEdit = Boolean(id);

  // Form state
  const [formData, setFormData] = useState({
    course_id: '',
    front_content: '',
    back_content: '',
    topic: '',
    difficulty: '',
    is_active: true,
  });
  const [errors, setErrors] = useState<Record<string, string>>({});
  const [showPreview, setShowPreview] = useState(false);

  // Fetch flashcard for editing
  const {
    data: flashcard,
    isLoading: isLoadingFlashcard,
    error: flashcardError,
  } = useQuery({
    queryKey: ['adminFlashcard', id],
    queryFn: () => getAdminFlashCard(Number(id)),
    enabled: isEdit,
  });

  // Fetch courses
  const { data: courses = [], isLoading: isLoadingCourses } = useQuery({
    queryKey: ['courses'],
    queryFn: () => getCourses(),
  });

  // Create mutation
  const createMutation = useMutation({
    mutationFn: createAdminFlashCard,
    onSuccess: (data) => {
      queryClient.invalidateQueries({ queryKey: ['adminFlashcards'] });
      navigate(`/admin/flashcards/${data.id}`);
    },
  });

  // Update mutation
  const updateMutation = useMutation({
    mutationFn: ({ id, data }: { id: number; data: AdminFlashCardUpdate }) =>
      updateAdminFlashCard(id, data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['adminFlashcards'] });
      queryClient.invalidateQueries({ queryKey: ['adminFlashcard', id] });
      navigate(`/admin/flashcards/${id}`);
    },
  });

  // Load flashcard data for editing
  useEffect(() => {
    if (flashcard) {
      setFormData({
        course_id: flashcard.course_id.toString(),
        front_content: flashcard.front_content,
        back_content: flashcard.back_content,
        topic: flashcard.topic || '',
        difficulty: flashcard.difficulty || '',
        is_active: flashcard.is_active,
      });
    }
  }, [flashcard]);

  const handleInputChange = (field: string) => (event: any) => {
    const value = event.target.type === 'checkbox' ? event.target.checked : event.target.value;
    setFormData(prev => ({ ...prev, [field]: value }));
    
    // Clear error when user starts typing
    if (errors[field]) {
      setErrors(prev => ({ ...prev, [field]: '' }));
    }
  };

  const validateForm = () => {
    const newErrors: Record<string, string> = {};

    if (!formData.course_id) {
      newErrors.course_id = 'Course is required';
    }
    if (!formData.front_content.trim()) {
      newErrors.front_content = 'Front content is required';
    }
    if (!formData.back_content.trim()) {
      newErrors.back_content = 'Back content is required';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = (event: React.FormEvent) => {
    event.preventDefault();
    
    if (!validateForm()) {
      return;
    }

    const submitData = {
      course_id: Number(formData.course_id),
      front_content: formData.front_content.trim(),
      back_content: formData.back_content.trim(),
      topic: formData.topic.trim() || undefined,
      difficulty: formData.difficulty || undefined,
      is_active: formData.is_active,
    };

    if (isEdit && id) {
      updateMutation.mutate({ id: Number(id), data: submitData });
    } else {
      createMutation.mutate(submitData as AdminFlashCardCreate);
    }
  };

  const handleCancel = () => {
    navigate('/admin/flashcards');
  };

  const handleImageUploaded = (imageUrl: string) => {
    // Image upload is handled separately, just refresh the flashcard data
    if (isEdit && id) {
      queryClient.invalidateQueries({ queryKey: ['adminFlashcard', id] });
    }
  };

  if (isEdit && isLoadingFlashcard) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
        <CircularProgress />
      </Box>
    );
  }

  if (isEdit && flashcardError) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="error">
          Failed to load flashcard. Please try again.
        </Alert>
      </Box>
    );
  }

  return (
    <Box sx={{ p: { xs: 2, md: 3 } }}>
      <PageHeader
        title={isEdit ? 'Edit Flashcard' : 'Create New Flashcard'}
        subtitle={isEdit ? 'Update flashcard details' : 'Add a new flashcard to the system'}
        action={
          <Box sx={{ display: 'flex', gap: 1 }}>
            <Button
              variant="outlined"
              startIcon={<PreviewIcon />}
              onClick={() => setShowPreview(!showPreview)}
              size={isMobile ? 'small' : 'medium'}
            >
              {showPreview ? 'Hide Preview' : 'Preview'}
            </Button>
          </Box>
        }
      />

      <Grid container spacing={3}>
        {/* Form */}
        <Grid item xs={12} md={showPreview ? 6 : 12}>
          <Paper sx={{ p: 3 }}>
            <form onSubmit={handleSubmit}>
              <Grid container spacing={3}>
                <Grid item xs={12}>
                  <FormControl fullWidth error={Boolean(errors.course_id)}>
                    <InputLabel>Course *</InputLabel>
                    <Select
                      value={formData.course_id}
                      onChange={handleInputChange('course_id')}
                      label="Course *"
                      disabled={isLoadingCourses}
                    >
                      {courses.map((course) => (
                        <MenuItem key={course.id} value={course.id.toString()}>
                          {course.name} ({course.code})
                        </MenuItem>
                      ))}
                    </Select>
                    {errors.course_id && (
                      <Typography variant="caption" color="error" sx={{ mt: 0.5 }}>
                        {errors.course_id}
                      </Typography>
                    )}
                  </FormControl>
                </Grid>

                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Front Content (Question) *"
                    multiline
                    rows={4}
                    value={formData.front_content}
                    onChange={handleInputChange('front_content')}
                    error={Boolean(errors.front_content)}
                    helperText={errors.front_content}
                    placeholder="Enter the question or front side of the flashcard..."
                  />
                </Grid>

                <Grid item xs={12}>
                  <TextField
                    fullWidth
                    label="Back Content (Answer) *"
                    multiline
                    rows={4}
                    value={formData.back_content}
                    onChange={handleInputChange('back_content')}
                    error={Boolean(errors.back_content)}
                    helperText={errors.back_content}
                    placeholder="Enter the answer or back side of the flashcard..."
                  />
                </Grid>

                <Grid item xs={12} sm={6}>
                  <TextField
                    fullWidth
                    label="Topic"
                    value={formData.topic}
                    onChange={handleInputChange('topic')}
                    placeholder="e.g., Algebra, Biology, History..."
                  />
                </Grid>

                <Grid item xs={12} sm={6}>
                  <FormControl fullWidth>
                    <InputLabel>Difficulty</InputLabel>
                    <Select
                      value={formData.difficulty}
                      onChange={handleInputChange('difficulty')}
                      label="Difficulty"
                    >
                      <MenuItem value="">None</MenuItem>
                      <MenuItem value="easy">Easy</MenuItem>
                      <MenuItem value="medium">Medium</MenuItem>
                      <MenuItem value="hard">Hard</MenuItem>
                    </Select>
                  </FormControl>
                </Grid>

                <Grid item xs={12}>
                  <FormControlLabel
                    control={
                      <Switch
                        checked={formData.is_active}
                        onChange={handleInputChange('is_active')}
                      />
                    }
                    label="Active"
                  />
                </Grid>

                {/* Image Upload Section */}
                {isEdit && id && (
                  <Grid item xs={12}>
                    <Divider sx={{ my: 2 }} />
                    <Typography variant="h6" gutterBottom>
                      Flashcard Image
                    </Typography>
                    <FlashCardImageUpload
                      flashcardId={Number(id)}
                      currentImageUrl={flashcard?.media_url}
                      onImageUploaded={handleImageUploaded}
                    />
                  </Grid>
                )}

                {/* Action Buttons */}
                <Grid item xs={12}>
                  <Box sx={{ display: 'flex', gap: 2, justifyContent: 'flex-end', mt: 2 }}>
                    <Button
                      variant="outlined"
                      startIcon={<CancelIcon />}
                      onClick={handleCancel}
                    >
                      Cancel
                    </Button>
                    <Button
                      type="submit"
                      variant="contained"
                      startIcon={<SaveIcon />}
                      disabled={createMutation.isPending || updateMutation.isPending}
                    >
                      {createMutation.isPending || updateMutation.isPending
                        ? 'Saving...'
                        : isEdit
                        ? 'Update Flashcard'
                        : 'Create Flashcard'}
                    </Button>
                  </Box>
                </Grid>
              </Grid>
            </form>
          </Paper>
        </Grid>

        {/* Preview */}
        {showPreview && (
          <Grid item xs={12} md={6}>
            <Paper sx={{ p: 3 }}>
              <Typography variant="h6" gutterBottom>
                Preview
              </Typography>
              <FlashCardPreview
                frontContent={formData.front_content}
                backContent={formData.back_content}
                topic={formData.topic}
                difficulty={formData.difficulty}
                imageUrl={flashcard?.media_url}
              />
            </Paper>
          </Grid>
        )}
      </Grid>

      {/* Error Display */}
      {(createMutation.error || updateMutation.error) && (
        <Alert severity="error" sx={{ mt: 2 }}>
          {createMutation.error?.message || updateMutation.error?.message}
        </Alert>
      )}
    </Box>
  );
};

export default AdminFlashCardForm;
