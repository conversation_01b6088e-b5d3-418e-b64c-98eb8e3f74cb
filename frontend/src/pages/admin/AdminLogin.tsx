import React, { useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import {
  Box,
  Card,
  CardContent,
  TextField,
  Button,
  Typography,
  Alert,
  InputAdornment,
  IconButton,
  Grid,
  useTheme,
  Fade,
} from '@mui/material';
import {
  Visibility,
  VisibilityOff,
  AdminPanelSettings,
  Lock as LockIcon,
  Person as PersonIcon,
} from '@mui/icons-material';
import { motion } from 'framer-motion';
import { useAuth } from '../../contexts/AuthContext';
import { adminLogin } from '../../api/admin';
import AnimatedBackground from '../../components/AnimatedBackground';
import EducationalIllustration from '../../components/EducationalIllustration';
import Logo from '../../components/Logo';

const MotionBox = motion(Box);

const AdminLogin: React.FC = () => {
  const [formData, setFormData] = useState({
    username: '',
    password: '',
  });
  const [showPassword, setShowPassword] = useState(false);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState('');

  const navigate = useNavigate();
  const location = useLocation();
  const theme = useTheme();
  const { login } = useAuth();

  const from = location.state?.from?.pathname || '/admin/dashboard';

  const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }));
    // Clear error when user starts typing
    if (error) setError('');
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setLoading(true);
    setError('');

    try {
      // Use admin-specific login endpoint
      const response = await adminLogin(formData);
      
      // Store token and get user data using regular auth flow
      localStorage.setItem('token', response.access_token);
      
      // Use the existing auth context to get user data
      await login(formData);
      
      // Navigate to admin dashboard
      navigate(from, { replace: true });
    } catch (err: any) {
      console.error('Admin login error:', err);
      setError(err.message || 'Admin login failed. Please check your credentials.');
    } finally {
      setLoading(false);
    }
  };

  const togglePasswordVisibility = () => {
    setShowPassword(!showPassword);
  };

  return (
    <AnimatedBackground>
      <Grid
        container
        sx={{
          maxWidth: { xs: '100%', md: 1000 },
          minHeight: { xs: 'auto', md: 600 },
          borderRadius: 3,
          overflow: 'hidden',
          backgroundColor: theme.palette.background.paper,
        }}
      >
        {/* Left side - Admin Login Form */}
        <Grid item xs={12} md={6}>
          <Card
            sx={{
              height: '100%',
              display: 'flex',
              flexDirection: 'column',
              justifyContent: 'center',
              borderRadius: 0,
              boxShadow: 'none',
              border: 'none',
            }}
          >
            <CardContent sx={{ p: { xs: 3, sm: 4 }, position: 'relative' }}>
              <MotionBox
                initial={{ opacity: 0, y: -20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.5 }}
                sx={{ textAlign: 'center', mb: 4 }}
              >
                <Logo size={80} sx={{ mb: 2 }} />
                <Typography variant="h4" component="h1" fontWeight="bold" gutterBottom>
                  Admin Portal
                </Typography>
                <Typography variant="body1" color="text.secondary">
                  Secure administrative access to CampusPQ
                </Typography>
              </MotionBox>

              {error && (
                <Fade in={true} timeout={1000}>
                  <Alert
                    severity="error"
                    sx={{
                      position: 'absolute',
                      top: 16,
                      left: 16,
                      right: 16,
                      zIndex: 1000,
                      borderRadius: 2,
                      wordBreak: 'break-word',
                      whiteSpace: 'pre-wrap',
                      overflow: 'hidden',
                      boxShadow: 3,
                      '& .MuiAlert-message': {
                        width: '100%',
                        overflow: 'hidden',
                        textOverflow: 'ellipsis'
                      }
                    }}
                  >
                    {error}
                  </Alert>
                </Fade>
              )}

              <form onSubmit={handleSubmit}>
                <MotionBox
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.5, delay: 0.2 }}
                >
                  <TextField
                    fullWidth
                    name="username"
                    label="Admin Email"
                    type="email"
                    variant="outlined"
                    margin="normal"
                    value={formData.username}
                    onChange={handleChange}
                    required
                    autoFocus
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <PersonIcon color="primary" />
                        </InputAdornment>
                      ),
                    }}
                    sx={{
                      mb: 2,
                      '& .MuiOutlinedInput-root': {
                        borderRadius: 2,
                        transition: 'all 0.3s ease-in-out',
                        '&:hover': {
                          transform: 'translateY(-2px)',
                        },
                        '&.Mui-focused': {
                          transform: 'translateY(-2px)',
                        }
                      }
                    }}
                  />
                </MotionBox>

                <MotionBox
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.5, delay: 0.3 }}
                >
                  <TextField
                    fullWidth
                    name="password"
                    label="Admin Password"
                    type={showPassword ? 'text' : 'password'}
                    variant="outlined"
                    margin="normal"
                    value={formData.password}
                    onChange={handleChange}
                    required
                    InputProps={{
                      startAdornment: (
                        <InputAdornment position="start">
                          <LockIcon color="primary" />
                        </InputAdornment>
                      ),
                      endAdornment: (
                        <InputAdornment position="end">
                          <IconButton
                            aria-label="toggle password visibility"
                            onClick={togglePasswordVisibility}
                            edge="end"
                          >
                            {showPassword ? <VisibilityOff /> : <Visibility />}
                          </IconButton>
                        </InputAdornment>
                      ),
                    }}
                    sx={{
                      mb: 3,
                      '& .MuiOutlinedInput-root': {
                        borderRadius: 2,
                        transition: 'all 0.3s ease-in-out',
                        '&:hover': {
                          transform: 'translateY(-2px)',
                        },
                        '&.Mui-focused': {
                          transform: 'translateY(-2px)',
                        }
                      }
                    }}
                  />
                </MotionBox>

                <MotionBox
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 0.4 }}
                >
                  <Button
                    fullWidth
                    type="submit"
                    variant="contained"
                    color="primary"
                    size="large"
                    disabled={loading}
                    sx={{
                      mt: 2,
                      mb: 3,
                      py: 1.5,
                      borderRadius: 2,
                      transition: 'all 0.3s ease',
                      '&:hover': {
                        transform: 'translateY(-2px)',
                      }
                    }}
                  >
                    {loading ? 'Authenticating...' : 'Access Admin Portal'}
                  </Button>
                </MotionBox>
              </form>

              <MotionBox
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.5, delay: 0.5 }}
                sx={{ textAlign: 'center' }}
              >
                <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
                  Authorized personnel only. All access is logged and monitored.
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Not an admin?{' '}
                  <Button
                    variant="text"
                    onClick={() => navigate('/login')}
                    sx={{
                      fontWeight: 'bold',
                      color: 'primary.main',
                      textDecoration: 'none',
                      transition: 'all 0.2s',
                      '&:hover': {
                        color: 'secondary.main',
                        textDecoration: 'underline'
                      }
                    }}
                  >
                    Student/Tutor Login
                  </Button>
                </Typography>
              </MotionBox>
            </CardContent>
          </Card>
        </Grid>

        {/* Right side - Educational Illustration */}
        <Grid
          item
          md={6}
          sx={{
            display: { xs: 'none', md: 'block' },
            bgcolor: theme.palette.mode === 'light'
              ? 'rgba(0, 0, 0, 0.02)'
              : 'rgba(255, 255, 255, 0.02)',
          }}
        >
          <EducationalIllustration
            title="Admin Portal"
            subtitle="Comprehensive platform management and analytics for administrators"
          />
        </Grid>
      </Grid>
    </AnimatedBackground>
  );
};

export default AdminLogin;
