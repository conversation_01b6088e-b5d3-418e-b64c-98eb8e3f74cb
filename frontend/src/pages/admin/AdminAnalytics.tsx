import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Paper,
  CircularProgress,
  Alert,
  Chip,
  LinearProgress,
  useTheme,
} from '@mui/material';
import {
  Analytics,
  TrendingUp,
  People,
  School,
  Quiz,
  Timeline,
} from '@mui/icons-material';
import { getAdminDashboardStats, AdminDashboardStats } from '../../api/admin';

interface MetricCardProps {
  title: string;
  value: number;
  subtitle?: string;
  icon: React.ReactNode;
  color: string;
  trend?: {
    value: number;
    isPositive: boolean;
  };
}

const MetricCard: React.FC<MetricCardProps> = ({
  title,
  value,
  subtitle,
  icon,
  color,
  trend,
}) => (
  <Card
    sx={{
      height: '100%',
      background: `linear-gradient(135deg, ${color}15 0%, ${color}05 100%)`,
      border: `1px solid ${color}30`,
    }}
  >
    <CardContent>
      <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
        <Box sx={{ color, mr: 2 }}>{icon}</Box>
        <Typography variant="h6" component="h3" fontWeight="bold">
          {title}
        </Typography>
      </Box>

      <Typography variant="h3" component="div" fontWeight="bold" color={color} sx={{ mb: 1 }}>
        {value.toLocaleString()}
      </Typography>

      {subtitle && (
        <Typography variant="body2" color="text.secondary" sx={{ mb: 1 }}>
          {subtitle}
        </Typography>
      )}

      {trend && (
        <Box sx={{ display: 'flex', alignItems: 'center' }}>
          <TrendingUp
            sx={{
              fontSize: 16,
              mr: 0.5,
              color: trend.isPositive ? 'success.main' : 'error.main',
              transform: trend.isPositive ? 'none' : 'rotate(180deg)',
            }}
          />
          <Typography
            variant="body2"
            sx={{
              color: trend.isPositive ? 'success.main' : 'error.main',
              fontWeight: 600,
            }}
          >
            {trend.isPositive ? '+' : ''}{trend.value}%
          </Typography>
        </Box>
      )}
    </CardContent>
  </Card>
);

const AdminAnalytics: React.FC = () => {
  const [stats, setStats] = useState<AdminDashboardStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const theme = useTheme();

  useEffect(() => {
    fetchAnalytics();
  }, []);

  const fetchAnalytics = async () => {
    try {
      setLoading(true);
      const data = await getAdminDashboardStats();
      setStats(data);
    } catch (err: any) {
      console.error('Failed to fetch analytics:', err);
      setError(err.message || 'Failed to load analytics data');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}>
        <CircularProgress size={60} />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ m: 2 }}>
        {error}
      </Alert>
    );
  }

  if (!stats) {
    return (
      <Alert severity="warning" sx={{ m: 2 }}>
        No analytics data available
      </Alert>
    );
  }

  const userEngagementRate = stats.users.total > 0 ? (stats.users.recent / stats.users.total) * 100 : 0;
  const schoolActiveRate = stats.schools.total > 0 ? (stats.schools.active / stats.schools.total) * 100 : 0;
  const courseActiveRate = stats.courses.total > 0 ? (stats.courses.active / stats.courses.total) * 100 : 0;

  return (
    <Box sx={{ p: { xs: 2, sm: 3 } }}>
      {/* Header */}
      <Paper
        sx={{
          p: { xs: 2, sm: 3 },
          mb: 3,
          background: theme.palette.mode === 'dark'
            ? `linear-gradient(135deg, ${theme.palette.grey[800]} 0%, ${theme.palette.grey[900]} 100%)`
            : `linear-gradient(135deg, ${theme.palette.background.paper} 0%, ${theme.palette.grey[50]} 100%)`,
          border: `1px solid ${theme.palette.divider}`,
          color: theme.palette.text.primary,
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Analytics sx={{ fontSize: { xs: 32, sm: 40 }, mr: 2, color: 'primary.main' }} />
          <Box>
            <Typography variant="h4" component="h1" fontWeight="bold" sx={{ fontSize: { xs: '1.5rem', sm: '2rem' } }}>
              Platform Analytics
            </Typography>
            <Typography variant="body1" sx={{ opacity: 0.7, fontSize: { xs: '0.875rem', sm: '1rem' } }}>
              Comprehensive insights into platform performance and usage
            </Typography>
          </Box>
        </Box>
      </Paper>

      {/* Key Metrics */}
      <Grid container spacing={3} sx={{ mb: 4 }}>
        <Grid item xs={12} sm={6} md={3}>
          <MetricCard
            title="Total Users"
            value={stats.users.total}
            subtitle={`${stats.users.recent} new this month`}
            icon={<People sx={{ fontSize: 32 }} />}
            color="#2196f3"
            trend={{ value: 12, isPositive: true }}
          />
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <MetricCard
            title="Active Schools"
            value={stats.schools.active}
            subtitle={`${stats.schools.total} total schools`}
            icon={<School sx={{ fontSize: 32 }} />}
            color="#4caf50"
          />
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <MetricCard
            title="Active Courses"
            value={stats.courses.active}
            subtitle={`${stats.courses.total} total courses`}
            icon={<Timeline sx={{ fontSize: 32 }} />}
            color="#ff9800"
          />
        </Grid>

        <Grid item xs={12} sm={6} md={3}>
          <MetricCard
            title="Questions"
            value={stats.questions.active}
            subtitle={`${stats.questions.total} total questions`}
            icon={<Quiz sx={{ fontSize: 32 }} />}
            color="#f44336"
          />
        </Grid>
      </Grid>

      {/* Detailed Analytics */}
      <Grid container spacing={3}>
        {/* User Distribution */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" component="h3" fontWeight="bold" sx={{ mb: 3 }}>
                User Distribution
              </Typography>
              
              <Box sx={{ mb: 2 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography variant="body2">Students</Typography>
                  <Typography variant="body2" fontWeight="bold">
                    {stats.users.students} ({((stats.users.students / stats.users.total) * 100).toFixed(1)}%)
                  </Typography>
                </Box>
                <LinearProgress
                  variant="determinate"
                  value={(stats.users.students / stats.users.total) * 100}
                  sx={{ height: 8, borderRadius: 4, backgroundColor: '#e3f2fd' }}
                />
              </Box>

              <Box sx={{ mb: 2 }}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography variant="body2">Tutors</Typography>
                  <Typography variant="body2" fontWeight="bold">
                    {stats.users.tutors} ({((stats.users.tutors / stats.users.total) * 100).toFixed(1)}%)
                  </Typography>
                </Box>
                <LinearProgress
                  variant="determinate"
                  value={(stats.users.tutors / stats.users.total) * 100}
                  sx={{ height: 8, borderRadius: 4, backgroundColor: '#fff3e0' }}
                  color="warning"
                />
              </Box>

              <Box>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', mb: 1 }}>
                  <Typography variant="body2">Admins</Typography>
                  <Typography variant="body2" fontWeight="bold">
                    {stats.users.admins} ({((stats.users.admins / stats.users.total) * 100).toFixed(1)}%)
                  </Typography>
                </Box>
                <LinearProgress
                  variant="determinate"
                  value={(stats.users.admins / stats.users.total) * 100}
                  sx={{ height: 8, borderRadius: 4, backgroundColor: '#ffebee' }}
                  color="error"
                />
              </Box>
            </CardContent>
          </Card>
        </Grid>

        {/* Platform Health */}
        <Grid item xs={12} md={6}>
          <Card>
            <CardContent>
              <Typography variant="h6" component="h3" fontWeight="bold" sx={{ mb: 3 }}>
                Platform Health
              </Typography>

              <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 1, mb: 2 }}>
                <Chip
                  label={`${schoolActiveRate.toFixed(1)}% Schools Active`}
                  color={schoolActiveRate > 80 ? 'success' : schoolActiveRate > 60 ? 'warning' : 'error'}
                  variant="outlined"
                />
                <Chip
                  label={`${courseActiveRate.toFixed(1)}% Courses Active`}
                  color={courseActiveRate > 80 ? 'success' : courseActiveRate > 60 ? 'warning' : 'error'}
                  variant="outlined"
                />
              </Box>

              <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                User Engagement Rate (Last 30 days)
              </Typography>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <Box sx={{ width: '100%', mr: 1 }}>
                  <LinearProgress
                    variant="determinate"
                    value={userEngagementRate}
                    sx={{ height: 10, borderRadius: 5 }}
                    color={userEngagementRate > 20 ? 'success' : userEngagementRate > 10 ? 'warning' : 'error'}
                  />
                </Box>
                <Typography variant="body2" fontWeight="bold">
                  {userEngagementRate.toFixed(1)}%
                </Typography>
              </Box>

              <Typography variant="body2" color="text.secondary">
                {stats.users.recent} new users joined in the last 30 days
              </Typography>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default AdminAnalytics;
