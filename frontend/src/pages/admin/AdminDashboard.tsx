import React, { useState, useEffect } from 'react';
import {
  Box,
  Grid,
  Card,
  CardContent,
  Typography,
  Button,
  Paper,
  Chip,
  IconButton,
  Tooltip,
  Alert,
  CircularProgress,
  useTheme,
} from '@mui/material';
import {
  People,
  School,
  Business,
  MenuBook,
  Quiz,
  TrendingUp,
  Add,
  Edit,
  Visibility,
  AdminPanelSettings,
  Dashboard as DashboardIcon,
  Style as FlashcardsIcon,
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { getAdminDashboardStats, AdminDashboardStats } from '../../api/admin';

interface StatCardProps {
  title: string;
  total: number;
  active?: number;
  inactive?: number;
  recent?: number;
  icon: React.ReactNode;
  color: string;
  onView: () => void;
  onAdd: () => void;
}

const StatCard: React.FC<StatCardProps> = ({
  title,
  total,
  active,
  inactive,
  recent,
  icon,
  color,
  onView,
  onAdd,
}) => {
  const theme = useTheme();

  return (
    <Card
      sx={{
        height: '100%',
        background: theme.palette.mode === 'dark'
          ? `linear-gradient(135deg, ${theme.palette.grey[800]} 0%, ${theme.palette.grey[900]} 100%)`
          : `linear-gradient(135deg, ${theme.palette.background.paper} 0%, ${theme.palette.grey[50]} 100%)`,
        border: `1px solid ${theme.palette.divider}`,
        transition: 'all 0.3s ease',
        '&:hover': {
          transform: 'translateY(-2px)',
          boxShadow: theme.shadows[8],
        },
      }}
    >
      <CardContent sx={{ p: { xs: 2, sm: 3 } }}>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <Box sx={{ color, mr: 2 }}>{icon}</Box>
          <Typography variant="h6" component="h2" fontWeight="bold" sx={{ fontSize: { xs: '1rem', sm: '1.25rem' } }}>
            {title}
          </Typography>
        </Box>

        <Typography
          variant="h3"
          component="div"
          fontWeight="bold"
          color={color}
          sx={{ mb: 2, fontSize: { xs: '1.75rem', sm: '2.5rem' } }}
        >
          {total.toLocaleString()}
        </Typography>

        <Box sx={{ mb: 2, display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
          {active !== undefined && (
            <Chip
              label={`${active} Active`}
              size="small"
              color="success"
              variant="outlined"
              sx={{ fontSize: { xs: '0.6rem', sm: '0.75rem' } }}
            />
          )}
          {inactive !== undefined && inactive > 0 && (
            <Chip
              label={`${inactive} Inactive`}
              size="small"
              color="error"
              variant="outlined"
              sx={{ fontSize: { xs: '0.6rem', sm: '0.75rem' } }}
            />
          )}
          {recent !== undefined && (
            <Chip
              label={`${recent} Recent`}
              size="small"
              color="info"
              variant="outlined"
              sx={{ fontSize: { xs: '0.6rem', sm: '0.75rem' } }}
            />
          )}
        </Box>

        <Box sx={{ display: 'flex', gap: 1, flexDirection: { xs: 'column', sm: 'row' } }}>
          <Button
            size="small"
            variant="outlined"
            startIcon={<Visibility />}
            onClick={onView}
            fullWidth
            sx={{ fontSize: { xs: '0.75rem', sm: '0.875rem' } }}
          >
            View
          </Button>
          <Button
            size="small"
            variant="contained"
            startIcon={<Add />}
            onClick={onAdd}
            fullWidth
            sx={{ fontSize: { xs: '0.75rem', sm: '0.875rem' } }}
          >
            Add
          </Button>
        </Box>
      </CardContent>
    </Card>
  );
};

const AdminDashboard: React.FC = () => {
  const [stats, setStats] = useState<AdminDashboardStats | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const navigate = useNavigate();
  const theme = useTheme();

  useEffect(() => {
    fetchDashboardStats();
  }, []);

  const fetchDashboardStats = async () => {
    try {
      setLoading(true);
      const data = await getAdminDashboardStats();
      setStats(data);
    } catch (err: any) {
      console.error('Failed to fetch dashboard stats:', err);
      setError(err.message || 'Failed to load dashboard statistics');
    } finally {
      setLoading(false);
    }
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}>
        <CircularProgress size={60} />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ m: 2 }}>
        {error}
        <Button onClick={fetchDashboardStats} sx={{ ml: 2 }}>
          Retry
        </Button>
      </Alert>
    );
  }

  if (!stats) {
    return (
      <Alert severity="warning" sx={{ m: 2 }}>
        No dashboard data available
      </Alert>
    );
  }

  return (
    <Box sx={{ p: { xs: 2, sm: 3 } }}>
      {/* Header */}
      <Paper
        sx={{
          p: { xs: 2, sm: 3 },
          mb: 3,
          background: theme.palette.mode === 'dark'
            ? `linear-gradient(135deg, ${theme.palette.grey[800]} 0%, ${theme.palette.grey[900]} 100%)`
            : `linear-gradient(135deg, ${theme.palette.background.paper} 0%, ${theme.palette.grey[50]} 100%)`,
          border: `1px solid ${theme.palette.divider}`,
          color: theme.palette.text.primary,
        }}
      >
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <AdminPanelSettings sx={{ fontSize: { xs: 32, sm: 40 }, mr: 2, color: 'primary.main' }} />
          <Box>
            <Typography variant="h4" component="h1" fontWeight="bold" sx={{ fontSize: { xs: '1.5rem', sm: '2rem' } }}>
              Admin Dashboard
            </Typography>
            <Typography variant="body1" sx={{ opacity: 0.7, fontSize: { xs: '0.875rem', sm: '1rem' } }}>
              Platform Management & Analytics
            </Typography>
          </Box>
        </Box>
      </Paper>

      {/* Statistics Grid */}
      <Grid container spacing={3}>
        <Grid item xs={12} sm={6} md={4}>
          <StatCard
            title="Users"
            total={stats.users.total}
            recent={stats.users.recent}
            icon={<People sx={{ fontSize: 32 }} />}
            color="#2196f3"
            onView={() => navigate('/admin/users')}
            onAdd={() => navigate('/admin/users/new')}
          />
        </Grid>

        <Grid item xs={12} sm={6} md={4}>
          <StatCard
            title="Schools"
            total={stats.schools.total}
            active={stats.schools.active}
            inactive={stats.schools.inactive}
            icon={<School sx={{ fontSize: 32 }} />}
            color="#4caf50"
            onView={() => navigate('/admin/schools')}
            onAdd={() => navigate('/admin/schools/new')}
          />
        </Grid>

        <Grid item xs={12} sm={6} md={4}>
          <StatCard
            title="Departments"
            total={stats.departments.total}
            active={stats.departments.active}
            inactive={stats.departments.inactive}
            icon={<Business sx={{ fontSize: 32 }} />}
            color="#ff9800"
            onView={() => navigate('/admin/departments')}
            onAdd={() => navigate('/admin/departments/new')}
          />
        </Grid>

        <Grid item xs={12} sm={6} md={4}>
          <StatCard
            title="Courses"
            total={stats.courses.total}
            active={stats.courses.active}
            inactive={stats.courses.inactive}
            icon={<MenuBook sx={{ fontSize: 32 }} />}
            color="#9c27b0"
            onView={() => navigate('/admin/courses')}
            onAdd={() => navigate('/admin/courses/new')}
          />
        </Grid>

        <Grid item xs={12} sm={6} md={4}>
          <StatCard
            title="Questions"
            total={stats.questions.total}
            active={stats.questions.active}
            inactive={stats.questions.inactive}
            icon={<Quiz sx={{ fontSize: 32 }} />}
            color="#f44336"
            onView={() => navigate('/admin/questions')}
            onAdd={() => navigate('/admin/questions/new')}
          />
        </Grid>

        <Grid item xs={12} sm={6} md={4}>
          <StatCard
            title="Flashcards"
            total={stats.flashcards.total}
            active={stats.flashcards.active}
            inactive={stats.flashcards.inactive}
            icon={<FlashcardsIcon sx={{ fontSize: 32 }} />}
            color="#795548"
            onView={() => navigate('/admin/flashcards')}
            onAdd={() => navigate('/admin/flashcards/new')}
          />
        </Grid>

        <Grid item xs={12} sm={6} md={4}>
          <Card
            sx={{
              height: '100%',
              background: theme.palette.mode === 'dark'
                ? `linear-gradient(135deg, ${theme.palette.grey[800]} 0%, ${theme.palette.grey[900]} 100%)`
                : `linear-gradient(135deg, ${theme.palette.background.paper} 0%, ${theme.palette.grey[50]} 100%)`,
              border: `1px solid ${theme.palette.divider}`,
            }}
          >
            <CardContent sx={{ p: { xs: 2, sm: 3 } }}>
              <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
                <TrendingUp sx={{ color: 'primary.main', fontSize: { xs: 28, sm: 32 }, mr: 2 }} />
                <Typography variant="h6" component="h2" fontWeight="bold" sx={{ fontSize: { xs: '1rem', sm: '1.25rem' } }}>
                  Quick Actions
                </Typography>
              </Box>

              <Box sx={{ display: 'flex', flexDirection: 'column', gap: 1 }}>
                <Button
                  variant="outlined"
                  startIcon={<People />}
                  onClick={() => navigate('/admin/users/bulk')}
                  fullWidth
                  sx={{ fontSize: { xs: '0.75rem', sm: '0.875rem' } }}
                >
                  Bulk User Management
                </Button>
                <Button
                  variant="outlined"
                  startIcon={<DashboardIcon />}
                  onClick={() => navigate('/admin/analytics')}
                  fullWidth
                  sx={{ fontSize: { xs: '0.75rem', sm: '0.875rem' } }}
                >
                  View Analytics
                </Button>
                <Button
                  variant="outlined"
                  startIcon={<Edit />}
                  onClick={() => navigate('/admin/settings')}
                  fullWidth
                  sx={{ fontSize: { xs: '0.75rem', sm: '0.875rem' } }}
                >
                  System Settings
                </Button>
              </Box>
            </CardContent>
          </Card>
        </Grid>
      </Grid>
    </Box>
  );
};

export default AdminDashboard;
