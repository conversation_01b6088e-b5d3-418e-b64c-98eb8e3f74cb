import React, { useState, useEffect } from 'react';
import {
  Box,
  Paper,
  Typography,
  Button,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  Checkbox,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  Alert,
  CircularProgress,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  Stack,
  TextField,
  useTheme,
  useMediaQuery,
} from '@mui/material';
import {
  People,
  Edit,
  Delete,
  FilterList,
  CheckCircle,
  Cancel,
} from '@mui/icons-material';
import { getUsersForBulkActions, bulkUpdateUsers, BulkUpdateRequest } from '../../api/admin';
import { User } from '../../api/auth';

const BulkUserManagement: React.FC = () => {
  const [users, setUsers] = useState<User[]>([]);
  const [selectedUsers, setSelectedUsers] = useState<number[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState('');
  const [filters, setFilters] = useState({
    role: '',
    is_active: '',
  });
  const [bulkAction, setBulkAction] = useState('');
  const [confirmDialog, setConfirmDialog] = useState(false);
  const [processing, setProcessing] = useState(false);
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));

  useEffect(() => {
    fetchUsers();
  }, [filters]);

  const fetchUsers = async () => {
    try {
      setLoading(true);
      const filterParams: any = {};
      if (filters.role) filterParams.role = filters.role;
      if (filters.is_active !== '') filterParams.is_active = filters.is_active === 'true';

      const data = await getUsersForBulkActions(filterParams);
      setUsers(data);
    } catch (err: any) {
      setError(err.message || 'Failed to fetch users');
    } finally {
      setLoading(false);
    }
  };

  const handleSelectAll = (checked: boolean) => {
    if (checked) {
      setSelectedUsers(users.map(user => user.id));
    } else {
      setSelectedUsers([]);
    }
  };

  const handleSelectUser = (userId: number, checked: boolean) => {
    if (checked) {
      setSelectedUsers(prev => [...prev, userId]);
    } else {
      setSelectedUsers(prev => prev.filter(id => id !== userId));
    }
  };

  const handleBulkAction = async () => {
    if (!bulkAction || selectedUsers.length === 0) return;

    setProcessing(true);
    try {
      const updateData: BulkUpdateRequest = {
        user_ids: selectedUsers,
        update_data: {},
      };

      switch (bulkAction) {
        case 'activate':
          updateData.update_data.is_active = true;
          break;
        case 'deactivate':
          updateData.update_data.is_active = false;
          break;
        case 'make_tutor':
          updateData.update_data.role = 'tutor';
          break;
        case 'make_student':
          updateData.update_data.role = 'student';
          break;
      }

      const result = await bulkUpdateUsers(updateData);
      
      if (result.success) {
        setSelectedUsers([]);
        setBulkAction('');
        fetchUsers();
        alert(`Successfully updated ${result.updated_count} users`);
      } else {
        alert(`Updated ${result.updated_count} users with ${result.errors.length} errors`);
      }
    } catch (err: any) {
      setError(err.message || 'Failed to perform bulk action');
    } finally {
      setProcessing(false);
      setConfirmDialog(false);
    }
  };

  const getRoleColor = (role: string) => {
    switch (role) {
      case 'admin': return 'error';
      case 'tutor': return 'warning';
      case 'student': return 'primary';
      default: return 'default';
    }
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}>
        <CircularProgress size={60} />
      </Box>
    );
  }

  return (
    <Box sx={{ p: { xs: 2, sm: 3 } }}>
      {/* Header */}
      <Paper sx={{ p: { xs: 2, sm: 3 }, mb: 3, border: `1px solid ${theme.palette.divider}` }}>
        <Box sx={{ display: 'flex', alignItems: 'center', mb: 2 }}>
          <People sx={{ fontSize: { xs: 28, sm: 32 }, mr: 2, color: 'primary.main' }} />
          <Typography variant="h4" component="h1" fontWeight="bold" sx={{ fontSize: { xs: '1.5rem', sm: '2rem' } }}>
            Bulk User Management
          </Typography>
        </Box>
        <Typography variant="body1" color="text.secondary" sx={{ fontSize: { xs: '0.875rem', sm: '1rem' } }}>
          Select multiple users and perform bulk operations
        </Typography>
      </Paper>

      {error && (
        <Alert severity="error" sx={{ mb: 3 }}>
          {error}
        </Alert>
      )}

      {/* Filters and Actions */}
      <Paper sx={{ p: 3, mb: 3 }}>
        <Stack direction={{ xs: 'column', md: 'row' }} spacing={2} alignItems="center">
          <FormControl size="small" sx={{ minWidth: 120 }}>
            <InputLabel>Role</InputLabel>
            <Select
              value={filters.role}
              label="Role"
              onChange={(e) => setFilters(prev => ({ ...prev, role: e.target.value }))}
            >
              <MenuItem value="">All Roles</MenuItem>
              <MenuItem value="student">Student</MenuItem>
              <MenuItem value="tutor">Tutor</MenuItem>
              <MenuItem value="admin">Admin</MenuItem>
            </Select>
          </FormControl>

          <FormControl size="small" sx={{ minWidth: 120 }}>
            <InputLabel>Status</InputLabel>
            <Select
              value={filters.is_active}
              label="Status"
              onChange={(e) => setFilters(prev => ({ ...prev, is_active: e.target.value }))}
            >
              <MenuItem value="">All Status</MenuItem>
              <MenuItem value="true">Active</MenuItem>
              <MenuItem value="false">Inactive</MenuItem>
            </Select>
          </FormControl>

          <Box sx={{ flexGrow: 1 }} />

          {selectedUsers.length > 0 && (
            <>
              <FormControl size="small" sx={{ minWidth: 150 }}>
                <InputLabel>Bulk Action</InputLabel>
                <Select
                  value={bulkAction}
                  label="Bulk Action"
                  onChange={(e) => setBulkAction(e.target.value)}
                >
                  <MenuItem value="activate">Activate Users</MenuItem>
                  <MenuItem value="deactivate">Deactivate Users</MenuItem>
                  <MenuItem value="make_tutor">Make Tutor</MenuItem>
                  <MenuItem value="make_student">Make Student</MenuItem>
                </Select>
              </FormControl>

              <Button
                variant="contained"
                onClick={() => setConfirmDialog(true)}
                disabled={!bulkAction || processing}
                startIcon={processing ? <CircularProgress size={20} /> : <Edit />}
              >
                Apply to {selectedUsers.length} users
              </Button>
            </>
          )}
        </Stack>
      </Paper>

      {/* Users Table */}
      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell padding="checkbox">
                <Checkbox
                  indeterminate={selectedUsers.length > 0 && selectedUsers.length < users.length}
                  checked={users.length > 0 && selectedUsers.length === users.length}
                  onChange={(e) => handleSelectAll(e.target.checked)}
                />
              </TableCell>
              <TableCell>Name</TableCell>
              <TableCell>Email</TableCell>
              <TableCell>Role</TableCell>
              <TableCell>Status</TableCell>
              <TableCell>Created</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {users.map((user) => (
              <TableRow key={user.id} hover>
                <TableCell padding="checkbox">
                  <Checkbox
                    checked={selectedUsers.includes(user.id)}
                    onChange={(e) => handleSelectUser(user.id, e.target.checked)}
                  />
                </TableCell>
                <TableCell>
                  <Typography variant="body2" fontWeight="medium">
                    {user.full_name}
                  </Typography>
                </TableCell>
                <TableCell>{user.email}</TableCell>
                <TableCell>
                  <Chip
                    label={user.role}
                    color={getRoleColor(user.role) as any}
                    size="small"
                    variant="outlined"
                  />
                </TableCell>
                <TableCell>
                  <Chip
                    icon={user.is_active ? <CheckCircle /> : <Cancel />}
                    label={user.is_active ? 'Active' : 'Inactive'}
                    color={user.is_active ? 'success' : 'error'}
                    size="small"
                    variant="outlined"
                  />
                </TableCell>
                <TableCell>
                  <Typography variant="body2" color="text.secondary">
                    {new Date(user.created_at).toLocaleDateString()}
                  </Typography>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </TableContainer>

      {/* Confirmation Dialog */}
      <Dialog open={confirmDialog} onClose={() => setConfirmDialog(false)}>
        <DialogTitle>Confirm Bulk Action</DialogTitle>
        <DialogContent>
          <Typography>
            Are you sure you want to apply "{bulkAction}" to {selectedUsers.length} selected users?
            This action cannot be undone.
          </Typography>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setConfirmDialog(false)}>Cancel</Button>
          <Button onClick={handleBulkAction} variant="contained" color="primary">
            Confirm
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default BulkUserManagement;
