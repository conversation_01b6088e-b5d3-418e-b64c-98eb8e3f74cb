import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Table,
  TableBody,
  TableCell,
  TableContainer,
  TableHead,
  TableRow,
  TablePagination,
  Chip,
  IconButton,
  Button,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Tooltip,
  Alert,
  Card,
  CardContent,
  Grid,
  Divider,
  Checkbox,
  Menu,
  ListItemText
} from '@mui/material';
import {
  Visibility as ViewIcon,
  Edit as EditIcon,
  Delete as DeleteIcon,
  Flag as FlagIcon,
  CheckCircle as ResolveIcon,
  Cancel as DismissIcon,
  MoreVert as MoreIcon,
  FilterList as FilterIcon,
  Refresh as RefreshIcon,
  QuestionAnswer as EditQuestionIcon
} from '@mui/icons-material';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { motion } from 'framer-motion';

import {
  getAllFlags,
  getFlagStatistics,
  updateFlagStatus,
  bulkUpdateFlagStatus,
  deleteFlag,
  updateQuestion,
  updateQuestionContent,
  updateFlagNotes,
  QuestionFlag,
  FlagStatistics as FlagStatsType,
  AdminFlagListRequest
} from '../../api/questionFlags';
import MathMarkdown from '../../components/common/MathMarkdown';
import { useToast } from '../../contexts/ToastContext';

const FlaggedQuestions: React.FC = () => {
  const [page, setPage] = useState(0);
  const [rowsPerPage, setRowsPerPage] = useState(20);
  const [selectedFlags, setSelectedFlags] = useState<number[]>([]);
  const [statusFilter, setStatusFilter] = useState<string>('');
  const [reasonFilter, setReasonFilter] = useState<string>('');
  const [showFilters, setShowFilters] = useState(false);
  const [selectedFlag, setSelectedFlag] = useState<QuestionFlag | null>(null);
  const [showDetailModal, setShowDetailModal] = useState(false);
  const [showStatusModal, setShowStatusModal] = useState(false);
  const [newStatus, setNewStatus] = useState<string>('');
  const [adminNotes, setAdminNotes] = useState<string>('');
  const [actionMenuAnchor, setActionMenuAnchor] = useState<null | HTMLElement>(null);
  const [showEditModal, setShowEditModal] = useState(false);
  const [editingFlag, setEditingFlag] = useState<QuestionFlag | null>(null);

  const queryClient = useQueryClient();
  const { showSuccess, showError } = useToast();

  // Fetch flags
  const {
    data: flagsData,
    isLoading: isLoadingFlags,
    error: flagsError,
    refetch: refetchFlags
  } = useQuery({
    queryKey: ['admin-flags', page, rowsPerPage, statusFilter, reasonFilter],
    queryFn: () => {
      const request: AdminFlagListRequest = {
        page: page + 1,
        per_page: rowsPerPage,
        filters: {
          ...(statusFilter && { status: statusFilter }),
          ...(reasonFilter && { reason: reasonFilter })
        }
      };
      console.log('Fetching flags with request:', request);
      return getAllFlags(request);
    },
    onSuccess: (data) => {
      console.log('Flags data received:', data);
    },
    onError: (error) => {
      console.error('Error fetching flags:', error);
    }
  });

  // Fetch statistics
  const { data: statistics } = useQuery({
    queryKey: ['flag-statistics'],
    queryFn: getFlagStatistics
  });

  // Update flag status mutation
  const updateStatusMutation = useMutation({
    mutationFn: ({ flagId, status, notes }: { flagId: number; status: string; notes?: string }) =>
      updateFlagStatus(flagId, status, notes),
    onSuccess: () => {
      showSuccess('Flag status updated successfully');
      queryClient.invalidateQueries({ queryKey: ['admin-flags'] });
      queryClient.invalidateQueries({ queryKey: ['flag-statistics'] });
      setShowStatusModal(false);
      setSelectedFlag(null);
    },
    onError: (error: any) => {
      showError(error.response?.data?.detail || 'Failed to update flag status');
    }
  });

  // Bulk update mutation
  const bulkUpdateMutation = useMutation({
    mutationFn: ({ flagIds, status, notes }: { flagIds: number[]; status: string; notes?: string }) =>
      bulkUpdateFlagStatus(flagIds, status, notes),
    onSuccess: (data) => {
      showSuccess(`Updated ${data.updated_flags} flags successfully`);
      queryClient.invalidateQueries({ queryKey: ['admin-flags'] });
      queryClient.invalidateQueries({ queryKey: ['flag-statistics'] });
      setSelectedFlags([]);
    },
    onError: (error: any) => {
      showError(error.response?.data?.detail || 'Failed to bulk update flags');
    }
  });

  // Delete flag mutation
  const deleteMutation = useMutation({
    mutationFn: deleteFlag,
    onSuccess: () => {
      showSuccess('Flag deleted successfully');
      queryClient.invalidateQueries({ queryKey: ['admin-flags'] });
      queryClient.invalidateQueries({ queryKey: ['flag-statistics'] });
    },
    onError: (error: any) => {
      showError(error.response?.data?.detail || 'Failed to delete flag');
    }
  });

  // Update question mutation
  const updateQuestionMutation = useMutation({
    mutationFn: ({ questionId, questionData }: {
      questionId: number;
      questionData: {
        content?: string;
        options?: Record<string, string>;
        answer?: string;
        explanation?: string;
      }
    }) => updateQuestion(questionId, questionData),
    onSuccess: () => {
      showSuccess('Question updated successfully');
      queryClient.invalidateQueries({ queryKey: ['admin-flags'] });
    },
    onError: (error: any) => {
      showError(error.response?.data?.detail || 'Failed to update question');
    }
  });

  // Update flag notes mutation
  const updateFlagNotesMutation = useMutation({
    mutationFn: ({ flagId, adminNotes }: { flagId: number; adminNotes: string }) =>
      updateFlagNotes(flagId, adminNotes),
    onSuccess: () => {
      showSuccess('Flag notes updated successfully');
      queryClient.invalidateQueries({ queryKey: ['admin-flags'] });
    },
    onError: (error: any) => {
      showError(error.response?.data?.detail || 'Failed to update flag notes');
    }
  });

  const handleChangePage = (event: unknown, newPage: number) => {
    setPage(newPage);
  };

  const handleChangeRowsPerPage = (event: React.ChangeEvent<HTMLInputElement>) => {
    setRowsPerPage(parseInt(event.target.value, 10));
    setPage(0);
  };

  const handleSelectFlag = (flagId: number) => {
    setSelectedFlags(prev =>
      prev.includes(flagId)
        ? prev.filter(id => id !== flagId)
        : [...prev, flagId]
    );
  };

  const handleSelectAllFlags = () => {
    if (selectedFlags.length === (flagsData?.flags.length || 0)) {
      setSelectedFlags([]);
    } else {
      setSelectedFlags(flagsData?.flags.map(flag => flag.id) || []);
    }
  };

  const handleViewFlag = (flag: QuestionFlag) => {
    setSelectedFlag(flag);
    setShowDetailModal(true);
  };

  const handleUpdateStatus = (flag: QuestionFlag) => {
    setSelectedFlag(flag);
    setNewStatus(flag.status);
    setAdminNotes(flag.admin_notes || '');
    setShowStatusModal(true);
  };

  const handleEditFlag = (flag: QuestionFlag) => {
    setEditingFlag(flag);
    setShowEditModal(true);
  };

  const handleSubmitStatusUpdate = () => {
    if (!selectedFlag) return;

    updateStatusMutation.mutate({
      flagId: selectedFlag.id,
      status: newStatus,
      notes: adminNotes.trim() || undefined
    });
  };

  const handleBulkAction = (action: string) => {
    if (selectedFlags.length === 0) return;

    bulkUpdateMutation.mutate({
      flagIds: selectedFlags,
      status: action,
      notes: `Bulk ${action} by admin`
    });
    setActionMenuAnchor(null);
  };

  const handleDeleteFlag = (flagId: number) => {
    if (window.confirm('Are you sure you want to delete this flag?')) {
      deleteMutation.mutate(flagId);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'pending':
        return 'warning';
      case 'under_review':
        return 'info';
      case 'resolved':
        return 'success';
      case 'dismissed':
        return 'error';
      default:
        return 'default';
    }
  };

  const formatDate = (dateString: string) => {
    return new Date(dateString).toLocaleDateString('en-US', {
      year: 'numeric',
      month: 'short',
      day: 'numeric',
      hour: '2-digit',
      minute: '2-digit'
    });
  };

  const getReasonLabels = (reasons: string[]) => {
    const reasonMap: Record<string, string> = {
      'wrong_answer': 'Wrong Answer',
      'incorrect_spelling': 'Spelling Error',
      'not_visible': 'Not Visible',
      'no_diagram': 'Missing Diagram',
      'incorrect_rendering': 'Rendering Issue',
      'no_options': 'Missing Options',
      'unclear_question': 'Unclear',
      'duplicate_question': 'Duplicate',
      'inappropriate_content': 'Inappropriate',
      'other': 'Other'
    };

    return reasons.map(reason => reasonMap[reason] || reason);
  };

  return (
    <Box sx={{ p: 3 }}>
      <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', mb: 3 }}>
        <Typography variant="h4" component="h1">
          Flagged Questions
        </Typography>
        <Box sx={{ display: 'flex', gap: 2 }}>
          <Button
            variant="outlined"
            startIcon={<FilterIcon />}
            onClick={() => setShowFilters(!showFilters)}
          >
            Filters
          </Button>
          <Button
            variant="outlined"
            startIcon={<RefreshIcon />}
            onClick={() => refetchFlags()}
          >
            Refresh
          </Button>
        </Box>
      </Box>

      {/* Debug Info */}
      <Box sx={{ mb: 2, p: 2, bgcolor: 'info.light', borderRadius: 1 }}>
        <Typography variant="subtitle2">Debug Info:</Typography>
        <Typography variant="body2">
          Loading: {isLoadingFlags ? 'Yes' : 'No'} |
          Error: {flagsError ? 'Yes' : 'No'} |
          Data: {flagsData ? `${flagsData.flags?.length || 0} flags` : 'None'} |
          Total: {flagsData?.total || 0}
        </Typography>
        {flagsError && (
          <Typography variant="body2" color="error">
            Error: {String(flagsError)}
          </Typography>
        )}
      </Box>

      {/* Statistics Cards */}
      {statistics && (
        <Grid container spacing={3} sx={{ mb: 3 }}>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Typography color="textSecondary" gutterBottom>
                  Total Flags
                </Typography>
                <Typography variant="h4">
                  {statistics.total_flags}
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Typography color="textSecondary" gutterBottom>
                  Pending Review
                </Typography>
                <Typography variant="h4" color="warning.main">
                  {statistics.pending_flags}
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Typography color="textSecondary" gutterBottom>
                  Resolved
                </Typography>
                <Typography variant="h4" color="success.main">
                  {statistics.resolved_flags}
                </Typography>
              </CardContent>
            </Card>
          </Grid>
          <Grid item xs={12} sm={6} md={3}>
            <Card>
              <CardContent>
                <Typography color="textSecondary" gutterBottom>
                  Dismissed
                </Typography>
                <Typography variant="h4" color="error.main">
                  {statistics.dismissed_flags}
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        </Grid>
      )}

      {/* Filters */}
      {showFilters && (
        <Paper sx={{ p: 2, mb: 3 }}>
          <Grid container spacing={2}>
            <Grid item xs={12} sm={6} md={3}>
              <FormControl fullWidth>
                <InputLabel>Status</InputLabel>
                <Select
                  value={statusFilter}
                  onChange={(e) => setStatusFilter(e.target.value)}
                  label="Status"
                >
                  <MenuItem value="">All</MenuItem>
                  <MenuItem value="pending">Pending</MenuItem>
                  <MenuItem value="under_review">Under Review</MenuItem>
                  <MenuItem value="resolved">Resolved</MenuItem>
                  <MenuItem value="dismissed">Dismissed</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12} sm={6} md={3}>
              <FormControl fullWidth>
                <InputLabel>Reason</InputLabel>
                <Select
                  value={reasonFilter}
                  onChange={(e) => setReasonFilter(e.target.value)}
                  label="Reason"
                >
                  <MenuItem value="">All</MenuItem>
                  <MenuItem value="wrong_answer">Wrong Answer</MenuItem>
                  <MenuItem value="incorrect_spelling">Spelling Error</MenuItem>
                  <MenuItem value="not_visible">Not Visible</MenuItem>
                  <MenuItem value="no_diagram">Missing Diagram</MenuItem>
                  <MenuItem value="incorrect_rendering">Rendering Issue</MenuItem>
                  <MenuItem value="no_options">Missing Options</MenuItem>
                  <MenuItem value="unclear_question">Unclear</MenuItem>
                  <MenuItem value="other">Other</MenuItem>
                </Select>
              </FormControl>
            </Grid>
          </Grid>
        </Paper>
      )}

      {/* Bulk Actions */}
      {selectedFlags.length > 0 && (
        <Paper sx={{ p: 2, mb: 2, bgcolor: 'primary.light', color: 'primary.contrastText' }}>
          <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
            <Typography>
              {selectedFlags.length} flag(s) selected
            </Typography>
            <Box>
              <Button
                color="inherit"
                startIcon={<ResolveIcon />}
                onClick={() => handleBulkAction('resolved')}
                sx={{ mr: 1 }}
              >
                Mark Resolved
              </Button>
              <Button
                color="inherit"
                startIcon={<DismissIcon />}
                onClick={() => handleBulkAction('dismissed')}
              >
                Dismiss
              </Button>
            </Box>
          </Box>
        </Paper>
      )}

      {/* Flags Table */}
      <TableContainer component={Paper}>
        <Table>
          <TableHead>
            <TableRow>
              <TableCell padding="checkbox">
                <Checkbox
                  checked={selectedFlags.length === (flagsData?.flags.length || 0) && flagsData?.flags.length > 0}
                  indeterminate={selectedFlags.length > 0 && selectedFlags.length < (flagsData?.flags.length || 0)}
                  onChange={handleSelectAllFlags}
                />
              </TableCell>
              <TableCell>Question</TableCell>
              <TableCell>Student</TableCell>
              <TableCell>Reasons</TableCell>
              <TableCell>Status</TableCell>
              <TableCell>Created</TableCell>
              <TableCell>Actions</TableCell>
            </TableRow>
          </TableHead>
          <TableBody>
            {flagsData?.flags.map((flag) => (
              <TableRow key={flag.id} hover>
                <TableCell padding="checkbox">
                  <Checkbox
                    checked={selectedFlags.includes(flag.id)}
                    onChange={() => handleSelectFlag(flag.id)}
                  />
                </TableCell>
                <TableCell>
                  <Box sx={{ maxWidth: 300 }}>
                    <Typography variant="body2" noWrap>
                      <MathMarkdown>
                        {flag.question?.content.substring(0, 100) + '...' || 'Question content'}
                      </MathMarkdown>
                    </Typography>
                    <Typography variant="caption" color="text.secondary">
                      ID: {flag.question_id} | {flag.question?.difficulty}
                    </Typography>
                  </Box>
                </TableCell>
                <TableCell>
                  <Typography variant="body2">
                    {flag.student?.full_name}
                  </Typography>
                  <Typography variant="caption" color="text.secondary">
                    {flag.student?.email}
                  </Typography>
                </TableCell>
                <TableCell>
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                    {getReasonLabels(flag.reasons).map((reason, index) => (
                      <Chip
                        key={index}
                        label={reason}
                        size="small"
                        variant="outlined"
                      />
                    ))}
                  </Box>
                </TableCell>
                <TableCell>
                  <Chip
                    label={flag.status.replace('_', ' ').toUpperCase()}
                    color={getStatusColor(flag.status) as any}
                    size="small"
                  />
                </TableCell>
                <TableCell>
                  <Typography variant="body2">
                    {formatDate(flag.created_at)}
                  </Typography>
                </TableCell>
                <TableCell>
                  <Box sx={{ display: 'flex', gap: 1 }}>
                    <Tooltip title="View Details">
                      <IconButton
                        size="small"
                        onClick={() => handleViewFlag(flag)}
                      >
                        <ViewIcon />
                      </IconButton>
                    </Tooltip>
                    <Tooltip title="Edit Question">
                      <IconButton
                        size="small"
                        onClick={() => handleEditFlag(flag)}
                        color="primary"
                      >
                        <EditQuestionIcon />
                      </IconButton>
                    </Tooltip>
                    <Tooltip title="Update Status">
                      <IconButton
                        size="small"
                        onClick={() => handleUpdateStatus(flag)}
                      >
                        <EditIcon />
                      </IconButton>
                    </Tooltip>
                    <Tooltip title="Delete Flag">
                      <IconButton
                        size="small"
                        onClick={() => handleDeleteFlag(flag.id)}
                        color="error"
                      >
                        <DeleteIcon />
                      </IconButton>
                    </Tooltip>
                  </Box>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
        <TablePagination
          rowsPerPageOptions={[10, 20, 50]}
          component="div"
          count={flagsData?.total || 0}
          rowsPerPage={rowsPerPage}
          page={page}
          onPageChange={handleChangePage}
          onRowsPerPageChange={handleChangeRowsPerPage}
        />
      </TableContainer>

      {/* Detail Modal */}
      <Dialog
        open={showDetailModal}
        onClose={() => setShowDetailModal(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          Flag Details
        </DialogTitle>
        <DialogContent>
          {selectedFlag && (
            <Box>
              <Typography variant="h6" gutterBottom>
                Question Content:
              </Typography>
              <Paper sx={{ p: 2, mb: 2, bgcolor: 'grey.50' }}>
                <MathMarkdown>{selectedFlag.question?.content || 'No content available'}</MathMarkdown>
              </Paper>

              <Typography variant="h6" gutterBottom>
                Flag Information:
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={6}>
                  <Typography variant="body2" color="text.secondary">
                    Student:
                  </Typography>
                  <Typography variant="body1">
                    {selectedFlag.student?.full_name} ({selectedFlag.student?.email})
                  </Typography>
                </Grid>
                <Grid item xs={6}>
                  <Typography variant="body2" color="text.secondary">
                    Status:
                  </Typography>
                  <Chip
                    label={selectedFlag.status.replace('_', ' ').toUpperCase()}
                    color={getStatusColor(selectedFlag.status) as any}
                    size="small"
                  />
                </Grid>
                <Grid item xs={12}>
                  <Typography variant="body2" color="text.secondary">
                    Reasons:
                  </Typography>
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5, mt: 0.5 }}>
                    {getReasonLabels(selectedFlag.reasons).map((reason, index) => (
                      <Chip
                        key={index}
                        label={reason}
                        size="small"
                        variant="outlined"
                      />
                    ))}
                  </Box>
                </Grid>
                {selectedFlag.description && (
                  <Grid item xs={12}>
                    <Typography variant="body2" color="text.secondary">
                      Additional Details:
                    </Typography>
                    <Typography variant="body1">
                      {selectedFlag.description}
                    </Typography>
                  </Grid>
                )}
                {selectedFlag.admin_notes && (
                  <Grid item xs={12}>
                    <Typography variant="body2" color="text.secondary">
                      Admin Notes:
                    </Typography>
                    <Typography variant="body1">
                      {selectedFlag.admin_notes}
                    </Typography>
                  </Grid>
                )}
              </Grid>
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowDetailModal(false)}>
            Close
          </Button>
          {selectedFlag && (
            <Button
              variant="contained"
              onClick={() => {
                setShowDetailModal(false);
                handleUpdateStatus(selectedFlag);
              }}
            >
              Update Status
            </Button>
          )}
        </DialogActions>
      </Dialog>

      {/* Status Update Modal */}
      <Dialog
        open={showStatusModal}
        onClose={() => setShowStatusModal(false)}
        maxWidth="sm"
        fullWidth
      >
        <DialogTitle>
          Update Flag Status
        </DialogTitle>
        <DialogContent>
          <Box sx={{ pt: 1 }}>
            <FormControl fullWidth sx={{ mb: 2 }}>
              <InputLabel>Status</InputLabel>
              <Select
                value={newStatus}
                onChange={(e) => setNewStatus(e.target.value)}
                label="Status"
              >
                <MenuItem value="pending">Pending</MenuItem>
                <MenuItem value="under_review">Under Review</MenuItem>
                <MenuItem value="resolved">Resolved</MenuItem>
                <MenuItem value="dismissed">Dismissed</MenuItem>
              </Select>
            </FormControl>
            <TextField
              fullWidth
              multiline
              rows={3}
              label="Admin Notes"
              value={adminNotes}
              onChange={(e) => setAdminNotes(e.target.value)}
              placeholder="Add notes about the resolution..."
            />
          </Box>
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowStatusModal(false)}>
            Cancel
          </Button>
          <Button
            variant="contained"
            onClick={handleSubmitStatusUpdate}
            disabled={updateStatusMutation.isPending}
          >
            {updateStatusMutation.isPending ? 'Updating...' : 'Update'}
          </Button>
        </DialogActions>
      </Dialog>

      {/* Edit Question Modal */}
      <Dialog
        open={showEditModal}
        onClose={() => setShowEditModal(false)}
        maxWidth="md"
        fullWidth
      >
        <DialogTitle>
          <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
            <EditQuestionIcon color="primary" />
            Edit Flagged Question
          </Box>
        </DialogTitle>
        <DialogContent>
          {editingFlag && (
            <Box>
              <Typography variant="h6" gutterBottom>
                Question Content:
              </Typography>
              <TextField
                fullWidth
                multiline
                rows={4}
                value={editingFlag.question?.content || ''}
                onChange={(e) => {
                  if (editingFlag.question) {
                    setEditingFlag({
                      ...editingFlag,
                      question: {
                        ...editingFlag.question,
                        content: e.target.value
                      }
                    });
                  }
                }}
                sx={{ mb: 2 }}
                helperText="Edit the question content. LaTeX math expressions are supported."
              />

              {/* LaTeX Preview */}
              {editingFlag.question?.content && (
                <Box sx={{ mb: 3, p: 2, bgcolor: 'grey.50', borderRadius: 1 }}>
                  <Typography variant="subtitle2" gutterBottom color="text.secondary">
                    Preview (with LaTeX rendering):
                  </Typography>
                  <MathMarkdown>{editingFlag.question.content}</MathMarkdown>
                </Box>
              )}

              <Divider sx={{ my: 3 }} />

              {/* Question Options */}
              <Typography variant="h6" gutterBottom>
                Answer Options:
              </Typography>
              <Grid container spacing={2}>
                {['A', 'B', 'C', 'D'].map((option) => (
                  <Grid item xs={12} sm={6} key={option}>
                    <TextField
                      fullWidth
                      label={`Option ${option}`}
                      multiline
                      rows={2}
                      value={editingFlag.question?.options?.[option] || ''}
                      onChange={(e) => {
                        if (editingFlag.question) {
                          setEditingFlag({
                            ...editingFlag,
                            question: {
                              ...editingFlag.question,
                              options: {
                                ...editingFlag.question.options,
                                [option]: e.target.value
                              }
                            }
                          });
                        }
                      }}
                      helperText="LaTeX math expressions supported"
                    />
                  </Grid>
                ))}
              </Grid>

              {/* Options Preview */}
              {editingFlag.question?.options && Object.keys(editingFlag.question.options).length > 0 && (
                <Box sx={{ mt: 2, mb: 3, p: 2, bgcolor: 'grey.50', borderRadius: 1 }}>
                  <Typography variant="subtitle2" gutterBottom color="text.secondary">
                    Options Preview (with LaTeX rendering):
                  </Typography>
                  <Grid container spacing={1}>
                    {Object.entries(editingFlag.question.options).map(([key, value]) => (
                      <Grid item xs={12} sm={6} key={key}>
                        <Box sx={{ p: 1, border: '1px solid', borderColor: 'grey.300', borderRadius: 1 }}>
                          <Typography variant="subtitle2" color="primary">
                            {key}:
                          </Typography>
                          <MathMarkdown>{value}</MathMarkdown>
                        </Box>
                      </Grid>
                    ))}
                  </Grid>
                </Box>
              )}

              <Divider sx={{ my: 3 }} />

              {/* Correct Answer */}
              <Typography variant="h6" gutterBottom>
                Correct Answer:
              </Typography>
              <FormControl fullWidth sx={{ mb: 3 }}>
                <InputLabel>Correct Answer</InputLabel>
                <Select
                  value={editingFlag.question?.answer || ''}
                  label="Correct Answer"
                  onChange={(e) => {
                    if (editingFlag.question) {
                      setEditingFlag({
                        ...editingFlag,
                        question: {
                          ...editingFlag.question,
                          answer: e.target.value
                        }
                      });
                    }
                  }}
                >
                  {['A', 'B', 'C', 'D'].map((option) => (
                    <MenuItem key={option} value={option}>
                      Option {option}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>

              <Divider sx={{ my: 3 }} />

              {/* Explanation */}
              <Typography variant="h6" gutterBottom>
                Explanation:
              </Typography>
              <TextField
                fullWidth
                multiline
                rows={4}
                value={editingFlag.question?.explanation || ''}
                onChange={(e) => {
                  if (editingFlag.question) {
                    setEditingFlag({
                      ...editingFlag,
                      question: {
                        ...editingFlag.question,
                        explanation: e.target.value
                      }
                    });
                  }
                }}
                sx={{ mb: 2 }}
                helperText="Provide a detailed explanation for the correct answer. LaTeX math expressions are supported."
              />

              {/* Explanation Preview */}
              {editingFlag.question?.explanation && (
                <Box sx={{ mb: 3, p: 2, bgcolor: 'grey.50', borderRadius: 1 }}>
                  <Typography variant="subtitle2" gutterBottom color="text.secondary">
                    Explanation Preview (with LaTeX rendering):
                  </Typography>
                  <MathMarkdown>{editingFlag.question.explanation}</MathMarkdown>
                </Box>
              )}

              <Divider sx={{ my: 3 }} />

              <Typography variant="h6" gutterBottom>
                Flag Information:
              </Typography>
              <Grid container spacing={2}>
                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle2">Student:</Typography>
                  <Typography variant="body2">
                    {editingFlag.student?.full_name} ({editingFlag.student?.email})
                  </Typography>
                </Grid>
                <Grid item xs={12} sm={6}>
                  <Typography variant="subtitle2">Reasons:</Typography>
                  <Box sx={{ display: 'flex', flexWrap: 'wrap', gap: 0.5 }}>
                    {getReasonLabels(editingFlag.reasons).map((reason, index) => (
                      <Chip key={index} label={reason} size="small" />
                    ))}
                  </Box>
                </Grid>
                <Grid item xs={12}>
                  <Typography variant="subtitle2">Description:</Typography>
                  <Typography variant="body2">
                    {editingFlag.description || 'No description provided'}
                  </Typography>
                </Grid>
              </Grid>

              <Divider sx={{ my: 3 }} />

              <Typography variant="h6" gutterBottom>
                Admin Notes:
              </Typography>
              <TextField
                fullWidth
                multiline
                rows={3}
                value={editingFlag.admin_notes || ''}
                onChange={(e) => {
                  setEditingFlag({
                    ...editingFlag,
                    admin_notes: e.target.value
                  });
                }}
                placeholder="Add admin notes about this flag..."
              />
            </Box>
          )}
        </DialogContent>
        <DialogActions>
          <Button onClick={() => setShowEditModal(false)}>
            Cancel
          </Button>
          <Button
            variant="contained"
            onClick={async () => {
              if (!editingFlag) return;

              try {
                // Update question with all fields
                if (editingFlag.question) {
                  const questionData: any = {};

                  if (editingFlag.question.content) {
                    questionData.content = editingFlag.question.content;
                  }

                  if (editingFlag.question.options) {
                    questionData.options = editingFlag.question.options;
                  }

                  if (editingFlag.question.answer) {
                    questionData.answer = editingFlag.question.answer;
                  }

                  if (editingFlag.question.explanation) {
                    questionData.explanation = editingFlag.question.explanation;
                  }

                  // Only update if there are changes
                  if (Object.keys(questionData).length > 0) {
                    await updateQuestionMutation.mutateAsync({
                      questionId: editingFlag.question_id,
                      questionData: questionData
                    });
                  }
                }

                // Update flag notes if they have changed
                if (editingFlag.admin_notes !== undefined) {
                  await updateFlagNotesMutation.mutateAsync({
                    flagId: editingFlag.id,
                    adminNotes: editingFlag.admin_notes
                  });
                }

                setShowEditModal(false);
                setEditingFlag(null);
              } catch (error) {
                // Error handling is done in the mutations
                console.error('Error saving changes:', error);
              }
            }}
            disabled={updateQuestionMutation.isPending || updateFlagNotesMutation.isPending}
          >
            {updateQuestionMutation.isPending || updateFlagNotesMutation.isPending ? 'Saving...' : 'Save Changes'}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default FlaggedQuestions;
