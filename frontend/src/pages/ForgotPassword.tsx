import React, { useState } from 'react';
import { Link as RouterLink, useNavigate } from 'react-router-dom';
import {
  Box,
  Card,
  CardContent,
  TextField,
  Button,
  Typography,
  Alert,
  CircularProgress,
  Link,
  Grid,
  useTheme,
} from '@mui/material';
import { motion } from 'framer-motion';
import { forgotPassword } from '../api/auth';
import AnimatedBackground from '../components/AnimatedBackground';
import EducationalIllustration from '../components/EducationalIllustration';
import { useEmailResendCountdown } from '../hooks/useEmailResendCountdown';

// Create motion components
const MotionBox = motion.create(Box);
const MotionCard = motion.create(Card);

const ForgotPassword: React.FC = () => {
  const [email, setEmail] = useState('');
  const [loading, setLoading] = useState(false);
  const [message, setMessage] = useState('');
  const [isSuccess, setIsSuccess] = useState(false);
  const theme = useTheme();
  const navigate = useNavigate();

  // Use countdown hook for password reset requests
  const { canResend, remainingTime, startCountdown } = useEmailResendCountdown({
    email: email || undefined,
    cooldownDuration: 60 // 60 seconds cooldown
  });

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!email.trim()) {
      setMessage('Please enter your email address');
      setIsSuccess(false);
      return;
    }

    if (!canResend) {
      setMessage(`Please wait ${remainingTime} seconds before requesting another reset link.`);
      setIsSuccess(false);
      return;
    }

    setLoading(true);
    setMessage('');

    try {
      const response = await forgotPassword({ email: email.trim() });
      setMessage(response.message);
      setIsSuccess(response.success);
      // Start countdown after successful email send
      if (response.success) {
        startCountdown();
      }
    } catch (error: any) {
      console.error('Forgot password error:', error);
      if (error.response?.data?.detail) {
        setMessage(error.response.data.detail);
      } else {
        setMessage('An error occurred. Please try again.');
      }
      setIsSuccess(false);
    } finally {
      setLoading(false);
    }
  };

  return (
    <AnimatedBackground>
      <Grid container sx={{ minHeight: '100vh' }}>
        {/* Left side - Forgot Password Form */}
        <Grid
          item
          xs={12}
          md={6}
          sx={{
            display: 'flex',
            alignItems: 'center',
            justifyContent: 'center',
            p: 4,
          }}
        >
          <MotionCard
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
            sx={{
              maxWidth: 400,
              width: '100%',
              boxShadow: theme.palette.mode === 'light'
                ? '0 10px 40px rgba(0, 0, 0, 0.1)'
                : '0 10px 40px rgba(0, 0, 0, 0.3)',
              borderRadius: 3,
              overflow: 'hidden',
              backdropFilter: 'blur(10px)',
              backgroundColor: theme.palette.mode === 'light'
                ? 'rgba(255, 255, 255, 0.9)'
                : 'rgba(18, 18, 18, 0.9)',
            }}
          >
            <CardContent sx={{ p: 4 }}>
              <MotionBox
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.5, delay: 0.2 }}
                sx={{ textAlign: 'center', mb: 4 }}
              >
                <Typography
                  variant="h4"
                  component="h1"
                  sx={{
                    fontWeight: 'bold',
                    background: `linear-gradient(45deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
                    backgroundClip: 'text',
                    WebkitBackgroundClip: 'text',
                    WebkitTextFillColor: 'transparent',
                    mb: 1,
                  }}
                >
                  Forgot Password?
                </Typography>
                <Typography variant="body2" color="text.secondary">
                  Enter your email address and we'll send you a link to reset your password.
                </Typography>
              </MotionBox>

              <form onSubmit={handleSubmit}>
                <MotionBox
                  initial={{ opacity: 0, x: -20 }}
                  animate={{ opacity: 1, x: 0 }}
                  transition={{ duration: 0.5, delay: 0.3 }}
                  sx={{ mb: 3 }}
                >
                  <TextField
                    fullWidth
                    id="email"
                    name="email"
                    label="Email Address"
                    type="email"
                    value={email}
                    onChange={(e) => setEmail(e.target.value)}
                    disabled={loading}
                    required
                    autoComplete="email"
                    autoFocus
                    sx={{
                      '& .MuiOutlinedInput-root': {
                        borderRadius: 2,
                        transition: 'all 0.2s',
                        '&:hover': {
                          boxShadow: '0 4px 12px rgba(0, 0, 0, 0.1)',
                        },
                        '&.Mui-focused': {
                          boxShadow: '0 4px 12px rgba(25, 118, 210, 0.2)',
                        },
                      },
                    }}
                  />
                </MotionBox>

                {message && (
                  <MotionBox
                    initial={{ opacity: 0, scale: 0.95 }}
                    animate={{ opacity: 1, scale: 1 }}
                    transition={{ duration: 0.3 }}
                    sx={{ mb: 3 }}
                  >
                    <Alert
                      severity={isSuccess ? 'success' : 'error'}
                      sx={{
                        borderRadius: 2,
                        width: '100%',
                        maxWidth: '100%',
                        minWidth: 0,
                        wordBreak: 'break-word',
                        whiteSpace: 'pre-wrap',
                        overflow: 'hidden',
                        '& .MuiAlert-message': {
                          width: '100%',
                          overflow: 'hidden',
                          textOverflow: 'ellipsis'
                        }
                      }}
                    >
                      {message}
                    </Alert>
                  </MotionBox>
                )}

                <MotionBox
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 0.4 }}
                  sx={{ mb: 3 }}
                >
                  <Button
                    type="submit"
                    fullWidth
                    variant="contained"
                    disabled={loading || !canResend}
                    sx={{
                      py: 1.5,
                      borderRadius: 2,
                      textTransform: 'none',
                      fontSize: '1rem',
                      fontWeight: 'bold',
                      background: `linear-gradient(45deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
                      boxShadow: '0 4px 15px rgba(0, 0, 0, 0.2)',
                      transition: 'all 0.3s',
                      '&:hover': {
                        transform: 'translateY(-2px)',
                        boxShadow: '0 6px 20px rgba(0, 0, 0, 0.3)',
                      },
                      '&:disabled': {
                        background: theme.palette.action.disabledBackground,
                        color: theme.palette.action.disabled,
                      },
                    }}
                  >
                    {loading ? (
                      <CircularProgress size={24} color="inherit" />
                    ) : !canResend ? (
                      `Send Reset Link (${remainingTime}s)`
                    ) : (
                      'Send Reset Link'
                    )}
                  </Button>
                </MotionBox>

                <MotionBox
                  initial={{ opacity: 0 }}
                  animate={{ opacity: 1 }}
                  transition={{ duration: 0.5, delay: 0.5 }}
                  sx={{ textAlign: 'center' }}
                >
                  <Typography variant="body2" color="text.secondary">
                    <Link
                      component={RouterLink}
                      to="/login"
                      sx={{
                        fontWeight: 'bold',
                        color: 'primary.main',
                        textDecoration: 'none',
                        transition: 'all 0.2s',
                        '&:hover': {
                          color: 'secondary.main',
                          textDecoration: 'underline'
                        }
                      }}
                    >
                      Back to Login
                    </Link>
                  </Typography>
                </MotionBox>
              </form>
            </CardContent>
          </MotionCard>
        </Grid>

        {/* Right side - Educational Illustration */}
        <Grid
          item
          md={6}
          sx={{
            display: { xs: 'none', md: 'block' },
            bgcolor: theme.palette.mode === 'light'
              ? 'rgba(0, 0, 0, 0.02)'
              : 'rgba(255, 255, 255, 0.02)',
          }}
        >
          <EducationalIllustration
            title="Reset Your Password"
            subtitle="Secure your account with a new password and continue your learning journey"
          />
        </Grid>
      </Grid>
    </AnimatedBackground>
  );
};

export default ForgotPassword;
