import React from 'react';
import {
  Box,
  Container,
  Typography,
  Paper,
  Divider,
  useTheme,
  Breadcrumbs,
  Link
} from '@mui/material';
import { Link as RouterLink } from 'react-router-dom';
import { motion } from 'framer-motion';

const MotionPaper = motion(Paper);

const TermsAndConditions: React.FC = () => {
  const theme = useTheme();

  const sections = [
    {
      title: "1. Acceptance of Terms",
      content: `By accessing and using CampusPQ ("the Platform"), you accept and agree to be bound by the terms and provision of this agreement. If you do not agree to abide by the above, please do not use this service.`
    },
    {
      title: "2. Platform Description",
      content: `CampusPQ is an educational platform that provides students with access to practice questions, flashcards, study materials, tutoring services, and other educational resources. The platform facilitates learning through various interactive tools and connects students with qualified tutors.`
    },
    {
      title: "3. User Accounts and Registration",
      content: `To access certain features of the Platform, you must register for an account. You agree to provide accurate, current, and complete information during registration and to update such information to keep it accurate, current, and complete. You are responsible for safeguarding your password and all activities under your account.`
    },
    {
      title: "4. User Responsibilities",
      content: `Users must: (a) Use the Platform only for lawful educational purposes; (b) Not share account credentials with others; (c) Not upload malicious content or attempt to harm the Platform; (d) Respect intellectual property rights; (e) Maintain appropriate conduct in all interactions; (f) Not engage in academic dishonesty or cheating.`
    },
    {
      title: "5. Content and Intellectual Property",
      content: `All content on the Platform, including but not limited to questions, explanations, study materials, and software, is owned by CampusPQ or its licensors and is protected by copyright and other intellectual property laws. Users may not reproduce, distribute, or create derivative works without explicit permission.`
    },
    {
      title: "6. User-Generated Content",
      content: `Users may upload study materials, ask questions, and contribute content. By uploading content, you grant CampusPQ a non-exclusive, worldwide, royalty-free license to use, modify, and distribute such content for educational purposes on the Platform. You represent that you own or have permission to upload such content.`
    },
    {
      title: "7. Privacy and Data Protection",
      content: `Your privacy is important to us. Our collection and use of personal information is governed by our Privacy Policy, which is incorporated into these Terms by reference. We implement appropriate security measures to protect your personal information.`
    },
    {
      title: "8. Payment and Subscriptions",
      content: `Certain features require payment. Subscription fees are charged in advance and are non-refundable except as required by law. We reserve the right to change pricing with reasonable notice. Payment processing is handled by secure third-party providers.`
    },
    {
      title: "9. Tutoring Services",
      content: `CampusPQ facilitates connections between students and independent tutors. Tutors are independent contractors, not employees of CampusPQ. We do not guarantee the quality of tutoring services and are not liable for disputes between students and tutors.`
    },
    {
      title: "10. Prohibited Uses",
      content: `You may not: (a) Use the Platform for any unlawful purpose; (b) Attempt to gain unauthorized access to any part of the Platform; (c) Upload viruses or malicious code; (d) Harass or harm other users; (e) Violate any applicable laws or regulations; (f) Use automated systems to access the Platform without permission.`
    }
  ];

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      {/* Breadcrumbs */}
      <Breadcrumbs sx={{ mb: 3 }}>
        <Link component={RouterLink} to="/" color="inherit">
          Home
        </Link>
        <Typography color="text.primary">Terms and Conditions</Typography>
      </Breadcrumbs>

      <MotionPaper
        elevation={2}
        sx={{ p: 4 }}
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        {/* Header */}
        <Box sx={{ textAlign: 'center', mb: 4 }}>
          <Typography variant="h3" component="h1" gutterBottom>
            Terms and Conditions
          </Typography>
          <Typography variant="subtitle1" color="text.secondary">
            Last updated: {new Date().toLocaleDateString()}
          </Typography>
        </Box>

        <Divider sx={{ mb: 4 }} />

        {/* Introduction */}
        <Typography variant="body1" paragraph sx={{ mb: 4 }}>
          Welcome to CampusPQ. These Terms and Conditions ("Terms") govern your use of our educational platform 
          and services. Please read these Terms carefully before using our Platform.
        </Typography>

        {/* Terms Sections */}
        {sections.map((section, index) => (
          <Box key={index} sx={{ mb: 4 }}>
            <Typography variant="h5" component="h2" gutterBottom sx={{ color: theme.palette.primary.main }}>
              {section.title}
            </Typography>
            <Typography variant="body1" paragraph sx={{ textAlign: 'justify', lineHeight: 1.7 }}>
              {section.content}
            </Typography>
            {index < sections.length - 1 && <Divider sx={{ mt: 3 }} />}
          </Box>
        ))}

        <Divider sx={{ my: 4 }} />

        {/* Additional Important Sections */}
        <Box sx={{ mb: 4 }}>
          <Typography variant="h5" component="h2" gutterBottom sx={{ color: theme.palette.primary.main }}>
            11. Limitation of Liability
          </Typography>
          <Typography variant="body1" paragraph sx={{ textAlign: 'justify', lineHeight: 1.7 }}>
            CampusPQ shall not be liable for any indirect, incidental, special, consequential, or punitive damages, 
            including without limitation, loss of profits, data, use, goodwill, or other intangible losses, resulting 
            from your use of the Platform.
          </Typography>
        </Box>

        <Divider sx={{ my: 3 }} />

        <Box sx={{ mb: 4 }}>
          <Typography variant="h5" component="h2" gutterBottom sx={{ color: theme.palette.primary.main }}>
            12. Termination
          </Typography>
          <Typography variant="body1" paragraph sx={{ textAlign: 'justify', lineHeight: 1.7 }}>
            We may terminate or suspend your account and access to the Platform immediately, without prior notice, 
            for any reason, including if you breach these Terms. Upon termination, your right to use the Platform 
            will cease immediately.
          </Typography>
        </Box>

        <Divider sx={{ my: 3 }} />

        <Box sx={{ mb: 4 }}>
          <Typography variant="h5" component="h2" gutterBottom sx={{ color: theme.palette.primary.main }}>
            13. Changes to Terms
          </Typography>
          <Typography variant="body1" paragraph sx={{ textAlign: 'justify', lineHeight: 1.7 }}>
            We reserve the right to modify these Terms at any time. We will notify users of any material changes 
            via email or through the Platform. Your continued use of the Platform after such modifications constitutes 
            acceptance of the updated Terms.
          </Typography>
        </Box>

        <Divider sx={{ my: 3 }} />

        <Box sx={{ mb: 4 }}>
          <Typography variant="h5" component="h2" gutterBottom sx={{ color: theme.palette.primary.main }}>
            14. Contact Information
          </Typography>
          <Typography variant="body1" paragraph sx={{ textAlign: 'justify', lineHeight: 1.7 }}>
            If you have any questions about these Terms and Conditions, please contact us at:
          </Typography>
          <Typography variant="body1" sx={{ ml: 2 }}>
            Email: <EMAIL><br />
            Address: CampusPQ Legal Department<br />
            Phone: +****************
          </Typography>
        </Box>

        {/* Footer */}
        <Box sx={{ textAlign: 'center', mt: 6, p: 3, bgcolor: theme.palette.grey[50], borderRadius: 2 }}>
          <Typography variant="body2" color="text.secondary">
            By using CampusPQ, you acknowledge that you have read, understood, and agree to be bound by these Terms and Conditions.
          </Typography>
        </Box>
      </MotionPaper>
    </Container>
  );
};

export default TermsAndConditions;
