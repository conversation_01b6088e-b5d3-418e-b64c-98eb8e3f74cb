import React from 'react';
import {
  Box,
  Container,
  Typography,
  Paper,
  Divider,
  useTheme,
  Breadcrumbs,
  Link
} from '@mui/material';
import { Link as RouterLink } from 'react-router-dom';
import { motion } from 'framer-motion';

const MotionPaper = motion(Paper);

const PrivacyPolicy: React.FC = () => {
  const theme = useTheme();

  const sections = [
    {
      title: "1. Information We Collect",
      content: `We collect information you provide directly to us, such as when you create an account, upload study materials, or contact us. This includes your name, email address, educational information, and any content you upload to the Platform.`
    },
    {
      title: "2. How We Use Your Information",
      content: `We use the information we collect to: (a) Provide and maintain our services; (b) Process transactions and send related information; (c) Send technical notices and support messages; (d) Communicate with you about products, services, and events; (e) Monitor and analyze trends and usage.`
    },
    {
      title: "3. Information Sharing",
      content: `We do not sell, trade, or otherwise transfer your personal information to third parties without your consent, except as described in this policy. We may share information with service providers who assist us in operating our Platform and conducting our business.`
    },
    {
      title: "4. Data Security",
      content: `We implement appropriate security measures to protect your personal information against unauthorized access, alteration, disclosure, or destruction. However, no method of transmission over the Internet is 100% secure.`
    },
    {
      title: "5. Cookies and Tracking",
      content: `We use cookies and similar tracking technologies to track activity on our Platform and hold certain information. You can instruct your browser to refuse all cookies or to indicate when a cookie is being sent.`
    },
    {
      title: "6. Data Retention",
      content: `We retain your personal information for as long as necessary to provide our services and fulfill the purposes outlined in this policy, unless a longer retention period is required by law.`
    },
    {
      title: "7. Your Rights",
      content: `You have the right to access, update, or delete your personal information. You may also have the right to restrict or object to certain processing of your data. Contact us to exercise these rights.`
    },
    {
      title: "8. Children's Privacy",
      content: `Our Platform is not intended for children under 13. We do not knowingly collect personal information from children under 13. If we become aware that we have collected such information, we will take steps to delete it.`
    }
  ];

  return (
    <Container maxWidth="lg" sx={{ py: 4 }}>
      {/* Breadcrumbs */}
      <Breadcrumbs sx={{ mb: 3 }}>
        <Link component={RouterLink} to="/" color="inherit">
          Home
        </Link>
        <Typography color="text.primary">Privacy Policy</Typography>
      </Breadcrumbs>

      <MotionPaper
        elevation={2}
        sx={{ p: 4 }}
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.5 }}
      >
        {/* Header */}
        <Box sx={{ textAlign: 'center', mb: 4 }}>
          <Typography variant="h3" component="h1" gutterBottom>
            Privacy Policy
          </Typography>
          <Typography variant="subtitle1" color="text.secondary">
            Last updated: {new Date().toLocaleDateString()}
          </Typography>
        </Box>

        <Divider sx={{ mb: 4 }} />

        {/* Introduction */}
        <Typography variant="body1" paragraph sx={{ mb: 4 }}>
          At CampusPQ, we are committed to protecting your privacy and ensuring the security of your personal 
          information. This Privacy Policy explains how we collect, use, and safeguard your information when 
          you use our educational platform.
        </Typography>

        {/* Privacy Sections */}
        {sections.map((section, index) => (
          <Box key={index} sx={{ mb: 4 }}>
            <Typography variant="h5" component="h2" gutterBottom sx={{ color: theme.palette.primary.main }}>
              {section.title}
            </Typography>
            <Typography variant="body1" paragraph sx={{ textAlign: 'justify', lineHeight: 1.7 }}>
              {section.content}
            </Typography>
            {index < sections.length - 1 && <Divider sx={{ mt: 3 }} />}
          </Box>
        ))}

        <Divider sx={{ my: 4 }} />

        {/* Additional Sections */}
        <Box sx={{ mb: 4 }}>
          <Typography variant="h5" component="h2" gutterBottom sx={{ color: theme.palette.primary.main }}>
            9. Third-Party Services
          </Typography>
          <Typography variant="body1" paragraph sx={{ textAlign: 'justify', lineHeight: 1.7 }}>
            Our Platform may contain links to third-party websites or services. We are not responsible for the 
            privacy practices of these third parties. We encourage you to read their privacy policies before 
            providing any personal information.
          </Typography>
        </Box>

        <Divider sx={{ my: 3 }} />

        <Box sx={{ mb: 4 }}>
          <Typography variant="h5" component="h2" gutterBottom sx={{ color: theme.palette.primary.main }}>
            10. International Data Transfers
          </Typography>
          <Typography variant="body1" paragraph sx={{ textAlign: 'justify', lineHeight: 1.7 }}>
            Your information may be transferred to and processed in countries other than your own. We ensure 
            appropriate safeguards are in place to protect your personal information in accordance with this 
            Privacy Policy.
          </Typography>
        </Box>

        <Divider sx={{ my: 3 }} />

        <Box sx={{ mb: 4 }}>
          <Typography variant="h5" component="h2" gutterBottom sx={{ color: theme.palette.primary.main }}>
            11. Changes to This Policy
          </Typography>
          <Typography variant="body1" paragraph sx={{ textAlign: 'justify', lineHeight: 1.7 }}>
            We may update this Privacy Policy from time to time. We will notify you of any material changes by 
            posting the new Privacy Policy on this page and updating the "Last updated" date.
          </Typography>
        </Box>

        <Divider sx={{ my: 3 }} />

        <Box sx={{ mb: 4 }}>
          <Typography variant="h5" component="h2" gutterBottom sx={{ color: theme.palette.primary.main }}>
            12. Contact Us
          </Typography>
          <Typography variant="body1" paragraph sx={{ textAlign: 'justify', lineHeight: 1.7 }}>
            If you have any questions about this Privacy Policy, please contact us at:
          </Typography>
          <Typography variant="body1" sx={{ ml: 2 }}>
            Email: <EMAIL><br />
            Address: CampusPQ Privacy Department<br />
            Phone: +****************
          </Typography>
        </Box>

        {/* Footer */}
        <Box sx={{ textAlign: 'center', mt: 6, p: 3, bgcolor: theme.palette.grey[50], borderRadius: 2 }}>
          <Typography variant="body2" color="text.secondary">
            Your privacy is important to us. We are committed to protecting your personal information and being 
            transparent about our data practices.
          </Typography>
        </Box>
      </MotionPaper>
    </Container>
  );
};

export default PrivacyPolicy;
