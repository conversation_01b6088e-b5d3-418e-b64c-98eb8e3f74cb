import React, { useState, useEffect } from 'react';
import {
  Box,
  Typography,
  Paper,
  Button,
  IconButton,
  LinearProgress,
  Chip,
  Card,
  CardContent,
  useTheme,
  useMediaQuery,
  Alert,
  CircularProgress,
  Tooltip,
  Switch,
  FormControlLabel,
  Stack
} from '@mui/material';
import {
  ArrowBack as ArrowBackIcon,
  ArrowForward as ArrowForwardIcon,
  Shuffle as ShuffleIcon,
  Refresh as RefreshIcon,
  Home as HomeIcon,
  TouchApp as TouchIcon,
  Topic as TopicIcon,
  PlayArrow as PlayIcon,
  Pause as PauseIcon,
  Speed as SpeedIcon
} from '@mui/icons-material';
import { motion, AnimatePresence } from 'framer-motion';
import { useParams, useNavigate, useLocation } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';

import { getGeneratedFlashcards } from '../../api/studentTools';
import { getPublicAdminFlashCards } from '../../api/adminFlashcards';
import { getFlashcardsForPractice } from '../../api/contentWithSharing';

interface Flashcard {
  id: number;
  front_content: string;
  back_content: string;
  topic?: string;
  difficulty?: string;
  card_type?: string;
  course_name?: string;
}

const FlashcardPractice: React.FC = () => {
  const theme = useTheme();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const navigate = useNavigate();
  const location = useLocation();
  const { courseId } = useParams<{ courseId: string }>();

  // State
  const [flashcards, setFlashcards] = useState<Flashcard[]>([]);
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isFlipped, setIsFlipped] = useState(false);
  const [isShuffled, setIsShuffled] = useState(false);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [autoTransition, setAutoTransition] = useState(false);
  const [autoTransitionSpeed, setAutoTransitionSpeed] = useState(4000); // 4 seconds to show question
  const [autoAnswerSpeed, setAutoAnswerSpeed] = useState(3000); // 3 seconds to show answer
  const [isPlaying, setIsPlaying] = useState(false);

  // Determine if this is generated flashcards or course flashcards
  const isGeneratedFlashcards = courseId === 'generated';
  const courseName = sessionStorage.getItem('practiceCourseName') || 'Flashcards';

  // Fetch flashcards
  useEffect(() => {
    const fetchFlashcards = async () => {
      try {
        setLoading(true);
        setError(null);

        if (isGeneratedFlashcards) {
          // Get generated flashcards from session storage IDs
          const flashcardIds = sessionStorage.getItem('practiceFlashcardIds');

          if (flashcardIds) {
            const ids = JSON.parse(flashcardIds);
            // Use the new sharing-aware function for practice mode
            const data = await getFlashcardsForPractice(ids);
            setFlashcards(data);
          } else {
            // Get all generated flashcards for the user
            const data = await getGeneratedFlashcards();
            setFlashcards(data);
          }
        } else {
          // Get admin flashcards for the course
          const data = await getPublicAdminFlashCards({
            course_id: courseId ? parseInt(courseId) : undefined,
            limit: 100
          });
          // Convert admin flashcards to our format
          const convertedFlashcards = data.map(card => ({
            id: card.id,
            front_content: card.front_content,
            back_content: card.back_content,
            topic: card.topic,
            difficulty: card.difficulty,
            course_name: card.course_name
          }));
          setFlashcards(convertedFlashcards);
        }
      } catch (err: any) {
        console.error('Error fetching flashcards:', err);
        setError(`Failed to load flashcards: ${err.response?.data?.detail || err.message || 'Unknown error'}`);
      } finally {
        setLoading(false);
      }
    };

    fetchFlashcards();
  }, [courseId, isGeneratedFlashcards]);

  // Auto-transition effect with proper question/answer flow
  useEffect(() => {
    let timeout: NodeJS.Timeout;

    if (autoTransition && isPlaying) {
      if (!isFlipped) {
        // Show question for autoTransitionSpeed, then flip to answer
        timeout = setTimeout(() => {
          setIsFlipped(true);
        }, autoTransitionSpeed);
      } else {
        // Show answer for autoAnswerSpeed, then move to next card
        timeout = setTimeout(() => {
          if (currentIndex < flashcards.length - 1) {
            setCurrentIndex(prev => prev + 1);
            setIsFlipped(false); // Reset to question for next card
          } else {
            setIsPlaying(false); // Stop at the end
          }
        }, autoAnswerSpeed);
      }
    }

    return () => {
      if (timeout) clearTimeout(timeout);
    };
  }, [autoTransition, isPlaying, isFlipped, currentIndex, flashcards.length, autoTransitionSpeed, autoAnswerSpeed]);

  const currentFlashcard = flashcards[currentIndex];

  const handleFlip = () => {
    setIsFlipped(!isFlipped);
  };

  const handleNext = () => {
    if (currentIndex < flashcards.length - 1) {
      setCurrentIndex(currentIndex + 1);
      setIsFlipped(false);
    }
  };

  const handlePrevious = () => {
    if (currentIndex > 0) {
      setCurrentIndex(currentIndex - 1);
      setIsFlipped(false);
    }
  };

  const handleShuffle = () => {
    const shuffled = [...flashcards].sort(() => Math.random() - 0.5);
    setFlashcards(shuffled);
    setCurrentIndex(0);
    setIsFlipped(false);
    setIsShuffled(true);
    setIsPlaying(false);
  };

  const handleReset = () => {
    setCurrentIndex(0);
    setIsFlipped(false);
    setIsShuffled(false);
    setIsPlaying(false);
  };

  const toggleAutoTransition = () => {
    setAutoTransition(!autoTransition);
    if (!autoTransition) {
      setIsPlaying(true);
    } else {
      setIsPlaying(false);
    }
  };

  const togglePlayPause = () => {
    setIsPlaying(!isPlaying);
  };

  const getDifficultyColor = (difficulty?: string) => {
    switch (difficulty?.toLowerCase()) {
      case 'easy':
        return theme.palette.success.main;
      case 'medium':
        return theme.palette.warning.main;
      case 'hard':
        return theme.palette.error.main;
      default:
        return theme.palette.primary.main;
    }
  };

  // Generate subtle background colors for cards
  const getCardBackgroundColor = (index: number) => {
    const lightColors = [
      '#f8fafc', // slate-50
      '#f0f9ff', // sky-50
      '#ecfdf5', // emerald-50
      '#fefce8', // yellow-50
      '#fdf2f8', // pink-50
      '#f3e8ff', // violet-50
      '#fff7ed', // orange-50
      '#f0fdfa', // teal-50
    ];

    const darkColors = [
      '#1e293b', // slate-800
      '#0c4a6e', // sky-900
      '#064e3b', // emerald-900
      '#713f12', // yellow-900
      '#831843', // pink-900
      '#4c1d95', // violet-900
      '#9a3412', // orange-900
      '#134e4a', // teal-900
    ];

    const colors = theme.palette.mode === 'dark' ? darkColors : lightColors;
    return colors[index % colors.length];
  };

  // Generate subtle border colors for cards
  const getCardBorderColor = (index: number) => {
    const lightBorders = [
      '#e2e8f0', // slate-200
      '#bae6fd', // sky-200
      '#a7f3d0', // emerald-200
      '#fef08a', // yellow-200
      '#fbcfe8', // pink-200
      '#ddd6fe', // violet-200
      '#fed7aa', // orange-200
      '#99f6e4', // teal-200
    ];

    const darkBorders = [
      '#475569', // slate-600
      '#0284c7', // sky-600
      '#059669', // emerald-600
      '#ca8a04', // yellow-600
      '#db2777', // pink-600
      '#7c3aed', // violet-600
      '#ea580c', // orange-600
      '#0d9488', // teal-600
    ];

    const colors = theme.palette.mode === 'dark' ? darkBorders : lightBorders;
    return colors[index % colors.length];
  };

  // Modern card styling with theme colors
  const cardStyle = {
    width: '100%',
    maxWidth: isMobile ? '100%' : 600,
    height: isMobile ? 280 : 350,
    margin: '0 auto',
    perspective: '1000px',
    cursor: 'pointer',
    px: isMobile ? 2 : 0,
  };

  const cardInnerStyle = {
    position: 'relative',
    width: '100%',
    height: '100%',
    textAlign: 'center',
    transition: 'transform 0.6s ease-in-out',
    transformStyle: 'preserve-3d',
    transform: isFlipped ? 'rotateY(180deg)' : 'rotateY(0deg)',
  };

  const cardFaceStyle = {
    position: 'absolute',
    width: '100%',
    height: '100%',
    backfaceVisibility: 'hidden',
    borderRadius: 3,
    boxShadow: theme.shadows[6],
    display: 'flex',
    flexDirection: 'column',
    justifyContent: 'center',
    alignItems: 'center',
    padding: theme.spacing(isMobile ? 2.5 : 4),
    backgroundColor: getCardBackgroundColor(currentIndex),
    border: `2px solid ${getCardBorderColor(currentIndex)}`,
    color: theme.palette.text.primary,
    transition: 'all 0.3s ease-in-out',
  };

  const cardBackStyle = {
    ...cardFaceStyle,
    transform: 'rotateY(180deg)',
    backgroundColor: theme.palette.mode === 'dark'
      ? getCardBackgroundColor(currentIndex + 1)
      : getCardBackgroundColor(currentIndex + 2),
    border: `2px solid ${getCardBorderColor(currentIndex + 1)}`,
  };

  if (loading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', minHeight: '60vh' }}>
        <CircularProgress size={60} />
      </Box>
    );
  }

  if (error) {
    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="error" sx={{ mb: 2 }}>
          {error}
        </Alert>
        <Button variant="contained" onClick={() => navigate('/mcq')}>
          Back to Dashboard
        </Button>
      </Box>
    );
  }

  if (flashcards.length === 0) {
    const flashcardIds = sessionStorage.getItem('practiceFlashcardIds');
    const courseName = sessionStorage.getItem('practiceCourseName');

    return (
      <Box sx={{ p: 3 }}>
        <Alert severity="info" sx={{ mb: 2 }}>
          {isGeneratedFlashcards ? (
            <>
              No flashcards found for practice.
              {flashcardIds ? (
                <Box sx={{ mt: 1 }}>
                  The flashcards you selected may have been deleted or are no longer available.
                </Box>
              ) : (
                <Box sx={{ mt: 1 }}>
                  No flashcards were selected for practice.
                </Box>
              )}
            </>
          ) : (
            'No flashcards available for this course.'
          )}
        </Alert>
        <Box sx={{ display: 'flex', gap: 2 }}>
          <Button variant="contained" onClick={() => navigate('/mcq')}>
            Back to Dashboard
          </Button>
          {isGeneratedFlashcards && (
            <Button variant="outlined" onClick={() => navigate('/tools/flashcards')}>
              Generate Flashcards
            </Button>
          )}
        </Box>
      </Box>
    );
  }

  return (
    <Box sx={{
      minHeight: '100vh',
      bgcolor: 'background.default',
      display: 'flex',
      flexDirection: 'column',
      p: isMobile ? 0.5 : 2,
      overflow: 'hidden' // Prevent horizontal scroll
    }}>
      {/* Header */}
      <Box sx={{
        display: 'flex',
        alignItems: 'center',
        mb: isMobile ? 1.5 : 2,
        px: isMobile ? 1 : 0,
        py: isMobile ? 0.5 : 0
      }}>
        <IconButton
          onClick={() => navigate('/tools/flashcards')}
          sx={{ mr: isMobile ? 1 : 2 }}
          size={isMobile ? 'small' : 'medium'}
        >
          <ArrowBackIcon />
        </IconButton>
        <Typography
          variant={isMobile ? 'subtitle1' : 'h5'}
          sx={{
            flexGrow: 1,
            fontWeight: 600,
            fontSize: isMobile ? '1.1rem' : undefined,
            lineHeight: isMobile ? 1.2 : undefined,
            overflow: 'hidden',
            textOverflow: 'ellipsis',
            whiteSpace: 'nowrap'
          }}
        >
          {courseName}
        </Typography>

      </Box>

      {/* Progress */}
      <Box sx={{ mb: isMobile ? 2 : 3, px: isMobile ? 1 : 0 }}>
        <Box sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          mb: isMobile ? 0.5 : 1
        }}>
          <Typography
            variant={isMobile ? 'caption' : 'body2'}
            color="text.secondary"
            fontWeight={500}
            sx={{ fontSize: isMobile ? '0.75rem' : undefined }}
          >
            {currentIndex + 1} / {flashcards.length}
          </Typography>
          <Typography
            variant={isMobile ? 'caption' : 'body2'}
            color="text.secondary"
            fontWeight={500}
            sx={{ fontSize: isMobile ? '0.75rem' : undefined }}
          >
            {Math.round(((currentIndex + 1) / flashcards.length) * 100)}%
          </Typography>
        </Box>
        <LinearProgress
          variant="determinate"
          value={((currentIndex + 1) / flashcards.length) * 100}
          sx={{
            height: isMobile ? 4 : 6,
            borderRadius: 3,
            bgcolor: theme.palette.mode === 'dark' ? 'grey.800' : 'grey.200'
          }}
        />
      </Box>

      {/* Auto-transition Controls */}
      <Box sx={{
        display: 'flex',
        flexDirection: isMobile ? 'column' : 'row',
        justifyContent: 'center',
        alignItems: 'center',
        gap: isMobile ? 1 : 2,
        mb: isMobile ? 1.5 : 2,
        px: isMobile ? 1 : 0
      }}>
        <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <FormControlLabel
            control={
              <Switch
                checked={autoTransition}
                onChange={toggleAutoTransition}
                size={isMobile ? 'small' : 'medium'}
              />
            }
            label={
              <Typography variant="body2" color="text.secondary">
                Auto-play
              </Typography>
            }
          />
          {autoTransition && (
            <IconButton
              onClick={togglePlayPause}
              size="small"
              sx={{
                bgcolor: theme.palette.primary.main,
                color: 'white',
                '&:hover': {
                  bgcolor: theme.palette.primary.dark,
                }
              }}
            >
              {isPlaying ? <PauseIcon fontSize="small" /> : <PlayIcon fontSize="small" />}
            </IconButton>
          )}
        </Box>

        {autoTransition && (
          <Box sx={{
            display: 'flex',
            alignItems: 'center',
            gap: 1,
            px: 2,
            py: 0.5,
            borderRadius: 2,
            bgcolor: theme.palette.mode === 'dark' ? 'grey.800' : 'grey.100'
          }}>
            <SpeedIcon fontSize="small" color="action" />
            <Typography variant="caption" color="text.secondary">
              Speed:
            </Typography>
            <Button
              size="small"
              variant={autoTransitionSpeed === 6000 ? 'contained' : 'text'}
              onClick={() => {
                setAutoTransitionSpeed(6000);
                setAutoAnswerSpeed(4000);
              }}
              sx={{ minWidth: 'auto', px: 1, fontSize: '0.7rem' }}
            >
              Slow
            </Button>
            <Button
              size="small"
              variant={autoTransitionSpeed === 4000 ? 'contained' : 'text'}
              onClick={() => {
                setAutoTransitionSpeed(4000);
                setAutoAnswerSpeed(3000);
              }}
              sx={{ minWidth: 'auto', px: 1, fontSize: '0.7rem' }}
            >
              Normal
            </Button>
            <Button
              size="small"
              variant={autoTransitionSpeed === 2500 ? 'contained' : 'text'}
              onClick={() => {
                setAutoTransitionSpeed(2500);
                setAutoAnswerSpeed(2000);
              }}
              sx={{ minWidth: 'auto', px: 1, fontSize: '0.7rem' }}
            >
              Fast
            </Button>
          </Box>
        )}
      </Box>

      {/* Instructions */}
      <Box sx={{
        mb: 2,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        gap: 1,
        px: isMobile ? 1 : 0
      }}>
        <TouchIcon fontSize="small" color="action" />
        <Typography variant="body2" color="text.secondary" textAlign="center">
          {isFlipped ? 'Showing Answer (Tap to see question)' : 'Showing Question (Tap to see answer)'}
        </Typography>
      </Box>

      {/* Flashcard Container */}
      <Box sx={{
        flex: 1,
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center',
        px: isMobile ? 1 : 2,
        py: 2
      }}>
        <AnimatePresence mode="wait">
          <motion.div
            key={currentIndex}
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            exit={{ opacity: 0, scale: 0.9 }}
            transition={{ duration: 0.4, ease: "easeInOut" }}
            style={{ width: '100%' }}
          >
            <Box sx={cardStyle} onClick={handleFlip}>
              <Box sx={cardInnerStyle}>
                {/* Front of card (Question) */}
                <Box sx={cardFaceStyle}>
                  {currentFlashcard?.topic && (
                    <Chip
                      icon={<TopicIcon fontSize="small" />}
                      label={currentFlashcard.topic}
                      size="small"
                      sx={{
                        mb: 2,
                        bgcolor: theme.palette.primary.main,
                        color: 'white',
                        '& .MuiChip-icon': { color: 'white' }
                      }}
                    />
                  )}

                  <Typography
                    variant={isMobile ? 'h6' : 'h5'}
                    sx={{
                      fontWeight: 600,
                      textAlign: 'center',
                      overflow: 'hidden',
                      display: '-webkit-box',
                      WebkitLineClamp: isMobile ? 4 : 6,
                      WebkitBoxOrient: 'vertical',
                      lineHeight: 1.5,
                      color: theme.palette.text.primary,
                    }}
                  >
                    {currentFlashcard?.front_content}
                  </Typography>
                </Box>

                {/* Back of card (Answer) */}
                <Box sx={cardBackStyle}>
                  {currentFlashcard?.difficulty && (
                    <Chip
                      label={currentFlashcard.difficulty.toUpperCase()}
                      size="small"
                      sx={{
                        mb: 2,
                        bgcolor: getDifficultyColor(currentFlashcard.difficulty),
                        color: 'white',
                        fontWeight: 'bold'
                      }}
                    />
                  )}

                  <Typography
                    variant={isMobile ? 'body1' : 'h6'}
                    sx={{
                      fontWeight: 500,
                      textAlign: 'center',
                      overflow: 'hidden',
                      display: '-webkit-box',
                      WebkitLineClamp: isMobile ? 5 : 7,
                      WebkitBoxOrient: 'vertical',
                      lineHeight: 1.5,
                      color: theme.palette.text.primary,
                    }}
                  >
                    {currentFlashcard?.back_content}
                  </Typography>
                </Box>
              </Box>
            </Box>
          </motion.div>
        </AnimatePresence>
      </Box>

      {/* Mobile-Optimized Navigation Controls */}
      <Box sx={{
        px: isMobile ? 1 : 2,
        pb: isMobile ? 2 : 3
      }}>
        {/* Flip Indicator - Mobile Compact */}
        <Box sx={{
          display: 'flex',
          justifyContent: 'center',
          mb: 2
        }}>
          <Box sx={{
            display: 'flex',
            alignItems: 'center',
            gap: isMobile ? 0.5 : 1,
            px: isMobile ? 1.5 : 2,
            py: isMobile ? 0.5 : 1,
            borderRadius: 2,
            bgcolor: theme.palette.mode === 'dark' ? 'grey.800' : 'grey.100'
          }}>
            <Box
              sx={{
                width: isMobile ? 6 : 8,
                height: isMobile ? 6 : 8,
                borderRadius: '50%',
                bgcolor: !isFlipped ? 'primary.main' : 'grey.400',
                transition: 'background-color 0.3s',
              }}
            />
            <Typography
              variant={isMobile ? 'caption' : 'body2'}
              color="text.secondary"
              sx={{
                mx: isMobile ? 0.5 : 1,
                fontSize: isMobile ? '0.7rem' : '0.875rem'
              }}
            >
              {isFlipped ? 'Answer' : 'Question'}
            </Typography>
            <Box
              sx={{
                width: isMobile ? 6 : 8,
                height: isMobile ? 6 : 8,
                borderRadius: '50%',
                bgcolor: isFlipped ? 'primary.main' : 'grey.400',
                transition: 'background-color 0.3s',
              }}
            />
          </Box>
        </Box>

        {/* Main Navigation - Mobile Optimized */}
        <Box sx={{
          display: 'flex',
          justifyContent: 'space-between',
          alignItems: 'center',
          mb: 2,
          gap: isMobile ? 1 : 2
        }}>
          <Button
            variant="contained"
            onClick={handlePrevious}
            disabled={currentIndex === 0}
            startIcon={!isMobile && <ArrowBackIcon />}
            sx={{
              minWidth: isMobile ? 80 : 120,
              height: isMobile ? 44 : 56,
              borderRadius: 3,
              fontWeight: 600,
              fontSize: isMobile ? '0.8rem' : '0.875rem',
              flex: isMobile ? 1 : 'none',
              maxWidth: isMobile ? '120px' : 'none'
            }}
          >
            {isMobile ? '← Prev' : 'Previous'}
          </Button>

          <Button
            variant="contained"
            onClick={handleNext}
            disabled={currentIndex === flashcards.length - 1}
            endIcon={!isMobile && <ArrowForwardIcon />}
            sx={{
              minWidth: isMobile ? 80 : 120,
              height: isMobile ? 44 : 56,
              borderRadius: 3,
              fontWeight: 600,
              fontSize: isMobile ? '0.8rem' : '0.875rem',
              flex: isMobile ? 1 : 'none',
              maxWidth: isMobile ? '120px' : 'none'
            }}
          >
            {isMobile ? 'Next →' : 'Next'}
          </Button>
        </Box>

        {/* Secondary Controls - Mobile Stacked */}
        <Box sx={{
          display: 'flex',
          flexDirection: isMobile ? 'column' : 'row',
          justifyContent: 'center',
          gap: isMobile ? 1 : 1.5
        }}>
          <Button
            variant="outlined"
            onClick={handleShuffle}
            startIcon={<ShuffleIcon />}
            size={isMobile ? 'small' : 'medium'}
            sx={{
              borderRadius: 2,
              fontSize: isMobile ? '0.8rem' : '0.875rem'
            }}
            fullWidth={isMobile}
          >
            {isMobile ? 'Shuffle Cards' : 'Shuffle'}
          </Button>

          <Button
            variant="outlined"
            onClick={handleReset}
            startIcon={<RefreshIcon />}
            size={isMobile ? 'small' : 'medium'}
            sx={{
              borderRadius: 2,
              fontSize: isMobile ? '0.8rem' : '0.875rem'
            }}
            fullWidth={isMobile}
          >
            {isMobile ? 'Reset Practice' : 'Reset'}
          </Button>
        </Box>
      </Box>
    </Box>
  );
};

export default FlashcardPractice;
