import React, { useState } from 'react';
import {
  Box,
  Typography,
  Paper,
  Button,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  SelectChangeEvent,
  CircularProgress,
  Alert,
  useTheme,
  useMediaQuery,
  Card,
  CardContent,
  CardActions,
  Chip,
  IconButton,
  Tooltip
} from '@mui/material';
import {
  School as SchoolIcon,
  PlayArrow as PlayArrowIcon,
  ArrowBack as ArrowBackIcon,
  Home as HomeIcon,
  Psychology as FlashcardIcon
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';
import { useQuery } from '@tanstack/react-query';
import { motion } from 'framer-motion';

import { useAuth } from '../../contexts/AuthContext';
import { getCourses, Course } from '../../api/courses';
import { getPublicAdminFlashCards, AdminFlashCardWithCourse } from '../../api/adminFlashcards';
import { getDepartments } from '../../api/departments';
import CourseSearchInput from '../../components/MCQ/CourseSearchInput';

// Create motion components
const MotionPaper = motion(Paper);
const MotionCard = motion(Card);

const CourseFlashcardDashboard: React.FC = () => {
  const theme = useTheme();
  const navigate = useNavigate();
  const { user } = useAuth();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const [selectedCourse, setSelectedCourse] = useState<string>('');

  // Fetch courses with enrolled courses prioritized
  const {
    data: courses = [],
    isLoading: isLoadingCourses
  } = useQuery({
    queryKey: ['courses', 'prioritizeEnrolled'],
    queryFn: () => getCourses(undefined, undefined, false, true)
  });

  // Fetch departments for search component
  const {
    data: departments = []
  } = useQuery({
    queryKey: ['departments'],
    queryFn: getDepartments
  });

  // Fetch flashcard counts for each course
  const {
    data: flashcardCounts = {},
    isLoading: isLoadingCounts
  } = useQuery({
    queryKey: ['flashcardCounts'],
    queryFn: async () => {
      const counts: Record<number, number> = {};
      
      // Get flashcard counts for each course
      for (const course of courses) {
        try {
          const flashcards = await getPublicAdminFlashCards({
            course_id: course.id,
            limit: 1000 // Get all to count
          });
          counts[course.id] = flashcards.length;
        } catch (error) {
          console.error(`Error fetching flashcards for course ${course.id}:`, error);
          counts[course.id] = 0;
        }
      }
      
      return counts;
    },
    enabled: courses.length > 0
  });

  // Handle course selection change
  const handleCourseChange = (event: SelectChangeEvent) => {
    setSelectedCourse(event.target.value);
  };

  // Start flashcard practice
  const startPractice = (courseId: number) => {
    navigate(`/flashcards/practice/${courseId}`);
  };

  // Get course name by ID
  const getCourseName = (courseId: number): string => {
    const course = courses.find(c => c.id === courseId);
    return course ? course.name : 'Unknown Course';
  };

  // Animation variants
  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  };

  const itemVariants = {
    hidden: { y: 20, opacity: 0 },
    visible: {
      y: 0,
      opacity: 1,
      transition: {
        type: 'spring',
        stiffness: 100
      }
    }
  };

  // Filter courses that have flashcards
  const coursesWithFlashcards = courses.filter(course => 
    (flashcardCounts[course.id] || 0) > 0
  );

  return (
    <Box sx={{ 
      minHeight: '100vh',
      bgcolor: 'background.default',
      p: isMobile ? 2 : 3
    }}>
      {/* Header */}
      <Box sx={{
        display: 'flex',
        alignItems: 'center',
        mb: 3,
        px: isMobile ? 1 : 0
      }}>
        <IconButton
          onClick={() => navigate('/dashboard')}
          sx={{ mr: 2 }}
          size={isMobile ? 'small' : 'medium'}
        >
          <ArrowBackIcon />
        </IconButton>
        <Box sx={{ flexGrow: 1 }}>
          <Typography
            variant={isMobile ? "h5" : "h4"}
            component="h1"
            fontWeight="bold"
            gutterBottom
          >
            Course Flashcards
          </Typography>
          <Typography
            variant={isMobile ? "body1" : "h6"}
            color="text.secondary"
          >
            Study with interactive flashcards for your courses
          </Typography>
        </Box>
        <IconButton
          onClick={() => navigate('/dashboard')}
          size={isMobile ? 'small' : 'medium'}
        >
          <HomeIcon />
        </IconButton>
      </Box>

      {/* Course Selection */}
      <MotionPaper
        variants={itemVariants}
        initial="hidden"
        animate="visible"
        sx={{
          p: 3,
          borderRadius: 2,
          border: `1px solid ${theme.palette.divider}`,
          mb: 3
        }}
      >
        <Typography variant="h6" fontWeight="bold" gutterBottom sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
          <SchoolIcon color="primary" />
          Select Course
        </Typography>

        {/* Course Search Input */}
        <CourseSearchInput
          courses={coursesWithFlashcards}
          departments={departments}
          onCourseSelect={(courseId) => setSelectedCourse(courseId)}
          placeholder="Search courses with flashcards..."
        />

        {/* Traditional Dropdown (kept as fallback) */}
        <FormControl fullWidth variant="outlined" size={isMobile ? "small" : "medium"} sx={{ mb: 2 }}>
          <InputLabel id="course-select-label">Or select from dropdown</InputLabel>
          <Select
            labelId="course-select-label"
            id="course-select"
            value={selectedCourse}
            onChange={handleCourseChange}
            label="Or select from dropdown"
            disabled={isLoadingCourses || coursesWithFlashcards.length === 0}
          >
            <MenuItem value="">
              <em>Select a course</em>
            </MenuItem>
            {coursesWithFlashcards.map((course) => (
              <MenuItem key={course.id} value={course.id.toString()}>
                <Box sx={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center', width: '100%' }}>
                  <span>{course.name}</span>
                  <Chip
                    label={`${flashcardCounts[course.id] || 0} cards`}
                    size="small"
                    color="primary"
                    variant="outlined"
                  />
                </Box>
              </MenuItem>
            ))}
          </Select>
        </FormControl>

        {selectedCourse && (
          <Button
            variant="contained"
            color="primary"
            fullWidth
            size={isMobile ? "medium" : "large"}
            startIcon={<PlayArrowIcon />}
            onClick={() => startPractice(parseInt(selectedCourse))}
            sx={{ borderRadius: 1.5 }}
          >
            Start Practice
          </Button>
        )}
      </MotionPaper>

      {/* Loading State */}
      {(isLoadingCourses || isLoadingCounts) && (
        <Box sx={{ display: 'flex', justifyContent: 'center', p: 4 }}>
          <CircularProgress />
        </Box>
      )}

      {/* No Courses Available */}
      {!isLoadingCourses && !isLoadingCounts && coursesWithFlashcards.length === 0 && (
        <Alert severity="info" sx={{ mb: 2 }}>
          No courses with flashcards available. Contact your instructor to add flashcards for your courses.
        </Alert>
      )}

      {/* Available Courses Grid */}
      {!isLoadingCourses && !isLoadingCounts && coursesWithFlashcards.length > 0 && (
        <motion.div
          variants={containerVariants}
          initial="hidden"
          animate="visible"
        >
          <Typography variant="h6" fontWeight="bold" gutterBottom sx={{ mb: 2 }}>
            Available Courses ({coursesWithFlashcards.length})
          </Typography>
          
          <Box sx={{
            display: 'grid',
            gridTemplateColumns: {
              xs: '1fr',
              sm: 'repeat(2, 1fr)',
              md: 'repeat(3, 1fr)',
              lg: 'repeat(4, 1fr)'
            },
            gap: 2
          }}>
            {coursesWithFlashcards.map((course) => (
              <MotionCard
                key={course.id}
                variants={itemVariants}
                sx={{
                  borderRadius: 2,
                  border: `1px solid ${theme.palette.divider}`,
                  transition: 'all 0.2s ease-in-out',
                  '&:hover': {
                    transform: 'translateY(-2px)',
                    boxShadow: theme.shadows[4]
                  }
                }}
              >
                <CardContent>
                  <Box sx={{ display: 'flex', alignItems: 'center', mb: 1 }}>
                    <FlashcardIcon color="primary" sx={{ mr: 1 }} />
                    <Typography variant="h6" component="h3" fontWeight="bold" noWrap>
                      {course.name}
                    </Typography>
                  </Box>
                  
                  <Typography variant="body2" color="text.secondary" gutterBottom>
                    {course.code}
                  </Typography>
                  
                  <Chip 
                    label={`${flashcardCounts[course.id] || 0} flashcards`}
                    size="small"
                    color="primary"
                    variant="outlined"
                  />
                </CardContent>
                
                <CardActions>
                  <Button
                    size="small"
                    variant="contained"
                    color="primary"
                    startIcon={<PlayArrowIcon />}
                    onClick={() => startPractice(course.id)}
                    fullWidth
                    sx={{ borderRadius: 1 }}
                  >
                    Practice
                  </Button>
                </CardActions>
              </MotionCard>
            ))}
          </Box>
        </motion.div>
      )}
    </Box>
  );
};

export default CourseFlashcardDashboard;
