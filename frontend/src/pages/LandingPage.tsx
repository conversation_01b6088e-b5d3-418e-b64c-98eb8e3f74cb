import React, { useState, useEffect } from 'react';
import {
  Box,
  Typo<PERSON>,
  Button,
  Grid,
  Container,
  useTheme,
  alpha,
  Stack,
  Paper,
  IconButton,
  AppBar,
  Toolbar,
  useMediaQuery,
  Drawer,
  List,
  ListItem,
  ListItemText,
  Divider,
  Link
} from '@mui/material';
import {
  PlayArrow as PlayIcon,
  ArrowForward as ArrowForwardIcon,
  Star as StarIcon,
  Menu as MenuIcon,
  Close as CloseIcon,
  CheckCircle as CheckIcon,
  TrendingUp as TrendingUpIcon,
  Security as SecurityIcon,
  Speed as SpeedIcon,
  Psychology as BrainIcon,
  School as SchoolIcon,
  Groups as GroupsIcon,
  Analytics as AnalyticsIcon
} from '@mui/icons-material';
import { Link as RouterLink, useNavigate } from 'react-router-dom';
import { useAuth } from '../contexts/AuthContext';
import Logo from '../components/Logo';

const LandingPage: React.FC = () => {
  const { isAuthenticated, user } = useAuth();
  const theme = useTheme();
  const navigate = useNavigate();
  const isMobile = useMediaQuery(theme.breakpoints.down('md'));
  const [mobileMenuOpen, setMobileMenuOpen] = useState(false);
  const [scrolled, setScrolled] = useState(false);

  const features = [
    {
      title: 'AI-Powered Learning',
      description: 'Personalized study paths that adapt to your learning style and pace',
      icon: <BrainIcon sx={{ fontSize: 48, color: 'primary.main' }} />,
      color: 'primary' as const
    },
    {
      title: 'Performance Analytics',
      description: 'Track your progress with detailed insights and improvement recommendations',
      icon: <AnalyticsIcon sx={{ fontSize: 48, color: 'success.main' }} />,
      color: 'success' as const
    },
    {
      title: 'Expert Tutors',
      description: 'Connect with qualified tutors for personalized guidance and support',
      icon: <GroupsIcon sx={{ fontSize: 48, color: 'secondary.main' }} />,
      color: 'secondary' as const
    }
  ];

  const stats = [
    { number: '50,000+', label: 'Active Students' },
    { number: '1M+', label: 'Questions Solved' },
    { number: '98%', label: 'Success Rate' },
    { number: '500+', label: 'Expert Tutors' }
  ];

  const benefits = [
    'Unlimited practice questions',
    'Real-time progress tracking',
    'Expert tutor support',
    'Mobile-friendly platform',
    'Secure and reliable',
    '24/7 accessibility'
  ];

  // Handle scroll effect for header
  useEffect(() => {
    const handleScroll = () => {
      const scrollPosition = window.scrollY;
      setScrolled(scrollPosition > 50);
    };

    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  const handleGetStarted = () => {
    if (isAuthenticated) {
      if (user?.role === 'admin') {
        navigate('/admin/dashboard');
      } else if (user?.role === 'tutor') {
        navigate('/tutor/dashboard');
      } else {
        navigate('/dashboard');
      }
    } else {
      navigate('/register');
    }
  };

  return (
    <>
      {/* Global styles for enhanced animations */}
      <style>
        {`
          @keyframes float {
            0%, 100% { transform: translateY(0px); }
            50% { transform: translateY(-10px); }
          }
          
          @keyframes fadeInUp {
            0% { transform: translateY(30px); opacity: 0; }
            100% { transform: translateY(0); opacity: 1; }
          }
          
          @keyframes glow {
            0% { text-shadow: 0 0 20px rgba(255,255,255,0.5); }
            100% { text-shadow: 0 0 30px rgba(255,255,255,0.8); }
          }
          
          .hover-3d {
            transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
            transform-style: preserve-3d;
          }
          
          .hover-3d:hover {
            transform: perspective(1000px) rotateX(5deg) rotateY(5deg) translateZ(20px);
          }
        `}
      </style>
      
      <Box sx={{ minHeight: '100vh', bgcolor: 'background.default' }}>
        {/* Navigation */}
        <AppBar
          position="fixed"
          elevation={scrolled ? 4 : 0}
          sx={{
            bgcolor: scrolled
              ? alpha(theme.palette.background.paper, 0.98)
              : alpha(theme.palette.background.paper, 0.95),
            backdropFilter: 'blur(20px)',
            borderBottom: scrolled
              ? `1px solid ${alpha(theme.palette.divider, 0.2)}`
              : `1px solid ${alpha(theme.palette.divider, 0.1)}`,
            color: 'text.primary',
            transition: 'all 0.3s ease-in-out',
            transform: scrolled ? 'translateY(0)' : 'translateY(0)',
            boxShadow: scrolled
              ? `0 4px 20px ${alpha(theme.palette.primary.main, 0.1)}`
              : 'none'
          }}
        >
          <Toolbar sx={{ justifyContent: 'space-between' }}>
            <Box sx={{
              display: 'flex',
              alignItems: 'center',
              justifyContent: 'flex-start',
              height: '100%'
            }}>
              <Logo size={48} sx={{
                mr: 1.5,
                display: 'block',
                verticalAlign: 'middle'
              }} />
              <Typography variant="h6" sx={{
                fontWeight: 800,
                color: 'primary.main',
                display: 'flex',
                alignItems: 'center',
                lineHeight: 1
              }}>
                CampusPQ
              </Typography>
            </Box>

            {!isMobile && (
              <Box sx={{ display: 'flex', alignItems: 'center' }}>
                <Button color="inherit" sx={{ mx: 1 }}>Features</Button>
                <Button color="inherit" sx={{ mx: 1 }}>Pricing</Button>
                <Button color="inherit" sx={{ mx: 1 }}>About</Button>
                <Button color="inherit" sx={{ mx: 1 }}>Contact</Button>
                <Divider orientation="vertical" flexItem sx={{ mx: 2, height: 24 }} />
                {!isAuthenticated ? (
                  <>
                    <Button component={RouterLink} to="/login" sx={{ mx: 1 }}>
                      Sign In
                    </Button>
                    <Button 
                      variant="contained" 
                      component={RouterLink} 
                      to="/register"
                      sx={{ ml: 1, borderRadius: 2 }}
                    >
                      Get Started
                    </Button>
                  </>
                ) : (
                  <Button 
                    variant="contained" 
                    onClick={handleGetStarted}
                    sx={{ ml: 1, borderRadius: 2 }}
                  >
                    Dashboard
                  </Button>
                )}
              </Box>
            )}

            {isMobile && (
              <IconButton onClick={() => setMobileMenuOpen(true)}>
                <MenuIcon />
              </IconButton>
            )}
          </Toolbar>
        </AppBar>

        {/* Mobile Menu */}
        <Drawer
          anchor="right"
          open={mobileMenuOpen}
          onClose={() => setMobileMenuOpen(false)}
        >
          <Box sx={{ width: 250, pt: 2 }}>
            <Box sx={{ display: 'flex', justifyContent: 'flex-end', px: 2, pb: 2 }}>
              <IconButton onClick={() => setMobileMenuOpen(false)}>
                <CloseIcon />
              </IconButton>
            </Box>
            <List>
              <ListItem button>
                <ListItemText primary="Features" />
              </ListItem>
              <ListItem button>
                <ListItemText primary="Pricing" />
              </ListItem>
              <ListItem button>
                <ListItemText primary="About" />
              </ListItem>
              <ListItem button>
                <ListItemText primary="Contact" />
              </ListItem>
              <Divider sx={{ my: 1 }} />
              {!isAuthenticated ? (
                <>
                  <ListItem button component={RouterLink} to="/login">
                    <ListItemText primary="Sign In" />
                  </ListItem>
                  <ListItem button component={RouterLink} to="/register">
                    <ListItemText primary="Get Started" />
                  </ListItem>
                </>
              ) : (
                <ListItem button onClick={handleGetStarted}>
                  <ListItemText primary="Dashboard" />
                </ListItem>
              )}
            </List>
          </Box>
        </Drawer>

        {/* Hero Section */}
        <Box
          sx={{
            pt: { xs: 12, md: 16 },
            pb: { xs: 8, md: 12 },
            background: `linear-gradient(135deg, ${alpha(theme.palette.primary.main, 0.05)} 0%, ${alpha(theme.palette.secondary.main, 0.05)} 100%)`,
            position: 'relative',
            overflow: 'hidden',
            '&::before': {
              content: '""',
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              background: `url("data:image/svg+xml,%3Csvg width='60' height='60' viewBox='0 0 60 60' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='none' fill-rule='evenodd'%3E%3Cg fill='%23${theme.palette.primary.main.slice(1)}' fill-opacity='0.03'%3E%3Ccircle cx='30' cy='30' r='2'/%3E%3C/g%3E%3C/g%3E%3C/svg%3E")`,
            }
          }}
        >
          <Container maxWidth="lg">
            <Grid container spacing={4} alignItems="center">
              <Grid item xs={12} md={6}>
                <Stack spacing={4}>
                  <Typography
                    variant="h1"
                    sx={{
                      fontSize: { xs: '2.5rem', sm: '3.5rem', md: '4.5rem' },
                      fontWeight: 900,
                      lineHeight: 1.1,
                      background: `linear-gradient(135deg, ${theme.palette.text.primary}, ${theme.palette.primary.main})`,
                      WebkitBackgroundClip: 'text',
                      WebkitTextFillColor: 'transparent',
                      animation: 'fadeInUp 1s ease-out',
                    }}
                  >
                    Master Your Studies with AI
                  </Typography>
                  
                  <Typography
                    variant="h5"
                    color="text.secondary"
                    sx={{ 
                      fontSize: { xs: '1.1rem', md: '1.3rem' },
                      lineHeight: 1.6,
                      maxWidth: '90%'
                    }}
                  >
                    Join thousands of students using our AI-powered platform to achieve 
                    better grades and accelerate their academic success.
                  </Typography>

                  <Stack direction={{ xs: 'column', sm: 'row' }} spacing={2}>
                    <Button
                      variant="contained"
                      size="large"
                      onClick={handleGetStarted}
                      endIcon={<ArrowForwardIcon />}
                      sx={{
                        py: 2,
                        px: 4,
                        fontSize: '1.1rem',
                        fontWeight: 700,
                        borderRadius: 3,
                        boxShadow: `0 8px 32px ${alpha(theme.palette.primary.main, 0.3)}`,
                        '&:hover': {
                          transform: 'translateY(-2px)',
                          boxShadow: `0 12px 40px ${alpha(theme.palette.primary.main, 0.4)}`,
                        },
                        transition: 'all 0.3s ease'
                      }}
                    >
                      {isAuthenticated ? 'Go to Dashboard' : 'Start Learning Free'}
                    </Button>
                    
                    <Button
                      variant="outlined"
                      size="large"
                      startIcon={<PlayIcon />}
                      sx={{
                        py: 2,
                        px: 4,
                        fontSize: '1.1rem',
                        fontWeight: 600,
                        borderRadius: 3,
                        borderWidth: 2,
                        '&:hover': {
                          borderWidth: 2,
                          transform: 'translateY(-2px)',
                        },
                        transition: 'all 0.3s ease'
                      }}
                    >
                      Watch Demo
                    </Button>
                  </Stack>

                  <Box sx={{ display: 'flex', alignItems: 'center', gap: 2, pt: 2 }}>
                    <Box sx={{ display: 'flex' }}>
                      {[1, 2, 3, 4, 5].map((star) => (
                        <StarIcon key={star} sx={{ color: '#FFD700', fontSize: 20 }} />
                      ))}
                    </Box>
                    <Typography variant="body2" color="text.secondary">
                      4.9/5 from 10,000+ students
                    </Typography>
                  </Box>
                </Stack>
              </Grid>

              <Grid item xs={12} md={6}>
                <Box
                  sx={{
                    position: 'relative',
                    display: 'flex',
                    justifyContent: 'center',
                    mt: { xs: 4, md: 0 }
                  }}
                >
                  <Paper
                    elevation={20}
                    sx={{
                      p: 6,
                      borderRadius: 4,
                      background: `linear-gradient(135deg, ${theme.palette.background.paper}, ${alpha(theme.palette.primary.main, 0.05)})`,
                      transform: 'rotate(-2deg)',
                      '&:hover': {
                        transform: 'rotate(0deg) scale(1.05)',
                      },
                      transition: 'transform 0.3s ease'
                    }}
                  >
                    <Stack spacing={3} alignItems="center" textAlign="center">
                      <SchoolIcon sx={{ fontSize: 80, color: 'primary.main', animation: 'float 3s ease-in-out infinite' }} />
                      <Typography variant="h6" sx={{ fontWeight: 600 }}>
                        Join 50,000+ Students
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Already achieving better results
                      </Typography>
                    </Stack>
                  </Paper>
                </Box>
              </Grid>
            </Grid>
          </Container>
        </Box>

        {/* Stats Section */}
        <Box sx={{ py: { xs: 6, md: 8 }, bgcolor: alpha(theme.palette.primary.main, 0.02) }}>
          <Container maxWidth="lg">
            <Grid container spacing={4}>
              {stats.map((stat, index) => (
                <Grid item xs={6} md={3} key={index}>
                  <Box
                    sx={{
                      textAlign: 'center',
                      '&:hover': {
                        transform: 'translateY(-5px) scale(1.05)',
                        transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                      },
                      transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)',
                    }}
                  >
                    <Typography
                      variant="h3"
                      sx={{
                        fontWeight: 900,
                        fontSize: { xs: '2rem', md: '3rem' },
                        color: 'primary.main',
                        mb: 1,
                        background: `linear-gradient(135deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
                        WebkitBackgroundClip: 'text',
                        WebkitTextFillColor: 'transparent',
                      }}
                    >
                      {stat.number}
                    </Typography>
                    <Typography variant="h6" color="text.secondary" sx={{ fontWeight: 600 }}>
                      {stat.label}
                    </Typography>
                  </Box>
                </Grid>
              ))}
            </Grid>
          </Container>
        </Box>

        {/* Features Section */}
        <Container maxWidth="lg" sx={{ py: { xs: 8, md: 12 } }}>
          <Stack spacing={8}>
            <Box textAlign="center">
              <Typography
                variant="h2"
                sx={{
                  fontSize: { xs: '2rem', md: '3rem' },
                  fontWeight: 800,
                  mb: 2,
                  color: 'text.primary',
                  background: `linear-gradient(135deg, ${theme.palette.text.primary}, ${theme.palette.primary.main})`,
                  WebkitBackgroundClip: 'text',
                  WebkitTextFillColor: 'transparent',
                }}
              >
                Why Students Choose CampusPQ
              </Typography>
              <Typography
                variant="h6"
                color="text.secondary"
                sx={{ maxWidth: 600, mx: 'auto', lineHeight: 1.6 }}
              >
                Our platform combines cutting-edge AI technology with proven educational
                methods to deliver exceptional learning outcomes.
              </Typography>
            </Box>

            <Box
              sx={{
                display: 'flex',
                flexDirection: { xs: 'column', md: 'row' },
                gap: 4,
                alignItems: 'stretch'
              }}
            >
              {features.map((feature, index) => (
                <Box key={index} sx={{ flex: { xs: 'none', md: 1 }, display: 'flex' }}>
                  <Paper
                    elevation={0}
                    className="hover-3d"
                    sx={{
                      p: 4,
                      width: '100%',
                      minHeight: { xs: 'auto', md: '320px' }, // Set minimum height for large screens
                      display: 'flex',
                      flexDirection: 'column',
                      borderRadius: 4,
                      border: `2px solid ${alpha(theme.palette.divider, 0.08)}`,
                      textAlign: 'center',
                      background: `linear-gradient(135deg, ${theme.palette.background.paper} 0%, ${alpha(theme.palette[feature.color].main, 0.02)} 100%)`,
                      position: 'relative',
                      overflow: 'hidden',
                      '&::before': {
                        content: '""',
                        position: 'absolute',
                        top: 0,
                        left: 0,
                        right: 0,
                        height: 4,
                        background: `linear-gradient(90deg, ${theme.palette[feature.color].main}, ${theme.palette[feature.color].light})`,
                        transform: 'scaleX(0)',
                        transformOrigin: 'left',
                        transition: 'transform 0.4s ease',
                      },
                      '&:hover': {
                        borderColor: alpha(theme.palette[feature.color].main, 0.3),
                        '&::before': {
                          transform: 'scaleX(1)',
                        },
                        '& .feature-icon': {
                          transform: 'scale(1.1) rotate(5deg)',
                        }
                      },
                    }}
                  >
                    <Stack spacing={3} alignItems="center" sx={{ height: '100%', justifyContent: 'space-between' }}>
                      <Box
                        className="feature-icon"
                        sx={{
                          transition: 'transform 0.4s ease',
                        }}
                      >
                        {feature.icon}
                      </Box>

                      <Typography
                        variant="h5"
                        sx={{
                          fontWeight: 700,
                          color: 'text.primary',
                          fontSize: { xs: '1.25rem', md: '1.5rem' }
                        }}
                      >
                        {feature.title}
                      </Typography>

                      <Typography
                        variant="body1"
                        color="text.secondary"
                        sx={{
                          lineHeight: 1.7,
                          fontSize: { xs: '0.95rem', md: '1rem' },
                          flex: 1,
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          textAlign: 'center'
                        }}
                      >
                        {feature.description}
                      </Typography>
                    </Stack>
                  </Paper>
                </Box>
              ))}
            </Box>
          </Stack>
        </Container>

        {/* Benefits Section */}
        <Box sx={{
          py: { xs: 8, md: 12 },
          bgcolor: theme.palette.mode === 'light'
            ? alpha(theme.palette.grey[100], 0.5)
            : alpha(theme.palette.grey[800], 0.3)
        }}>
          <Container maxWidth="lg">
            <Grid container spacing={6} alignItems="center">
              <Grid item xs={12} md={6}>
                <Stack spacing={4}>
                  <Typography
                    variant="h2"
                    sx={{
                      fontSize: { xs: '2rem', md: '2.5rem' },
                      fontWeight: 800,
                      color: 'text.primary',
                      background: `linear-gradient(135deg, ${theme.palette.text.primary}, ${theme.palette.success.main})`,
                      WebkitBackgroundClip: 'text',
                      WebkitTextFillColor: 'transparent',
                    }}
                  >
                    Everything You Need to Succeed
                  </Typography>
                  <Typography variant="h6" color="text.secondary" sx={{ lineHeight: 1.7 }}>
                    Our comprehensive platform provides all the tools and resources
                    you need to excel in your academic journey.
                  </Typography>
                  <Stack spacing={2}>
                    {benefits.map((benefit, index) => (
                      <Box
                        key={index}
                        sx={{
                          display: 'flex',
                          alignItems: 'center',
                          '&:hover': {
                            transform: 'translateX(10px)',
                            '& .check-icon': {
                              transform: 'scale(1.2) rotate(360deg)',
                            }
                          },
                          transition: 'transform 0.3s ease',
                        }}
                      >
                        <CheckIcon
                          className="check-icon"
                          sx={{
                            color: 'success.main',
                            mr: 2,
                            fontSize: 24,
                            transition: 'transform 0.3s ease',
                          }}
                        />
                        <Typography variant="body1" sx={{ fontWeight: 500, fontSize: '1.1rem' }}>
                          {benefit}
                        </Typography>
                      </Box>
                    ))}
                  </Stack>
                </Stack>
              </Grid>

              <Grid item xs={12} md={6}>
                <Box sx={{ display: 'flex', justifyContent: 'center' }}>
                  <Paper
                    elevation={12}
                    className="hover-3d"
                    sx={{
                      p: 6,
                      borderRadius: 4,
                      textAlign: 'center',
                      background: `linear-gradient(135deg, ${theme.palette.background.paper}, ${alpha(theme.palette.secondary.main, 0.05)})`,
                      maxWidth: 400,
                      position: 'relative',
                      overflow: 'hidden',
                      '&::before': {
                        content: '""',
                        position: 'absolute',
                        top: -50,
                        left: -50,
                        width: 100,
                        height: 100,
                        background: `radial-gradient(circle, ${alpha(theme.palette.primary.main, 0.1)} 0%, transparent 70%)`,
                        animation: 'float 3s ease-in-out infinite',
                      },
                    }}
                  >
                    <Stack spacing={3} alignItems="center">
                      <Box
                        sx={{
                          display: 'flex',
                          gap: 1,
                          '& > *': {
                            animation: 'float 2s ease-in-out infinite',
                            '&:nth-of-type(2)': { animationDelay: '0.2s' },
                            '&:nth-of-type(3)': { animationDelay: '0.4s' },
                          }
                        }}
                      >
                        <SpeedIcon sx={{ fontSize: 40, color: 'primary.main' }} />
                        <SecurityIcon sx={{ fontSize: 40, color: 'success.main' }} />
                        <TrendingUpIcon sx={{ fontSize: 40, color: 'secondary.main' }} />
                      </Box>
                      <Typography
                        variant="h3"
                        sx={{
                          fontWeight: 800,
                          background: `linear-gradient(135deg, ${theme.palette.primary.main}, ${theme.palette.secondary.main})`,
                          WebkitBackgroundClip: 'text',
                          WebkitTextFillColor: 'transparent',
                        }}
                      >
                        99.9%
                      </Typography>
                      <Typography variant="h6" sx={{ fontWeight: 600 }}>
                        Platform Uptime
                      </Typography>
                      <Typography variant="body2" color="text.secondary">
                        Reliable, secure, and always available when you need it most.
                      </Typography>
                    </Stack>
                  </Paper>
                </Box>
              </Grid>
            </Grid>
          </Container>
        </Box>

        {/* Community Section */}
        <Box sx={{
          py: { xs: 6, md: 8 },
          bgcolor: theme.palette.mode === 'light'
            ? alpha(theme.palette.success.main, 0.05)
            : alpha(theme.palette.success.main, 0.1)
        }}>
          <Container maxWidth="md">
            <Stack spacing={4} alignItems="center" textAlign="center">
              <Box sx={{
                display: 'flex',
                alignItems: 'center',
                justifyContent: 'center',
                mb: 2
              }}>
                <GroupsIcon sx={{
                  fontSize: 48,
                  color: 'success.main',
                  mr: 2
                }} />
                <Typography
                  variant="h3"
                  sx={{
                    fontSize: { xs: '1.8rem', md: '2.2rem' },
                    fontWeight: 700,
                    color: 'text.primary',
                  }}
                >
                  Join Our Student Community
                </Typography>
              </Box>

              <Typography
                variant="h6"
                color="text.secondary"
                sx={{
                  lineHeight: 1.6,
                  maxWidth: 600,
                  mx: 'auto'
                }}
              >
                Connect with thousands of students, share study tips, get instant help,
                and stay motivated together in our active WhatsApp community!
              </Typography>

              <Box sx={{
                display: 'flex',
                flexDirection: { xs: 'column', sm: 'row' },
                gap: 2,
                alignItems: 'center',
                justifyContent: 'center'
              }}>
                <Button
                  variant="contained"
                  size="large"
                  href="https://chat.whatsapp.com/JzGYp4sfwWdJlB7pFzlTnd"
                  target="_blank"
                  rel="noopener noreferrer"
                  startIcon={<GroupsIcon />}
                  sx={{
                    py: 2,
                    px: 4,
                    fontSize: '1.1rem',
                    fontWeight: 700,
                    bgcolor: '#25d366',
                    color: 'white',
                    borderRadius: 3,
                    '&:hover': {
                      bgcolor: '#128c7e',
                      transform: 'translateY(-2px)',
                    },
                    transition: 'all 0.3s ease'
                  }}
                >
                  Join WhatsApp Community
                </Button>

                <Stack direction="row" spacing={3} sx={{ opacity: 0.8 }}>
                  <Box sx={{ textAlign: 'center' }}>
                    <Typography variant="h6" sx={{ fontWeight: 700, color: 'success.main' }}>
                      5,000+
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Active Members
                    </Typography>
                  </Box>
                  <Box sx={{ textAlign: 'center' }}>
                    <Typography variant="h6" sx={{ fontWeight: 700, color: 'success.main' }}>
                      24/7
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Support
                    </Typography>
                  </Box>
                  <Box sx={{ textAlign: 'center' }}>
                    <Typography variant="h6" sx={{ fontWeight: 700, color: 'success.main' }}>
                      Instant
                    </Typography>
                    <Typography variant="body2" color="text.secondary">
                      Help
                    </Typography>
                  </Box>
                </Stack>
              </Box>

              <Box sx={{
                display: 'flex',
                flexWrap: 'wrap',
                gap: 2,
                justifyContent: 'center',
                mt: 3
              }}>
                {[
                  '📚 Study Tips',
                  '🤝 Peer Support',
                  '💡 Quick Answers',
                  '🎯 Motivation',
                  '📝 Resource Sharing',
                  '🏆 Success Stories'
                ].map((feature, index) => (
                  <Box
                    key={index}
                    sx={{
                      bgcolor: theme.palette.mode === 'light'
                        ? alpha(theme.palette.success.main, 0.1)
                        : alpha(theme.palette.success.main, 0.2),
                      px: 2,
                      py: 1,
                      borderRadius: 2,
                      border: `1px solid ${alpha(theme.palette.success.main, 0.3)}`
                    }}
                  >
                    <Typography variant="body2" sx={{ fontWeight: 500 }}>
                      {feature}
                    </Typography>
                  </Box>
                ))}
              </Box>
            </Stack>
          </Container>
        </Box>

        {/* CTA Section */}
        <Box
          sx={{
            py: { xs: 8, md: 12 },
            background: `linear-gradient(135deg, ${theme.palette.primary.main}, ${theme.palette.primary.dark})`,
            color: 'white',
            textAlign: 'center',
            position: 'relative',
            overflow: 'hidden',
            '&::before': {
              content: '""',
              position: 'absolute',
              top: 0,
              left: 0,
              right: 0,
              bottom: 0,
              background: `url("data:image/svg+xml,%3Csvg width='100' height='100' viewBox='0 0 100 100' xmlns='http://www.w3.org/2000/svg'%3E%3Cg fill='%23ffffff' fill-opacity='0.05'%3E%3Cpolygon points='50 0 60 40 100 50 60 60 50 100 40 60 0 50 40 40'/%3E%3C/g%3E%3C/svg%3E")`,
              animation: 'float 20s linear infinite',
            }
          }}
        >
          <Container maxWidth="md" sx={{ position: 'relative', zIndex: 1 }}>
            <Stack spacing={4} alignItems="center">
              <Typography
                variant="h2"
                sx={{
                  fontSize: { xs: '2rem', md: '3rem' },
                  fontWeight: 800,
                  mb: 2,
                  animation: 'glow 2s ease-in-out infinite alternate',
                }}
              >
                Ready to Transform Your Learning?
              </Typography>
              <Typography
                variant="h6"
                sx={{
                  opacity: 0.9,
                  lineHeight: 1.6,
                  maxWidth: 500
                }}
              >
                Join thousands of students who are already achieving better results.
                Start your free trial today.
              </Typography>

              {!isAuthenticated && (
                <Stack direction={{ xs: 'column', sm: 'row' }} spacing={2}>
                  <Button
                    variant="contained"
                    size="large"
                    component={RouterLink}
                    to="/register"
                    endIcon={<ArrowForwardIcon />}
                    sx={{
                      py: 2,
                      px: 4,
                      fontSize: '1.1rem',
                      fontWeight: 700,
                      bgcolor: 'white',
                      color: 'primary.main',
                      borderRadius: 3,
                      '&:hover': {
                        bgcolor: alpha('#ffffff', 0.9),
                        transform: 'translateY(-2px)',
                      },
                      transition: 'all 0.3s ease'
                    }}
                  >
                    Start Learning Free
                  </Button>
                  <Button
                    variant="outlined"
                    size="large"
                    component={RouterLink}
                    to="/login"
                    sx={{
                      py: 2,
                      px: 4,
                      fontSize: '1.1rem',
                      fontWeight: 600,
                      borderColor: 'white',
                      color: 'white',
                      borderRadius: 3,
                      borderWidth: 2,
                      '&:hover': {
                        borderColor: 'white',
                        bgcolor: alpha('#ffffff', 0.1),
                        borderWidth: 2,
                        transform: 'translateY(-2px)',
                      },
                      transition: 'all 0.3s ease'
                    }}
                  >
                    Sign In
                  </Button>
                </Stack>
              )}
            </Stack>
          </Container>
        </Box>

        {/* Footer */}
        <Box sx={{ py: 6, bgcolor: 'grey.900', color: 'white' }}>
          <Container maxWidth="lg">
            <Grid container spacing={4}>
              <Grid item xs={12} md={4}>
                <Stack spacing={2}>
                  <Box sx={{
                    display: 'flex',
                    alignItems: 'center',
                    justifyContent: 'flex-start'
                  }}>
                    <Logo size={40} sx={{
                      mr: 1.5,
                      filter: 'brightness(0) invert(1)',
                      display: 'block',
                      verticalAlign: 'middle'
                    }} />
                    <Typography variant="h6" sx={{
                      fontWeight: 800,
                      display: 'flex',
                      alignItems: 'center',
                      lineHeight: 1
                    }}>
                      CampusPQ
                    </Typography>
                  </Box>
                  <Typography variant="body2" sx={{ opacity: 0.8 }}>
                    Empowering students with AI-driven learning solutions for academic excellence.
                  </Typography>
                </Stack>
              </Grid>

              <Grid item xs={12} md={8}>
                <Grid container spacing={4}>
                  <Grid item xs={6} md={3}>
                    <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
                      Product
                    </Typography>
                    <Stack spacing={1}>
                      <Typography variant="body2" sx={{ opacity: 0.8 }}>Features</Typography>
                      <Typography variant="body2" sx={{ opacity: 0.8 }}>Pricing</Typography>
                      <Typography variant="body2" sx={{ opacity: 0.8 }}>Demo</Typography>
                    </Stack>
                  </Grid>

                  <Grid item xs={6} md={3}>
                    <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
                      Company
                    </Typography>
                    <Stack spacing={1}>
                      <Typography variant="body2" sx={{ opacity: 0.8 }}>About</Typography>
                      <Typography variant="body2" sx={{ opacity: 0.8 }}>Contact</Typography>
                      <Typography variant="body2" sx={{ opacity: 0.8 }}>Careers</Typography>
                    </Stack>
                  </Grid>

                  <Grid item xs={6} md={3}>
                    <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
                      Support
                    </Typography>
                    <Stack spacing={1}>
                      <Typography variant="body2" sx={{ opacity: 0.8 }}>Help Center</Typography>
                      <Typography variant="body2" sx={{ opacity: 0.8 }}>Documentation</Typography>
                      <Link
                        href="https://chat.whatsapp.com/JzGYp4sfwWdJlB7pFzlTnd"
                        target="_blank"
                        rel="noopener noreferrer"
                        sx={{
                          color: 'inherit',
                          opacity: 0.8,
                          textDecoration: 'none',
                          '&:hover': { opacity: 1, color: '#25d366' },
                          transition: 'all 0.3s ease'
                        }}
                      >
                        <Typography variant="body2">💬 WhatsApp Community</Typography>
                      </Link>
                    </Stack>
                  </Grid>

                  <Grid item xs={6} md={3}>
                    <Typography variant="h6" sx={{ mb: 2, fontWeight: 600 }}>
                      Legal
                    </Typography>
                    <Stack spacing={1}>
                      <Link component={RouterLink} to="/privacy" sx={{ color: 'inherit', opacity: 0.8, textDecoration: 'none', '&:hover': { opacity: 1 } }}>
                        <Typography variant="body2">Privacy Policy</Typography>
                      </Link>
                      <Link component={RouterLink} to="/terms" sx={{ color: 'inherit', opacity: 0.8, textDecoration: 'none', '&:hover': { opacity: 1 } }}>
                        <Typography variant="body2">Terms & Conditions</Typography>
                      </Link>
                      <Typography variant="body2" sx={{ opacity: 0.8 }}>Security</Typography>
                    </Stack>
                  </Grid>
                </Grid>
              </Grid>
            </Grid>

            <Divider sx={{ my: 4, bgcolor: alpha('#ffffff', 0.1) }} />

            <Box sx={{ textAlign: 'center' }}>
              <Typography variant="body2" sx={{ opacity: 0.6 }}>
                © 2024 CampusPQ. All rights reserved.
              </Typography>
            </Box>
          </Container>
        </Box>
      </Box>
    </>
  );
};

export default LandingPage;
