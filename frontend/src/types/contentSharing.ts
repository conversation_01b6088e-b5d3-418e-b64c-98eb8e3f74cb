/**
 * Types for the new simplified content sharing system
 */

export enum ContentType {
  MCQ = 'MCQ',
  FLASHCARD = 'FLASHCARD',
  SUMMARY = 'SUMMARY'
}

export enum AccessType {
  SHARED_LINK = 'SHARED_LINK',
  EMAIL_INVITATION = 'EMAIL_INVITATION',
  DIRECT_SHARE = 'DIRECT_SHARE'
}

// Request/Response types
export interface ShareContentRequest {
  content_type: ContentType;
  content_id: number;
  content_ids?: number[];  // Multiple content IDs for batch sharing
  share_message?: string;
  invited_emails?: string[];
  create_public_link?: boolean;
}

export interface ShareContentResponse {
  content_type: ContentType;
  content_id: number;
  invitations_sent: string[];
  invitations_failed: string[];
  public_share_url?: string;
  message: string;
}

export interface AcceptInvitationRequest {
  invitation_token: string;
}

export interface AcceptInvitationResponse {
  success: boolean;
  message: string;
  content_type: ContentType;
  content_id: number;
  redirect_url?: string;
}

export interface AccessPublicShareRequest {
  share_token: string;
}

export interface AccessPublicShareResponse {
  success: boolean;
  message: string;
  content_type: ContentType;
  content_id: number;
  redirect_url?: string;
}

// Content with sharing metadata
export interface SharedContentItem {
  // Content details
  content_type: ContentType;
  content_id: number;
  content_data: any; // The actual content (question, flashcard, summary)
  
  // Sharing metadata
  is_shared: boolean;
  shared_by_name?: string;
  shared_by_email?: string;
  share_message?: string;
  shared_at?: string;
  
  // Permissions
  can_edit: boolean;
  can_delete: boolean;
  
  // Display tags
  tags: string[];
}

// Summary types
export interface MySharedContentSummary {
  content_type: ContentType;
  content_id: number;
  content_title: string;
  course_name?: string;
  total_invitations: number;
  accepted_invitations: number;
  public_link_accesses: number;
  has_public_link: boolean;
  created_at: string;
}

export interface SharedWithMeSummary {
  content_type: ContentType;
  content_id: number;
  content_title: string;
  course_name?: string;
  shared_by_name: string;
  shared_by_email: string;
  share_message?: string;
  shared_at: string;
  last_accessed?: string;
}

// UI Component Props
export interface ShareButtonProps {
  content_type: ContentType;
  content_id: number;
  content_ids?: number[];  // Multiple content IDs for batch sharing
  content_title: string;
  course_name?: string;
  disabled?: boolean;
  size?: 'small' | 'medium' | 'large';
}

export interface ShareModalProps {
  open: boolean;
  onClose: () => void;
  content_type: ContentType;
  content_id: number;
  content_ids?: number[];  // Multiple content IDs for batch sharing
  content_title: string;
  course_name?: string;
}

export interface ContentTagProps {
  is_shared: boolean;
  shared_by_name?: string;
  tags: string[];
  size?: 'small' | 'medium';
}

// Content type specific interfaces
export interface MCQWithSharing extends SharedContentItem {
  content_type: ContentType.MCQ;
  content_data: {
    id: number;
    content: string;
    question_type: string;
    difficulty: string;
    topic?: string;
    options?: any;
    answer: string;
    explanation?: string;
    course_name?: string;
    created_at: string;
  };
}

export interface FlashcardWithSharing extends SharedContentItem {
  content_type: ContentType.FLASHCARD;
  content_data: {
    id: number;
    front_content: string;
    back_content: string;
    topic?: string;
    difficulty?: string;
    card_type?: string;
    course_name?: string;
    created_at: string;
  };
}

export interface SummaryWithSharing extends SharedContentItem {
  content_type: ContentType.SUMMARY;
  content_data: {
    id: number;
    title: string;
    overview: string;
    detailed_explanation: string;
    comprehensive_summary: string;
    key_concepts: any;
    main_topics: any;
    course_name?: string;
    created_at: string;
  };
}
