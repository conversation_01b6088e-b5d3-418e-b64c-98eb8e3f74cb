export enum ProcessingStatus {
  PENDING = "pending",
  UPLOADING = "uploading",
  PROCESSING = "processing",
  EMBEDDING = "embedding",
  GENERATING = "generating",
  COMPLETED = "completed",
  FAILED = "failed"
}



// Gemini Files API Types
export enum DifficultyLevel {
  EASY = "easy",
  MEDIUM = "medium",
  HARD = "hard"
}

export interface GeminiFileUploadResponse {
  message: string;
  gemini_file_id: string;
  original_filename: string;
  file_size: number;
  upload_time: string;
  auto_delete_time: string;
  course_name?: string;
}

export interface GeminiMCQOption {
  option_text: string;
  is_correct: boolean;
}

export interface GeminiGeneratedMCQ {
  question_text: string;
  options: GeminiMCQOption[];
  correct_answer: string;
  explanation: string;
  topic: string;
  difficulty: DifficultyLevel;
  bloom_taxonomy_level: string;
}

export interface GeminiMCQGenerationResponse {
  questions: GeminiGeneratedMCQ[];
  total_count: number;
  source_topics: string[];
  saved_question_ids: number[];
  message: string;
}

export interface GeminiGeneratedFlashcard {
  front_content: string;
  back_content: string;
  topic: string;
  difficulty: DifficultyLevel;
  card_type: string;
}

export interface GeminiFlashcardGenerationResponse {
  flashcards: GeminiGeneratedFlashcard[];
  total_count: number;
  source_topics: string[];
  saved_flashcard_ids: number[];
  message: string;
}

export interface GeminiFile {
  id: number;
  gemini_file_id: string;
  original_filename: string;
  file_size: number;
  mime_type: string;
  student_id: number;
  course_name?: string;
  upload_time: string;
  auto_delete_time: string;
  is_deleted: boolean;
  created_at: string;
  updated_at: string;
}

export interface GeminiFileListResponse {
  files: GeminiFile[];
  total_count: number;
}

// Upload management types
export interface UploadStartResponse {
  upload_id: string;
  task_id: string;
  message: string;
  status: string;
}

export interface UploadTaskResponse {
  id: string;
  task_id: string;
  fileName: string;
  fileSize: number;
  courseName?: string;
  status: 'pending' | 'uploading' | 'processing' | 'completed' | 'failed' | 'cancelled';
  progress: number;
  bytes_uploaded: number;
  upload_speed?: number;
  estimated_time_remaining?: number;
  created_at: string;
  started_at?: string;
  completed_at?: string;
  error_message?: string;
  result?: any;
  gemini_file_id?: string;
}

export interface UploadListResponse {
  uploads: UploadTaskResponse[];
  total: number;
  active_count: number;
  completed_count: number;
  failed_count: number;
}

export interface GeminiGeneratedNoteExplanation {
  title: string;
  overview: string;
  detailed_explanation: string;
  comprehensive_summary: string;
  key_concepts: string[];
  main_topics: string[];
  word_count: number;
}

export interface GeminiNoteExplanationGenerationResponse {
  explanation: GeminiGeneratedNoteExplanation;
  source_info: Record<string, any>;
  saved_explanation_id: number;
  message: string;
}

export interface GeneratedNoteExplanation {
  id: number;
  title: string;
  overview: string;
  detailed_explanation: string;
  comprehensive_summary: string;
  key_concepts: string[];
  main_topics: string[];
  word_count: number;
  course_name?: string;
  original_filename?: string;
  gemini_file_id?: string;
  created_at: string;
  updated_at: string;
}