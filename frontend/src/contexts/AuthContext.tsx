import React, { createContext, useContext, useState, useEffect, ReactNode } from 'react';
import { User, login as apiLogin, getCurrentUser, LoginRequest, refreshToken } from '../api/auth';
import { getMyTutorProfile } from '../api/tutors';
import { forceLogout, isAuthenticationError } from '../utils/authUtils';

interface AuthContextType {
  user: User | null;
  isAuthenticated: boolean;
  isLoading: boolean;
  login: (data: LoginRequest) => Promise<void>;
  logout: () => void;
  refreshUser: () => Promise<void>;
  isProfileComplete: boolean;
  isEmailVerified: boolean;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

interface AuthProviderProps {
  children: ReactNode;
}

export const AuthProvider: React.FC<AuthProviderProps> = ({ children }) => {
  const [user, setUser] = useState<User | null>(null);
  const [isLoading, setIsLoading] = useState(true);
  const [tutorProfileExists, setTutorProfileExists] = useState<boolean | null>(null);

  // Helper function to set and persist tutor profile status
  const setTutorProfileExistsWithPersistence = (exists: boolean | null) => {
    console.log('Setting tutorProfileExists to:', exists);
    setTutorProfileExists(exists);
    if (exists !== null) {
      localStorage.setItem('tutorProfileExists', JSON.stringify(exists));
    } else {
      localStorage.removeItem('tutorProfileExists');
    }
  };

  useEffect(() => {
    const initAuth = async () => {
      const token = localStorage.getItem('token');

      // If no token exists, clear any stale data and finish loading
      if (!token) {
        localStorage.removeItem('user');
        localStorage.removeItem('refresh_token');
        localStorage.removeItem('tutorProfileExists');
        setUser(null);
        setTutorProfileExists(null);
        setIsLoading(false);
        return;
      }

      try {
        // Always validate token by fetching current user data
        // This ensures the token is still valid and not expired
        console.log('Validating authentication token...');
        const userData = await getCurrentUser();

        // Token is valid, set user data
        setUser(userData);
        // Store user data in localStorage for persistence
        localStorage.setItem('user', JSON.stringify(userData));

        // If user is a tutor, check if they have a tutor profile
        if (userData.role === 'tutor') {
          console.log('User is a tutor, checking for existing tutor profile...');
          try {
            const tutorProfile = await getMyTutorProfile();
            console.log('Tutor profile found:', tutorProfile);
            setTutorProfileExistsWithPersistence(true);
          } catch (error) {
            // 404 means no tutor profile exists
            if (error.response?.status === 404) {
              console.log('No tutor profile found (404)');
              setTutorProfileExistsWithPersistence(false);
            } else {
              console.error('Error checking tutor profile:', error);
              setTutorProfileExistsWithPersistence(false);
            }
          }
        }
      } catch (error) {
        console.error('Token validation failed:', error);

        // Clear all authentication data for any authentication error
        if (isAuthenticationError(error)) {
          console.log('Authentication error detected, clearing session data');
          localStorage.removeItem('token');
          localStorage.removeItem('refresh_token');
          localStorage.removeItem('user');
          localStorage.removeItem('tutorProfileExists');
          setUser(null);
          setTutorProfileExists(null);

          // Don't redirect here - let the ProtectedRoute handle it
          // This prevents redirect loops and allows proper route handling
        } else {
          // For network errors, keep the stored user data but log the error
          console.error('Network error during token validation, keeping stored session');
          const storedUser = localStorage.getItem('user');
          if (storedUser) {
            try {
              const userData = JSON.parse(storedUser);
              setUser(userData);

              // Set tutor profile status from localStorage if available
              if (userData.role === 'tutor') {
                const storedTutorProfileExists = localStorage.getItem('tutorProfileExists');
                if (storedTutorProfileExists !== null) {
                  setTutorProfileExists(JSON.parse(storedTutorProfileExists));
                }
              }
            } catch (parseError) {
              console.error('Failed to parse stored user data:', parseError);
              // Clear corrupted data
              localStorage.removeItem('user');
              localStorage.removeItem('tutorProfileExists');
            }
          }
        }
      }

      setIsLoading(false);
    };

    initAuth();
  }, []);

  const login = async (data: LoginRequest) => {
    const response = await apiLogin(data);
    localStorage.setItem('token', response.access_token);
    localStorage.setItem('refresh_token', response.refresh_token);
    const userData = await getCurrentUser();
    // Store user data in localStorage to persist across refreshes
    localStorage.setItem('user', JSON.stringify(userData));
    setUser(userData);

    // If user is a tutor, check if they have a tutor profile
    if (userData.role === 'tutor') {
      try {
        await getMyTutorProfile();
        setTutorProfileExistsWithPersistence(true);
      } catch (error) {
        // 404 means no tutor profile exists
        if (error.response?.status === 404) {
          setTutorProfileExistsWithPersistence(false);
        } else {
          console.error('Error checking tutor profile:', error);
          setTutorProfileExistsWithPersistence(false);
        }
      }
    }
  };

  const logout = () => {
    localStorage.removeItem('token');
    localStorage.removeItem('refresh_token');
    localStorage.removeItem('user');
    localStorage.removeItem('tutorProfileExists');
    setUser(null);
    setTutorProfileExists(null);
  };

  const refreshUser = async () => {
    try {
      // Check if token exists
      const token = localStorage.getItem('token');
      if (!token) {
        console.error('No token found during refreshUser');
        throw new Error('Authentication token not found. Please log in again.');
      }

      console.log('Refreshing user data with token:', token.substring(0, 10) + '...');
      const userData = await getCurrentUser();
      console.log('User data refreshed successfully:', userData);

      localStorage.setItem('user', JSON.stringify(userData));
      setUser(userData);

      // If user is a tutor, check if they have a tutor profile
      if (userData.role === 'tutor') {
        try {
          await getMyTutorProfile();
          setTutorProfileExistsWithPersistence(true);
        } catch (error) {
          // 404 means no tutor profile exists
          if (error.response?.status === 404) {
            setTutorProfileExistsWithPersistence(false);
          } else {
            console.error('Error checking tutor profile during refresh:', error);
            setTutorProfileExistsWithPersistence(false);
          }
        }
      }

      return userData;
    } catch (error) {
      console.error('Failed to refresh user data:', error);

      // Handle specific error cases
      if (error.response) {
        if (isAuthenticationError(error)) {
          console.error('Authentication token expired or invalid');
          // Force logout and redirect silently
          console.log('Session expired, redirecting to login');
          forceLogout();
          // Don't throw error - let the redirect happen silently
          return null;
        } else if (error.response.status === 403) {
          console.error('User does not have permission to access this resource');
          throw new Error('You do not have permission to access this resource.');
        }
      }

      throw error;
    }
  };

  // Check if user has completed their profile
  // For tutors, having a tutor profile means they're complete (tutor profile creation sets profile_completed)
  // For other users, just basic profile
  const isProfileComplete = user ? (
    user.role === 'tutor'
      ? tutorProfileExists === true  // If tutor profile exists, consider complete
      : user.profile_completed
  ) : false;

  // Debug logging for tutor profile completion
  if (user?.role === 'tutor') {
    console.log('AuthContext - Tutor profile status:', {
      userId: user.id,
      userProfileCompleted: user.profile_completed,
      tutorProfileExists,
      isProfileComplete,
      shouldCheckProfile: tutorProfileExists !== null
    });
  }

  // Don't redirect tutors to profile completion if we're still checking tutor profile status
  const shouldCheckProfile = user?.role === 'tutor' ? tutorProfileExists !== null : true;

  // Check if user has verified their email
  const isEmailVerified = user ? user.email_verified : false;

  return (
    <AuthContext.Provider
      value={{
        user,
        isAuthenticated: !!user,
        isLoading: isLoading || (user?.role === 'tutor' && tutorProfileExists === null),
        login,
        logout,
        refreshUser,
        isProfileComplete: isProfileComplete && shouldCheckProfile,
        isEmailVerified,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};

export const useAuth = (): AuthContextType => {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};
