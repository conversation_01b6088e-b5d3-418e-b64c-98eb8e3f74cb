/**
 * Utility functions for handling authentication errors and session management
 */

/**
 * Clears all authentication data from localStorage
 */
export const clearAuthData = (): void => {
  localStorage.removeItem('token');
  localStorage.removeItem('refresh_token');
  localStorage.removeItem('user');
  localStorage.removeItem('tutorProfileExists');
};

/**
 * Checks if the current path is a public route that doesn't require authentication
 */
export const isPublicRoute = (path: string): boolean => {
  const publicPaths = ['/login', '/register', '/', '/verify-email-pending'];
  return publicPaths.some(publicPath => path.startsWith(publicPath));
};

/**
 * Checks if an error response indicates an authentication failure
 */
export const isAuthenticationError = (error: any): boolean => {
  if (!error.response) {
    // Check for network errors that might indicate auth issues
    const message = error.message?.toLowerCase() || '';
    return (
      message.includes('session has expired') ||
      message.includes('authentication') ||
      message.includes('unauthorized') ||
      message.includes('token')
    );
  }

  const status = error.response.status;
  const detail = error.response.data?.detail || '';
  const detailLower = detail.toLowerCase();

  // 401 is always an authentication error
  if (status === 401) return true;

  // 402 Payment Required - sometimes used for subscription/auth issues
  if (status === 402) return true;

  // 403 can be either authentication or authorization error
  if (status === 403) {
    return (
      detailLower.includes('validate credentials') ||
      detailLower.includes('user not found') ||
      detailLower.includes('session has expired') ||
      detailLower.includes('could not validate credentials') ||
      detailLower.includes('token') ||
      detailLower.includes('authentication') ||
      detailLower.includes('unauthorized')
    );
  }

  // 422 Unprocessable Entity - sometimes used for token validation errors
  if (status === 422) {
    return (
      detailLower.includes('token') ||
      detailLower.includes('authentication') ||
      detailLower.includes('credentials')
    );
  }

  return false;
};

/**
 * Handles authentication errors by clearing session data and redirecting to login
 */
export const handleAuthenticationError = (error: any): void => {
  const currentPath = window.location.pathname;
  
  // Only handle auth errors if we're not already on a public page
  if (isAuthenticationError(error) && !isPublicRoute(currentPath)) {
    console.log('Authentication error detected, clearing session and redirecting to login');
    
    // Clear all authentication data
    clearAuthData();
    
    // Force immediate redirect to login page
    window.location.replace('/login');
  }
};

/**
 * Forces a logout by clearing auth data and redirecting to login
 */
export const forceLogout = (): void => {
  console.log('Forcing logout due to authentication failure');
  clearAuthData();
  window.location.replace('/login');
};

/**
 * Checks if a JWT token is expired or will expire soon
 */
export const isTokenExpired = (token: string, bufferMinutes: number = 5): boolean => {
  try {
    // JWT tokens have 3 parts separated by dots
    const parts = token.split('.');
    if (parts.length !== 3) {
      return true; // Invalid token format
    }

    // Decode the payload (second part)
    const payload = JSON.parse(atob(parts[1]));

    if (!payload.exp) {
      return true; // No expiration time
    }

    // Convert expiration time to milliseconds and add buffer
    const expirationTime = payload.exp * 1000;
    const bufferTime = bufferMinutes * 60 * 1000;
    const now = Date.now();

    return now >= (expirationTime - bufferTime);
  } catch (error) {
    console.error('Error checking token expiration:', error);
    return true; // Assume expired if we can't parse it
  }
};

/**
 * Checks if the current token is valid and not expired
 */
export const isTokenValid = (): boolean => {
  const token = localStorage.getItem('token');
  if (!token) {
    return false;
  }

  return !isTokenExpired(token);
};
