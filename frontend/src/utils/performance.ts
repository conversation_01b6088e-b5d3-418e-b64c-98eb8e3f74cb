/**
 * Performance monitoring utilities for the frontend app
 */

export class PerformanceMonitor {
  private static instance: PerformanceMonitor;
  private startTime: number;
  private metrics: Map<string, number> = new Map();

  private constructor() {
    this.startTime = performance.now();
    this.setupPerformanceObserver();
  }

  static getInstance(): PerformanceMonitor {
    if (!PerformanceMonitor.instance) {
      PerformanceMonitor.instance = new PerformanceMonitor();
    }
    return PerformanceMonitor.instance;
  }

  private setupPerformanceObserver() {
    if ('PerformanceObserver' in window) {
      try {
        // Monitor navigation timing
        const observer = new PerformanceObserver((list) => {
          for (const entry of list.getEntries()) {
            if (entry.entryType === 'navigation') {
              const navEntry = entry as PerformanceNavigationTiming;
              this.logNavigationMetrics(navEntry);
            }
          }
        });
        observer.observe({ entryTypes: ['navigation'] });
      } catch (error) {
        console.warn('Performance observer not supported:', error);
      }
    }
  }

  private logNavigationMetrics(entry: PerformanceNavigationTiming) {
    const metrics = {
      'DNS Lookup': entry.domainLookupEnd - entry.domainLookupStart,
      'TCP Connection': entry.connectEnd - entry.connectStart,
      'Request': entry.responseStart - entry.requestStart,
      'Response': entry.responseEnd - entry.responseStart,
      'DOM Processing': entry.domContentLoadedEventStart - entry.responseEnd,
      'Load Complete': entry.loadEventEnd - entry.loadEventStart,
      'Total Load Time': entry.loadEventEnd - entry.navigationStart,
    };

    console.group('🚀 App Performance Metrics');
    Object.entries(metrics).forEach(([name, value]) => {
      if (value > 0) {
        console.log(`${name}: ${value.toFixed(2)}ms`);
        this.metrics.set(name, value);
      }
    });
    console.groupEnd();

    // Log warnings for slow metrics
    if (metrics['Total Load Time'] > 3000) {
      console.warn('⚠️ Slow app startup detected:', metrics['Total Load Time'].toFixed(2) + 'ms');
    }
  }

  markStart(label: string) {
    performance.mark(`${label}-start`);
  }

  markEnd(label: string) {
    performance.mark(`${label}-end`);
    try {
      performance.measure(label, `${label}-start`, `${label}-end`);
      const measure = performance.getEntriesByName(label)[0];
      console.log(`⏱️ ${label}: ${measure.duration.toFixed(2)}ms`);
      this.metrics.set(label, measure.duration);
    } catch (error) {
      console.warn(`Failed to measure ${label}:`, error);
    }
  }

  getMetrics(): Map<string, number> {
    return new Map(this.metrics);
  }

  logBundleSize() {
    if ('navigator' in window && 'connection' in navigator) {
      const connection = (navigator as any).connection;
      if (connection) {
        console.log('📊 Network Info:', {
          effectiveType: connection.effectiveType,
          downlink: connection.downlink,
          rtt: connection.rtt
        });
      }
    }
  }
}

// Initialize performance monitoring
export const perfMonitor = PerformanceMonitor.getInstance();

// Log initial metrics
if (typeof window !== 'undefined') {
  window.addEventListener('load', () => {
    setTimeout(() => {
      perfMonitor.logBundleSize();
    }, 1000);
  });
}
