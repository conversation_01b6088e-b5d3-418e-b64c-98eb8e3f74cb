/**
 * Utility functions for handling API errors and generating user-friendly messages
 */

export interface ApiError {
  response?: {
    status: number;
    data?: {
      detail?: string | UsageLimitErrorData | Record<string, unknown>;
    };
  };
  code?: string;
  message?: string;
}

export interface UsageLimitErrorData {
  error: string;
  usage_type: string;
  current_usage: number;
  limit: number;
  remaining: number;
  month: string;
  reset_date: string;
  message?: string; // User-friendly message from backend
}

export interface UserFriendlyErrorResult {
  message: string;
  isUsageLimitError: boolean;
  errorData?: UsageLimitErrorData;
}

/**
 * Parse usage limit error and generate user-friendly message
 */
export const parseUsageLimitError = (error: ApiError): UserFriendlyErrorResult => {
  // Check if this is a 429 status code (Too Many Requests)
  if (error.response && error.response.status === 429) {
    const errorData = error.response.data?.detail;
    
    // Check if we have structured usage limit error data
    if (typeof errorData === 'object' && errorData &&
        'error' in errorData && 'usage_type' in errorData &&
        'current_usage' in errorData && 'limit' in errorData &&
        'remaining' in errorData && 'reset_date' in errorData) {

      const usageLimitData = errorData as UsageLimitErrorData;

      // Use backend-provided message if available, otherwise generate one
      let message: string;
      if (usageLimitData.message) {
        message = usageLimitData.message;
      } else {
        const resetDate = new Date(usageLimitData.reset_date).toLocaleDateString();
        const usageType = getUsageTypeDisplayName(usageLimitData.usage_type);
        message = `Monthly ${usageType} limit reached (${usageLimitData.current_usage}/${usageLimitData.limit}). ` +
                 `Your limit will reset on ${resetDate}. You have ${usageLimitData.remaining} ${usageType.toLowerCase()}s remaining this month.`;
      }

      return {
        message,
        isUsageLimitError: true,
        errorData: usageLimitData
      };
    }
    
    // Fallback for 429 errors without structured data
    return {
      message: 'Monthly usage limit reached. Please try again next month or upgrade your plan for higher limits.',
      isUsageLimitError: true
    };
  }
  
  return {
    message: '',
    isUsageLimitError: false
  };
};

/**
 * Get display name for usage type
 */
const getUsageTypeDisplayName = (usageType: string): string => {
  switch (usageType) {
    case 'pdf_upload':
      return 'upload';
    case 'mcq_generation':
      return 'MCQ generation';
    case 'flashcard_generation':
      return 'flashcard generation';
    case 'summary_generation':
      return 'summary generation';
    default:
      return 'usage';
  }
};

/**
 * Generate comprehensive user-friendly error message for any API error
 */
export const generateUserFriendlyErrorMessage = (error: ApiError, defaultMessage?: string): string => {
  // First check if it's a usage limit error
  const usageLimitResult = parseUsageLimitError(error);
  if (usageLimitResult.isUsageLimitError) {
    return usageLimitResult.message;
  }
  
  // Handle other specific error types
  if (error.response && error.response.data) {
    const errorDetail = error.response.data.detail;
    
    // If backend already provided a user-friendly string message, use it
    if (typeof errorDetail === 'string') {
      return errorDetail;
    }
    
    // Handle specific status codes
    switch (error.response.status) {
      case 400:
        return 'Invalid request. Please check your input and try again.';
      case 401:
        return 'Authentication required. Please log in and try again.';
      case 403:
        return 'Access denied. You do not have permission to perform this action.';
      case 404:
        return 'The requested resource was not found. Please try again.';
      case 413:
        return 'File too large. Please upload a smaller file.';
      case 422:
        return 'Invalid data provided. Please check your input and try again.';
      case 500:
        return 'Server error. Please try again later or contact support if the problem persists.';
      case 502:
      case 503:
      case 504:
        return 'Service temporarily unavailable. Please try again in a few minutes.';
      default:
        return errorDetail ? `Error: ${JSON.stringify(errorDetail)}` : 'An unexpected error occurred. Please try again.';
    }
  }
  
  // Handle network and timeout errors
  if (error.code === 'NETWORK_ERROR' || error.message?.includes('Network Error')) {
    return 'Network connection issue. Please check your internet connection and try again.';
  }
  
  if (error.code === 'ECONNABORTED' || error.message?.includes('timeout')) {
    return 'Request timed out. Please try again or check your connection.';
  }
  
  // Use provided default message or generic fallback
  return defaultMessage || error.message || 'An unexpected error occurred. Please try again.';
};

/**
 * Check if error is related to usage limits
 */
export const isUsageLimitError = (error: ApiError): boolean => {
  return parseUsageLimitError(error).isUsageLimitError;
};

/**
 * Get usage limit error data if available
 */
export const getUsageLimitErrorData = (error: ApiError): UsageLimitErrorData | null => {
  const result = parseUsageLimitError(error);
  return result.errorData || null;
};
