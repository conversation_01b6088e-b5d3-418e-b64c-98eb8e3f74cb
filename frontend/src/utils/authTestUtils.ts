/**
 * Utility functions for testing authentication error handling
 * These should only be used in development/testing environments
 */

import { forceLogout } from './authUtils';

/**
 * Simulates an expired token by setting an invalid token in localStorage
 * This can be used to test authentication error handling
 */
export const simulateExpiredToken = (): void => {
  if (process.env.NODE_ENV !== 'development') {
    console.warn('simulateExpiredToken should only be used in development');
    return;
  }
  
  localStorage.setItem('token', 'expired.invalid.token');
  console.log('Simulated expired token set. Next API call should trigger authentication error.');
};

/**
 * Simulates a 401 authentication error
 * This can be used to test error handling flows
 */
export const simulateAuthError = (): void => {
  if (process.env.NODE_ENV !== 'development') {
    console.warn('simulateAuthError should only be used in development');
    return;
  }
  
  console.log('Simulating authentication error...');
  forceLogout();
};

/**
 * Logs current authentication state for debugging
 */
export const logAuthState = (): void => {
  const token = localStorage.getItem('token');
  const user = localStorage.getItem('user');
  const tutorProfile = localStorage.getItem('tutorProfileExists');
  
  console.log('Current Authentication State:', {
    hasToken: !!token,
    tokenPreview: token ? `${token.substring(0, 20)}...` : null,
    hasUser: !!user,
    user: user ? JSON.parse(user) : null,
    tutorProfileExists: tutorProfile,
    currentPath: window.location.pathname
  });
};

// Make these functions available globally in development
if (process.env.NODE_ENV === 'development') {
  (window as any).authTestUtils = {
    simulateExpiredToken,
    simulateAuthError,
    logAuthState,
    forceLogout
  };
  
  console.log('Auth test utilities available at window.authTestUtils');
}
