import { StrictMode } from 'react'
import { createRoot } from 'react-dom/client'
import './index.css'
import App from './App.tsx'

// Load KaTeX CSS asynchronously to avoid blocking initial render
const loadKatexCSS = async () => {
  try {
    await import('katex/dist/katex.min.css');
  } catch (error) {
    console.warn('Failed to load KaTeX CSS:', error);
  }
};

// Load KaTeX after initial render
setTimeout(loadKatexCSS, 100);

createRoot(document.getElementById('root')!).render(
  <StrictMode>
    <App />
  </StrictMode>,
)
