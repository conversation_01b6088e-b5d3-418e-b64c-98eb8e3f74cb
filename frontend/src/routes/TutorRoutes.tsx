import React from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import ProtectedRoute from './ProtectedRoute';
import TutorLayout from '../components/Layout/TutorLayout';
import TutorDashboard from '../pages/tutor/TutorDashboard';
import TutorProfileForm from '../pages/tutor/TutorProfileForm';

import TutorEarnings from '../pages/tutor/TutorEarnings';
import TutorReviews from '../pages/tutor/TutorReviews';
import TutorAnalytics from '../pages/tutor/TutorAnalytics';
import TutorBookingRequests from '../pages/tutor/BookingRequests';

// Import existing session pages that tutors can access
import SessionsList from '../pages/sessions/SessionsList';
import SessionDetail from '../pages/sessions/SessionDetail';
import SessionForm from '../pages/sessions/SessionForm';



const TutorRoutes: React.FC = () => {
  return (
    <Routes>
      {/* Tutor Protected Routes with Layout */}
      <Route element={<ProtectedRoute requiredRole="tutor" />}>
        <Route element={<TutorLayout />}>
          {/* Dashboard */}
          <Route path="/" element={<Navigate to="/tutor/dashboard" replace />} />
          <Route path="/dashboard" element={<TutorDashboard />} />
          
          {/* Profile Management */}
          <Route path="/profile" element={<TutorProfileForm />} />

          {/* Booking Requests */}
          <Route path="/booking-requests" element={<TutorBookingRequests />} />

          {/* Tutorial Management */}
          <Route path="/tutorials" element={<SessionsList />} />
          <Route path="/tutorials/new" element={<SessionForm />} />
          <Route path="/tutorials/:id" element={<SessionDetail />} />
          <Route path="/tutorials/:id/edit" element={<SessionForm />} />

          {/* Legacy session routes - redirect to tutorials */}
          <Route path="/sessions" element={<Navigate to="/tutor/tutorials" replace />} />
          <Route path="/sessions/new" element={<Navigate to="/tutor/tutorials/new" replace />} />
          <Route path="/sessions/:id" element={<Navigate to="/tutor/tutorials/:id" replace />} />
          <Route path="/sessions/:id/edit" element={<Navigate to="/tutor/tutorials/:id/edit" replace />} />

          {/* Tutor-specific pages */}
          <Route path="/earnings" element={<TutorEarnings />} />
          <Route path="/reviews" element={<TutorReviews />} />
          <Route path="/analytics" element={<TutorAnalytics />} />
        </Route>
      </Route>
    </Routes>
  );
};

export default TutorRoutes;
