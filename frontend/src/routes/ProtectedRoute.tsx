import React from 'react';
import { Navigate, Outlet, useLocation } from 'react-router-dom';
import { CircularProgress, Box, Alert } from '@mui/material';
import { useAuth } from '../contexts/AuthContext';
import { clearAuthData } from '../utils/authUtils';

interface ProtectedRouteProps {
  requiredRole?: 'student' | 'tutor' | 'admin';
  children?: React.ReactNode;
  skipProfileCheck?: boolean;
}

const ProtectedRoute: React.FC<ProtectedRouteProps> = ({ requiredRole, children, skipProfileCheck = false }) => {
  const { isAuthenticated, user, isLoading, isProfileComplete, isEmailVerified } = useAuth();
  const location = useLocation();

  // Check if token exists in localStorage but user is not authenticated
  React.useEffect(() => {
    const token = localStorage.getItem('token');
    if (token && !isAuthenticated && !isLoading) {
      console.log('Token exists but user not authenticated, clearing invalid session data');
      clearAuthData();
    }
  }, [isAuthenticated, isLoading]);

  // Show loading state while authentication is being checked
  if (isLoading) {
    return (
      <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '100vh' }}>
        <CircularProgress />
      </Box>
    );
  }

  // Redirect to login if not authenticated
  if (!isAuthenticated) {
    // Clear any stale auth data before redirecting
    clearAuthData();
    // Save the location they were trying to access for redirecting after login
    return <Navigate to="/login" state={{ from: location }} replace />;
  }

  // Check if user needs to verify their email (except for profile completion page)
  if (isAuthenticated && !isEmailVerified && location.pathname !== '/complete-profile') {
    console.log('User needs to verify email, redirecting to email verification pending page');
    return <Navigate to="/verify-email-pending" state={{
      email: user?.email,
      message: 'Please verify your email address before accessing your account.'
    }} />;
  }

  // Check if user needs to complete their profile (only after email is verified)
  if (!skipProfileCheck && isAuthenticated && isEmailVerified && !isProfileComplete) {
    // Route tutors to tutor profile completion, others to regular profile completion
    if (user?.role === 'tutor' && location.pathname !== '/complete-tutor-profile') {
      console.log('Tutor needs to complete profile, redirecting to tutor profile completion page');
      return <Navigate to="/complete-tutor-profile" />;
    } else if (user?.role !== 'tutor' && location.pathname !== '/complete-profile') {
      console.log('User needs to complete profile, redirecting to profile completion page');
      return <Navigate to="/complete-profile" />;
    }
  }

  // Special case: If tutor is on profile completion page but profile is actually complete,
  // let the page handle the redirect to avoid loops
  if (user?.role === 'tutor' && location.pathname === '/complete-tutor-profile' && isProfileComplete) {
    console.log('Tutor on profile completion page but profile is complete - letting page handle redirect');
    // Don't redirect here, let the TutorProfileCompletion page handle it
  }

  // Check if user has the required role
  if (requiredRole && user?.role !== requiredRole) {
    console.log(`Access denied: User role ${user?.role} does not match required role ${requiredRole}`);

    // Different handling based on user role
    if (user?.role === 'student' && (requiredRole === 'tutor' || requiredRole === 'admin')) {
      return (
        <Box sx={{ p: 3 }}>
          <Alert severity="error">
            Students do not have access to {requiredRole === 'tutor' ? 'tutor' : 'admin'} features.
            Please contact your administrator if you believe this is an error.
          </Alert>
        </Box>
      );
    } else if (user?.role === 'tutor' && requiredRole === 'admin') {
      return (
        <Box sx={{ p: 3 }}>
          <Alert severity="error">
            Tutors do not have access to administrative features.
            Please contact your administrator if you believe this is an error.
          </Alert>
        </Box>
      );
    } else if (user?.role === 'tutor' && requiredRole === 'student') {
      return (
        <Box sx={{ p: 3 }}>
          <Alert severity="error">
            This feature is only available to students.
            Tutors have access to different features.
          </Alert>
        </Box>
      );
    } else if (user?.role === 'admin' && (requiredRole === 'student' || requiredRole === 'tutor')) {
      // Admins can access student and tutor features for testing purposes
      console.log(`Admin accessing ${requiredRole} feature`);
      return children ? <>{children}</> : <Outlet />;
    }

    // Default fallback - redirect to role-specific dashboard
    if (user?.role === 'admin') {
      return <Navigate to="/admin/dashboard" />;
    } else if (user?.role === 'tutor') {
      return <Navigate to="/tutor/dashboard" />;
    } else {
      return <Navigate to="/dashboard" />; // Student dashboard
    }
  }

  // If children are provided, render them, otherwise use Outlet
  return children ? <>{children}</> : <Outlet />;
};

export default ProtectedRoute;
