import React, { Suspense, lazy } from 'react';
import { Routes, Route, Navigate } from 'react-router-dom';
import { Box, CircularProgress } from '@mui/material';
import MainLayout from '../components/Layout/MainLayout';
import DashboardLayout from '../components/Layout/DashboardLayout';
import ExamLayout from '../components/Layout/ExamLayout';
import ProtectedRoute from './ProtectedRoute';

// Critical pages loaded immediately
import LandingPage from '../pages/LandingPage';
import LoginNew from '../pages/auth/LoginNew';
import RegisterEnhanced from '../pages/auth/RegisterEnhanced';

// Test components (development only)
const MathRenderingTest = lazy(() => import('../components/test/MathRenderingTest'));

// Lazy load heavy components
const AdminRoutes = lazy(() => import('./AdminRoutes'));
const TutorRoutes = lazy(() => import('./TutorRoutes'));
const Dashboard = lazy(() => import('../pages/Dashboard'));
const EmailVerification = lazy(() => import('../pages/auth/EmailVerification'));
const EmailVerificationPending = lazy(() => import('../pages/auth/EmailVerificationPending'));
const ForgotPassword = lazy(() => import('../pages/ForgotPassword'));
const ResetPasswordNew = lazy(() => import('../pages/ResetPasswordNew'));

// MCQ pages - lazy loaded
const MCQDashboard = lazy(() => import('../pages/mcq/MCQDashboard'));
const PracticeMode = lazy(() => import('../pages/mcq/PracticeMode'));
const ExamMode = lazy(() => import('../pages/mcq/ExamMode'));
const ExamResults = lazy(() => import('../pages/mcq/ExamResults'));
const MCQHistory = lazy(() => import('../pages/mcq/MCQHistory'));
const MCQBookmarks = lazy(() => import('../pages/mcq/MCQBookmarks'));
const ExamHistory = lazy(() => import('../pages/mcq/ExamHistory'));
const ExamDetails = lazy(() => import('../pages/mcq/ExamDetails'));

// Performance Analytics - lazy loaded
const PerformanceAnalytics = lazy(() => import('../pages/PerformanceAnalytics'));

// Flashcard pages - lazy loaded
const FlashcardPractice = lazy(() => import('../pages/flashcards/FlashcardPractice'));
const CourseFlashcardDashboard = lazy(() => import('../pages/flashcards/CourseFlashcardDashboard'));

// School pages - lazy loaded
const SchoolsList = lazy(() => import('../pages/schools/SchoolsList'));
const SchoolDetail = lazy(() => import('../pages/schools/SchoolDetail'));
const SchoolForm = lazy(() => import('../pages/schools/SchoolForm'));

// Department pages - lazy loaded
const DepartmentsList = lazy(() => import('../pages/departments/DepartmentsList'));
const DepartmentDetail = lazy(() => import('../pages/departments/DepartmentDetail'));
const DepartmentForm = lazy(() => import('../pages/departments/DepartmentForm'));

// Course pages - lazy loaded
const CoursesList = lazy(() => import('../pages/courses/CoursesList'));
const CourseDetail = lazy(() => import('../pages/courses/CourseDetail'));
const CourseForm = lazy(() => import('../pages/courses/CourseForm'));

// Question pages - lazy loaded
const QuestionsList = lazy(() => import('../pages/questions/QuestionsList'));
const QuestionDetail = lazy(() => import('../pages/questions/QuestionDetail'));
const QuestionForm = lazy(() => import('../pages/questions/QuestionForm'));

// Session pages - lazy loaded
const SessionsList = lazy(() => import('../pages/sessions/SessionsList'));
const SessionDetail = lazy(() => import('../pages/sessions/SessionDetail'));
const SessionForm = lazy(() => import('../pages/sessions/SessionForm'));

// User pages - lazy loaded
const UsersList = lazy(() => import('../pages/users/UsersList'));
const UserDetail = lazy(() => import('../pages/users/UserDetail'));
const UserForm = lazy(() => import('../pages/users/UserForm'));
const Profile = lazy(() => import('../pages/profile/Profile'));
const SimpleProfileCompletion = lazy(() => import('../pages/profile/SimpleProfileCompletion'));
const Gamification = lazy(() => import('../pages/Gamification'));
const ToolsDashboard = lazy(() => import('../pages/tools/ToolsDashboard'));
const PDFUploadPage = lazy(() => import('../pages/tools/PDFUploadPage'));
const MCQGenerationPage = lazy(() => import('../pages/tools/MCQGenerationPage'));
const FlashcardGenerationPage = lazy(() => import('../pages/tools/FlashcardGenerationPage'));
const NoteExplanationPage = lazy(() => import('../pages/tools/NoteExplanationPage'));

const MyNotesPage = lazy(() => import('../pages/tools/MyNotesPage'));

// Sharing components - lazy loaded
const AcceptSharePageNew = lazy(() => import('../components/sharing/AcceptSharePageNew'));

const TutorDiscovery = lazy(() => import('../pages/tutors/TutorDiscovery'));
const TutorProfile = lazy(() => import('../pages/tutors/TutorProfile'));
const FindTutors = lazy(() => import('../pages/tutors/FindTutors'));
const StudentBookingRequests = lazy(() => import('../pages/student/BookingRequests'));
const ChatPage = lazy(() => import('../pages/chat/ChatPage'));
const TutorProfileCompletion = lazy(() => import('../pages/tutor/TutorProfileCompletion'));
const TutorRedirect = lazy(() => import('../components/TutorRedirect'));

// Legal pages - lazy loaded
const TermsAndConditions = lazy(() => import('../pages/legal/TermsAndConditions'));
const PrivacyPolicy = lazy(() => import('../pages/legal/PrivacyPolicy'));

// Loading component
const PageLoader = () => (
  <Box sx={{ display: 'flex', justifyContent: 'center', alignItems: 'center', height: '50vh' }}>
    <CircularProgress />
  </Box>
);

const AppRoutes: React.FC = () => {
  return (
    <Suspense fallback={<PageLoader />}>
      <Routes>
        {/* Admin Routes - Separate from main app */}
        <Route path="/admin/*" element={<AdminRoutes />} />

        {/* Tutor Routes - Separate layout for tutors */}
        <Route path="/tutor/*" element={<TutorRoutes />} />

      {/* Landing page outside of MainLayout */}
      <Route path="/" element={<LandingPage />} />

      {/* Login page outside of MainLayout */}
      <Route path="/login" element={<LoginNew />} />

      {/* Register page outside of MainLayout */}
      <Route path="/register" element={<RegisterEnhanced />} />

      {/* Email verification pages outside of MainLayout */}
      <Route path="/verify-email" element={<EmailVerification />} />
      <Route path="/verify-email-pending" element={<EmailVerificationPending />} />

      {/* Password reset pages outside of MainLayout */}
      <Route path="/forgot-password" element={<ForgotPassword />} />
      <Route path="/reset-password" element={<ResetPasswordNew />} />

      {/* Legal pages outside of MainLayout */}
      <Route path="/terms" element={<TermsAndConditions />} />
      <Route path="/privacy" element={<PrivacyPolicy />} />

      {/* Development test pages */}
      <Route path="/test/math-rendering" element={<MathRenderingTest />} />

      {/* Profile completion page outside of MainLayout */}
      <Route path="/complete-profile" element={
        <ProtectedRoute skipProfileCheck={true}>
          <SimpleProfileCompletion />
        </ProtectedRoute>
      } />

      {/* Tutor profile completion page outside of MainLayout */}
      <Route path="/complete-tutor-profile" element={
        <ProtectedRoute requiredRole="tutor" skipProfileCheck={true}>
          <TutorProfileCompletion />
        </ProtectedRoute>
      } />

      {/* Dashboard with its own layout - Student only */}
      <Route path="/dashboard" element={
        <ProtectedRoute requiredRole="student">
          <DashboardLayout>
            <Dashboard />
          </DashboardLayout>
        </ProtectedRoute>
      } />

      {/* Exam mode with special layout (no header/footer) */}
      <Route path="/mcq/exam/:courseId" element={
        <ProtectedRoute requiredRole="student">
          <ExamLayout>
            <ExamMode />
          </ExamLayout>
        </ProtectedRoute>
      } />

      {/* Generated questions exam mode with clean URL */}
      <Route path="/mcq/exam/generated" element={
        <ProtectedRoute requiredRole="student">
          <ExamLayout>
            <ExamMode />
          </ExamLayout>
        </ProtectedRoute>
      } />

      {/* Exam results page */}
      <Route path="/mcq/results/:examId" element={
        <ProtectedRoute requiredRole="student">
          <MainLayout>
            <ExamResults />
          </MainLayout>
        </ProtectedRoute>
      } />

      {/* All other routes inside MainLayout */}
      <Route
        path="/*"
        element={
          <MainLayout>
            <Routes>
              {/* Routes for all authenticated users */}
              <Route element={<ProtectedRoute />}>
                {/* Common routes for all users */}
                <Route path="/profile" element={<Profile />} />

                {/* Chat routes for students and tutors */}
                <Route path="/chat" element={<ChatPage />} />
                <Route path="/chat/:roomId" element={<ChatPage />} />

                {/* Read-only routes for all users */}
                <Route path="/schools" element={<SchoolsList />} />
                <Route path="/schools/:id" element={<SchoolDetail />} />

                <Route path="/departments" element={<DepartmentsList />} />
                <Route path="/departments/:id" element={<DepartmentDetail />} />

                <Route path="/courses" element={<CoursesList />} />
                <Route path="/courses/:id" element={<CourseDetail />} />

                {/* Questions routes restricted to tutors and admins */}
                <Route element={<ProtectedRoute requiredRole="tutor" />}>
                  <Route path="/questions" element={<QuestionsList />} />
                  <Route path="/questions/:id" element={<QuestionDetail />} />
                </Route>
              </Route>

              {/* Student-specific routes */}
              <Route element={<ProtectedRoute requiredRole="student" />}>
                <Route path="/mcq" element={<MCQDashboard />} />
                <Route path="/mcq/practice/generated" element={<PracticeMode />} />
                <Route path="/mcq/practice/:courseId" element={<PracticeMode />} />
                <Route path="/mcq/history" element={<MCQHistory />} />
                <Route path="/mcq/bookmarks" element={<MCQBookmarks />} />
                <Route path="/mcq/exam-history" element={<ExamHistory />} />
                <Route path="/mcq/exam-details/:examId" element={<ExamDetails />} />
                <Route path="/analytics" element={<PerformanceAnalytics />} />
                <Route path="/gamification" element={<Gamification />} />
                <Route path="/share/:shareToken" element={<AcceptSharePageNew />} />
                <Route path="/share/accept" element={<AcceptSharePageNew />} />

                {/* Flashcard routes */}
                <Route path="/flash-cards/courses" element={<CourseFlashcardDashboard />} />
                <Route path="/flashcards/practice/:courseId" element={<FlashcardPractice />} />

                {/* Tools routes */}
                <Route path="/tools" element={<ToolsDashboard />} />
                <Route path="/tools/upload" element={<PDFUploadPage />} />
                <Route path="/tools/mcq" element={<MCQGenerationPage />} />
                <Route path="/tools/flashcards" element={<FlashcardGenerationPage />} />
                <Route path="/tools/note-explanation" element={<NoteExplanationPage />} />
                <Route path="/my-notes" element={<MyNotesPage />} />
                <Route path="/booking-requests" element={<StudentBookingRequests />} />
              </Route>

              {/* Tutorial routes - accessible to both students and tutors */}
              <Route path="/tutorials" element={
                <TutorRedirect tutorPath="/tutor/tutorials">
                  <SessionsList />
                </TutorRedirect>
              } />
              <Route path="/tutorials/new" element={
                <TutorRedirect tutorPath="/tutor/tutorials">
                  <SessionForm />
                </TutorRedirect>
              } />
              <Route path="/tutorials/:id" element={
                <TutorRedirect tutorPath="/tutor/tutorials" preserveParams={true}>
                  <SessionDetail />
                </TutorRedirect>
              } />
              <Route path="/tutorials/:id/edit" element={
                <TutorRedirect tutorPath="/tutor/tutorials" preserveParams={true}>
                  <SessionForm />
                </TutorRedirect>
              } />

              {/* Tutor finding routes - accessible to both students and tutors */}
              <Route path="/tutors" element={
                <TutorRedirect tutorPath="/tutor/dashboard">
                  <FindTutors />
                </TutorRedirect>
              } />
              <Route path="/tutors/:id" element={
                <TutorRedirect tutorPath="/tutor/dashboard" preserveParams={true}>
                  <TutorProfile />
                </TutorRedirect>
              } />

              {/* Legacy session routes - redirect to tutorials */}
              <Route path="/sessions/new" element={<Navigate to="/tutorials/new" replace />} />
              <Route path="/sessions/:id/edit" element={<Navigate to="/tutorials/:id/edit" replace />} />

              {/* Admin-only routes are now handled by AdminRoutes */}

              {/* Fallback route */}
              <Route path="*" element={<Navigate to="/" replace />} />
            </Routes>
          </MainLayout>
        }
      />
      </Routes>
    </Suspense>
  );
};

export default AppRoutes;
