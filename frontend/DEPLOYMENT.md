# Frontend Deployment Guide

## Vercel Deployment

### Prerequisites
1. Vercel account
2. GitHub repository connected to Vercel
3. Backend API deployed and accessible

### Environment Variables
Set the following environment variables in your Vercel project settings:

```
VITE_API_URL=https://your-backend-domain.com/api/v1
```

### Deployment Steps

#### Option 1: Automatic Deployment (Recommended)
1. Connect your GitHub repository to Vercel
2. Set the root directory to `frontend`
3. Vercel will automatically detect the Vite configuration
4. Set environment variables in Vercel dashboard
5. Deploy automatically on every push to main branch

#### Option 2: Manual Deployment
1. Install Vercel CLI: `npm i -g vercel`
2. Navigate to frontend directory: `cd frontend`
3. Login to Vercel: `vercel login`
4. Deploy: `vercel --prod`

### Build Configuration
- **Framework**: Vite
- **Build Command**: `npm run build:production`
- **Output Directory**: `dist`
- **Install Command**: `npm install`

### Environment Files
- `.env.production` - Production environment variables
- `.env.local.example` - Example for local development
- `vercel.json` - Vercel configuration for SPA routing

### Important Notes
1. The app is configured as a Single Page Application (SPA)
2. All routes are handled client-side via React Router
3. API calls are made to the backend URL specified in `VITE_API_URL`
4. Static assets are cached for 1 year for optimal performance

### Troubleshooting
- If routes don't work, ensure `vercel.json` is properly configured
- If API calls fail, check the `VITE_API_URL` environment variable
- For build errors, check TypeScript compilation issues

### Performance Optimizations
- Assets are automatically optimized by Vite
- Code splitting is enabled for better loading performance
- Static assets have long-term caching headers
