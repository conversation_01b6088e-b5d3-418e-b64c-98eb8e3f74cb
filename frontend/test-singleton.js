// Simple test to verify the singleton behavior
// This would be run in a browser console to test the upload manager

// Simulate multiple components using the upload manager
console.log('Testing Upload Manager Singleton...');

// Mock the API call
let apiCallCount = 0;
const mockGetUploadList = () => {
  apiCallCount++;
  console.log(`API call #${apiCallCount} at ${new Date().toISOString()}`);
  return Promise.resolve({
    uploads: [],
    total: 0,
    active_count: 0,
    completed_count: 0,
    failed_count: 0
  });
};

// Replace the actual API call with our mock
window.mockGetUploadList = mockGetUploadList;

// Test: Create multiple "components" that would use useUploadManager
const simulateComponent = (name) => {
  console.log(`${name} component initialized`);
  
  // This simulates what useUploadManager does
  const manager = {
    subscribe: (callback) => {
      console.log(`${name} subscribed to upload manager`);
      return () => console.log(`${name} unsubscribed from upload manager`);
    }
  };
  
  return manager;
};

// Simulate 3 components using the upload manager
const component1 = simulateComponent('GlobalUploadIndicator');
const component2 = simulateComponent('UploadStatusBanner');
const component3 = simulateComponent('GeminiPDFUploadTool');

console.log('Test completed. Check that only one polling interval is created.');
console.log('Expected: Single polling interval, not multiple intervals per component');
