{"name": "frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "./vite-compat", "build": "vite build --mode production", "build:production": "NODE_ENV=production tsc -b && vite build", "build:vercel": "vite build --mode production", "build:skip-ts": "vite build --mode production", "vercel-build": "vite build --mode production", "lint": "eslint .", "preview": "./vite-compat preview", "deploy": "npm run build:production"}, "dependencies": {"@emotion/react": "^11.14.0", "@emotion/styled": "^11.14.0", "@mui/icons-material": "^7.0.2", "@mui/material": "^7.0.2", "@mui/x-date-pickers": "^8.1.0", "@tanstack/react-query": "^5.75.1", "axios": "^1.9.0", "date-fns": "^4.1.0", "formik": "^2.4.6", "framer-motion": "^12.9.4", "katex": "^0.16.0", "mathjax-full": "^3.2.2", "react": "^18.3.1", "react-dom": "^18.3.1", "react-markdown": "^10.1.0", "react-router-dom": "^6.28.0", "recharts": "^2.15.3", "rehype-katex": "^7.0.0", "remark-math": "^6.0.0", "yup": "^1.6.1"}, "devDependencies": {"@eslint/js": "^9.22.0", "@types/node": "^24.0.8", "@types/react": "^18.3.12", "@types/react-dom": "^18.3.1", "@vitejs/plugin-react": "^4.3.4", "eslint": "^9.22.0", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "typescript": "~5.7.2", "typescript-eslint": "^8.26.1", "vite": "^6.3.1"}}