<?xml version="1.0" encoding="UTF-8"?>
<svg version="1.1" xmlns="http://www.w3.org/2000/svg" width="120" height="120" viewBox="0 0 120 120">
  <defs>
    <linearGradient id="logoGradient" x1="0%" y1="0%" x2="100%" y2="100%">
      <stop offset="0%" style="stop-color:#009287;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#00b4a6;stop-opacity:1" />
    </linearGradient>
  </defs>
  
  <!-- Background circle -->
  <circle cx="60" cy="60" r="55" fill="url(#logoGradient)" stroke="#ffffff" stroke-width="2"/>
  
  <!-- Letter C -->
  <path d="M35 40 Q25 40 25 60 Q25 80 35 80 Q45 80 50 75" 
        fill="none" stroke="#ffffff" stroke-width="6" stroke-linecap="round"/>
  
  <!-- Letter P -->
  <path d="M60 40 L60 80 M60 40 L75 40 Q85 40 85 50 Q85 60 75 60 L60 60" 
        fill="none" stroke="#ffffff" stroke-width="6" stroke-linecap="round" stroke-linejoin="round"/>
  
  <!-- Letter Q -->
  <circle cx="85" cy="65" r="15" fill="none" stroke="#ffffff" stroke-width="6"/>
  <path d="M92 72 L100 80" fill="none" stroke="#ffffff" stroke-width="6" stroke-linecap="round"/>
  
  <!-- Small decorative elements -->
  <circle cx="30" cy="25" r="3" fill="#ffffff" opacity="0.8"/>
  <circle cx="90" cy="25" r="3" fill="#ffffff" opacity="0.8"/>
  <circle cx="30" cy="95" r="3" fill="#ffffff" opacity="0.8"/>
</svg>
