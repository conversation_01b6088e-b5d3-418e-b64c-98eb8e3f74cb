import { defineConfig } from 'vite'
import react from '@vitejs/plugin-react'
import path from 'path'

// https://vite.dev/config/
export default defineConfig({
  plugins: [react()],
  base: '/',
  server: {
    fs: {
      // Allow serving files from one level up to the project root
      allow: [
        // Allow serving files from the project root
        path.resolve(__dirname, '..'),
      ],
    },
  },
  optimizeDeps: {
    include: [
      'katex',
      'react',
      'react-dom',
      'react-router-dom',
      '@emotion/react',
      '@emotion/styled',
      '@mui/material',
      '@mui/icons-material',
      '@tanstack/react-query',
      'framer-motion',
      'axios'
    ],
    force: false, // Only force when needed
  },
  build: {
    commonjsOptions: {
      include: [/katex/, /node_modules/],
      transformMixedEsModules: true,
    },
    rollupOptions: {
      output: {
        manualChunks: {
          vendor: ['react', 'react-dom', 'react-router-dom'],
          mui: ['@mui/material', '@mui/icons-material', '@emotion/react', '@emotion/styled'],
          utils: ['axios', '@tanstack/react-query', 'framer-motion'],
          math: ['katex', 'react-markdown', 'remark-math', 'rehype-katex']
        },
      },
    },
    assetsDir: 'assets',
    copyPublicDir: true,
    target: 'esnext',
    minify: 'esbuild',
  },
  publicDir: 'public',
  resolve: {
    alias: {
      'react': path.resolve(__dirname, 'node_modules/react'),
      'react-dom': path.resolve(__dirname, 'node_modules/react-dom'),
    },
  },
  define: {
    'process.env.NODE_ENV': JSON.stringify(process.env.NODE_ENV || 'development'),
  },
})
