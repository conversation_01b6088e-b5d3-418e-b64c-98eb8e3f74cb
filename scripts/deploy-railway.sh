#!/bin/bash

# Simple Railway deployment script
# This deploys the backend with Celery worker support

set -e

echo "🚂 Deploying to Railway..."
echo "========================="
echo

# Check if we have Railway CLI
if ! command -v railway &> /dev/null; then
    echo "❌ Railway CLI not found. Please install it:"
    echo "   npm install -g @railway/cli"
    echo "   railway login"
    exit 1
fi

# Check if logged in
if ! railway whoami &> /dev/null; then
    echo "❌ Not logged into Railway. Please run:"
    echo "   railway login"
    exit 1
fi

echo "✅ Railway CLI ready"
echo

# Deploy
echo "🚀 Deploying..."
railway up

echo
echo "✅ Deployment complete!"
echo
echo "📋 Next steps:"
echo "  1. Set REDIS_URL in Railway dashboard"
echo "  2. Set START_MODE=worker in Railway dashboard"
echo "  3. Test note upload functionality"
echo
echo "🔧 To get Redis URL:"
echo "  - Go to https://redis.com/try-free/"
echo "  - Create free database"
echo "  - Copy Redis URL to Railway variables"
echo
