#!/bin/bash

# Debug Railway deployment and check logs
# This script helps you see what's happening in production

set -e

# Colors for output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo "🔍 Railway Debug Tool"
    echo "===================="
    echo
}

check_railway_cli() {
    if ! command -v railway &> /dev/null; then
        log_error "Railway CLI not found. Please install it first:"
        echo "  npm install -g @railway/cli"
        echo "  railway login"
        exit 1
    fi
    
    log_info "Railway CLI found ✓"
}

check_railway_login() {
    if ! railway whoami &> /dev/null; then
        log_error "Not logged into Railway. Please run:"
        echo "  railway login"
        exit 1
    fi
    
    log_info "Railway authentication verified ✓"
}

show_deployment_status() {
    log_info "Getting deployment status..."
    echo
    
    # Get Railway status
    railway status || log_warning "Could not get Railway status"
    echo
}

show_environment_variables() {
    log_info "Checking environment variables..."
    echo
    
    # Check critical variables
    echo "🔧 Critical Variables:"
    
    if railway variables get REDIS_URL &> /dev/null; then
        log_success "REDIS_URL is set"
    else
        log_error "REDIS_URL is NOT set"
    fi
    
    if railway variables get QDRANT_URL &> /dev/null; then
        log_success "QDRANT_URL is set"
    else
        log_warning "QDRANT_URL is NOT set"
    fi
    
    if railway variables get DATABASE_URL &> /dev/null; then
        log_success "DATABASE_URL is set"
    else
        log_error "DATABASE_URL is NOT set"
    fi
    
    if railway variables get SECRET_KEY &> /dev/null; then
        log_success "SECRET_KEY is set"
    else
        log_error "SECRET_KEY is NOT set"
    fi
    
    echo
    echo "📋 All Variables:"
    railway variables list || log_warning "Could not list variables"
    echo
}

show_recent_logs() {
    log_info "Showing recent deployment logs..."
    echo
    
    echo "📋 Recent Logs (last 50 lines):"
    railway logs --lines 50 || log_warning "Could not get logs"
    echo
}

test_api_endpoints() {
    log_info "Testing API endpoints..."
    echo
    
    # Get Railway URL
    RAILWAY_URL=$(railway status --json 2>/dev/null | grep -o '"url":"[^"]*"' | cut -d'"' -f4 || echo "")
    
    if [ -n "$RAILWAY_URL" ]; then
        echo "🌐 Railway URL: $RAILWAY_URL"
        echo
        
        # Test health endpoint
        echo "🧪 Testing health endpoint..."
        if curl -f -s "$RAILWAY_URL/api/v1/health" > /dev/null; then
            log_success "Health endpoint is responding ✓"
        else
            log_error "Health endpoint is not responding ✗"
        fi
        
        # Test docs endpoint
        echo "🧪 Testing docs endpoint..."
        if curl -f -s "$RAILWAY_URL/api/v1/docs" > /dev/null; then
            log_success "Docs endpoint is responding ✓"
        else
            log_warning "Docs endpoint is not responding"
        fi
        
    else
        log_warning "Could not determine Railway URL"
    fi
    echo
}

show_celery_debug_info() {
    log_info "Celery Debug Information..."
    echo
    
    echo "🔍 Looking for Celery-related logs..."
    railway logs --lines 100 | grep -i celery || echo "No Celery logs found"
    echo
    
    echo "🔍 Looking for Redis-related logs..."
    railway logs --lines 100 | grep -i redis || echo "No Redis logs found"
    echo
    
    echo "🔍 Looking for worker-related logs..."
    railway logs --lines 100 | grep -i worker || echo "No worker logs found"
    echo
}

show_note_upload_debug() {
    log_info "Note Upload Debug Information..."
    echo
    
    echo "🔍 Looking for note upload logs..."
    railway logs --lines 100 | grep -i "note\|upload\|process_note" || echo "No note upload logs found"
    echo
    
    echo "🔍 Looking for task-related logs..."
    railway logs --lines 100 | grep -i "task\|queue" || echo "No task logs found"
    echo
}

main() {
    print_header
    
    check_railway_cli
    check_railway_login
    
    echo "This tool will help you debug your Railway deployment."
    echo "It will check logs, environment variables, and test endpoints."
    echo
    
    show_deployment_status
    show_environment_variables
    test_api_endpoints
    show_celery_debug_info
    show_note_upload_debug
    show_recent_logs
    
    echo
    log_info "Debug complete!"
    echo
    echo "📋 Next Steps:"
    echo "  1. Check if REDIS_URL and other variables are set correctly"
    echo "  2. Look for error messages in the logs above"
    echo "  3. Test note upload functionality"
    echo "  4. If issues persist, check Railway dashboard for more details"
    echo
}

main "$@"
