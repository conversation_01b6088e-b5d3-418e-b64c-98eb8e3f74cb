#!/bin/bash

# CampusPQ Initial Setup Script
# This script sets up the initial AWS infrastructure and prepares for deployment

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
AWS_REGION=${AWS_REGION:-us-east-1}
PROJECT_NAME="campuspq"
ENVIRONMENT=${1:-prod}

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

check_prerequisites() {
    log_info "Checking prerequisites..."
    
    # Check if required tools are installed
    command -v aws >/dev/null 2>&1 || { log_error "AWS CLI is required but not installed. Please install it first."; exit 1; }
    command -v terraform >/dev/null 2>&1 || { log_error "Terraform is required but not installed. Please install it first."; exit 1; }
    command -v docker >/dev/null 2>&1 || { log_error "Docker is required but not installed. Please install it first."; exit 1; }
    command -v jq >/dev/null 2>&1 || { log_error "jq is required but not installed. Please install it first."; exit 1; }
    
    # Check AWS credentials
    aws sts get-caller-identity >/dev/null 2>&1 || { log_error "AWS credentials not configured. Please run 'aws configure' first."; exit 1; }
    
    log_success "Prerequisites check passed"
}

get_aws_account_id() {
    aws sts get-caller-identity --query Account --output text
}

create_terraform_backend() {
    log_info "Setting up Terraform backend..."
    
    local bucket_name="${PROJECT_NAME}-terraform-state-$(get_aws_account_id)"
    local table_name="${PROJECT_NAME}-terraform-locks"
    
    # Create S3 bucket for Terraform state
    if ! aws s3 ls "s3://$bucket_name" >/dev/null 2>&1; then
        log_info "Creating S3 bucket for Terraform state: $bucket_name"
        aws s3 mb "s3://$bucket_name" --region $AWS_REGION
        
        # Enable versioning
        aws s3api put-bucket-versioning \
            --bucket "$bucket_name" \
            --versioning-configuration Status=Enabled
        
        # Enable encryption
        aws s3api put-bucket-encryption \
            --bucket "$bucket_name" \
            --server-side-encryption-configuration '{
                "Rules": [
                    {
                        "ApplyServerSideEncryptionByDefault": {
                            "SSEAlgorithm": "AES256"
                        }
                    }
                ]
            }'
        
        # Block public access
        aws s3api put-public-access-block \
            --bucket "$bucket_name" \
            --public-access-block-configuration \
            BlockPublicAcls=true,IgnorePublicAcls=true,BlockPublicPolicy=true,RestrictPublicBuckets=true
    else
        log_info "S3 bucket already exists: $bucket_name"
    fi
    
    # Create DynamoDB table for state locking
    if ! aws dynamodb describe-table --table-name "$table_name" >/dev/null 2>&1; then
        log_info "Creating DynamoDB table for Terraform state locking: $table_name"
        aws dynamodb create-table \
            --table-name "$table_name" \
            --attribute-definitions AttributeName=LockID,AttributeType=S \
            --key-schema AttributeName=LockID,KeyType=HASH \
            --provisioned-throughput ReadCapacityUnits=5,WriteCapacityUnits=5 \
            --region $AWS_REGION
        
        # Wait for table to be created
        aws dynamodb wait table-exists --table-name "$table_name" --region $AWS_REGION
    else
        log_info "DynamoDB table already exists: $table_name"
    fi
    
    # Update Terraform backend configuration
    local backend_config="terraform/backend.tf"
    cat > "$backend_config" << EOF
terraform {
  backend "s3" {
    bucket         = "$bucket_name"
    key            = "infrastructure/terraform.tfstate"
    region         = "$AWS_REGION"
    dynamodb_table = "$table_name"
    encrypt        = true
  }
}
EOF
    
    log_success "Terraform backend configured"
}

create_ssl_certificate() {
    log_info "Setting up SSL certificate..."
    
    local domain_name
    read -p "Enter your domain name (e.g., campuspq.com): " domain_name
    
    if [ -z "$domain_name" ]; then
        log_warning "No domain provided. You'll need to create an SSL certificate manually later."
        return
    fi
    
    # Request certificate
    local cert_arn=$(aws acm request-certificate \
        --domain-name "$domain_name" \
        --subject-alternative-names "*.$domain_name" \
        --validation-method DNS \
        --region $AWS_REGION \
        --query CertificateArn \
        --output text)
    
    log_info "SSL certificate requested: $cert_arn"
    log_warning "You need to validate the certificate via DNS before proceeding with deployment."
    log_info "Check your ACM console: https://$AWS_REGION.console.aws.amazon.com/acm/home?region=$AWS_REGION"
    
    # Update terraform variables
    sed -i "s|ssl_certificate_arn = \"\"|ssl_certificate_arn = \"$cert_arn\"|" terraform/environments/prod.tfvars
    sed -i "s|domain_name = \"campuspq.com\"|domain_name = \"$domain_name\"|" terraform/environments/prod.tfvars
    sed -i "s|frontend_url = \"https://campuspq.com\"|frontend_url = \"https://$domain_name\"|" terraform/environments/prod.tfvars
    sed -i "s|emails_from_email = \"<EMAIL>\"|emails_from_email = \"noreply@$domain_name\"|" terraform/environments/prod.tfvars
}

setup_github_secrets() {
    log_info "GitHub Secrets Setup Instructions"
    echo
    echo "You need to configure the following secrets in your GitHub repository:"
    echo "Go to: Settings > Secrets and variables > Actions"
    echo
    echo "Required secrets:"
    echo "- AWS_ACCESS_KEY_ID: Your AWS access key"
    echo "- AWS_SECRET_ACCESS_KEY: Your AWS secret key"
    echo "- AWS_ACCOUNT_ID: $(get_aws_account_id)"
    echo "- DB_PASSWORD: Secure database password"
    echo "- SECRET_KEY: Application secret key (generate with: openssl rand -base64 32)"
    echo "- OPENAI_API_KEY: Your OpenAI API key"
    echo "- GEMINI_API_KEY: Your Google Gemini API key"
    echo "- CLOUDINARY_URL: Your Cloudinary URL"
    echo "- CLOUDINARY_API_KEY: Your Cloudinary API key"
    echo "- CLOUDINARY_API_SECRET: Your Cloudinary API secret"
    echo "- SMTP_USER: Your SMTP username"
    echo "- SMTP_PASSWORD: Your SMTP password"
    echo "- DOMAIN_NAME: Your domain name"
    echo
    read -p "Press Enter after configuring GitHub secrets..."
}

create_env_file() {
    log_info "Creating environment file..."
    
    local env_file=".env.${ENVIRONMENT}"
    
    if [ ! -f "$env_file" ]; then
        cp backend/.env.example "$env_file"
        log_info "Created $env_file - please update it with your values"
    else
        log_info "Environment file $env_file already exists"
    fi
}

initialize_terraform() {
    log_info "Initializing Terraform..."
    
    cd terraform
    terraform init
    terraform validate
    cd ..
    
    log_success "Terraform initialized successfully"
}

show_next_steps() {
    log_info "Setup completed! Next steps:"
    echo
    echo "1. Update your environment file (.env.${ENVIRONMENT}) with actual values"
    echo "2. Validate your SSL certificate in ACM console"
    echo "3. Configure GitHub secrets as shown above"
    echo "4. Run the deployment:"
    echo "   ./scripts/deploy.sh $ENVIRONMENT"
    echo
    echo "5. Or deploy via GitHub Actions by pushing to main branch"
    echo
    echo "Useful commands:"
    echo "- View infrastructure plan: cd terraform && terraform plan -var-file=environments/${ENVIRONMENT}.tfvars"
    echo "- Deploy infrastructure only: ./scripts/deploy.sh $ENVIRONMENT (choose option 1)"
    echo "- Rollback deployment: ./scripts/rollback.sh $ENVIRONMENT"
    echo
}

# Main setup flow
main() {
    log_info "Starting CampusPQ AWS setup for environment: $ENVIRONMENT"
    echo
    
    check_prerequisites
    create_terraform_backend
    create_ssl_certificate
    create_env_file
    initialize_terraform
    setup_github_secrets
    show_next_steps
    
    log_success "Setup completed successfully!"
}

# Run main function
main "$@"
