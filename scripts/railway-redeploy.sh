#!/bin/bash

# Quick redeploy to Railway after fixing syntax error

set -e

# Colors for output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

echo "🚂 Railway Redeploy"
echo "==================="
echo

log_info "All f-string syntax errors in email_service.py have been fixed!"
log_info "Fixed issues:"
echo "  ✅ Line 91-95: Removed backslashes from f-string expressions"
echo "  ✅ Line 925: Fixed tutor message formatting"
echo "  ✅ Python syntax validation passed"
log_info "Redeploying to Railway..."

# Redeploy
railway up

log_success "🎉 Redeployment initiated!"
echo
echo "📋 Monitor deployment:"
echo "  railway logs"
echo
echo "🧪 Test when ready:"
echo "  railway open"
echo "  curl https://your-app.up.railway.app/api/v1/health"
