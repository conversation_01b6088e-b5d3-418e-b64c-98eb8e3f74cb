#!/bin/bash

# Setup Railway environment variables from your .env file
# This script helps you copy the variables to Railway

set -e

# Colors for output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo "🔧 Railway Environment Setup"
    echo "============================"
    echo
    echo "This script will help you set up environment variables in Railway"
    echo "using the values from your backend/.env file."
    echo
}

check_env_file() {
    if [ ! -f "backend/.env" ]; then
        log_error ".env file not found at backend/.env"
        exit 1
    fi
    
    log_info ".env file found ✓"
}

show_variables() {
    log_info "Variables from your .env file:"
    echo
    
    # Read and display variables
    while IFS='=' read -r key value; do
        # Skip comments and empty lines
        if [[ $key =~ ^#.*$ ]] || [[ -z $key ]]; then
            continue
        fi
        
        # Mask sensitive values for display
        if [[ $key == *"KEY"* ]] || [[ $key == *"PASSWORD"* ]] || [[ $key == *"SECRET"* ]]; then
            masked_value="${value:0:10}..."
            echo "  $key=$masked_value"
        else
            echo "  $key=$value"
        fi
    done < backend/.env
    
    echo
}

show_railway_commands() {
    log_info "Railway CLI commands to set these variables:"
    echo
    echo "Copy and paste these commands one by one:"
    echo
    
    # Generate railway variable set commands
    while IFS='=' read -r key value; do
        # Skip comments and empty lines
        if [[ $key =~ ^#.*$ ]] || [[ -z $key ]]; then
            continue
        fi
        
        # Remove any quotes from value
        clean_value=$(echo "$value" | sed 's/^"//;s/"$//')
        
        echo "railway variables set $key=\"$clean_value\""
    done < backend/.env
    
    echo
}

show_manual_instructions() {
    log_info "Manual setup instructions:"
    echo
    echo "If you prefer to use the Railway GUI:"
    echo "1. Go to https://railway.app/dashboard"
    echo "2. Click on your CampusPQ project"
    echo "3. Click on your backend service"
    echo "4. Go to Variables tab"
    echo "5. Click '+ New Variable' for each variable below:"
    echo
    
    # Show variables for manual entry
    while IFS='=' read -r key value; do
        # Skip comments and empty lines
        if [[ $key =~ ^#.*$ ]] || [[ -z $key ]]; then
            continue
        fi
        
        # Remove any quotes from value
        clean_value=$(echo "$value" | sed 's/^"//;s/"$//')
        
        echo "   Name: $key"
        echo "   Value: $clean_value"
        echo
    done < backend/.env
}

main() {
    print_header
    
    check_env_file
    show_variables
    
    echo "Choose how you want to set up the variables:"
    echo "1. Show Railway CLI commands (recommended)"
    echo "2. Show manual GUI instructions"
    echo "3. Both"
    echo
    
    read -p "Enter your choice (1-3): " choice
    echo
    
    case $choice in
        1)
            show_railway_commands
            ;;
        2)
            show_manual_instructions
            ;;
        3)
            show_railway_commands
            show_manual_instructions
            ;;
        *)
            log_warning "Invalid choice. Showing both options:"
            show_railway_commands
            show_manual_instructions
            ;;
    esac
    
    echo
    log_success "Setup complete!"
    echo
    echo "📋 Next Steps:"
    echo "  1. Set the environment variables using one of the methods above"
    echo "  2. Wait for Railway to redeploy (automatic)"
    echo "  3. Test note upload functionality"
    echo "  4. Use './scripts/debug-railway.sh' to check logs"
    echo
}

main "$@"
