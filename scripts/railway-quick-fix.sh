#!/bin/bash

# Quick fix for Railway deployment - Set minimum required environment variables

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo
    echo "🚂 =================================="
    echo "🚂  Railway Quick Fix Setup"
    echo "🚂 =================================="
    echo
}

check_railway_cli() {
    if ! command -v railway >/dev/null 2>&1; then
        log_error "Railway CLI not found. Please install it first:"
        echo "npm install -g @railway/cli"
        exit 1
    fi
}

set_minimum_variables() {
    log_info "Setting minimum required environment variables in Railway..."
    
    echo
    echo "🔧 Setting basic configuration variables..."
    
    # Basic app configuration
    railway variables set ENVIRONMENT=production
    railway variables set DEBUG=false
    railway variables set API_V1_PREFIX=/api/v1
    railway variables set PROJECT_NAME=CampusPQ
    railway variables set AI_PROVIDER=gemini
    railway variables set QDRANT_COLLECTION=campuspq_embeddings
    
    # Performance settings for Railway free tier
    railway variables set CACHE_ENABLED=true
    railway variables set CACHE_TTL_HOURS=24
    railway variables set MAX_PARALLEL_CHUNKS=3
    railway variables set BATCH_SIZE=2
    
    # CORS settings (allow all origins for Railway deployment)
    railway variables set BACKEND_CORS_ORIGINS="*"
    
    log_success "Basic configuration variables set!"
}

set_database_url() {
    log_info "Setting database URL (your Supabase instance)..."
    
    railway variables set DATABASE_URL="postgresql://postgres:GKNvV4kL28Li!<EMAIL>:5432/postgres"
    
    log_success "Database URL set!"
}

generate_secret_key() {
    log_info "Generating and setting SECRET_KEY..."
    
    # Generate a secure secret key
    SECRET_KEY=$(openssl rand -base64 32)
    railway variables set SECRET_KEY="$SECRET_KEY"
    
    log_success "SECRET_KEY generated and set!"
    echo "Generated SECRET_KEY: $SECRET_KEY"
}

set_optional_defaults() {
    log_info "Setting optional service defaults (you can update these later)..."
    
    # Set placeholder values for optional services
    railway variables set REDIS_URL="redis://localhost:6379/0"
    railway variables set QDRANT_URL="http://localhost:6333"
    railway variables set FRONTEND_URL="https://your-frontend-url.com"
    railway variables set EMAILS_FROM_EMAIL="<EMAIL>"
    
    log_warning "Optional services set to defaults. Update these in Railway dashboard:"
    echo "- REDIS_URL: Get from Redis Cloud"
    echo "- QDRANT_URL: Get from Qdrant Cloud"
    echo "- FRONTEND_URL: Your actual frontend URL"
    echo "- OPENAI_API_KEY: Your OpenAI API key (optional)"
    echo "- GEMINI_API_KEY: Your Gemini API key (optional)"
    echo "- CLOUDINARY_URL: Your Cloudinary URL (optional)"
    echo "- CLOUDINARY_API_KEY: Your Cloudinary API key (optional)"
    echo "- CLOUDINARY_API_SECRET: Your Cloudinary API secret (optional)"
}

deploy_app() {
    log_info "Deploying to Railway..."
    
    railway up
    
    log_success "Deployment initiated!"
}

show_next_steps() {
    log_success "Quick fix completed!"
    echo
    echo "🎯 Your app should now deploy successfully to Railway!"
    echo
    echo "📋 Next steps:"
    echo "1. 🔍 Check deployment status: railway logs"
    echo "2. 🌐 Get your app URL: railway open"
    echo "3. 🧪 Test health endpoint: https://your-app.up.railway.app/api/v1/health"
    echo "4. ⚙️ Update optional services in Railway dashboard:"
    echo "   - Redis Cloud URL"
    echo "   - Qdrant Cloud URL"
    echo "   - Your frontend URL"
    echo "   - API keys (OpenAI, Gemini, Cloudinary)"
    echo
    echo "🔗 Railway Dashboard: https://railway.app/dashboard"
    echo
}

main() {
    print_header
    
    log_info "This script will set the minimum required environment variables for Railway deployment"
    echo
    
    check_railway_cli
    
    echo "This will:"
    echo "✅ Set basic app configuration"
    echo "✅ Configure your Supabase database"
    echo "✅ Generate a secure SECRET_KEY"
    echo "✅ Set default values for optional services"
    echo "✅ Deploy your app"
    echo
    
    read -p "Continue? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        log_info "Cancelled by user"
        exit 0
    fi
    
    set_minimum_variables
    set_database_url
    generate_secret_key
    set_optional_defaults
    deploy_app
    show_next_steps
    
    log_success "🎉 Railway deployment should now work!"
}

main "$@"
