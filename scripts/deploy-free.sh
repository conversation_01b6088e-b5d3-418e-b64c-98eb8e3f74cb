#!/bin/bash

# CampusPQ Free Deployment Script
# Deploy to free hosting services: Railway, Render, Vercel

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

check_prerequisites() {
    log_info "Checking prerequisites for free deployment..."
    
    # Check if required tools are installed
    command -v git >/dev/null 2>&1 || { log_error "Git is required but not installed. Aborting."; exit 1; }
    command -v npm >/dev/null 2>&1 || { log_error "Node.js/npm is required but not installed. Aborting."; exit 1; }
    
    log_success "Prerequisites check passed"
}

setup_free_services() {
    log_info "Setting up free service accounts..."
    echo
    echo "You'll need accounts on these free services:"
    echo "1. 🚂 Railway.app - Backend API & Worker hosting"
    echo "   - Sign up: https://railway.app"
    echo "   - Free tier: 500 hours/month, 1GB RAM"
    echo
    echo "2. 🎨 Render.com - Alternative backend hosting"
    echo "   - Sign up: https://render.com"
    echo "   - Free tier: 750 hours/month, 512MB RAM"
    echo
    echo "3. ▲ Vercel - Frontend hosting"
    echo "   - Sign up: https://vercel.com"
    echo "   - Free tier: Unlimited static sites"
    echo
    echo "4. ☁️ Redis Cloud - Redis hosting"
    echo "   - Sign up: https://redis.com/try-free"
    echo "   - Free tier: 30MB Redis instance"
    echo
    echo "5. 🗄️ Supabase - Database (you already have this)"
    echo "   - Your database: postgresql://postgres:GKNvV4kL28Li!<EMAIL>:5432/postgres"
    echo
    echo "6. 📁 Cloudinary - File storage"
    echo "   - Sign up: https://cloudinary.com"
    echo "   - Free tier: 25GB storage, 25GB bandwidth"
    echo
    read -p "Press Enter after setting up these accounts..."
}

deploy_to_railway() {
    log_info "Deploying backend to Railway..."

    if ! command -v railway >/dev/null 2>&1; then
        log_info "Installing Railway CLI..."
        npm install -g @railway/cli
    fi

    log_info "Step-by-step Railway deployment:"
    echo
    echo "🚂 RAILWAY DEPLOYMENT STEPS:"
    echo "================================"
    echo
    echo "1. 📝 Login to Railway:"
    echo "   railway login"
    echo
    echo "2. 🎯 Initialize project (in your project root):"
    echo "   railway init"
    echo "   - Choose 'Deploy from existing repo'"
    echo "   - Select your GitHub repository"
    echo
    echo "3. 🐳 Deploy the backend:"
    echo "   railway up"
    echo "   - This will use backend/Dockerfile.free"
    echo "   - Railway will automatically detect and build"
    echo
    echo "4. ⚙️ Set environment variables in Railway dashboard:"
    echo "   Go to: https://railway.app/dashboard"
    echo "   Click your project > Variables tab"
    echo
    echo "   Required variables:"
    echo "   ├── DATABASE_URL=postgresql://postgres:GKNvV4kL28Li!<EMAIL>:5432/postgres"
    echo "   ├── SECRET_KEY=$(openssl rand -base64 32)"
    echo "   ├── REDIS_URL=redis://your-redis-cloud-url"
    echo "   ├── QDRANT_URL=https://your-qdrant-cloud-url"
    echo "   ├── OPENAI_API_KEY=your-openai-key"
    echo "   ├── GEMINI_API_KEY=your-gemini-key"
    echo "   ├── CLOUDINARY_URL=your-cloudinary-url"
    echo "   ├── CLOUDINARY_API_KEY=your-cloudinary-key"
    echo "   ├── CLOUDINARY_API_SECRET=your-cloudinary-secret"
    echo "   ├── SMTP_USER=your-smtp-user"
    echo "   ├── SMTP_PASSWORD=your-smtp-password"
    echo "   └── FRONTEND_URL=your-frontend-url"
    echo
    echo "5. 🔄 Deploy worker (optional - for background tasks):"
    echo "   railway add"
    echo "   - Add another service for the worker"
    echo "   - Use same environment variables"
    echo "   - Set start command: celery -A app.core.celery_app worker --loglevel=info --concurrency=1"
    echo
    echo "6. ✅ Test your deployment:"
    echo "   Your API will be available at: https://your-app.up.railway.app"
    echo "   Test health endpoint: https://your-app.up.railway.app/api/v1/health"
    echo

    read -p "Press Enter to continue with Railway CLI commands..."

    log_info "Running Railway CLI commands..."
    echo
    echo "🔐 Logging into Railway..."
    railway login

    echo
    echo "🎯 Initializing Railway project..."
    railway init

    echo
    echo "🚀 Deploying to Railway..."
    railway up

    echo
    log_success "Railway deployment initiated!"
    echo
    echo "📋 Next steps:"
    echo "1. Go to https://railway.app/dashboard"
    echo "2. Click on your project"
    echo "3. Add the environment variables listed above"
    echo "4. Your backend will be available at the Railway-provided URL"
    echo "5. Update your frontend to use the new backend URL"
    echo
}

deploy_to_render() {
    log_info "Deploying to Render..."
    
    echo "To deploy to Render:"
    echo "1. Connect your GitHub repository to Render"
    echo "2. Create a new Web Service"
    echo "3. Use the render.yaml configuration"
    echo "4. Set environment variables in Render dashboard"
    echo
    echo "Render will automatically deploy from your GitHub repository"
    echo
}

deploy_to_vercel() {
    log_info "Deploying frontend to Vercel..."
    
    if ! command -v vercel >/dev/null 2>&1; then
        log_info "Installing Vercel CLI..."
        npm install -g vercel
    fi
    
    echo "To deploy frontend to Vercel:"
    echo "1. Run: vercel login"
    echo "2. Run: vercel --prod"
    echo "3. Follow the prompts"
    echo
    echo "Or connect your GitHub repository to Vercel for automatic deployments"
    echo
}

setup_redis_cloud() {
    log_info "Setting up Redis Cloud..."
    
    echo "Redis Cloud setup:"
    echo "1. Go to https://redis.com/try-free"
    echo "2. Create a free account"
    echo "3. Create a new database"
    echo "4. Copy the connection string"
    echo "5. Add it as REDIS_CLOUD_URL in your hosting service"
    echo
}

setup_qdrant() {
    log_info "Setting up Qdrant vector database..."
    
    echo "Qdrant options:"
    echo "1. 🆓 Qdrant Cloud (free tier): https://cloud.qdrant.io"
    echo "2. 🐳 Self-hosted on Railway/Render (uses your free hours)"
    echo "3. 💻 Local development only"
    echo
    echo "For production, recommend Qdrant Cloud free tier"
    echo
}

update_database_config() {
    log_info "Updating database configuration for Supabase..."
    
    # Update backend environment files
    if [ -f "backend/.env.example" ]; then
        sed -i 's|DATABASE_URL=.*|DATABASE_URL=postgresql://postgres:GKNvV4kL28Li!<EMAIL>:5432/postgres|' backend/.env.example
    fi
    
    # Update docker-compose files
    if [ -f "docker-compose.free.yml" ]; then
        log_success "Free deployment configuration already updated"
    fi
    
    log_success "Database configuration updated for Supabase"
}

run_database_migrations() {
    log_info "Running database migrations on Supabase..."
    
    echo "To run migrations on your Supabase database:"
    echo "1. Install dependencies: cd backend && pip install -r requirements.txt"
    echo "2. Set DATABASE_URL: export DATABASE_URL='postgresql://postgres:GKNvV4kL28Li!<EMAIL>:5432/postgres'"
    echo "3. Run migrations: alembic upgrade head"
    echo "4. Populate initial data: python scripts/populate_nigerian_schools.py"
    echo
    read -p "Press Enter after running migrations..."
}

show_deployment_summary() {
    log_info "Free Deployment Summary"
    echo
    echo "🎯 Deployment Strategy:"
    echo "├── 🗄️  Database: Supabase (your existing instance)"
    echo "├── 🚂 Backend API: Railway.app (free tier)"
    echo "├── ⚙️  Background Worker: Railway.app (free tier)"
    echo "├── ☁️  Redis: Redis Cloud (free tier)"
    echo "├── 🔍 Vector DB: Qdrant Cloud (free tier)"
    echo "├── ▲ Frontend: Vercel (free tier)"
    echo "└── 📁 File Storage: Cloudinary (free tier)"
    echo
    echo "💰 Total Monthly Cost: $0 (within free tiers)"
    echo
    echo "📊 Free Tier Limits:"
    echo "├── Railway: 500 hours/month, 1GB RAM"
    echo "├── Render: 750 hours/month, 512MB RAM"
    echo "├── Vercel: Unlimited static hosting"
    echo "├── Redis Cloud: 30MB storage"
    echo "├── Qdrant Cloud: 1GB storage"
    echo "└── Cloudinary: 25GB storage, 25GB bandwidth"
    echo
    echo "🔗 Next Steps:"
    echo "1. Set up accounts on all free services"
    echo "2. Deploy backend to Railway or Render"
    echo "3. Deploy frontend to Vercel"
    echo "4. Configure environment variables"
    echo "5. Run database migrations"
    echo "6. Test your deployment"
    echo
}

create_env_template() {
    log_info "Creating environment template for free deployment..."
    
    cat > .env.free << 'EOF'
# CampusPQ Free Deployment Environment Variables
# Copy these to your hosting service dashboards

# Database (Supabase)
DATABASE_URL=postgresql://postgres:GKNvV4kL28Li!<EMAIL>:5432/postgres

# Redis (Redis Cloud - get from your Redis Cloud dashboard)
REDIS_CLOUD_URL=redis://default:<EMAIL>:12345

# Vector Database (Qdrant Cloud - get from your Qdrant Cloud dashboard)
QDRANT_URL=https://your-cluster.qdrant.io:6333

# Application Security
SECRET_KEY=your-secret-key-here-generate-with-openssl-rand-base64-32

# AI APIs
OPENAI_API_KEY=your-openai-api-key
GEMINI_API_KEY=your-gemini-api-key
AI_PROVIDER=gemini

# File Storage (Cloudinary)
CLOUDINARY_URL=cloudinary://api_key:api_secret@cloud_name
CLOUDINARY_API_KEY=your-cloudinary-api-key
CLOUDINARY_API_SECRET=your-cloudinary-api-secret

# Email (Free SMTP - Gmail, SendGrid, etc.)
SMTP_USER=your-smtp-username
SMTP_PASSWORD=your-smtp-password
EMAILS_FROM_EMAIL=<EMAIL>

# Frontend URL (Vercel)
FRONTEND_URL=https://campuspq.vercel.app

# Environment
ENVIRONMENT=production
DEBUG=false
EOF
    
    log_success "Environment template created: .env.free"
}

# Main deployment flow
main() {
    log_info "Starting CampusPQ free deployment setup"
    echo
    
    check_prerequisites
    setup_free_services
    update_database_config
    create_env_template
    run_database_migrations
    
    echo
    echo "Choose your deployment method:"
    echo "1) Railway.app only (backend) - Frontend already running"
    echo "2) Railway.app + setup external services"
    echo "3) Setup instructions only"
    read -p "Enter your choice (1-3): " choice

    case $choice in
        1)
            deploy_to_railway
            setup_redis_cloud
            setup_qdrant
            ;;
        2)
            deploy_to_railway
            setup_redis_cloud
            setup_qdrant
            show_deployment_summary
            ;;
        3)
            log_info "Setup instructions provided above"
            ;;
        *)
            log_error "Invalid choice. Showing Railway deployment."
            deploy_to_railway
            setup_redis_cloud
            setup_qdrant
            ;;
    esac
    
    log_success "Free deployment setup completed!"
    echo
    echo "📖 Check the generated .env.free file for all required environment variables"
    echo "🚀 Follow the deployment instructions for each service"
    echo "📧 Contact support if you need help with any service setup"
}

# Run main function
main "$@"
