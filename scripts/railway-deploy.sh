#!/bin/bash

# Railway Backend Deployment Script for CampusPQ
# Simplified deployment for backend only (frontend already running)

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

print_header() {
    echo
    echo "🚂 =================================="
    echo "🚂  CampusPQ Railway Deployment"
    echo "🚂 =================================="
    echo
}

check_prerequisites() {
    log_info "Checking prerequisites..."
    
    # Check if required tools are installed
    command -v git >/dev/null 2>&1 || { log_error "Git is required but not installed. Aborting."; exit 1; }
    command -v npm >/dev/null 2>&1 || { log_error "Node.js/npm is required but not installed. Aborting."; exit 1; }
    
    # Check if we're in a git repository
    if [ ! -d ".git" ]; then
        log_error "This directory is not a Git repository. Please run from your project root."
        exit 1
    fi
    
    log_success "Prerequisites check passed"
}

install_railway_cli() {
    if ! command -v railway >/dev/null 2>&1; then
        log_info "Installing Railway CLI..."
        npm install -g @railway/cli
        log_success "Railway CLI installed"
    else
        log_info "Railway CLI already installed"
    fi
}

setup_external_services() {
    log_info "External services setup required:"
    echo
    echo "📋 Before deploying, set up these FREE services:"
    echo
    echo "1. 🔴 Redis Cloud (Cache & Message Queue)"
    echo "   • Sign up: https://redis.com/try-free"
    echo "   • Create free database (30MB)"
    echo "   • Copy connection string"
    echo
    echo "2. 🟣 Qdrant Cloud (Vector Database)"
    echo "   • Sign up: https://cloud.qdrant.io"
    echo "   • Create free cluster (1GB)"
    echo "   • Copy API endpoint"
    echo
    echo "3. 🟠 Cloudinary (File Storage)"
    echo "   • Sign up: https://cloudinary.com"
    echo "   • Get API credentials from dashboard"
    echo
    echo "4. 🔑 Generate Secret Key"
    echo "   • Run: openssl rand -base64 32"
    echo "   • Copy the generated key"
    echo
    read -p "Press Enter after setting up these services..."
}

deploy_to_railway() {
    log_info "Starting Railway deployment..."
    
    echo
    echo "🔐 Step 1: Login to Railway"
    railway login
    
    echo
    echo "🎯 Step 2: Initialize Railway project"
    if [ ! -f "railway.toml" ]; then
        railway init
    else
        log_info "Railway project already initialized"
    fi
    
    echo
    echo "🐳 Step 3: Deploy backend"
    log_info "Deploying backend using backend/Dockerfile.free..."
    railway up
    
    log_success "Backend deployed to Railway!"
}

show_environment_variables() {
    log_info "Environment Variables Setup"
    echo
    echo "📝 Go to Railway Dashboard: https://railway.app/dashboard"
    echo "   1. Click on your project"
    echo "   2. Go to 'Variables' tab"
    echo "   3. Add these variables:"
    echo
    echo "🗄️  DATABASE (Already configured):"
    echo "   DATABASE_URL=postgresql://postgres:GKNvV4kL28Li!<EMAIL>:5432/postgres"
    echo
    echo "🔐 SECURITY:"
    echo "   SECRET_KEY=your-generated-secret-key"
    echo
    echo "☁️  EXTERNAL SERVICES:"
    echo "   REDIS_URL=redis://default:password@your-redis-host:port"
    echo "   QDRANT_URL=https://your-cluster.qdrant.io:6333"
    echo "   QDRANT_COLLECTION=campuspq_embeddings"
    echo
    echo "🤖 AI APIS:"
    echo "   OPENAI_API_KEY=your-openai-key"
    echo "   GEMINI_API_KEY=your-gemini-key"
    echo "   AI_PROVIDER=gemini"
    echo
    echo "📁 FILE STORAGE:"
    echo "   CLOUDINARY_URL=cloudinary://api_key:api_secret@cloud_name"
    echo "   CLOUDINARY_API_KEY=your-cloudinary-key"
    echo "   CLOUDINARY_API_SECRET=your-cloudinary-secret"
    echo
    echo "🌐 FRONTEND:"
    echo "   FRONTEND_URL=your-existing-frontend-url"
    echo
    echo "⚙️  APPLICATION:"
    echo "   ENVIRONMENT=production"
    echo "   DEBUG=false"
    echo "   API_V1_PREFIX=/api/v1"
    echo
}

test_deployment() {
    log_info "Testing deployment..."
    
    echo
    echo "🧪 Testing your Railway deployment:"
    echo
    echo "1. Get your Railway URL:"
    echo "   railway open"
    echo
    echo "2. Test health endpoint:"
    echo "   curl https://your-app.up.railway.app/api/v1/health"
    echo
    echo "3. View API documentation:"
    echo "   https://your-app.up.railway.app/api/v1/docs"
    echo
    echo "4. Check logs:"
    echo "   railway logs"
    echo
}

setup_worker_service() {
    log_info "Setting up Celery worker (optional)..."
    
    echo
    echo "🔄 To add background task processing:"
    echo
    echo "1. Add new service:"
    echo "   railway add"
    echo "   • Choose 'Empty Service'"
    echo "   • Name it 'campuspq-worker'"
    echo
    echo "2. Configure worker:"
    echo "   • Use same environment variables"
    echo "   • Set custom start command:"
    echo "     celery -A app.core.celery_app worker --loglevel=info --concurrency=1"
    echo
    echo "3. Deploy worker:"
    echo "   railway up"
    echo
}

show_next_steps() {
    log_success "Railway deployment completed!"
    echo
    echo "🎯 Next Steps:"
    echo
    echo "1. 📝 Add environment variables in Railway dashboard"
    echo "2. 🧪 Test your API endpoints"
    echo "3. 🔗 Update your frontend to use Railway backend URL"
    echo "4. 🔄 Optionally set up worker service for background tasks"
    echo "5. 📊 Monitor your app in Railway dashboard"
    echo
    echo "📋 Useful Commands:"
    echo "   railway logs     # View application logs"
    echo "   railway open     # Open Railway dashboard"
    echo "   railway status   # Check deployment status"
    echo "   railway up       # Redeploy"
    echo
    echo "🔗 Your backend will be available at:"
    echo "   https://your-app.up.railway.app"
    echo
    echo "📖 For detailed setup instructions, see:"
    echo "   RAILWAY-BACKEND-SETUP.md"
    echo "   .env.railway (environment variables template)"
    echo
}

run_database_migrations() {
    log_info "Database migrations..."
    echo
    echo "🗄️  Your Supabase database is already configured!"
    echo
    echo "To run migrations (if needed):"
    echo "1. Install dependencies: cd backend && pip install -r requirements.txt"
    echo "2. Set DATABASE_URL: export DATABASE_URL='postgresql://postgres:GKNvV4kL28Li!<EMAIL>:5432/postgres'"
    echo "3. Run migrations: alembic upgrade head"
    echo "4. Populate data: python scripts/populate_nigerian_schools.py"
    echo
    read -p "Press Enter to continue..."
}

# Main deployment flow
main() {
    print_header
    
    log_info "Deploying CampusPQ backend to Railway"
    log_info "Frontend: Already running ✅"
    log_info "Backend: Deploying to Railway 🚂"
    echo
    
    check_prerequisites
    install_railway_cli
    setup_external_services
    deploy_to_railway
    show_environment_variables
    run_database_migrations
    test_deployment
    
    echo
    read -p "Do you want to set up a worker service for background tasks? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        setup_worker_service
    fi
    
    show_next_steps
    
    log_success "🎉 Railway deployment setup completed!"
}

# Run main function
main "$@"
