#!/bin/bash

# CampusPQ Rollback Script
# This script handles rollback procedures for CampusPQ

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
ENVIRONMENT=${1:-prod}
AWS_REGION=${AWS_REGION:-us-east-1}
PROJECT_NAME="campuspq"

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

check_prerequisites() {
    log_info "Checking prerequisites..."
    
    # Check if required tools are installed
    command -v aws >/dev/null 2>&1 || { log_error "AWS CLI is required but not installed. Aborting."; exit 1; }
    
    # Check AWS credentials
    aws sts get-caller-identity >/dev/null 2>&1 || { log_error "AWS credentials not configured. Aborting."; exit 1; }
    
    log_success "Prerequisites check passed"
}

list_deployments() {
    log_info "Listing recent deployments..."
    
    # List recent task definition revisions
    echo "Backend API Task Definitions:"
    aws ecs list-task-definitions \
        --family-prefix $PROJECT_NAME-backend-api \
        --status ACTIVE \
        --sort DESC \
        --max-items 5 \
        --query 'taskDefinitionArns' \
        --output table \
        --region $AWS_REGION
    
    echo
    echo "Worker Task Definitions:"
    aws ecs list-task-definitions \
        --family-prefix $PROJECT_NAME-celery-worker \
        --status ACTIVE \
        --sort DESC \
        --max-items 5 \
        --query 'taskDefinitionArns' \
        --output table \
        --region $AWS_REGION
    
    echo
    echo "Frontend Task Definitions:"
    aws ecs list-task-definitions \
        --family-prefix $PROJECT_NAME-frontend \
        --status ACTIVE \
        --sort DESC \
        --max-items 5 \
        --query 'taskDefinitionArns' \
        --output table \
        --region $AWS_REGION
}

rollback_service() {
    local service_name=$1
    local task_definition_family=$2
    
    log_info "Rolling back $service_name..."
    
    # Get the current task definition revision
    current_revision=$(aws ecs describe-services \
        --cluster $PROJECT_NAME-$ENVIRONMENT-cluster \
        --services $service_name \
        --query 'services[0].taskDefinition' \
        --output text \
        --region $AWS_REGION | grep -o '[0-9]*$')
    
    if [ "$current_revision" -le 1 ]; then
        log_error "Cannot rollback $service_name - already at the first revision"
        return 1
    fi
    
    # Calculate previous revision
    previous_revision=$((current_revision - 1))
    previous_task_def="$task_definition_family:$previous_revision"
    
    log_info "Rolling back $service_name from revision $current_revision to $previous_revision"
    
    # Update service with previous task definition
    aws ecs update-service \
        --cluster $PROJECT_NAME-$ENVIRONMENT-cluster \
        --service $service_name \
        --task-definition $previous_task_def \
        --region $AWS_REGION >/dev/null
    
    log_success "$service_name rollback initiated"
}

rollback_to_specific_revision() {
    local service_name=$1
    local task_definition_family=$2
    local revision=$3
    
    log_info "Rolling back $service_name to revision $revision..."
    
    # Check if the revision exists
    task_def="$task_definition_family:$revision"
    if ! aws ecs describe-task-definition \
        --task-definition $task_def \
        --region $AWS_REGION >/dev/null 2>&1; then
        log_error "Task definition $task_def does not exist"
        return 1
    fi
    
    # Update service with specified task definition
    aws ecs update-service \
        --cluster $PROJECT_NAME-$ENVIRONMENT-cluster \
        --service $service_name \
        --task-definition $task_def \
        --region $AWS_REGION >/dev/null
    
    log_success "$service_name rolled back to revision $revision"
}

wait_for_rollback() {
    log_info "Waiting for rollback to complete..."
    
    # Wait for services to stabilize
    aws ecs wait services-stable \
        --cluster $PROJECT_NAME-$ENVIRONMENT-cluster \
        --services $PROJECT_NAME-backend-service $PROJECT_NAME-worker-service $PROJECT_NAME-frontend-service \
        --region $AWS_REGION
    
    log_success "Rollback completed successfully"
}

rollback_database() {
    log_warning "Database rollback requires manual intervention!"
    echo
    echo "To rollback database changes:"
    echo "1. Connect to your RDS instance"
    echo "2. Restore from a backup or run specific migration rollback commands"
    echo "3. Use Alembic downgrade commands if applicable"
    echo
    echo "Database endpoint can be found in AWS Parameter Store:"
    echo "  Parameter: /$PROJECT_NAME-$ENVIRONMENT/database-url"
    echo
    read -p "Press Enter to continue after handling database rollback..."
}

show_current_status() {
    log_info "Current deployment status:"
    echo
    
    # Show current task definition revisions
    echo "Current Task Definition Revisions:"
    
    backend_revision=$(aws ecs describe-services \
        --cluster $PROJECT_NAME-$ENVIRONMENT-cluster \
        --services $PROJECT_NAME-backend-service \
        --query 'services[0].taskDefinition' \
        --output text \
        --region $AWS_REGION | grep -o '[0-9]*$')
    echo "  Backend API: $backend_revision"
    
    worker_revision=$(aws ecs describe-services \
        --cluster $PROJECT_NAME-$ENVIRONMENT-cluster \
        --services $PROJECT_NAME-worker-service \
        --query 'services[0].taskDefinition' \
        --output text \
        --region $AWS_REGION | grep -o '[0-9]*$')
    echo "  Worker: $worker_revision"
    
    frontend_revision=$(aws ecs describe-services \
        --cluster $PROJECT_NAME-$ENVIRONMENT-cluster \
        --services $PROJECT_NAME-frontend-service \
        --query 'services[0].taskDefinition' \
        --output text \
        --region $AWS_REGION | grep -o '[0-9]*$')
    echo "  Frontend: $frontend_revision"
    
    echo
    
    # Show service status
    echo "Service Status:"
    aws ecs describe-services \
        --cluster $PROJECT_NAME-$ENVIRONMENT-cluster \
        --services $PROJECT_NAME-backend-service $PROJECT_NAME-worker-service $PROJECT_NAME-frontend-service \
        --query 'services[*].[serviceName,status,runningCount,desiredCount]' \
        --output table \
        --region $AWS_REGION
}

# Main rollback flow
main() {
    log_info "Starting CampusPQ rollback for environment: $ENVIRONMENT"
    echo
    
    check_prerequisites
    show_current_status
    
    echo
    echo "Rollback Options:"
    echo "1) Rollback all services to previous revision"
    echo "2) Rollback specific service to previous revision"
    echo "3) Rollback to specific revision"
    echo "4) List available deployments"
    echo "5) Show current status only"
    read -p "Enter your choice (1-5): " choice
    
    case $choice in
        1)
            log_warning "This will rollback ALL services to their previous revisions."
            read -p "Are you sure? (y/N): " -n 1 -r
            echo
            if [[ $REPLY =~ ^[Yy]$ ]]; then
                rollback_service "$PROJECT_NAME-backend-service" "$PROJECT_NAME-backend-api"
                rollback_service "$PROJECT_NAME-worker-service" "$PROJECT_NAME-celery-worker"
                rollback_service "$PROJECT_NAME-frontend-service" "$PROJECT_NAME-frontend"
                wait_for_rollback
                
                read -p "Do you need to rollback database changes? (y/N): " -n 1 -r
                echo
                if [[ $REPLY =~ ^[Yy]$ ]]; then
                    rollback_database
                fi
            else
                log_info "Rollback cancelled"
                exit 0
            fi
            ;;
        2)
            echo "Which service to rollback?"
            echo "1) Backend API"
            echo "2) Worker"
            echo "3) Frontend"
            read -p "Enter choice (1-3): " service_choice
            
            case $service_choice in
                1) rollback_service "$PROJECT_NAME-backend-service" "$PROJECT_NAME-backend-api" ;;
                2) rollback_service "$PROJECT_NAME-worker-service" "$PROJECT_NAME-celery-worker" ;;
                3) rollback_service "$PROJECT_NAME-frontend-service" "$PROJECT_NAME-frontend" ;;
                *) log_error "Invalid choice"; exit 1 ;;
            esac
            wait_for_rollback
            ;;
        3)
            echo "Which service to rollback?"
            echo "1) Backend API"
            echo "2) Worker"
            echo "3) Frontend"
            read -p "Enter choice (1-3): " service_choice
            read -p "Enter revision number: " revision
            
            case $service_choice in
                1) rollback_to_specific_revision "$PROJECT_NAME-backend-service" "$PROJECT_NAME-backend-api" "$revision" ;;
                2) rollback_to_specific_revision "$PROJECT_NAME-worker-service" "$PROJECT_NAME-celery-worker" "$revision" ;;
                3) rollback_to_specific_revision "$PROJECT_NAME-frontend-service" "$PROJECT_NAME-frontend" "$revision" ;;
                *) log_error "Invalid choice"; exit 1 ;;
            esac
            wait_for_rollback
            ;;
        4)
            list_deployments
            exit 0
            ;;
        5)
            exit 0
            ;;
        *)
            log_error "Invalid choice. Exiting."
            exit 1
            ;;
    esac
    
    echo
    show_current_status
    log_success "Rollback completed successfully!"
}

# Run main function
main "$@"
