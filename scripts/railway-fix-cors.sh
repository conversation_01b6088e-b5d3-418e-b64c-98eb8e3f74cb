#!/bin/bash

# Quick CORS fix for Railway deployment

set -e

# Colors for output
GREEN='\033[0;32m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

echo "🚂 Railway CORS Quick Fix"
echo "========================="
echo

log_info "Fixing CORS configuration for Railway..."

# Fix CORS origins
railway variables set BACKEND_CORS_ORIGINS="*"

log_success "CORS configuration updated!"

log_info "Redeploying to apply changes..."
railway up

log_success "🎉 Deployment should now work!"
echo
echo "Test your API: railway open"
