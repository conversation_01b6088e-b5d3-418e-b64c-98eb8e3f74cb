#!/bin/bash

# CampusPQ Deployment Script
# This script handles the complete deployment process for CampusPQ

set -e  # Exit on any error

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
ENVIRONMENT=${1:-prod}
AWS_REGION=${AWS_REGION:-us-east-1}
PROJECT_NAME="campuspq"

# Functions
log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

check_prerequisites() {
    log_info "Checking prerequisites..."
    
    # Check if required tools are installed
    command -v aws >/dev/null 2>&1 || { log_error "AWS CLI is required but not installed. Aborting."; exit 1; }
    command -v terraform >/dev/null 2>&1 || { log_error "Terraform is required but not installed. Aborting."; exit 1; }
    command -v docker >/dev/null 2>&1 || { log_error "Docker is required but not installed. Aborting."; exit 1; }
    
    # Check AWS credentials
    aws sts get-caller-identity >/dev/null 2>&1 || { log_error "AWS credentials not configured. Aborting."; exit 1; }
    
    log_success "Prerequisites check passed"
}

deploy_infrastructure() {
    log_info "Deploying infrastructure with Terraform..."
    
    cd terraform
    
    # Initialize Terraform
    terraform init
    
    # Plan the deployment
    terraform plan -var-file="environments/${ENVIRONMENT}.tfvars" -out=tfplan
    
    # Ask for confirmation
    echo
    read -p "Do you want to apply these changes? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        terraform apply tfplan
        log_success "Infrastructure deployment completed"
    else
        log_warning "Infrastructure deployment cancelled"
        exit 1
    fi
    
    cd ..
}

build_and_push_images() {
    log_info "Building and pushing Docker images..."
    
    # Get ECR login token
    aws ecr get-login-password --region $AWS_REGION | docker login --username AWS --password-stdin $(aws sts get-caller-identity --query Account --output text).dkr.ecr.$AWS_REGION.amazonaws.com
    
    # Build and push backend image
    log_info "Building backend image..."
    docker build -t $PROJECT_NAME-backend:latest -f backend/Dockerfile backend/
    docker tag $PROJECT_NAME-backend:latest $(aws sts get-caller-identity --query Account --output text).dkr.ecr.$AWS_REGION.amazonaws.com/$PROJECT_NAME-backend:latest
    docker push $(aws sts get-caller-identity --query Account --output text).dkr.ecr.$AWS_REGION.amazonaws.com/$PROJECT_NAME-backend:latest
    
    # Build and push worker image
    log_info "Building worker image..."
    docker build -t $PROJECT_NAME-worker:latest -f backend/Dockerfile.worker backend/
    docker tag $PROJECT_NAME-worker:latest $(aws sts get-caller-identity --query Account --output text).dkr.ecr.$AWS_REGION.amazonaws.com/$PROJECT_NAME-worker:latest
    docker push $(aws sts get-caller-identity --query Account --output text).dkr.ecr.$AWS_REGION.amazonaws.com/$PROJECT_NAME-worker:latest
    
    # Build and push frontend image
    log_info "Building frontend image..."
    docker build -t $PROJECT_NAME-frontend:latest -f frontend/Dockerfile frontend/
    docker tag $PROJECT_NAME-frontend:latest $(aws sts get-caller-identity --query Account --output text).dkr.ecr.$AWS_REGION.amazonaws.com/$PROJECT_NAME-frontend:latest
    docker push $(aws sts get-caller-identity --query Account --output text).dkr.ecr.$AWS_REGION.amazonaws.com/$PROJECT_NAME-frontend:latest
    
    log_success "Docker images built and pushed successfully"
}

update_ecs_services() {
    log_info "Updating ECS services..."
    
    # Update backend service
    aws ecs update-service \
        --cluster $PROJECT_NAME-$ENVIRONMENT-cluster \
        --service $PROJECT_NAME-backend-service \
        --force-new-deployment \
        --region $AWS_REGION
    
    # Update worker service
    aws ecs update-service \
        --cluster $PROJECT_NAME-$ENVIRONMENT-cluster \
        --service $PROJECT_NAME-worker-service \
        --force-new-deployment \
        --region $AWS_REGION
    
    # Update frontend service
    aws ecs update-service \
        --cluster $PROJECT_NAME-$ENVIRONMENT-cluster \
        --service $PROJECT_NAME-frontend-service \
        --force-new-deployment \
        --region $AWS_REGION
    
    log_success "ECS services updated successfully"
}

wait_for_deployment() {
    log_info "Waiting for deployment to complete..."
    
    # Wait for services to stabilize
    aws ecs wait services-stable \
        --cluster $PROJECT_NAME-$ENVIRONMENT-cluster \
        --services $PROJECT_NAME-backend-service $PROJECT_NAME-worker-service $PROJECT_NAME-frontend-service \
        --region $AWS_REGION
    
    log_success "Deployment completed successfully"
}

run_database_migrations() {
    log_info "Running database migrations..."
    
    # Get the task definition ARN
    TASK_DEF_ARN=$(aws ecs describe-task-definition \
        --task-definition $PROJECT_NAME-backend-api \
        --query 'taskDefinition.taskDefinitionArn' \
        --output text \
        --region $AWS_REGION)
    
    # Get subnet and security group IDs
    SUBNET_ID=$(aws ec2 describe-subnets \
        --filters "Name=tag:Name,Values=$PROJECT_NAME-$ENVIRONMENT-private-subnet-1" \
        --query 'Subnets[0].SubnetId' \
        --output text \
        --region $AWS_REGION)
    
    SECURITY_GROUP_ID=$(aws ec2 describe-security-groups \
        --filters "Name=tag:Name,Values=$PROJECT_NAME-$ENVIRONMENT-ecs-sg" \
        --query 'SecurityGroups[0].GroupId' \
        --output text \
        --region $AWS_REGION)
    
    # Run migration task
    TASK_ARN=$(aws ecs run-task \
        --cluster $PROJECT_NAME-$ENVIRONMENT-cluster \
        --task-definition $TASK_DEF_ARN \
        --launch-type FARGATE \
        --network-configuration "awsvpcConfiguration={subnets=[$SUBNET_ID],securityGroups=[$SECURITY_GROUP_ID],assignPublicIp=DISABLED}" \
        --overrides '{"containerOverrides":[{"name":"campuspq-api","command":["alembic","upgrade","head"]}]}' \
        --query 'tasks[0].taskArn' \
        --output text \
        --region $AWS_REGION)
    
    # Wait for migration to complete
    aws ecs wait tasks-stopped \
        --cluster $PROJECT_NAME-$ENVIRONMENT-cluster \
        --tasks $TASK_ARN \
        --region $AWS_REGION
    
    log_success "Database migrations completed"
}

show_deployment_info() {
    log_info "Deployment Information:"
    echo
    echo "Environment: $ENVIRONMENT"
    echo "AWS Region: $AWS_REGION"
    echo
    
    # Get ALB DNS name
    ALB_DNS=$(aws elbv2 describe-load-balancers \
        --names $PROJECT_NAME-$ENVIRONMENT-alb \
        --query 'LoadBalancers[0].DNSName' \
        --output text \
        --region $AWS_REGION 2>/dev/null || echo "Not found")
    
    echo "Application URL: https://$ALB_DNS"
    echo "API URL: https://$ALB_DNS/api/v1"
    echo
    echo "CloudWatch Dashboard: https://$AWS_REGION.console.aws.amazon.com/cloudwatch/home?region=$AWS_REGION#dashboards:name=$PROJECT_NAME-$ENVIRONMENT-dashboard"
    echo
}

# Main deployment flow
main() {
    log_info "Starting CampusPQ deployment for environment: $ENVIRONMENT"
    echo
    
    check_prerequisites
    
    # Ask what to deploy
    echo "What would you like to deploy?"
    echo "1) Infrastructure only"
    echo "2) Application only"
    echo "3) Full deployment (infrastructure + application)"
    echo "4) Database migrations only"
    read -p "Enter your choice (1-4): " choice
    
    case $choice in
        1)
            deploy_infrastructure
            ;;
        2)
            build_and_push_images
            update_ecs_services
            wait_for_deployment
            ;;
        3)
            deploy_infrastructure
            build_and_push_images
            update_ecs_services
            run_database_migrations
            wait_for_deployment
            ;;
        4)
            run_database_migrations
            ;;
        *)
            log_error "Invalid choice. Exiting."
            exit 1
            ;;
    esac
    
    show_deployment_info
    log_success "Deployment completed successfully!"
}

# Run main function
main "$@"
