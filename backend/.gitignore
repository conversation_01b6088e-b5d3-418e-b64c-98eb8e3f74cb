# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg

# Environment
.env
.venv
env/
venv/
ENV/
env.bak/
venv.bak/

# Database
*.db
campuspq.db
app.db
*.sqlite
*.sqlite3
migrations/
alembic/versions/

# Node.js
node_modules/
npm-debug.log
yarn-debug.log
yarn-error.log
.pnpm-debug.log
.npm
.yarn-integrity

# Project specific
openai_docs/
fonts/
qdrant_storage/
storage/
test_files/
uploads/
snapshots/
qdrant
*.pyc
__pycache__/

# Large files and binaries
*.mmap
*.dat
*.bin
*.h5
*.hdf5
*.pkl
*.pickle
*.joblib
*.model
*.onnx
*.pt
*.pth
*.weights
*.pb

# IDE and editors
.idea/
.vscode/
*.swp
*.swo
*~
.DS_Store
.project
.classpath
.settings/
*.sublime-workspace
*.sublime-project

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*

# Testing
.coverage
htmlcov/
.pytest_cache/
.tox/
.nox/
coverage/
.coverage.*
nosetests.xml
coverage.xml
*.cover
*.pdf
*.csv
