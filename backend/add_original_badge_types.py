#!/usr/bin/env python3

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.db.session import SessionLocal
from sqlalchemy import text

def add_original_badge_types():
    """Add the original badge types that are missing"""
    db = SessionLocal()
    
    try:
        print("=== ADDING ORIGINAL BADGE TYPES ===")
        
        # Original badge types that need to be added
        original_badge_types = [
            'course_completion',
            'exam_excellence', 
            'practice_master',
            'streak',
            'first_perfect_score',
            'question_master',
            'quick_learner',
            'consistent_learner'
        ]
        
        # Check existing badge types
        try:
            result = db.execute(text("SELECT unnest(enum_range(NULL::badgetype))")).fetchall()
            existing_types = [row[0] for row in result]
            print(f"Existing badge types: {len(existing_types)}")
        except Exception as e:
            print(f"Could not check existing types: {e}")
            existing_types = []
        
        # Add original badge types
        added_count = 0
        for badge_type in original_badge_types:
            if badge_type not in existing_types:
                try:
                    db.execute(text(f"ALTER TYPE badgetype ADD VALUE '{badge_type}'"))
                    db.commit()
                    added_count += 1
                    print(f"✅ Added original type: {badge_type}")
                except Exception as e:
                    print(f"❌ Error adding {badge_type}: {e}")
                    db.rollback()
            else:
                print(f"⚠️  Already exists: {badge_type}")
        
        print(f"Added {added_count} original badge types")
        
        # Now check all badge types
        result = db.execute(text("SELECT unnest(enum_range(NULL::badgetype))")).fetchall()
        all_types = [row[0] for row in result]
        print(f"\nTotal badge types now: {len(all_types)}")
        
        # Show all types
        print("All badge types:")
        for i, badge_type in enumerate(sorted(all_types), 1):
            print(f"  {i:2d}. {badge_type}")
        
        print("\n✅ Original badge types migration completed!")
        print("Now you can run the complete badge migration script.")
        
    except Exception as e:
        print(f"❌ Error adding original badge types: {e}")
        db.rollback()
        import traceback
        traceback.print_exc()
    finally:
        db.close()

if __name__ == "__main__":
    add_original_badge_types()
