#!/usr/bin/env python3

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.db.session import SessionLocal
from app.models.user import User
from app.models.gamification import PointsTransaction, UserBadge, UserStreak
from sqlalchemy import text

def debug_leaderboard():
    db = SessionLocal()
    
    try:
        print("=== DEBUGGING LEADERBOARD ===")
        
        # Check if there are any students
        students = db.query(User).filter(User.role == 'student').all()
        print(f'Total students: {len(students)}')
        
        if len(students) == 0:
            print("No students found! This is the problem.")
            # Check all users
            all_users = db.query(User).all()
            print(f'Total users: {len(all_users)}')
            for user in all_users[:5]:
                print(f'  User: {user.full_name} (Role: {user.role})')
        else:
            print("Students found:")
            for student in students[:10]:  # Show first 10 students
                print(f'  Student: {student.full_name} (ID: {student.id})')
                
                # Check points for this student
                points = db.execute(text('SELECT COALESCE(SUM(points), 0) FROM pointstransaction WHERE user_id = :user_id'), {'user_id': student.id}).scalar()
                print(f'    Points: {points}')
                
                # Check badges
                badges = db.query(UserBadge).filter(UserBadge.user_id == student.id).count()
                print(f'    Badges: {badges}')
                
                # Check streak
                streak = db.execute(text('SELECT COALESCE(current_streak, 0) FROM userstreak WHERE user_id = :user_id'), {'user_id': student.id}).scalar() or 0
                print(f'    Streak: {streak}')
        
        # Check total points transactions
        total_transactions = db.query(PointsTransaction).count()
        print(f'\nTotal points transactions: {total_transactions}')
        
        # Check total badges
        total_badges = db.query(UserBadge).count()
        print(f'Total user badges: {total_badges}')
        
        # Check total streaks
        total_streaks = db.query(UserStreak).count()
        print(f'Total user streaks: {total_streaks}')
        
        # Test the leaderboard query directly
        print("\n=== TESTING LEADERBOARD QUERY ===")
        student_query = """
            SELECT DISTINCT
                u.id,
                u.full_name,
                u.profile_picture_url,
                s.name as school_name,
                d.name as department_name
            FROM
                "user" u
            LEFT JOIN
                school s ON u.institution_id = s.id
            LEFT JOIN
                department d ON u.department_id = d.id
            LEFT JOIN
                user_course uc ON u.id = uc.user_id
            WHERE u.role = :role
            LIMIT 10
        """

        students_result = db.execute(text(student_query), {"role": "STUDENT"}).fetchall()
        print(f"Query returned {len(students_result)} students")
        
        for student in students_result:
            print(f"  {student.full_name} (ID: {student.id})")
            
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()
    finally:
        db.close()

if __name__ == "__main__":
    debug_leaderboard()
