#!/bin/bash

# Railway-optimized startup script
# Starts API immediately for fast deployment detection

set -e

echo "🚀 Starting CampusPQ for Railway"
echo "================================"

# Print basic environment info
echo "🔧 Environment:"
echo "  - PORT: ${PORT:-8000}"
echo "  - ENVIRONMENT: ${ENVIRONMENT:-production}"
echo "  - DEBUG: ${DEBUG:-false}"
echo ""

# Create necessary directories
mkdir -p uploads logs

# Quick Redis check (non-blocking)
echo "🔍 Quick Redis check..."
python -c "
import redis
import os
try:
    redis_url = os.getenv('REDIS_URL', 'redis://localhost:6379/0')
    r = redis.from_url(redis_url, socket_connect_timeout=2)
    r.ping()
    print('✅ Redis available')
except Exception as e:
    print(f'⚠️  Redis not available: {e}')
    print('⚠️  Background tasks will be disabled')
" 2>/dev/null || echo "⚠️  Redis check failed - continuing anyway"

# Start Celery worker in background (if Redis is available)
if [ "${ENABLE_WORKER:-true}" = "true" ]; then
    echo "⚡ Starting Redis Cloud optimized background worker..."
    celery -A app.core.celery_app worker \
        --loglevel=warning \
        --concurrency=1 \
        --max-memory-per-child=200000 \
        --max-tasks-per-child=5 \
        -Q lightning_queue,celery \
        > logs/celery.log 2>&1 &
    
    WORKER_PID=$!
    echo "   Worker PID: $WORKER_PID (running in background)"
    
    # Give worker 3 seconds to start, then continue
    sleep 3
    
    if kill -0 $WORKER_PID 2>/dev/null; then
        echo "   ✅ Worker started successfully"
    else
        echo "   ⚠️  Worker failed to start - API will run without background tasks"
    fi
else
    echo "⚠️  Worker disabled - API only mode"
fi

# Start the API server immediately
echo ""
echo "📡 Starting API server..."
echo "   URL: http://0.0.0.0:${PORT:-8000}"
echo "   Health: http://0.0.0.0:${PORT:-8000}/api/v1/health"
echo ""

# Use exec to replace the shell process (important for Railway)
exec uvicorn app.main:app \
    --host 0.0.0.0 \
    --port ${PORT:-8000} \
    --workers 1 \
    --log-level warning \
    --access-log
