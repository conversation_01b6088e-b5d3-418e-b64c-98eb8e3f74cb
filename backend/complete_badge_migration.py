#!/usr/bin/env python3

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.db.session import SessionLocal
from sqlalchemy import text
from app.services.gamification_service import DEFAULT_BADGES

def complete_badge_migration():
    """Add all remaining badges to Supabase"""
    db = SessionLocal()
    
    try:
        print("=== COMPLETING BADGE MIGRATION TO SUPABASE ===")
        
        # Check current badge count
        current_count = db.execute(text("SELECT COUNT(*) FROM badge")).scalar()
        print(f"Current badges in database: {current_count}")
        print(f"Total badges to create: {len(DEFAULT_BADGES)}")
        
        added_count = 0
        updated_count = 0
        
        for badge_data in DEFAULT_BADGES:
            try:
                # Check if badge already exists
                existing = db.execute(
                    text("SELECT id, points_value FROM badge WHERE name = :name"),
                    {"name": badge_data["name"]}
                ).fetchone()
                
                if existing:
                    # Update existing badge with new point values
                    if existing[1] != badge_data["points_value"]:
                        db.execute(
                            text("""
                                UPDATE badge 
                                SET points_value = :points_value, 
                                    description = :description,
                                    icon_url = :icon_url,
                                    updated_at = NOW()
                                WHERE name = :name
                            """),
                            {
                                "name": badge_data["name"],
                                "points_value": badge_data["points_value"],
                                "description": badge_data["description"],
                                "icon_url": badge_data["icon_url"]
                            }
                        )
                        updated_count += 1
                        print(f"✅ Updated: {badge_data['name']} ({existing[1]} → {badge_data['points_value']} pts)")
                    else:
                        print(f"⚠️  Already exists: {badge_data['name']} ({badge_data['points_value']} pts)")
                else:
                    # Create new badge
                    db.execute(
                        text("""
                            INSERT INTO badge (name, description, badge_type, icon_url, points_value, created_at, updated_at)
                            VALUES (:name, :description, :badge_type, :icon_url, :points_value, NOW(), NOW())
                        """),
                        {
                            "name": badge_data["name"],
                            "description": badge_data["description"],
                            "badge_type": badge_data["badge_type"],
                            "icon_url": badge_data["icon_url"],
                            "points_value": badge_data["points_value"]
                        }
                    )
                    added_count += 1
                    print(f"🆕 Added: {badge_data['name']} ({badge_data['points_value']} pts)")
                    
            except Exception as e:
                print(f"❌ Error with badge {badge_data['name']}: {e}")
                db.rollback()
                continue
        
        db.commit()
        
        # Final verification
        final_count = db.execute(text("SELECT COUNT(*) FROM badge")).scalar()
        print(f"\n✅ Migration completed!")
        print(f"   Added: {added_count} new badges")
        print(f"   Updated: {updated_count} existing badges")
        print(f"   Total badges: {final_count}")
        
        # Show badge categories
        print(f"\n🏆 Badge Categories:")
        categories = {}
        for badge in DEFAULT_BADGES:
            badge_type = badge["badge_type"].value
            category = badge_type.split('_')[0] if '_' in badge_type else badge_type
            if category not in categories:
                categories[category] = []
            categories[category].append(badge["name"])
        
        for category, badges in categories.items():
            print(f"   {category.title()}: {len(badges)} badges")
        
        # Show point distribution
        print(f"\n📊 Point Value Distribution:")
        point_ranges = {"1-5 pts": 0, "6-10 pts": 0, "11-20 pts": 0, "21+ pts": 0}
        for badge in DEFAULT_BADGES:
            points = badge["points_value"]
            if points <= 5:
                point_ranges["1-5 pts"] += 1
            elif points <= 10:
                point_ranges["6-10 pts"] += 1
            elif points <= 20:
                point_ranges["11-20 pts"] += 1
            else:
                point_ranges["21+ pts"] += 1
        
        for range_name, count in point_ranges.items():
            print(f"   {range_name}: {count} badges")
        
        print(f"\n🎯 System Summary:")
        print(f"   📊 Point System: 1-5 pts per action")
        print(f"   🎯 20 Levels: 0-11,750+ pts")
        print(f"   🏆 {final_count} Badges: 1-50 pts")
        print(f"   🏅 Milestones: 50/500/2,500/10,000 pts")
        print(f"   🎨 SVG Icons: All badges and levels")
        
    except Exception as e:
        print(f"❌ Error during badge migration: {e}")
        db.rollback()
        import traceback
        traceback.print_exc()
    finally:
        db.close()

if __name__ == "__main__":
    complete_badge_migration()
