#!/usr/bin/env python3

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.db.session import SessionLocal
from sqlalchemy import text

def test_connection_and_migrate():
    """Test Supabase connection and apply basic migration"""
    db = SessionLocal()
    
    try:
        print("=== TESTING SUPABASE CONNECTION ===")
        
        # Test basic connection
        result = db.execute(text("SELECT 1")).scalar()
        print(f"✅ Connection successful: {result}")
        
        # Check existing tables
        tables = db.execute(text("""
            SELECT table_name FROM information_schema.tables 
            WHERE table_schema = 'public' 
            AND table_name IN ('userlevel', 'badge', 'pointstransaction')
        """)).fetchall()
        
        print(f"✅ Found tables: {[t[0] for t in tables]}")
        
        # Step 1: Add new badge types (one by one with error handling)
        print("\n=== ADDING NEW BADGE TYPES ===")
        new_badge_types = [
            'STREAK_WARRIOR', 'STREAK_LEGEND', 'STREAK_IMMORTAL',
            'SPEED_DEMON', 'LIGHTNING_FAST', 'FLASH_MASTER',
            'SHARPSHOOTER', 'PRECISION_MASTER', 'PERFECTIONIST',
            'EARLY_BIRD', 'NIGHT_OWL', 'WEEKEND_WARRIOR', 'COMEBACK_KID',
            'FIRST_LOGIN', 'PROFILE_COMPLETE', 'EXPLORER',
            'COLLECTOR', 'OVERACHIEVER',
            'HUNDRED_CLUB', 'THOUSAND_CLUB', 'ELITE_CLUB', 'LEGENDARY_CLUB'
        ]
        
        added_count = 0
        for badge_type in new_badge_types:
            try:
                db.execute(text(f"ALTER TYPE badgetype ADD VALUE '{badge_type}'"))
                db.commit()
                added_count += 1
                print(f"✅ Added: {badge_type}")
            except Exception as e:
                if "already exists" in str(e) or "duplicate" in str(e):
                    print(f"⚠️  Already exists: {badge_type}")
                else:
                    print(f"❌ Error adding {badge_type}: {e}")
                db.rollback()
        
        print(f"Added {added_count} new badge types")
        
        # Step 2: Update existing levels with new point requirements
        print("\n=== UPDATING LEVEL REQUIREMENTS ===")
        level_updates = [
            (1, "Novice", 0, 9),
            (2, "Beginner", 10, 24),
            (3, "Apprentice", 25, 49),
            (4, "Student", 50, 99),
            (5, "Scholar", 100, 174),
            (10, "Mentor", 800, 1099),
            (15, "Champion", 3400, 4399),
            (20, "Immortal", 11750, 9999999)
        ]
        
        for level_num, name, min_pts, max_pts in level_updates:
            try:
                db.execute(
                    text("UPDATE userlevel SET min_points = :min_pts, max_points = :max_pts WHERE level_number = :level_num"),
                    {"min_pts": min_pts, "max_pts": max_pts, "level_num": level_num}
                )
                print(f"✅ Updated Level {level_num} ({name}): {min_pts}-{max_pts} pts")
            except Exception as e:
                print(f"❌ Error updating level {level_num}: {e}")
        
        db.commit()
        
        # Step 3: Add some key badges
        print("\n=== ADDING KEY BADGES ===")
        key_badges = [
            {
                "name": "First Login",
                "description": "Welcome to the platform!",
                "badge_type": "FIRST_LOGIN",
                "icon_url": "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMzIiIGN5PSIzMiIgcj0iMzAiIGZpbGw9IiM0Qzc5RkYiIHN0cm9rZT0iIzJENTVGRiIgc3Ryb2tlLXdpZHRoPSI0Ii8+CjxwYXRoIGQ9Ik0yNCAzMkwzMCA0Mkw0NCAyOCIgc3Ryb2tlPSIjRkZGRkZGIiBzdHJva2Utd2lkdGg9IjQiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8L3N2Zz4K",
                "points_value": 2
            },
            {
                "name": "Hundred Club",
                "description": "Earn 50 total points",
                "badge_type": "HUNDRED_CLUB",
                "icon_url": "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMzIiIGN5PSIzMiIgcj0iMzAiIGZpbGw9IiNDMEM0Q0MiIHN0cm9rZT0iI0E1QTlCNCIgc3Ryb2tlLXdpZHRoPSI0Ii8+CjxwYXRoIGQ9Ik0yNCAyNEg0MFYzNkgyNFYyNFoiIGZpbGw9IiNGRkZGRkYiLz4KPHRleHQgeD0iMzIiIHk9IjMyIiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMTIiIGZpbGw9IiNDMEM0Q0MiIHRleHQtYW5jaG9yPSJtaWRkbGUiPjEwMDwvdGV4dD4KPC9zdmc+Cg==",
                "points_value": 1
            },
            {
                "name": "Speed Demon",
                "description": "Answer 10 questions in under 30 seconds each",
                "badge_type": "SPEED_DEMON",
                "icon_url": "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMzIiIGN5PSIzMiIgcj0iMzAiIGZpbGw9IiNGRkVCM0IiIHN0cm9rZT0iI0Y1OUUwQiIgc3Ryb2tlLXdpZHRoPSI0Ii8+CjxwYXRoIGQ9Ik0yMCAzMkwzMiAyMEw0NCAzMkwzMiA0NEwyMCAzMloiIGZpbGw9IiNGRkZGRkYiLz4KPC9zdmc+Cg==",
                "points_value": 4
            }
        ]
        
        for badge_data in key_badges:
            try:
                # Check if badge already exists
                existing = db.execute(
                    text("SELECT id FROM badge WHERE name = :name"),
                    {"name": badge_data["name"]}
                ).fetchone()
                
                if not existing:
                    db.execute(
                        text("""
                            INSERT INTO badge (name, description, badge_type, icon_url, points_value, created_at, updated_at)
                            VALUES (:name, :description, :badge_type, :icon_url, :points_value, NOW(), NOW())
                        """),
                        badge_data
                    )
                    print(f"✅ Added badge: {badge_data['name']}")
                else:
                    print(f"⚠️  Badge already exists: {badge_data['name']}")
            except Exception as e:
                print(f"❌ Error adding badge {badge_data['name']}: {e}")
        
        db.commit()
        
        # Step 4: Verify the migration
        print("\n=== VERIFICATION ===")
        level_count = db.execute(text("SELECT COUNT(*) FROM userlevel")).scalar()
        badge_count = db.execute(text("SELECT COUNT(*) FROM badge")).scalar()
        
        print(f"✅ Levels in database: {level_count}")
        print(f"✅ Badges in database: {badge_count}")
        
        # Show sample level requirements
        sample_levels = db.execute(text("""
            SELECT level_number, name, min_points, max_points 
            FROM userlevel 
            WHERE level_number IN (1, 5, 10, 20)
            ORDER BY level_number
        """)).fetchall()
        
        print("\n📊 Sample Level Requirements:")
        for level in sample_levels:
            print(f"   Level {level[0]:2d} ({level[1]:12s}): {level[2]:4,}-{level[3]:5,} pts")
        
        print("\n✅ SUPABASE MIGRATION COMPLETED!")
        print("\n🎯 Next steps:")
        print("   1. Test the gamification system with your app")
        print("   2. Run the full badge creation script if needed")
        print("   3. Verify leaderboard functionality")
        
    except Exception as e:
        print(f"❌ Error during migration: {e}")
        db.rollback()
        import traceback
        traceback.print_exc()
    finally:
        db.close()

if __name__ == "__main__":
    test_connection_and_migrate()
