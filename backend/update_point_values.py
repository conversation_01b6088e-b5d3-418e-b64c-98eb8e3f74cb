#!/usr/bin/env python3

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.db.session import SessionLocal
from app.models.gamification import Badge, UserLevel
from sqlalchemy import text

def update_point_values():
    """Update badge point values and level requirements to match lower point system"""
    db = SessionLocal()
    
    try:
        print("=== UPDATING POINT VALUES TO LOWER SYSTEM ===")
        
        # Update badge point values
        badge_updates = {
            "First Perfect Score": 5,
            "Practice Master": 3,
            "Course Completion": 5,
            "Quick Learner": 4,
            "Consistent Learner": 3,
            "Streak Champion": 3,
            "Streak Warrior": 8,
            "Streak Legend": 20,
            "Streak Immortal": 50,
            "Speed Demon": 4,
            "Lightning Fast": 6,
            "Flash Master": 10,
            "Sharpshooter": 5,
            "Precision Master": 8,
            "Perfectionist": 15,
            "Early Bird": 4,
            "Night Owl": 4,
            "Weekend Warrior": 3,
            "Comeback Kid": 5,
            "First Login": 2,
            "Profile Complete": 3,
            "Explorer": 3,
            "Hundred Club": 1,
            "Thousand Club": 5,
            "Elite Club": 15,
            "Legendary Club": 50,
            "Collector": 8,
            "Overachiever": 20
        }
        
        print("Updating badge point values...")
        for badge_name, new_points in badge_updates.items():
            badge = db.query(Badge).filter(Badge.name == badge_name).first()
            if badge:
                old_points = badge.points_value
                badge.points_value = new_points
                print(f"  {badge_name}: {old_points} → {new_points} points")
            else:
                print(f"  ⚠️  Badge not found: {badge_name}")
        
        # Update level requirements
        level_updates = [
            (1, "Novice", 0, 9),
            (2, "Beginner", 10, 24),
            (3, "Apprentice", 25, 49),
            (4, "Student", 50, 99),
            (5, "Scholar", 100, 174),
            (6, "Researcher", 175, 274),
            (7, "Specialist", 275, 399),
            (8, "Expert", 400, 574),
            (9, "Professional", 575, 799),
            (10, "Mentor", 800, 1099),
            (11, "Master", 1100, 1474),
            (12, "Sage", 1475, 1974),
            (13, "Virtuoso", 1975, 2599),
            (14, "Grandmaster", 2600, 3399),
            (15, "Champion", 3400, 4399),
            (16, "Elite", 4400, 5649),
            (17, "Prodigy", 5650, 7249),
            (18, "Genius", 7250, 9249),
            (19, "Legend", 9250, 11749),
            (20, "Immortal", 11750, 9999999)
        ]
        
        print("\nUpdating level requirements...")
        for level_num, name, min_pts, max_pts in level_updates:
            level = db.query(UserLevel).filter(UserLevel.level_number == level_num).first()
            if level:
                old_min = level.min_points
                old_max = level.max_points
                level.min_points = min_pts
                level.max_points = max_pts
                print(f"  Level {level_num:2d} ({name:12s}): {old_min:6,}-{old_max:7,} → {min_pts:4,}-{max_pts:5,} points")
            else:
                print(f"  ⚠️  Level not found: {level_num}")
        
        # Update milestone badge requirements in the check function
        print("\nUpdating milestone requirements...")
        milestone_updates = {
            "Hundred Club": 50,  # Was 100
            "Thousand Club": 500,  # Was 1000  
            "Elite Club": 2500,  # Was 10000
            "Legendary Club": 10000  # Was 100000
        }
        
        for badge_name, new_threshold in milestone_updates.items():
            badge = db.query(Badge).filter(Badge.name == badge_name).first()
            if badge:
                # Update description to reflect new threshold
                if "100 total points" in badge.description:
                    badge.description = badge.description.replace("100 total points", "50 total points")
                elif "1,000 total points" in badge.description:
                    badge.description = badge.description.replace("1,000 total points", "500 total points")
                elif "10,000 total points" in badge.description:
                    badge.description = badge.description.replace("10,000 total points", "2,500 total points")
                elif "100,000 total points" in badge.description:
                    badge.description = badge.description.replace("100,000 total points", "10,000 total points")
                print(f"  {badge_name}: threshold → {new_threshold} points")
        
        db.commit()
        
        print("\n✅ Point values updated successfully!")
        print("\n=== NEW POINT SYSTEM SUMMARY ===")
        print("📊 Action Points:")
        print("  • Correct Answer: 1 pt")
        print("  • Exam Completion: 2 pts") 
        print("  • Practice Completion: 2 pts")
        print("  • Perfect Score: 5 pts")
        print("  • Speed Bonus: +1 pt")
        print("  • Streak Bonuses: +3 to +10 pts")
        
        print("\n🏆 Badge Points: 1-50 pts (down from 25-2500)")
        print("🎯 Level Requirements: 0-11,750+ pts (down from 0-235,000+)")
        print("🏅 Milestones: 50/500/2,500/10,000 pts (down from 100/1K/10K/100K)")
        
    except Exception as e:
        print(f"❌ Error updating point values: {e}")
        db.rollback()
        import traceback
        traceback.print_exc()
    finally:
        db.close()

if __name__ == "__main__":
    update_point_values()
