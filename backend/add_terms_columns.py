#!/usr/bin/env python3
"""
Scrip<PERSON> to add terms acceptance columns to the user table
"""

import os
import sys
import psycopg2
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT

# Add the app directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__)))

from app.core.config import settings

def add_terms_columns():
    """Add terms acceptance columns to user table"""
    try:
        # Connect to database using DATABASE_URL
        conn = psycopg2.connect(settings.DATABASE_URL)
        conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
        cursor = conn.cursor()
        
        print("Adding terms acceptance columns...")
        
        # Add terms_accepted column
        cursor.execute('''
            ALTER TABLE "user" 
            ADD COLUMN IF NOT EXISTS terms_accepted BOOLEAN DEFAULT FALSE
        ''')
        print("✅ Added terms_accepted column")
        
        # Add terms_accepted_at column
        cursor.execute('''
            ALTER TABLE "user" 
            ADD COLUMN IF NOT EXISTS terms_accepted_at TIMESTAMP
        ''')
        print("✅ Added terms_accepted_at column")
        
        # Update alembic version
        cursor.execute('''
            UPDATE alembic_version 
            SET version_num = 'd7e8f9g0h1i2'
        ''')
        print("✅ Updated alembic version")
        
        cursor.close()
        conn.close()
        
        print("\n🎉 Successfully added terms acceptance columns!")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        sys.exit(1)

if __name__ == "__main__":
    add_terms_columns()
