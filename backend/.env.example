# Database Configuration
# Choose ONE of the following database configurations:

# Option 1: Local PostgreSQL
DATABASE_URL=postgresql://username:password@localhost:5432/campuspq

# Option 2: Supabase (replace with your actual Supabase URL)
# DATABASE_URL=postgresql://postgres:<EMAIL>:5432/postgres

# Database Management Settings
# Set to "true" to automatically initialize database on startup
DB_AUTO_INIT=true

# Set to "true" to force recreate all tables on startup (DESTRUCTIVE!)
# Only use this in development when you want a fresh database
DB_FORCE_RECREATE=false

# API Keys
OPENAI_API_KEY=your_openai_api_key_here
GEMINI_API_KEY=your_gemini_api_key_here

# AI Provider Selection - SWITCH MODELS HERE!
# Choose between "openai" or "gemini" for AI processing
# OpenAI: Fast, high-quality processing with vision capabilities (GPT-4o)
# Gemini: Advanced document understanding with multiagent system (Gemini 1.5 Flash)
AI_PROVIDER=gemini

# Redis Configuration (for Celery)
REDIS_URL=redis://localhost:6379/0

# Vector Database Configuration - REMOVED (using Gemini Files API instead)

# Cloudinary Configuration (for image uploads)
CLOUDINARY_URL=cloudinary://api_key:api_secret@cloud_name
CLOUDINARY_API_KEY=your_cloudinary_api_key
CLOUDINARY_API_SECRET=your_cloudinary_api_secret

# Email Configuration
SMTP_USER=your_smtp_user
SMTP_PASSWORD=your_smtp_password
EMAILS_FROM_EMAIL=<EMAIL>

# Security
SECRET_KEY=your_secret_key_here
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# CORS Settings
BACKEND_CORS_ORIGINS=["http://localhost:3000", "http://localhost:8000"]

# Environment
ENVIRONMENT=development