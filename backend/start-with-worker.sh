#!/bin/bash

# Start both API and Celery worker in Railway
# This is a workaround for Railway's single-service limitation

set -e

echo "🚀 Starting CampusPQ with Celery Worker"
echo "======================================"

# Print environment info for debugging
echo "🔧 Environment Information:"
echo "  - REDIS_URL: ${REDIS_URL:-'NOT SET'}"
echo "  - QDRANT_URL: ${QDRANT_URL:-'NOT SET'}"
echo "  - DATABASE_URL: ${DATABASE_URL:-'NOT SET'}"
echo "  - AI_PROVIDER: ${AI_PROVIDER:-'NOT SET'}"
echo "  - PORT: ${PORT:-8000}"
echo ""

# Check if Redis is available
echo "🔍 Checking Redis connection..."
python -c "
import redis
import os
import sys
try:
    redis_url = os.getenv('REDIS_URL', 'redis://localhost:6379/0')
    print(f'Connecting to Redis: {redis_url}')
    r = redis.from_url(redis_url)
    r.ping()
    print('✅ Redis connection successful')
except Exception as e:
    print(f'❌ Redis connection failed: {e}')
    print('⚠️  Note upload functionality will not work without Redis')
    print('⚠️  Continuing anyway...')
"

# Check if Qdrant is available
echo "🔍 Checking Qdrant connection..."
python -c "
import os
import requests
try:
    qdrant_url = os.getenv('QDRANT_URL', 'http://localhost:6333')
    print(f'Connecting to Qdrant: {qdrant_url}')
    response = requests.get(f'{qdrant_url}/health', timeout=5)
    if response.status_code == 200:
        print('✅ Qdrant connection successful')
    else:
        print(f'⚠️  Qdrant responded with status: {response.status_code}')
except Exception as e:
    print(f'❌ Qdrant connection failed: {e}')
    print('⚠️  Vector search functionality may not work')
    print('⚠️  Continuing anyway...')
"

# Start Celery worker in background with Redis Cloud optimized settings
echo "⚡ Starting Redis Cloud optimized Celery worker..."
echo "   Command: celery -A app.core.celery_app worker --loglevel=info --concurrency=2 --max-memory-per-child=300000 --max-tasks-per-child=10 -Q lightning_queue,celery"

# Create log file for celery worker
mkdir -p logs
celery -A app.core.celery_app worker --loglevel=info --concurrency=2 --max-memory-per-child=300000 --max-tasks-per-child=10 -Q lightning_queue,celery > logs/celery.log 2>&1 &
WORKER_PID=$!

echo "   Worker PID: $WORKER_PID"

# Give worker time to start
echo "⏳ Waiting for worker to start..."
sleep 10

# Check if worker is running
if kill -0 $WORKER_PID 2>/dev/null; then
    echo "✅ Celery worker started successfully (PID: $WORKER_PID)"
    echo "📋 Worker logs (last 10 lines):"
    tail -n 10 logs/celery.log || echo "   No logs yet"
else
    echo "❌ Failed to start Celery worker"
    echo "📋 Worker logs:"
    cat logs/celery.log || echo "   No logs found"
fi

# Start the API server
echo ""
echo "📡 Starting API server..."
echo "   Command: uvicorn app.main:app --host 0.0.0.0 --port ${PORT:-8000} --workers 1"
echo "   Access logs will appear below:"
echo ""

exec uvicorn app.main:app --host 0.0.0.0 --port ${PORT:-8000} --workers 1
