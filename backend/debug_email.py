#!/usr/bin/env python3
"""
Debug email configuration
"""

import sys
import os
sys.path.append('.')

def debug_email_config():
    """Debug email configuration"""
    print("🔍 Debugging email configuration...")
    
    try:
        from app.core.config import settings
        print(f"SMTP_USER: {repr(settings.SMTP_USER)}")
        print(f"SMTP_PASSWORD: {repr(settings.SMTP_PASSWORD)}")
        print(f"SMTP configured: {bool(settings.SMTP_USER and settings.SMTP_PASSWORD)}")
        
        # Test the file saving function directly
        from app.services.email_service import _save_email_to_file
        
        print("\n📧 Testing file saving function...")
        result = _save_email_to_file(
            email_to="<EMAIL>",
            subject="Test Email",
            html_content="<h1>Test</h1><p>This is a test email</p>",
            text_content="Test email content"
        )
        
        print(f"File save result: {result}")
        
        # Check if directory was created
        emails_dir = "development_emails"
        if os.path.exists(emails_dir):
            files = os.listdir(emails_dir)
            print(f"✅ Email directory exists with {len(files)} files")
            if files:
                latest_file = files[-1]
                print(f"Latest file: {latest_file}")
        else:
            print("❌ Email directory was not created")
            
    except Exception as e:
        print(f"❌ Debug failed: {e}")
        import traceback
        traceback.print_exc()

def test_send_email_function():
    """Test the main send_email function"""
    print("\n📧 Testing main send_email function...")
    
    try:
        from app.services.email_service import send_email
        
        result = send_email(
            email_to="<EMAIL>",
            subject="Debug Test Email",
            html_content="<h1>Debug Test</h1><p>This is a debug test email</p>",
            background=False  # Send synchronously for testing
        )
        
        print(f"Send email result: {result}")
        
        # Check if directory was created
        emails_dir = "development_emails"
        if os.path.exists(emails_dir):
            files = os.listdir(emails_dir)
            print(f"✅ Email directory exists with {len(files)} files")
            if files:
                latest_file = files[-1]
                print(f"Latest file: {latest_file}")
                
                # Show file content
                with open(os.path.join(emails_dir, latest_file), 'r') as f:
                    content = f.read()
                    print(f"File content preview:")
                    print(content[:300] + "..." if len(content) > 300 else content)
        else:
            print("❌ Email directory was not created")
            
    except Exception as e:
        print(f"❌ Send email test failed: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_email_config()
    test_send_email_function()
