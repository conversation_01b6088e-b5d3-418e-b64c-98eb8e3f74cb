import os
import shutil
import uuid
from pathlib import Path
from typing import List, Optional

from fastapi import UploadFile


class FileHandler:
    def __init__(self, upload_dir: str = "uploads"):
        self.upload_dir = upload_dir
        os.makedirs(self.upload_dir, exist_ok=True)

    async def save_upload(self, file: UploadFile, subdir: Optional[str] = None) -> str:
        """
        Save an uploaded file to the upload directory.

        Args:
            file: The uploaded file
            subdir: Optional subdirectory within the upload directory

        Returns:
            Path to the saved file
        """
        # Create a unique filename
        filename = f"{uuid.uuid4()}_{file.filename}"

        # Create the target directory if it doesn't exist
        target_dir = self.upload_dir
        if subdir:
            target_dir = os.path.join(target_dir, subdir)
            os.makedirs(target_dir, exist_ok=True)

        # Save the file
        file_path = os.path.join(target_dir, filename)
        with open(file_path, "wb") as buffer:
            shutil.copyfileobj(file.file, buffer)

        return file_path

    def delete_file(self, file_path: str) -> bool:
        """
        Delete a file.

        Args:
            file_path: Path to the file to delete

        Returns:
            True if successful, False otherwise
        """
        try:
            if os.path.exists(file_path):
                os.remove(file_path)
                return True
            return False
        except Exception:
            return False

    def get_file_extension(self, filename: str) -> str:
        """
        Get the extension of a file.

        Args:
            filename: Name of the file

        Returns:
            Extension of the file without the leading dot
        """
        # Get the suffix and remove the leading dot
        suffix = Path(filename).suffix.lower()
        return suffix[1:] if suffix.startswith('.') else suffix

    def is_allowed_file(self, filename: str, allowed_extensions: List[str]) -> bool:
        """
        Check if a file has an allowed extension.

        Args:
            filename: Name of the file
            allowed_extensions: List of allowed extensions

        Returns:
            True if the file has an allowed extension, False otherwise
        """
        return self.get_file_extension(filename) in allowed_extensions


# Create a singleton instance
file_handler = FileHandler()
