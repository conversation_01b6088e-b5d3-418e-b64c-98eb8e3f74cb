"""
Streaming file handler to prevent memory leaks during large file processing
"""
import asyncio
import tempfile
import os
import io
from typing import Async<PERSON>enerator, Optional, BinaryIO
from contextlib import asynccontextmanager
from fastapi import UploadFile
import logging

logger = logging.getLogger(__name__)


class StreamingFileHandler:
    """
    Handler for streaming file operations to prevent memory leaks
    """
    
    def __init__(self, chunk_size: int = 8192):  # 8KB chunks
        self.chunk_size = chunk_size
    
    @asynccontextmanager
    async def stream_to_temp_file(self, file: UploadFile) -> AsyncGenerator[str, None]:
        """
        Stream upload file to temporary file to avoid loading entire file into memory
        
        Args:
            file: FastAPI UploadFile object
            
        Yields:
            Path to temporary file
        """
        temp_file_path = None
        try:
            # Create temporary file
            with tempfile.NamedTemporaryFile(delete=False, suffix='.tmp') as temp_file:
                temp_file_path = temp_file.name
                
                # Stream file content in chunks
                total_size = 0
                while True:
                    chunk = await file.read(self.chunk_size)
                    if not chunk:
                        break
                    
                    temp_file.write(chunk)
                    total_size += len(chunk)
                    
                    # Check size limit during streaming
                    if total_size > 30 * 1024 * 1024:  # 30MB limit
                        raise ValueError(f"File size exceeds 30MB limit")
                
                logger.info(f"Streamed {total_size} bytes to temporary file: {temp_file_path}")
            
            yield temp_file_path
            
        except Exception as e:
            logger.error(f"Error streaming file to temp: {str(e)}")
            raise
        finally:
            # Clean up temporary file
            if temp_file_path and os.path.exists(temp_file_path):
                try:
                    os.unlink(temp_file_path)
                    logger.debug(f"Cleaned up temporary file: {temp_file_path}")
                except Exception as e:
                    logger.warning(f"Failed to clean up temp file {temp_file_path}: {e}")
    
    async def read_file_chunks(self, file: UploadFile) -> AsyncGenerator[bytes, None]:
        """
        Read file in chunks to prevent memory overload
        
        Args:
            file: FastAPI UploadFile object
            
        Yields:
            File chunks as bytes
        """
        try:
            total_size = 0
            while True:
                chunk = await file.read(self.chunk_size)
                if not chunk:
                    break
                
                total_size += len(chunk)
                
                # Check size limit during streaming
                if total_size > 30 * 1024 * 1024:  # 30MB limit
                    raise ValueError(f"File size exceeds 30MB limit")
                
                yield chunk
                
        except Exception as e:
            logger.error(f"Error reading file chunks: {str(e)}")
            raise
    
    async def get_file_content_safe(self, file: UploadFile, max_size: int = 30 * 1024 * 1024) -> bytes:
        """
        Safely read file content with size validation and timeout
        
        Args:
            file: FastAPI UploadFile object
            max_size: Maximum allowed file size in bytes
            
        Returns:
            File content as bytes
            
        Raises:
            ValueError: If file is too large
            asyncio.TimeoutError: If reading takes too long
        """
        try:
            # Add timeout to file reading
            content = await asyncio.wait_for(file.read(), timeout=60.0)  # 1 minute timeout
            
            if len(content) > max_size:
                raise ValueError(f"File size {len(content)} bytes exceeds maximum of {max_size} bytes")
            
            # Reset file pointer for potential reuse
            await file.seek(0)
            
            return content
            
        except asyncio.TimeoutError:
            logger.error(f"Timeout reading file: {file.filename}")
            raise
        except Exception as e:
            logger.error(f"Error reading file {file.filename}: {str(e)}")
            raise
    
    def create_memory_efficient_upload_file(self, content: bytes, filename: str, content_type: str = "application/pdf") -> UploadFile:
        """
        Create an UploadFile object from bytes content in a memory-efficient way
        
        Args:
            content: File content as bytes
            filename: Original filename
            content_type: MIME type
            
        Returns:
            UploadFile object
        """
        # Use BytesIO for memory efficiency
        file_obj = io.BytesIO(content)
        
        # Create UploadFile with proper cleanup
        upload_file = UploadFile(
            filename=filename,
            file=file_obj,
            content_type=content_type
        )
        
        return upload_file
    
    @asynccontextmanager
    async def temp_file_from_bytes(self, content: bytes) -> AsyncGenerator[str, None]:
        """
        Create temporary file from bytes content
        
        Args:
            content: File content as bytes
            
        Yields:
            Path to temporary file
        """
        temp_file_path = None
        try:
            with tempfile.NamedTemporaryFile(delete=False, suffix='.tmp') as temp_file:
                temp_file_path = temp_file.name
                temp_file.write(content)
            
            yield temp_file_path
            
        finally:
            if temp_file_path and os.path.exists(temp_file_path):
                try:
                    os.unlink(temp_file_path)
                except Exception as e:
                    logger.warning(f"Failed to clean up temp file {temp_file_path}: {e}")


# Global instance
streaming_file_handler = StreamingFileHandler()
