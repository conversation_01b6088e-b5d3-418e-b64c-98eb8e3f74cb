"""
Configuration settings for the LangChain multiagent system.

This module contains configuration classes and settings for managing
the multiagent question and flashcard generation system.
"""

from dataclasses import dataclass
from typing import Optional
import os


@dataclass
class MultiAgentSettings:
    """Settings for multiagent question generation."""
    
    # Thresholds for when to use multiagent approach (set to 1 to always use multiagent)
    mcq_multiagent_threshold: int = 1
    flashcard_multiagent_threshold: int = 1
    
    # Agent configuration (optimized for speed and reliability)
    max_questions_per_agent: int = 10
    min_questions_per_agent: int = 3
    max_agents: int = 8
    
    # Timeout and retry settings (increased for better reliability)
    timeout_per_agent: int = 90
    retry_attempts: int = 3
    
    # Model settings
    default_model_type: str = "gemini"  # "gemini" or "openai"
    temperature: float = 0.3
    max_tokens_per_agent: int = 2000
    
    # Performance settings
    enable_parallel_execution: bool = True
    max_concurrent_agents: int = 4
    
    # Error handling (no fallbacks for high quality)
    fallback_to_single_agent: bool = False
    min_success_rate: float = 0.8  # Minimum success rate to consider multiagent successful
    
    # Logging
    enable_detailed_logging: bool = True
    log_agent_performance: bool = True


# Global settings instance
multiagent_settings = MultiAgentSettings()


def get_multiagent_threshold(content_type: str) -> int:
    """Get the threshold for when to use multiagent approach."""
    if content_type.lower() == "mcq":
        return multiagent_settings.mcq_multiagent_threshold
    elif content_type.lower() == "flashcard":
        return multiagent_settings.flashcard_multiagent_threshold
    else:
        return 15  # Default threshold


def should_use_multiagent(question_count: int, content_type: str) -> bool:
    """Determine if multiagent approach should be used. Always returns True now."""
    # Always use multiagent for high quality generation
    return True


def get_optimal_agent_count(total_questions: int) -> int:
    """Calculate optimal number of agents for the given question count."""
    max_per_agent = multiagent_settings.max_questions_per_agent
    max_agents = multiagent_settings.max_agents
    
    if total_questions <= max_per_agent:
        return 1
    
    # Calculate number of agents needed
    num_agents = min(max_agents, (total_questions + max_per_agent - 1) // max_per_agent)
    return max(1, num_agents)


def get_questions_per_agent(total_questions: int, num_agents: int) -> list[int]:
    """Distribute questions evenly across agents."""
    base_questions = total_questions // num_agents
    extra_questions = total_questions % num_agents
    
    distribution = []
    for i in range(num_agents):
        questions_for_agent = base_questions + (1 if i < extra_questions else 0)
        distribution.append(questions_for_agent)
    
    return distribution


# Environment-based configuration overrides
def load_config_from_env():
    """Load configuration from environment variables."""
    if os.getenv("MCQ_MULTIAGENT_THRESHOLD"):
        multiagent_settings.mcq_multiagent_threshold = int(os.getenv("MCQ_MULTIAGENT_THRESHOLD"))
    
    if os.getenv("FLASHCARD_MULTIAGENT_THRESHOLD"):
        multiagent_settings.flashcard_multiagent_threshold = int(os.getenv("FLASHCARD_MULTIAGENT_THRESHOLD"))
    
    if os.getenv("MAX_QUESTIONS_PER_AGENT"):
        multiagent_settings.max_questions_per_agent = int(os.getenv("MAX_QUESTIONS_PER_AGENT"))
    
    if os.getenv("MAX_AGENTS"):
        multiagent_settings.max_agents = int(os.getenv("MAX_AGENTS"))
    
    if os.getenv("AGENT_TIMEOUT"):
        multiagent_settings.timeout_per_agent = int(os.getenv("AGENT_TIMEOUT"))
    
    if os.getenv("MULTIAGENT_MODEL_TYPE"):
        multiagent_settings.default_model_type = os.getenv("MULTIAGENT_MODEL_TYPE")
    
    if os.getenv("DISABLE_MULTIAGENT_FALLBACK"):
        multiagent_settings.fallback_to_single_agent = False


# Load configuration on import
load_config_from_env()
