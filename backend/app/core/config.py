import os
from typing import Any, Dict, List, Optional, Union

from pydantic import AnyHttpUrl, PostgresDsn, field_validator
from pydantic_settings import BaseSettings, SettingsConfigDict


class Settings(BaseSettings):
    model_config = SettingsConfigDict(env_file=".env", case_sensitive=True)

    API_V1_PREFIX: str = "/api/v1"
    PROJECT_NAME: str = "CampusPQ"
    DEBUG: bool = False
    SECRET_KEY: str = "dev-secret-key-change-in-production"  # Default for Railway deployment
    ALGORITHM: str = "HS256"
    ACCESS_TOKEN_EXPIRE_MINUTES: int = 60  # 1 hour
    REFRESH_TOKEN_EXPIRE_DAYS: int = 30  # 30 days

    # CORS - Use List[str] instead of List[AnyHttpUrl] for Railway compatibility
    BACKEND_CORS_ORIGINS: List[str] = [
        "http://localhost:3000",
        "http://localhost:5173",
        "https://campuspq.vercel.app",
        "https://*.vercel.app",
        "*"  # Allow all for now, remove in production
    ]

    @field_validator("BACKEND_CORS_ORIGINS", mode="before")
    def assemble_cors_origins(cls, v: Union[str, List[str]]) -> List[str]:
        if isinstance(v, str):
            if v.startswith("[") and v.endswith("]"):
                # Handle JSON-like string format
                import json
                try:
                    return json.loads(v)
                except json.JSONDecodeError:
                    # Fallback to comma-separated
                    return [i.strip().strip('"\'') for i in v.strip("[]").split(",")]
            else:
                # Handle comma-separated string
                return [i.strip() for i in v.split(",")]
        elif isinstance(v, list):
            return [str(item) for item in v]
        return ["*"]  # Default fallback

    # Database
    DATABASE_URL: str

    # OpenAI (optional for Railway deployment)
    OPENAI_API_KEY: Optional[str] = None

    # Gemini (optional for Railway deployment)
    GEMINI_API_KEY: Optional[str] = None

    # AI Provider Selection (openai or gemini)
    AI_PROVIDER: str = "gemini"  # Default to Gemini

    # Redis (optional - only if using Celery)
    REDIS_URL: Optional[str] = None

    # Qdrant settings removed - using Gemini Files API instead

    # Enhanced PDF Processing Settings
    OCR_CONFIDENCE_THRESHOLD: float = 60.0
    OCR_LOW_CONFIDENCE_THRESHOLD: float = 40.0
    DEFAULT_CHUNK_SIZE: int = 1200
    CHUNK_OVERLAP: int = 200
    ENABLE_HANDWRITING_OCR: bool = True
    ENABLE_ENHANCED_PIPELINE: bool = True

    # Caching Settings
    CACHE_ENABLED: bool = True
    CACHE_TTL_HOURS: int = 24

    # Timeout Settings
    REQUEST_TIMEOUT_SECONDS: float = 30.0
    DATABASE_TIMEOUT_SECONDS: float = 20.0
    WEBSOCKET_TIMEOUT_SECONDS: float = 300.0  # 5 minutes
    FILE_UPLOAD_TIMEOUT_SECONDS: float = 300.0  # 2 minutes
    AI_PROCESSING_TIMEOUT_SECONDS: float = 300.0  # 3 minutes

    # Performance Settings
    MAX_PARALLEL_CHUNKS: int = 5
    BATCH_SIZE: int = 3

    # Startup Optimization Settings
    LAZY_LOAD_ML_SERVICES: bool = True  # Enable lazy loading for ML services
    LAZY_LOAD_AI_SERVICES: bool = True  # Enable lazy loading for AI services

    # Cloudinary Settings (optional for Railway deployment)
    CLOUDINARY_URL: Optional[str] = None
    CLOUDINARY_CLOUD_NAME: Optional[str] = None
    CLOUDINARY_API_KEY: Optional[str] = None
    CLOUDINARY_API_SECRET: Optional[str] = None

    # Email Settings
    SMTP_HOST: str = "smtp.gmail.com"
    SMTP_PORT: int = 587
    SMTP_USER: Optional[str] = None
    SMTP_PASSWORD: Optional[str] = None
    EMAILS_FROM_EMAIL: Optional[str] = None
    EMAILS_FROM_NAME: str = "CampusPQ"
    EMAIL_VERIFICATION_TOKEN_EXPIRE_HOURS: int = 24
    PASSWORD_RESET_TOKEN_EXPIRE_HOURS: int = 1

    # Frontend URL for email verification links
    FRONTEND_URL: str = "http://localhost:5173"


settings = Settings()
