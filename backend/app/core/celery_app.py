from celery import Celery
import logging

from app.core.config import settings

logger = logging.getLogger(__name__)

# Initialize Celery only if Redis URL is configured
celery_app = None

if settings.REDIS_URL:
    try:
        celery_app = Celery(
            "worker",
            broker=settings.REDIS_URL,
            backend=None,  # Disable result backend to reduce Redis connections
            include=["app.tasks"],
        )
        logger.info("Celery initialized successfully")
    except Exception as e:
        logger.warning(f"Failed to initialize Celery: {str(e)} - background tasks disabled")
        celery_app = None
else:
    logger.info("Redis URL not configured - Celery disabled")

# Use default queue for all tasks to ensure they are processed
# celery_app.conf.task_routes = {
#     "app.tasks.process_pdf_for_questions": "main-queue",
#     "app.tasks.generate_ai_questions": "ai-queue",
#     "app.tasks.process_note": "note-queue",
#     "app.tasks.generate_mcqs": "mcq-queue",
#     "app.tasks.generate_flash_cards": "flash-card-queue",
# }

# Configure Celery only if it was successfully initialized
if celery_app:
    celery_app.conf.update(
        task_serializer="json",
        accept_content=["json"],
        result_serializer="json",
        timezone="UTC",
        enable_utc=True,
        worker_prefetch_multiplier=1,  # Reduced to minimize Redis connections
        task_acks_late=True,
        worker_max_tasks_per_child=10,  # Reduced to prevent connection buildup
        task_time_limit=300,  # 5 minutes max per task (reduced from 10)
        task_soft_time_limit=240,  # 4 minutes soft limit
        worker_disable_rate_limits=True,  # Remove rate limiting for speed
        task_compression='gzip',  # Compress task data
        result_compression='gzip',  # Compress results

        # Connection optimization with timeouts
        broker_connection_retry_on_startup=True,
        broker_connection_retry=True,
        broker_connection_max_retries=3,  # Reduced retries
        broker_pool_limit=2,  # Very low connection pool limit
        result_backend_max_connections=2,  # Very low result backend connections

        # Connection management with timeouts
        broker_transport_options={
            'max_connections': 2,  # Minimal connections per worker
            'retry_on_timeout': True,
            'socket_keepalive': False,  # Disable keepalive to reduce connections
            'socket_connect_timeout': 5,  # Add connection timeout
            'socket_timeout': 10,  # Add socket timeout
        },

        task_routes={
            'app.tasks.process_note_lightning': {'queue': 'lightning_queue'},
            'app.tasks.generate_mcqs_lightning': {'queue': 'lightning_queue'},
            'app.tasks.generate_flash_cards_lightning': {'queue': 'lightning_queue'},
            'app.tasks.generate_summary_lightning': {'queue': 'lightning_queue'},
            'app.tasks.upload_pdf_to_gemini_task': {'queue': 'celery'},  # Default queue for uploads
        },
    )
