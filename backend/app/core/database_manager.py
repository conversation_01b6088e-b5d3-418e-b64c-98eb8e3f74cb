"""
Automatic Database Management System
Handles database creation, migration, and initialization automatically
Works with both local PostgreSQL and Supabase
"""

import os
import logging
from typing import Optional
from sqlalchemy import create_engine, text, inspect
from sqlalchemy.exc import OperationalError, ProgrammingError
from sqlalchemy.engine import Engine

from app.core.config import settings
from app.db.base import Base
from app.db.session import engine
from app.models import *  # Import all models to ensure they're registered

logger = logging.getLogger(__name__)

class DatabaseManager:
    """Automatic database management system"""
    
    def __init__(self):
        self.engine = engine
        self.database_url = settings.DATABASE_URL
        self.is_supabase = "supabase" in self.database_url.lower()
        
    def get_database_name(self) -> str:
        """Extract database name from URL"""
        try:
            # Parse database name from URL
            if "postgresql://" in self.database_url:
                return self.database_url.split("/")[-1].split("?")[0]
            return "campuspq"  # Default name
        except:
            return "campuspq"
    
    def create_database_if_not_exists(self) -> bool:
        """Create database if it doesn't exist (for local PostgreSQL only)"""
        if self.is_supabase:
            logger.info("Using Supabase - database creation handled by Supabase")
            return True
            
        try:
            db_name = self.get_database_name()
            
            # Create engine without database name to connect to postgres
            base_url = self.database_url.rsplit('/', 1)[0]
            temp_engine = create_engine(f"{base_url}/postgres")
            
            with temp_engine.connect() as conn:
                # Check if database exists
                result = conn.execute(
                    text("SELECT 1 FROM pg_database WHERE datname = :db_name"),
                    {"db_name": db_name}
                )
                
                if not result.fetchone():
                    # Database doesn't exist, create it
                    conn.execute(text("COMMIT"))  # End any transaction
                    conn.execute(text(f'CREATE DATABASE "{db_name}"'))
                    logger.info(f"Created database: {db_name}")
                else:
                    logger.info(f"Database {db_name} already exists")
                    
            temp_engine.dispose()
            return True
            
        except Exception as e:
            logger.error(f"Error creating database: {e}")
            return False
    
    def check_connection(self) -> bool:
        """Check if database connection is working"""
        try:
            with self.engine.connect() as conn:
                conn.execute(text("SELECT 1"))
            logger.info("Database connection successful")
            return True
        except Exception as e:
            logger.error(f"Database connection failed: {e}")
            return False
    
    def get_existing_tables(self) -> set:
        """Get list of existing tables in database"""
        try:
            inspector = inspect(self.engine)
            return set(inspector.get_table_names())
        except Exception as e:
            logger.error(f"Error getting table list: {e}")
            return set()
    
    def create_all_tables(self) -> bool:
        """Create all tables defined in models"""
        try:
            logger.info("Creating all database tables...")
            Base.metadata.create_all(bind=self.engine)
            logger.info("All tables created successfully")
            return True
        except Exception as e:
            logger.error(f"Error creating tables: {e}")
            return False
    
    def drop_all_tables(self) -> bool:
        """Drop all tables (use with caution!)"""
        try:
            logger.warning("Dropping all database tables...")
            Base.metadata.drop_all(bind=self.engine)
            logger.info("All tables dropped successfully")
            return True
        except Exception as e:
            logger.error(f"Error dropping tables: {e}")
            return False
    
    def recreate_all_tables(self) -> bool:
        """Drop and recreate all tables"""
        try:
            logger.info("Recreating all database tables...")
            self.drop_all_tables()
            return self.create_all_tables()
        except Exception as e:
            logger.error(f"Error recreating tables: {e}")
            return False
    
    def check_table_exists(self, table_name: str) -> bool:
        """Check if a specific table exists"""
        existing_tables = self.get_existing_tables()
        return table_name in existing_tables
    
    def initialize_database(self, force_recreate: bool = False) -> bool:
        """
        Main initialization method
        
        Args:
            force_recreate: If True, drop and recreate all tables
        """
        try:
            logger.info("Starting database initialization...")
            
            # Step 1: Create database if needed (local only)
            if not self.create_database_if_not_exists():
                return False
            
            # Step 2: Check connection
            if not self.check_connection():
                return False
            
            # Step 3: Handle table creation
            if force_recreate:
                logger.info("Force recreate enabled - dropping and recreating all tables")
                return self.recreate_all_tables()
            else:
                # Check if tables exist
                existing_tables = self.get_existing_tables()
                expected_tables = {
                    'user', 'school', 'department', 'course', 'question', 
                    'session', 'question_flag', 'admin_flashcard', 
                    'generated_flashcard', 'tutor_profile', 'booking_request'
                }
                
                missing_tables = expected_tables - existing_tables
                
                if missing_tables:
                    logger.info(f"Missing tables detected: {missing_tables}")
                    logger.info("Creating missing tables...")
                    return self.create_all_tables()
                else:
                    logger.info("All required tables exist")
                    return True
                    
        except Exception as e:
            logger.error(f"Database initialization failed: {e}")
            return False

# Global instance
db_manager = DatabaseManager()

def auto_initialize_database() -> bool:
    """
    Auto-initialize database based on environment variables
    """
    # Check environment variable for force recreate
    force_recreate = os.getenv("DB_FORCE_RECREATE", "false").lower() == "true"
    
    # Check if auto-init is disabled
    auto_init = os.getenv("DB_AUTO_INIT", "true").lower() == "true"
    
    if not auto_init:
        logger.info("Database auto-initialization disabled")
        return True
    
    logger.info("Auto-initializing database...")
    return db_manager.initialize_database(force_recreate=force_recreate)

def get_database_status() -> dict:
    """Get current database status for debugging"""
    try:
        existing_tables = db_manager.get_existing_tables()
        connection_ok = db_manager.check_connection()
        
        return {
            "connection": connection_ok,
            "database_url": db_manager.database_url,
            "is_supabase": db_manager.is_supabase,
            "database_name": db_manager.get_database_name(),
            "existing_tables": list(existing_tables),
            "table_count": len(existing_tables)
        }
    except Exception as e:
        return {
            "error": str(e),
            "connection": False
        }
