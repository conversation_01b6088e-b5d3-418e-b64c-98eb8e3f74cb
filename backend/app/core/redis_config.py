"""
Redis connection configuration optimized for Redis Cloud
"""
import redis
from urllib.parse import urlparse
from app.core.config import settings


def create_redis_pool():
    """
    Create a Redis connection pool optimized for Redis Cloud with connection limits
    """
    # Parse Redis URL
    url = urlparse(settings.REDIS_URL)
    
    # Create connection pool with strict limits for Redis Cloud
    pool = redis.ConnectionPool(
        host=url.hostname,
        port=url.port or 6379,
        password=url.password,
        db=int(url.path[1:]) if url.path and len(url.path) > 1 else 0,
        
        # Connection pool settings optimized for Redis Cloud
        max_connections=10,  # Very low limit for Redis Cloud free tier
        retry_on_timeout=True,
        socket_keepalive=True,
        socket_connect_timeout=5,
        socket_timeout=5,
        
        # Health check settings
        health_check_interval=30,
    )
    
    return pool


def get_redis_client():
    """
    Get a Redis client using the optimized connection pool
    """
    pool = create_redis_pool()
    return redis.Redis(connection_pool=pool)


# Global Redis client for the application
redis_client = get_redis_client()
