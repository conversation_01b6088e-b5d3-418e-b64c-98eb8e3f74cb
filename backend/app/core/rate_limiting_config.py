"""
Rate limiting configuration for API services.
"""
from dataclasses import dataclass
from typing import Optional


@dataclass
class RateLimitConfig:
    """Configuration for rate limiting API calls."""
    
    # Gemini API rate limiting (Optimized for speed while maintaining quality)
    gemini_max_requests_per_minute: int = 15  # Use full free tier limit
    gemini_min_request_interval: float = 1.0  # 1 second between requests (much faster)
    gemini_max_retries: int = 3  # Fewer retries for faster processing
    gemini_base_delay: float = 1.0  # Shorter base delay
    gemini_max_delay: float = 60.0  # Maximum delay (1 minute, not 5 minutes)
    gemini_jitter_range: float = 0.1  # 10% jitter
    
    # OpenAI API rate limiting (if used)
    openai_max_requests_per_minute: int = 60
    openai_min_request_interval: float = 1.0
    openai_max_retries: int = 3
    openai_base_delay: float = 1.0
    openai_max_delay: float = 60.0
    openai_jitter_range: float = 0.1
    
    # General API settings
    enable_rate_limiting: bool = True
    enable_retry_logic: bool = True
    log_rate_limit_events: bool = True
    
    # Fallback settings
    enable_ocr_fallback: bool = True
    ocr_fallback_on_rate_limit: bool = True
    max_fallback_attempts: int = 2


@dataclass
class ProcessingConfig:
    """Configuration for PDF processing pipeline."""
    
    # Processing preferences
    prefer_gemini_over_ocr: bool = True
    use_enhanced_pipeline: bool = True
    enable_parallel_processing: bool = True
    
    # Chunking settings
    default_chunk_size: int = 1200  # tokens
    max_chunk_overlap: int = 200
    preserve_headings: bool = True
    
    # Vector store settings
    enable_vector_indexing: bool = True
    vector_similarity_threshold: float = 0.7
    max_chunks_per_document: int = 100
    
    # MCQ generation settings
    multiagent_threshold: int = 15  # Use multiagent for 15+ questions
    max_questions_per_agent: int = 12
    max_agents: int = 8
    agent_timeout: int = 60  # seconds


# Global configuration instances
rate_limit_config = RateLimitConfig()
processing_config = ProcessingConfig()


def update_rate_limits_for_tier(tier: str = "free"):
    """Update rate limits based on API tier."""
    global rate_limit_config
    
    if tier.lower() == "free":
        # Gemini free tier: 15 requests per minute (optimized for speed)
        rate_limit_config.gemini_max_requests_per_minute = 15
        rate_limit_config.gemini_min_request_interval = 1.0  # Much faster
        rate_limit_config.gemini_max_retries = 3  # Fewer retries
        rate_limit_config.gemini_base_delay = 1.0  # Shorter delays
        
    elif tier.lower() == "paid":
        # Gemini paid tier: higher limits
        rate_limit_config.gemini_max_requests_per_minute = 60
        rate_limit_config.gemini_min_request_interval = 1.0
        rate_limit_config.gemini_max_retries = 3
        rate_limit_config.gemini_base_delay = 1.0
        
    elif tier.lower() == "enterprise":
        # Enterprise tier: very high limits
        rate_limit_config.gemini_max_requests_per_minute = 300
        rate_limit_config.gemini_min_request_interval = 0.2
        rate_limit_config.gemini_max_retries = 3
        rate_limit_config.gemini_base_delay = 0.5


def get_rate_limit_settings() -> dict:
    """Get current rate limiting settings as dictionary."""
    return {
        "gemini": {
            "max_requests_per_minute": rate_limit_config.gemini_max_requests_per_minute,
            "min_request_interval": rate_limit_config.gemini_min_request_interval,
            "max_retries": rate_limit_config.gemini_max_retries,
            "base_delay": rate_limit_config.gemini_base_delay,
            "max_delay": rate_limit_config.gemini_max_delay,
        },
        "general": {
            "enable_rate_limiting": rate_limit_config.enable_rate_limiting,
            "enable_retry_logic": rate_limit_config.enable_retry_logic,
            "enable_ocr_fallback": rate_limit_config.enable_ocr_fallback,
        }
    }
