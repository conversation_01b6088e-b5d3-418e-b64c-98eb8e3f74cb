import asyncio
import json
import logging
import pickle
from typing import Any, Optional, Union
import redis.asyncio as redis
from app.core.config import settings

logger = logging.getLogger(__name__)


class RedisCache:
    """
    High-performance Redis cache for PDF processing optimization.
    Supports both text and binary data caching.
    """
    
    def __init__(self):
        self.redis_client = None
        self._connection_pool = None
        
    async def _get_client(self):
        """Get or create Redis client with connection pooling."""
        if self.redis_client is None:
            try:
                # Check if Redis URL is configured
                if not settings.REDIS_URL:
                    logger.info("Redis URL not configured - caching disabled")
                    return None

                # Create connection pool for better performance
                self._connection_pool = redis.ConnectionPool.from_url(
                    settings.REDIS_URL,
                    max_connections=10,  # Reduced for stability
                    retry_on_timeout=True,
                    socket_keepalive=True,
                    socket_keepalive_options={},
                    socket_connect_timeout=5,  # Add connection timeout
                    socket_timeout=5  # Add socket timeout
                )
                self.redis_client = redis.Redis(connection_pool=self._connection_pool)

                # Test connection with timeout
                await asyncio.wait_for(self.redis_client.ping(), timeout=5.0)
                logger.info("Redis cache connected successfully")

            except asyncio.TimeoutError:
                logger.warning("Redis connection timeout - caching disabled")
                self.redis_client = None
            except Exception as e:
                logger.warning(f"Redis cache unavailable: {str(e)} - caching disabled")
                self.redis_client = None

        return self.redis_client
    
    async def get(self, key: str) -> Optional[Any]:
        """
        Get value from cache.
        
        Args:
            key: Cache key
            
        Returns:
            Cached value or None if not found
        """
        try:
            client = await self._get_client()
            if client is None:
                return None
                
            data = await client.get(key)
            if data is None:
                return None
                
            # Try to deserialize as JSON first, then pickle
            try:
                return json.loads(data)
            except (json.JSONDecodeError, TypeError):
                try:
                    return pickle.loads(data)
                except:
                    # Return raw bytes if deserialization fails
                    return data
                    
        except Exception as e:
            logger.warning(f"Cache get error for key {key}: {str(e)}")
            return None
    
    async def set(
        self, 
        key: str, 
        value: Any, 
        ttl: int = 3600
    ) -> bool:
        """
        Set value in cache.
        
        Args:
            key: Cache key
            value: Value to cache
            ttl: Time to live in seconds
            
        Returns:
            True if successful, False otherwise
        """
        try:
            client = await self._get_client()
            if client is None:
                return False
                
            # Serialize data efficiently
            if isinstance(value, (str, int, float, bool)):
                data = json.dumps(value)
            elif isinstance(value, (dict, list)):
                try:
                    data = json.dumps(value)
                except (TypeError, ValueError):
                    data = pickle.dumps(value)
            elif isinstance(value, bytes):
                data = value
            else:
                data = pickle.dumps(value)
                
            await client.setex(key, ttl, data)
            return True
            
        except Exception as e:
            logger.warning(f"Cache set error for key {key}: {str(e)}")
            return False
    
    async def delete(self, key: str) -> bool:
        """
        Delete key from cache.
        
        Args:
            key: Cache key to delete
            
        Returns:
            True if successful, False otherwise
        """
        try:
            client = await self._get_client()
            if client is None:
                return False
                
            await client.delete(key)
            return True
            
        except Exception as e:
            logger.warning(f"Cache delete error for key {key}: {str(e)}")
            return False
    
    async def exists(self, key: str) -> bool:
        """
        Check if key exists in cache.
        
        Args:
            key: Cache key to check
            
        Returns:
            True if key exists, False otherwise
        """
        try:
            client = await self._get_client()
            if client is None:
                return False
                
            result = await client.exists(key)
            return bool(result)
            
        except Exception as e:
            logger.warning(f"Cache exists error for key {key}: {str(e)}")
            return False
    
    async def clear_pattern(self, pattern: str) -> int:
        """
        Clear all keys matching pattern.
        
        Args:
            pattern: Pattern to match (e.g., "pdf_content:*")
            
        Returns:
            Number of keys deleted
        """
        try:
            client = await self._get_client()
            if client is None:
                return 0
                
            keys = await client.keys(pattern)
            if keys:
                deleted = await client.delete(*keys)
                return deleted
            return 0
            
        except Exception as e:
            logger.warning(f"Cache clear pattern error for {pattern}: {str(e)}")
            return 0
    
    async def close(self):
        """Close Redis connection."""
        try:
            if self.redis_client:
                await self.redis_client.close()
            if self._connection_pool:
                await self._connection_pool.disconnect()
        except Exception as e:
            logger.warning(f"Error closing Redis connection: {str(e)}")


# Create singleton instance
redis_cache = RedisCache()
