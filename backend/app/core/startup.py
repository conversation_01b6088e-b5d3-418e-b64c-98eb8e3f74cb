"""
Application Startup Manager
Handles all startup tasks including database initialization
"""

import logging
import sys
from typing import Optional

from app.core.database_manager import auto_initialize_database, get_database_status
from app.core.config import settings

logger = logging.getLogger(__name__)

class StartupManager:
    """Manages application startup tasks"""
    
    def __init__(self):
        self.startup_tasks = []
        self.startup_complete = False
    
    def add_startup_task(self, task_name: str, task_func, critical: bool = True):
        """Add a startup task"""
        self.startup_tasks.append({
            "name": task_name,
            "func": task_func,
            "critical": critical
        })
    
    def run_startup_tasks(self) -> bool:
        """Run all startup tasks"""
        logger.info("Starting application initialization...")
        
        failed_tasks = []
        
        for task in self.startup_tasks:
            try:
                logger.info(f"Running startup task: {task['name']}")
                success = task['func']()
                
                if not success and task['critical']:
                    logger.error(f"Critical startup task failed: {task['name']}")
                    failed_tasks.append(task['name'])
                elif not success:
                    logger.warning(f"Non-critical startup task failed: {task['name']}")
                else:
                    logger.info(f"Startup task completed: {task['name']}")
                    
            except Exception as e:
                logger.error(f"Startup task '{task['name']}' raised exception: {e}")
                if task['critical']:
                    failed_tasks.append(task['name'])
        
        if failed_tasks:
            logger.error(f"Critical startup tasks failed: {failed_tasks}")
            return False
        
        self.startup_complete = True
        logger.info("Application initialization completed successfully")
        return True

# Global startup manager
startup_manager = StartupManager()

def initialize_application() -> bool:
    """Initialize the entire application"""
    
    # Add database initialization task
    startup_manager.add_startup_task(
        "Database Initialization",
        auto_initialize_database,
        critical=True
    )
    
    # Add other startup tasks here as needed
    # startup_manager.add_startup_task("Redis Connection", check_redis, critical=False)
    # startup_manager.add_startup_task("External APIs", check_external_apis, critical=False)
    
    return startup_manager.run_startup_tasks()

def get_application_status() -> dict:
    """Get application status for health checks"""
    db_status = get_database_status()
    
    return {
        "startup_complete": startup_manager.startup_complete,
        "database": db_status,
        "environment": {
            "debug": settings.DEBUG,
            "environment": getattr(settings, 'ENVIRONMENT', 'development'),
        }
    }

def handle_startup_failure():
    """Handle startup failure"""
    logger.error("Application startup failed!")
    logger.error("Please check your configuration and database connection.")
    logger.error("Database status:")
    
    try:
        status = get_database_status()
        for key, value in status.items():
            logger.error(f"  {key}: {value}")
    except Exception as e:
        logger.error(f"  Could not get database status: {e}")
    
    logger.error("Exiting application...")
    sys.exit(1)
