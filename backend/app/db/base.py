# Import all the models, so that Base has them before being
# imported by <PERSON><PERSON><PERSON>
from app.db.base_class import Base  # noqa
from app.models.user import User  # noqa
from app.models.school import School  # noqa
from app.models.department import Department  # noqa
from app.models.course import Course  # noqa
from app.models.question import Question  # noqa
from app.models.session import Session  # noqa
from app.models.tutor import Tu<PERSON>Profile, TutorReview  # noqa
from app.models.student_progress import StudentQuestionAttempt, StudentBookmark, StudentExam  # noqa
from app.models.gamification import Badge, UserBadge, PointsTransaction, UserLevel, UserStreak  # noqa
from app.models.ai_analytics import AIAssistantInteraction  # noqa
from app.models.generated_flashcard import GeneratedFlashcard  # noqa
from app.models.generated_note_explanation import GeneratedNoteExplanation  # noqa
from app.models.student_usage_limit import StudentUsageLimit  # noqa
from app.models.ml_recommendations import (  # noqa
    UserBehaviorEvent, UserFeatureVector, ContentFeatureVector,
    MLRecommendation, MLModelMetadata, UserLearningSession
)
from app.models.gemini_file import GeminiFile  # noqa
from app.models.upload_task import UploadTask  # noqa
from app.models.refresh_token import RefreshToken  # noqa
from app.models.content_access import ContentAccess, ShareInvitation, PublicShare  # noqa
from app.models.question_flag import QuestionFlag  # noqa

