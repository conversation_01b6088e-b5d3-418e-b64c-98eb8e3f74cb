"""
Monthly tasks for resetting usage limits and cleanup
"""

import logging
from datetime import datetime, timezone
from sqlalchemy.orm import Session

from app.db.session import SessionLocal
from app.services.usage_limit_service import usage_limit_service
from app.models.student_usage_limit import StudentUsageLimit

logger = logging.getLogger(__name__)


def reset_monthly_usage_limits():
    """
    Reset usage limits for all students at the beginning of each month.
    This should be run as a scheduled task on the 1st of each month.
    """
    db = SessionLocal()
    
    try:
        logger.info("Starting monthly usage limits reset...")
        
        # Get current month
        current_month = usage_limit_service.get_current_month()
        logger.info(f"Current month: {current_month}")
        
        # Get all usage records from previous months
        previous_records = db.query(StudentUsageLimit).filter(
            StudentUsageLimit.usage_month != current_month
        ).all()
        
        if not previous_records:
            logger.info("No previous month records found to reset")
            return 0
        
        # Reset usage counts for previous month records
        reset_count = 0
        for record in previous_records:
            if record.usage_count > 0:
                logger.info(
                    f"Resetting usage for student {record.student_id}, "
                    f"type {record.usage_type}, month {record.usage_month}: "
                    f"{record.usage_count} -> 0"
                )
                record.usage_count = 0
                reset_count += 1
        
        db.commit()
        
        logger.info(f"Monthly usage limits reset completed. Reset {reset_count} records.")
        return reset_count
        
    except Exception as e:
        logger.error(f"Error during monthly usage limits reset: {str(e)}")
        db.rollback()
        raise
    
    finally:
        db.close()


def cleanup_old_usage_records():
    """
    Clean up usage records older than 12 months to keep database size manageable.
    This should be run monthly after the usage reset.
    """
    db = SessionLocal()
    
    try:
        logger.info("Starting cleanup of old usage records...")
        
        # Calculate cutoff date (12 months ago)
        current_date = datetime.now(timezone.utc)
        cutoff_year = current_date.year
        cutoff_month = current_date.month - 12
        
        if cutoff_month <= 0:
            cutoff_month += 12
            cutoff_year -= 1
        
        cutoff_month_str = f"{cutoff_year}-{cutoff_month:02d}"
        logger.info(f"Deleting usage records older than: {cutoff_month_str}")
        
        # Delete old records
        old_records = db.query(StudentUsageLimit).filter(
            StudentUsageLimit.usage_month < cutoff_month_str
        ).all()
        
        if not old_records:
            logger.info("No old usage records found to delete")
            return 0
        
        delete_count = len(old_records)
        
        for record in old_records:
            db.delete(record)
        
        db.commit()
        
        logger.info(f"Cleanup completed. Deleted {delete_count} old usage records.")
        return delete_count
        
    except Exception as e:
        logger.error(f"Error during usage records cleanup: {str(e)}")
        db.rollback()
        raise
    
    finally:
        db.close()


def run_monthly_maintenance():
    """
    Run all monthly maintenance tasks.
    This is the main function that should be called by the scheduler.
    """
    logger.info("Starting monthly maintenance tasks...")
    
    try:
        # Reset usage limits
        reset_count = reset_monthly_usage_limits()
        
        # Cleanup old records
        cleanup_count = cleanup_old_usage_records()
        
        logger.info(
            f"Monthly maintenance completed successfully. "
            f"Reset {reset_count} usage records, deleted {cleanup_count} old records."
        )
        
        return {
            "success": True,
            "reset_count": reset_count,
            "cleanup_count": cleanup_count,
            "timestamp": datetime.now(timezone.utc).isoformat()
        }
        
    except Exception as e:
        logger.error(f"Monthly maintenance failed: {str(e)}")
        return {
            "success": False,
            "error": str(e),
            "timestamp": datetime.now(timezone.utc).isoformat()
        }


if __name__ == "__main__":
    # Allow running this script directly for testing
    import sys
    import os
    
    # Add the backend directory to the Python path
    sys.path.insert(0, os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
    
    # Configure logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
    )
    
    print("Running monthly maintenance tasks...")
    result = run_monthly_maintenance()
    print(f"Result: {result}")
