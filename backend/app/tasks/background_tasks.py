"""
Background tasks - Celery optional for simplified architecture
"""
import asyncio
import os
import tempfile
import io
from datetime import datetime
from typing import Dict, Any, Optional

from sqlalchemy.orm import Session
from fastapi import UploadFile

from app.core.celery_app import celery_app
from app.db.session import SessionLocal
from app.services.gemini_files_service import GeminiFilesService
from app.core.config import settings

import logging

logger = logging.getLogger(__name__)


# Utility function to run async code in sync context safely
def run_async_safely(coro):
    """Run async coroutine safely in sync context with timeout"""
    try:
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            # Add timeout to prevent hanging
            return loop.run_until_complete(asyncio.wait_for(coro, timeout=300.0))  # 5 minute timeout
        finally:
            loop.close()
    except asyncio.TimeoutError:
        logger.error("Async operation timed out after 5 minutes")
        raise
    except Exception as e:
        logger.error(f"Error running async operation: {str(e)}")
        raise


def cleanup_expired_gemini_files_sync() -> Dict[str, Any]:
    """
    Synchronous cleanup of expired Gemini files
    Can be called directly or via Celery
    """
    try:
        logger.info("Starting cleanup of expired Gemini files")

        # Create database session with timeout
        db = SessionLocal()

        try:
            # Initialize Gemini files service
            gemini_service = GeminiFilesService()

            # Run the cleanup with timeout protection
            cleanup_result = run_async_safely(
                gemini_service.cleanup_expired_files(db)
            )
            
            return {
                "status": "success",
                "message": f"Cleanup completed successfully",
                "files_cleaned": cleanup_result.get("files_cleaned", 0),
                "files_failed": cleanup_result.get("files_failed", 0),
                "timestamp": datetime.utcnow().isoformat()
            }
                
        finally:
            try:
                db.close()
            except:
                pass  # Ignore close errors
            
    except Exception as e:
        logger.error(f"Error in cleanup task: {str(e)}")
        return {
            "status": "error",
            "message": f"Cleanup failed: {str(e)}",
            "files_cleaned": 0,
            "files_failed": 0,
            "timestamp": datetime.utcnow().isoformat()
        }


def upload_pdf_to_gemini_sync(
    upload_id: str,
    student_id: int,
    file_data: bytes,
    original_filename: str,
    file_size: int,
    course_name: Optional[str] = None
) -> Dict[str, Any]:
    """
    Synchronous PDF upload to Gemini
    Can be called directly or via Celery
    """
    try:
        logger.info(f"Starting PDF upload to Gemini: {upload_id}")

        # Create database session
        db = SessionLocal()

        try:
            # Initialize Gemini files service
            gemini_service = GeminiFilesService()

            # Create temporary file
            with tempfile.NamedTemporaryFile(delete=False, suffix='.pdf') as temp_file:
                temp_file.write(file_data)
                temp_file_path = temp_file.name

            try:
                # Import streaming file handler
                from app.utils.streaming_file_handler import streaming_file_handler

                # Create memory-efficient UploadFile object
                mock_file = streaming_file_handler.create_memory_efficient_upload_file(
                    file_data, original_filename, "application/pdf"
                )

                # Upload to Gemini with timeout protection
                upload_result = run_async_safely(
                    gemini_service.upload_pdf_to_gemini(
                        file=mock_file,
                        student_id=student_id,
                        db=db,
                        course_name=course_name,
                        file_content=file_data
                    )
                )

                # Clean up file data from memory
                del file_data

                return {
                    "status": "success",
                    "message": "PDF uploaded successfully",
                    "upload_result": upload_result,
                    "timestamp": datetime.utcnow().isoformat()
                }

            finally:
                # Clean up temp file
                try:
                    os.unlink(temp_file_path)
                except:
                    pass

        finally:
            try:
                db.close()
            except:
                pass

    except Exception as e:
        logger.error(f"Error in upload task {upload_id}: {str(e)}")
        return {
            "status": "error",
            "message": f"Upload failed: {str(e)}",
            "timestamp": datetime.utcnow().isoformat()
        }


# Define Celery tasks if available, otherwise use direct functions
if celery_app:
    @celery_app.task(bind=True)
    def cleanup_expired_gemini_files_task(self) -> Dict[str, Any]:
        """Celery task wrapper for cleanup"""
        return cleanup_expired_gemini_files_sync()

    @celery_app.task(bind=True)
    def upload_pdf_to_gemini_task(
        self,
        upload_id: str,
        student_id: int,
        file_data: bytes,
        original_filename: str,
        file_size: int,
        course_name: Optional[str] = None
    ) -> Dict[str, Any]:
        """Celery task wrapper for PDF upload"""
        return upload_pdf_to_gemini_sync(
            upload_id, student_id, file_data, original_filename, file_size, course_name
        )

else:
    def cleanup_expired_gemini_files_task() -> Dict[str, Any]:
        """Direct function call when Celery unavailable"""
        logger.info("Running cleanup directly (Celery not available)")
        return cleanup_expired_gemini_files_sync()

    def upload_pdf_to_gemini_task(
        upload_id: str,
        student_id: int,
        file_data: bytes,
        original_filename: str,
        file_size: int,
        course_name: Optional[str] = None
    ) -> Dict[str, Any]:
        """Direct function call when Celery unavailable"""
        logger.info(f"Running upload directly (Celery not available): {upload_id}")
        return upload_pdf_to_gemini_sync(
            upload_id, student_id, file_data, original_filename, file_size, course_name
        )


# Legacy task stubs for compatibility
def cleanup_duplicate_uploads_task() -> Dict[str, Any]:
    """Legacy task - no longer needed with direct uploads"""
    return {
        "status": "skipped",
        "message": "Duplicate cleanup not needed with direct uploads",
        "timestamp": datetime.utcnow().isoformat()
    }
