from datetime import datetime
from typing import Optional

from pydantic import BaseModel, ConfigDict


# Shared properties
class DepartmentBase(BaseModel):
    name: str
    description: str
    is_active: bool = True
    school_id: int


# Properties to receive via API on creation
class DepartmentCreate(DepartmentBase):
    pass


# Properties to receive via API on update
class DepartmentUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    is_active: Optional[bool] = None
    school_id: Optional[int] = None


# Properties shared by models stored in DB
class DepartmentInDBBase(DepartmentBase):
    id: int
    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True)


# Properties to return via API
class Department(DepartmentInDBBase):
    pass
