"""
Schemas for the simplified content access sharing system.
"""

from datetime import datetime
from typing import Optional, List, Any, Dict
from pydantic import BaseModel, ConfigD<PERSON>, EmailStr, Field

from app.models.content_access import ContentType, AccessType


# Base schemas
class ContentAccessBase(BaseModel):
    content_type: ContentType
    content_id: int
    access_type: AccessType
    share_message: Optional[str] = None


class ShareInvitationBase(BaseModel):
    content_type: ContentType
    content_id: int
    content_ids: Optional[str] = None  # JSON string of content IDs for batch sharing
    invited_email: EmailStr
    share_message: Optional[str] = None


class PublicShareBase(BaseModel):
    content_type: ContentType
    content_id: int
    content_ids: Optional[str] = None  # JSON string of content IDs for batch sharing
    share_message: Optional[str] = None


# Create schemas
class ContentAccessCreate(ContentAccessBase):
    user_id: int
    shared_by_id: int
    share_token: Optional[str] = None


class ShareInvitationCreate(ShareInvitationBase):
    shared_by_id: int


class PublicShareCreate(PublicShareBase):
    shared_by_id: int


# Update schemas
class ContentAccessUpdate(BaseModel):
    is_active: Optional[bool] = None
    accessed_at: Optional[datetime] = None


class ShareInvitationUpdate(BaseModel):
    is_accepted: Optional[bool] = None
    accepted_at: Optional[datetime] = None
    accepted_by_id: Optional[int] = None


class PublicShareUpdate(BaseModel):
    is_active: Optional[bool] = None
    share_message: Optional[str] = None


# Response schemas
class ContentAccess(ContentAccessBase):
    id: int
    user_id: int
    shared_by_id: int
    share_token: Optional[str] = None
    can_edit: bool
    can_delete: bool
    is_active: bool
    accessed_at: Optional[datetime] = None
    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True)


class ShareInvitation(ShareInvitationBase):
    id: int
    shared_by_id: int
    invitation_token: str
    is_accepted: bool
    accepted_at: Optional[datetime] = None
    accepted_by_id: Optional[int] = None
    created_at: datetime
    expires_at: Optional[datetime] = None

    model_config = ConfigDict(from_attributes=True)


class PublicShare(PublicShareBase):
    id: int
    shared_by_id: int
    share_token: str
    is_active: bool
    access_count: int
    created_at: datetime
    expires_at: Optional[datetime] = None

    model_config = ConfigDict(from_attributes=True)


# Special request/response schemas
class ShareContentRequest(BaseModel):
    """Request to share content with users"""
    content_type: ContentType
    content_id: int  # Primary content ID (for backward compatibility)
    content_ids: Optional[List[int]] = None  # Multiple content IDs for batch sharing
    share_message: Optional[str] = None
    invited_emails: Optional[List[EmailStr]] = Field(default_factory=list)
    create_public_link: bool = False


class ShareContentResponse(BaseModel):
    """Response after sharing content"""
    content_type: ContentType
    content_id: int  # Primary content ID
    content_ids: Optional[List[int]] = None  # All shared content IDs
    invitations_sent: List[str] = Field(default_factory=list)
    invitations_failed: List[str] = Field(default_factory=list)
    public_share_url: Optional[str] = None
    message: str


class AcceptInvitationRequest(BaseModel):
    """Request to accept a share invitation"""
    invitation_token: str


class AcceptInvitationResponse(BaseModel):
    """Response after accepting invitation"""
    success: bool
    message: str
    content_type: ContentType
    content_id: int
    redirect_url: Optional[str] = None


class AccessPublicShareRequest(BaseModel):
    """Request to access public share"""
    share_token: str


class AccessPublicShareResponse(BaseModel):
    """Response after accessing public share"""
    success: bool
    message: str
    content_type: ContentType
    content_id: int  # Primary content ID
    content_ids: Optional[List[int]] = None  # All content IDs that were shared
    redirect_url: Optional[str] = None


# Content with access info (for frontend display)
class SharedContentItem(BaseModel):
    """Content item with sharing metadata for display"""
    # Content details
    content_type: ContentType
    content_id: int
    content_data: Dict[str, Any]  # The actual content (question, flashcard, summary)
    
    # Sharing metadata
    is_shared: bool = False  # True if this content was shared with the user
    shared_by_name: Optional[str] = None
    shared_by_email: Optional[str] = None
    share_message: Optional[str] = None
    shared_at: Optional[datetime] = None
    
    # Permissions
    can_edit: bool = True  # False for shared content
    can_delete: bool = True  # False for shared content
    
    # Display tags
    tags: List[str] = Field(default_factory=list)  # e.g., ["shared", "read-only"]


class MySharedContentSummary(BaseModel):
    """Summary of content I've shared with others"""
    content_type: ContentType
    content_id: int
    content_title: str
    course_name: Optional[str] = None
    total_invitations: int
    accepted_invitations: int
    public_link_accesses: int
    has_public_link: bool
    created_at: datetime

    model_config = ConfigDict(from_attributes=True)


class SharedWithMeSummary(BaseModel):
    """Summary of content shared with me"""
    content_type: ContentType
    content_id: int
    content_title: str
    course_name: Optional[str] = None
    shared_by_name: str
    shared_by_email: str
    share_message: Optional[str] = None
    shared_at: datetime
    last_accessed: Optional[datetime] = None

    model_config = ConfigDict(from_attributes=True)
