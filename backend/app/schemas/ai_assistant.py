from typing import List, Optional, Literal

from pydantic import BaseModel, ConfigDict


class AIAssistantResponse(BaseModel):
    """Response from the AI assistant."""
    content: str
    sources: Optional[List[str]] = None

    model_config = ConfigDict(from_attributes=True)


class AIAssistantStreamEvent(BaseModel):
    """Streaming event from the AI assistant."""
    type: Literal["start", "token", "error", "end"]
    content: str = ""
    sources: Optional[List[str]] = None
    error: Optional[str] = None

    model_config = ConfigDict(from_attributes=True)


class AssistantQuery(BaseModel):
    """Query for the AI assistant."""
    query: str
