from datetime import datetime
from typing import Dict, Any, Optional
from pydantic import BaseModel, ConfigDict

from app.models.student_usage_limit import UsageType


class UsageLimitBase(BaseModel):
    """Base schema for usage limits"""
    usage_type: UsageType
    usage_count: int = 0
    usage_limit: int = 5
    usage_month: str


class UsageLimitCreate(UsageLimitBase):
    """Schema for creating usage limit records"""
    student_id: int


class UsageLimitUpdate(BaseModel):
    """Schema for updating usage limit records"""
    usage_count: Optional[int] = None
    usage_limit: Optional[int] = None


class UsageLimit(UsageLimitBase):
    """Schema for usage limit responses"""
    id: int
    student_id: int
    created_at: datetime
    updated_at: datetime
    
    model_config = ConfigDict(from_attributes=True)
    
    @property
    def is_limit_reached(self) -> bool:
        """Check if the usage limit has been reached"""
        return self.usage_count >= self.usage_limit
    
    @property
    def remaining_usage(self) -> int:
        """Get remaining usage count"""
        return max(0, self.usage_limit - self.usage_count)


class UsageCheckResponse(BaseModel):
    """Response schema for usage limit checks"""
    allowed: bool
    reason: str
    current_usage: int
    limit: int
    remaining: int
    month: str


class UsageStatsResponse(BaseModel):
    """Response schema for usage statistics"""
    student_id: int
    month: str
    usage_stats: Dict[str, Dict[str, Any]]
    
    model_config = ConfigDict(from_attributes=True)


class UsageLimitError(BaseModel):
    """Schema for usage limit error responses"""
    error: str
    usage_type: str
    current_usage: int
    limit: int
    remaining: int
    month: str
    reset_date: str  # Next month when limits reset
