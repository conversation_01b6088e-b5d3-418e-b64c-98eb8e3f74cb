from datetime import datetime
from typing import Optional

from pydantic import BaseModel, ConfigDict


# Shared properties
class SchoolBase(BaseModel):
    name: str
    description: str
    location: str
    is_active: bool = True


# Properties to receive via API on creation
class SchoolCreate(SchoolBase):
    pass


# Properties to receive via API on update
class SchoolUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    location: Optional[str] = None
    is_active: Optional[bool] = None


# Properties shared by models stored in DB
class SchoolInDBBase(SchoolBase):
    id: int
    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True)


# Properties to return via API
class School(SchoolInDBBase):
    pass
