from datetime import datetime
from typing import Optional

from pydantic import BaseModel, ConfigDict


# Shared properties
class GeminiFileBase(BaseModel):
    gemini_file_id: str
    original_filename: str
    file_size: int
    mime_type: str
    course_name: Optional[str] = None


# Properties to receive on item creation
class GeminiFileCreate(GeminiFileBase):
    student_id: int
    auto_delete_time: datetime


# Properties to receive on item update
class GeminiFileUpdate(BaseModel):
    course_name: Optional[str] = None
    is_deleted: Optional[bool] = None


# Properties shared by models stored in DB
class GeminiFileInDBBase(GeminiFileBase):
    id: int
    student_id: int
    upload_time: datetime
    auto_delete_time: datetime
    is_deleted: bool
    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True)


# Properties to return to client
class GeminiFile(GeminiFileInDBBase):
    pass


# Properties stored in DB
class GeminiFileInDB(GeminiFileInDBBase):
    pass


# Response schemas
class GeminiFileUploadResponse(BaseModel):
    gemini_file_id: str
    filename: str
    file_size: int
    course_name: Optional[str] = None
    message: str


class GeminiFileListResponse(BaseModel):
    files: list[GeminiFile]
    total_count: int
