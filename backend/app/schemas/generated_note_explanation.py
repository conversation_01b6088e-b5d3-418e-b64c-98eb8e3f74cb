from datetime import datetime
from typing import Optional, List, Dict, Any
from pydantic import BaseModel, ConfigDict, Field


class GeneratedNoteExplanationBase(BaseModel):
    title: str
    overview: str
    detailed_explanation: str
    comprehensive_summary: str
    key_concepts: List[str]
    main_topics: List[str]
    word_count: Optional[int] = None
    course_name: Optional[str] = None
    gemini_file_id: Optional[str] = None
    original_filename: Optional[str] = None
    is_active: bool = True


class GeneratedNoteExplanationCreate(GeneratedNoteExplanationBase):
    student_id: int


class GeneratedNoteExplanationUpdate(BaseModel):
    title: Optional[str] = None
    overview: Optional[str] = None
    detailed_explanation: Optional[str] = None
    comprehensive_summary: Optional[str] = None
    key_concepts: Optional[List[str]] = None
    main_topics: Optional[List[str]] = None
    word_count: Optional[int] = None
    course_name: Optional[str] = None
    is_active: Optional[bool] = None


class GeneratedNoteExplanationInDBBase(GeneratedNoteExplanationBase):
    id: int
    student_id: int
    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True)


class GeneratedNoteExplanation(GeneratedNoteExplanationInDBBase):
    pass


# Response schemas for note explanation generation
class NoteExplanationResponse(BaseModel):
    """Response for generated note explanation"""
    title: str
    overview: str
    detailed_explanation: str
    comprehensive_summary: str
    key_concepts: List[str]
    main_topics: List[str]
    word_count: int


class NoteExplanationGenerationResponse(BaseModel):
    """Response containing generated note explanation"""
    explanation: NoteExplanationResponse
    source_info: Dict[str, Any]
    saved_explanation_id: int
    message: str
