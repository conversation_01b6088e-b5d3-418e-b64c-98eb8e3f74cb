"""
Pydantic schemas for upload tasks
"""
from datetime import datetime
from typing import Optional, Dict, Any
from pydantic import BaseModel, Field

from app.models.upload_task import UploadStatus


class UploadTaskBase(BaseModel):
    """Base upload task schema"""
    original_filename: str
    file_size: int
    course_name: Optional[str] = None


class UploadTaskCreate(UploadTaskBase):
    """Schema for creating upload tasks"""
    upload_id: str
    task_id: str
    student_id: int


class UploadTaskUpdate(BaseModel):
    """Schema for updating upload tasks"""
    status: Optional[UploadStatus] = None
    progress: Optional[float] = None
    bytes_uploaded: Optional[int] = None
    upload_speed: Optional[float] = None
    estimated_time_remaining: Optional[float] = None
    error_message: Optional[str] = None
    error_details: Optional[Dict[str, Any]] = None
    result: Optional[Dict[str, Any]] = None
    gemini_file_id: Optional[str] = None


class UploadTaskResponse(UploadTaskBase):
    """Schema for upload task responses"""
    id: str = Field(alias="upload_id")
    task_id: str
    status: UploadStatus
    progress: float
    bytes_uploaded: int
    upload_speed: Optional[float] = None
    estimated_time_remaining: Optional[float] = None
    created_at: datetime
    started_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    error_message: Optional[str] = None
    result: Optional[Dict[str, Any]] = None
    gemini_file_id: Optional[str] = None
    
    class Config:
        from_attributes = True
        allow_population_by_field_name = True


class UploadProgressUpdate(BaseModel):
    """Schema for progress updates"""
    upload_id: str
    progress: float = Field(ge=0, le=100)
    status: UploadStatus
    message: Optional[str] = None
    bytes_uploaded: Optional[int] = None
    upload_speed: Optional[float] = None
    estimated_time_remaining: Optional[float] = None
    error: Optional[str] = None
    result: Optional[Dict[str, Any]] = None


class UploadStartRequest(BaseModel):
    """Schema for starting an upload"""
    course_name: Optional[str] = None


class UploadStartResponse(BaseModel):
    """Schema for upload start response"""
    upload_id: str
    task_id: str
    message: str
    status: str = "started"


class UploadListResponse(BaseModel):
    """Schema for listing uploads"""
    uploads: list[UploadTaskResponse]
    total: int
    active_count: int
    completed_count: int
    failed_count: int
