from datetime import datetime, timezone
from typing import Dict, Optional

from pydantic import BaseModel, ConfigDict

from app.models.ai_analytics import InteractionType


class AIAssistantInteractionBase(BaseModel):
    """Base schema for AI assistant interaction."""
    query: str
    response: str
    interaction_type: InteractionType
    interaction_metadata: Dict = {}  # Renamed from metadata to match model
    course_id: Optional[int] = None
    question_id: Optional[int] = None
    response_time_ms: Optional[int] = None
    success: bool = True


class AIAssistantInteractionCreate(AIAssistantInteractionBase):
    """Schema for creating an AI assistant interaction."""
    user_id: Optional[int] = None
    timestamp: datetime = datetime.now(timezone.utc)  # Use timezone-aware UTC time


class AIAssistantInteraction(AIAssistantInteractionBase):
    """Schema for an AI assistant interaction."""
    id: int
    user_id: Optional[int] = None
    timestamp: datetime

    model_config = ConfigDict(from_attributes=True)


class AIAssistantAnalytics(BaseModel):
    """Schema for AI assistant analytics."""
    total_interactions: int
    unique_users: int
    avg_interactions_per_user: float
    success_rate: float
    interactions_by_type: Dict[str, int]
    common_queries: list[Dict[str, str | int]]

    model_config = ConfigDict(from_attributes=True)
