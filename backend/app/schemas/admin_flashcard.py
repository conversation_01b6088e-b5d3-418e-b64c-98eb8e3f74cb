from datetime import datetime
from typing import Optional, List
from pydantic import BaseModel, ConfigDict


class AdminFlashCardBase(BaseModel):
    front_content: str
    back_content: str
    topic: Optional[str] = None
    difficulty: Optional[str] = None
    media_url: Optional[str] = None
    is_active: bool = True


class AdminFlashCardCreate(AdminFlashCardBase):
    course_id: int


class AdminFlashCardUpdate(BaseModel):
    front_content: Optional[str] = None
    back_content: Optional[str] = None
    topic: Optional[str] = None
    difficulty: Optional[str] = None
    media_url: Optional[str] = None
    is_active: Optional[bool] = None
    course_id: Optional[int] = None


class AdminFlashCardInDBBase(AdminFlashCardBase):
    id: int
    course_id: int
    created_by: int
    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True)


class AdminFlashCard(AdminFlashCardInDBBase):
    pass


class AdminFlashCardWithCourse(AdminFlashCardInDBBase):
    course_name: Optional[str] = None
    course_code: Optional[str] = None


# CSV Upload schemas
class AdminFlashCardCSVUploadResponse(BaseModel):
    success: bool
    total_rows: int
    created_flashcards: int
    validation_errors: List[str]
    creation_errors: List[str]
    flashcards: List[AdminFlashCard]


# Bulk operations
class AdminFlashCardBulkCreate(BaseModel):
    flashcards: List[AdminFlashCardCreate]


class AdminFlashCardBulkUpdate(BaseModel):
    flashcard_ids: List[int]
    update_data: AdminFlashCardUpdate


class AdminFlashCardBulkDelete(BaseModel):
    flashcard_ids: List[int]


# Image upload
class AdminFlashCardImageUploadResponse(BaseModel):
    success: bool
    image_url: str
    message: str


# Statistics
class AdminFlashCardStats(BaseModel):
    total: int
    active: int
    inactive: int
    by_course: List[dict]
    by_difficulty: List[dict]
    recent: int
