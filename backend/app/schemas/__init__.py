from app.schemas.token import Token, TokenPayload, RefreshTokenRequest
from app.schemas.user import User, UserCreate, UserInDB, UserUpdate, ProfileComplete, EmailVerificationRequest, EmailVerificationConfirm, EmailVerificationResponse, PasswordResetRequest, PasswordResetConfirm, PasswordResetResponse
from app.schemas.school import School, SchoolCreate, SchoolUpdate
from app.schemas.department import Department, DepartmentCreate, DepartmentUpdate
from app.schemas.course import Course, CourseCreate, CourseUpdate
from app.schemas.question import Question, QuestionCreate, QuestionUpdate, AIQuestionRequest
from app.schemas.session import Session, SessionCreate, SessionUpdate, SessionBookingRequest, SessionWithRelations, resolve_session_forward_refs
from app.schemas.booking_request import BookingRequest, BookingRequestCreate, BookingRequestUpdate, BookingRequestResponse, BookingRequestFilters, BookingRequestListItem
from app.schemas.chat import Chat<PERSON><PERSON>, Chat<PERSON>essage, ChatMessageCreate, UserOnlineStatus, WebSocketMessage, ChatJoinRequest, ChatMessageSend, OnlineStatusUpdate
from app.schemas.tutor import TutorProfile, TutorProfileCreate, TutorProfileUpdate, TutorReview, TutorReviewCreate, TutorReviewUpdate, TutorDashboardStats, TutorSearchFilters, TutorListItem
from app.schemas.ai_assistant import AIAssistantResponse, AssistantQuery
from app.schemas.student_progress import (
    StudentQuestionAttempt, StudentQuestionAttemptCreate, StudentQuestionAttemptUpdate,
    StudentBookmark, StudentBookmarkCreate, StudentBookmarkUpdate,
    StudentExam, StudentExamCreate, StudentExamUpdate, StudentExamWithAttempts,
    StudentExamAttempt, StudentExamAttemptCreate
)
from app.schemas.gamification import (
    Badge, BadgeCreate, BadgeUpdate,
    UserBadge, UserBadgeCreate,
    PointsTransaction, PointsTransactionCreate,
    UserLevel, UserLevelCreate, UserLevelUpdate,
    UserStreak, UserStreakCreate, UserStreakUpdate,
    UserGamificationProfile, LeaderboardEntry, Leaderboard
)

from app.schemas.ml_recommendations import (
    UserBehaviorEvent, UserBehaviorEventCreate,
    UserFeatureVector, UserFeatureVectorCreate,
    ContentFeatureVector, ContentFeatureVectorCreate,
    MLRecommendation, MLRecommendationCreate, MLRecommendationUpdate,
    MLModelMetadata, MLModelMetadataCreate,
    UserLearningSession, UserLearningSessionCreate, UserLearningSessionUpdate,
    BehaviorTrackingRequest, RecommendationRequest, RecommendationResponse,
    LearningInsightsResponse, SessionStartRequest, SessionEndRequest,
    ModelPerformanceResponse, SimilarUsersResponse
)
from app.schemas.admin_flashcard import (
    AdminFlashCard, AdminFlashCardCreate, AdminFlashCardUpdate, AdminFlashCardWithCourse,
    AdminFlashCardCSVUploadResponse, AdminFlashCardBulkCreate, AdminFlashCardBulkUpdate, AdminFlashCardBulkDelete,
    AdminFlashCardImageUploadResponse, AdminFlashCardStats
)
from app.schemas.notification import (
    Notification, NotificationCreate, NotificationUpdate, NotificationInDB,
    NotificationMarkRead, NotificationBulkAction
)
from app.schemas.content_access import (
    ContentAccess, ContentAccessCreate, ContentAccessUpdate,
    ShareInvitation, ShareInvitationCreate, ShareInvitationUpdate,
    PublicShare, PublicShareCreate, PublicShareUpdate,
    ShareContentRequest, ShareContentResponse,
    AcceptInvitationRequest, AcceptInvitationResponse,
    AccessPublicShareRequest, AccessPublicShareResponse,
    SharedContentItem, MySharedContentSummary, SharedWithMeSummary
)

# Resolve forward references after all schemas are imported
resolve_session_forward_refs()
