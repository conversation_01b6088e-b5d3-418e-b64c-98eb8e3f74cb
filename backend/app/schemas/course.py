from datetime import datetime
from typing import Optional

from pydantic import BaseModel, ConfigDict


# Shared properties
class CourseBase(BaseModel):
    name: str
    description: str
    code: str
    is_active: bool = True
    school_id: int
    department_id: Optional[int] = None


# Properties to receive via API on creation
class CourseCreate(CourseBase):
    pass


# Properties to receive via API on update
class CourseUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    code: Optional[str] = None
    is_active: Optional[bool] = None
    school_id: Optional[int] = None
    department_id: Optional[int] = None


# Properties shared by models stored in DB
class CourseInDBBase(CourseBase):
    id: int
    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True)


# Properties to return via API
class Course(CourseInDBBase):
    pass
