"""
Pydantic schemas for Gemini structured output responses.
These schemas define the expected structure for MCQs, flashcards, and summaries.
"""

from typing import List, Optional
from pydantic import BaseModel, Field
from enum import Enum


class DifficultyLevel(str, Enum):
    """Difficulty levels for generated content"""
    EASY = "easy"
    MEDIUM = "medium"
    HARD = "hard"


class MCQOption(BaseModel):
    """Individual MCQ option"""
    option_text: str = Field(description="The text of the option")
    is_correct: bool = Field(description="Whether this option is correct")


class GeneratedMCQ(BaseModel):
    """Single MCQ with structured format"""
    question_text: str = Field(description="The question text")
    options: List[MCQOption] = Field(description="List of 4 options", min_length=4, max_length=4)
    correct_answer: str = Field(description="The correct answer text")
    explanation: str = Field(description="Explanation of why the answer is correct")
    topic: str = Field(description="The topic or subject area this question covers")
    difficulty: DifficultyLevel = Field(description="Difficulty level of the question")
    bloom_taxonomy_level: str = Field(description="<PERSON>'s taxonomy level (Remember, Understand, Apply, Analyze, Evaluate, Create)")


class MCQGenerationResponse(BaseModel):
    """Response containing multiple MCQs"""
    questions: List[GeneratedMCQ] = Field(description="List of generated MCQs")
    total_count: int = Field(description="Total number of questions generated")
    source_topics: List[str] = Field(description="Main topics covered in the source material")


class GeneratedFlashcard(BaseModel):
    """Single flashcard with front and back content"""
    front_content: str = Field(description="Question or prompt on the front of the card")
    back_content: str = Field(description="Answer or explanation on the back of the card")
    topic: str = Field(description="The topic or subject area this flashcard covers")
    difficulty: DifficultyLevel = Field(description="Difficulty level of the flashcard")
    card_type: str = Field(description="Type of flashcard (definition, concept, formula, example, etc.)")


class FlashcardGenerationResponse(BaseModel):
    """Response containing multiple flashcards"""
    flashcards: List[GeneratedFlashcard] = Field(description="List of generated flashcards")
    total_count: int = Field(description="Total number of flashcards generated")
    source_topics: List[str] = Field(description="Main topics covered in the source material")


class SummarySection(BaseModel):
    """Individual section of a summary"""
    heading: str = Field(description="Section heading or title")
    content: str = Field(description="Content of this section")
    key_points: List[str] = Field(description="Key points from this section")


class GeneratedSummary(BaseModel):
    """Structured summary of document content"""
    title: str = Field(description="Title or main topic of the document")
    overview: str = Field(description="Brief overview of the entire document")
    sections: List[SummarySection] = Field(description="Detailed sections of the summary")
    key_concepts: List[str] = Field(description="Most important concepts from the document")
    main_topics: List[str] = Field(description="Main topics covered in the document")
    word_count: int = Field(description="Approximate word count of the summary")


class SummaryGenerationResponse(BaseModel):
    """Response containing document summary"""
    summary: GeneratedSummary = Field(description="The generated summary")
    source_info: dict = Field(description="Information about the source document")


class GeneratedNoteExplanation(BaseModel):
    """Comprehensive note explanation with detailed content breakdown"""
    title: str = Field(description="Enhanced title for the explained notes")
    overview: str = Field(description="Brief overview of the entire document")
    detailed_explanation: str = Field(description="Thorough, comprehensive explanation of all concepts and topics in the document")
    comprehensive_summary: str = Field(description="Summary of the enhanced explanation and key takeaways")
    key_concepts: List[str] = Field(description="Most important concepts from the document")
    main_topics: List[str] = Field(description="Main topics covered in the document")
    word_count: int = Field(description="Approximate word count of the detailed explanation")


class NoteExplanationGenerationResponse(BaseModel):
    """Response containing note explanation"""
    explanation: GeneratedNoteExplanation = Field(description="The generated note explanation")
    source_info: dict = Field(description="Information about the source document")
