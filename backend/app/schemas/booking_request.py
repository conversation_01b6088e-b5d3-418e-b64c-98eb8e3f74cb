from datetime import datetime
from typing import Optional

from pydantic import BaseModel, ConfigDict

from app.models.booking_request import BookingRequestStatus


# Shared properties
class BookingRequestBase(BaseModel):
    course_id: Optional[int] = None
    message: Optional[str] = None  # Optional message from student


# Properties to receive via API on creation
class BookingRequestCreate(BookingRequestBase):
    tutor_id: int


# Properties to receive via API on update
class BookingRequestUpdate(BaseModel):
    course_id: Optional[int] = None
    message: Optional[str] = None
    status: Optional[BookingRequestStatus] = None
    tutor_response: Optional[str] = None


# Properties shared by models stored in DB
class BookingRequestInDBBase(BookingRequestBase):
    id: int
    student_id: int
    tutor_id: int
    status: BookingRequestStatus
    tutor_response: Optional[str] = None
    response_date: Optional[datetime] = None
    chat_room_id: Optional[int] = None  # Link to chat room when accepted
    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True)


# Properties to return via API
class BookingRequest(BookingRequestInDBBase):
    # Include related data
    student_name: Optional[str] = None
    student_email: Optional[str] = None
    tutor_name: Optional[str] = None
    tutor_email: Optional[str] = None
    course_name: Optional[str] = None


# Properties for tutor response
class BookingRequestResponse(BaseModel):
    status: BookingRequestStatus  # accepted or declined
    tutor_response: Optional[str] = None


# Properties for listing booking requests with filters
class BookingRequestListItem(BaseModel):
    id: int
    subject: str
    preferred_date: datetime
    duration_hours: int
    session_type: str
    status: BookingRequestStatus
    student_name: str
    tutor_name: str
    course_name: Optional[str] = None
    created_at: datetime

    model_config = ConfigDict(from_attributes=True)


# Filters for searching booking requests
class BookingRequestFilters(BaseModel):
    status: Optional[BookingRequestStatus] = None
    tutor_id: Optional[int] = None
    student_id: Optional[int] = None
    course_id: Optional[int] = None
    date_from: Optional[datetime] = None
    date_to: Optional[datetime] = None
