from datetime import datetime
from typing import Optional

from pydantic import BaseModel, ConfigDict

from app.models.notification import NotificationType


# Shared properties
class NotificationBase(BaseModel):
    title: str
    message: str
    type: NotificationType
    related_id: Optional[int] = None
    related_type: Optional[str] = None


# Properties to receive via API on creation
class NotificationCreate(NotificationBase):
    user_id: int


# Properties to receive via API on update
class NotificationUpdate(BaseModel):
    title: Optional[str] = None
    message: Optional[str] = None
    is_read: Optional[bool] = None


# Properties shared by models stored in DB
class NotificationInDBBase(NotificationBase):
    id: int
    user_id: int
    is_read: bool
    created_at: datetime
    read_at: Optional[datetime] = None

    model_config = ConfigDict(from_attributes=True)


# Properties to return via API
class Notification(NotificationInDBBase):
    # Additional fields for enhanced functionality
    chat_room_id: Optional[int] = None  # For booking acceptance notifications

    @classmethod
    def from_db_with_chat_room(cls, notification_obj, chat_room_id: Optional[int] = None):
        """Create notification response with chat room information"""
        data = {
            "id": notification_obj.id,
            "user_id": notification_obj.user_id,
            "type": notification_obj.type,
            "title": notification_obj.title,
            "message": notification_obj.message,
            "is_read": notification_obj.is_read,
            "related_id": notification_obj.related_id,
            "related_type": notification_obj.related_type,
            "created_at": notification_obj.created_at,
            "read_at": notification_obj.read_at,
            "chat_room_id": chat_room_id
        }
        return cls(**data)


# Properties stored in DB
class NotificationInDB(NotificationInDBBase):
    pass


# For marking notifications as read
class NotificationMarkRead(BaseModel):
    notification_id: int


# For bulk operations
class NotificationBulkAction(BaseModel):
    action: str  # "mark_all_read", "delete_old"
    days_old: Optional[int] = 30  # For delete_old action
