from datetime import datetime
from typing import Optional, List
from pydantic import BaseModel, ConfigDict


class GeneratedFlashcardBase(BaseModel):
    front_content: str
    back_content: str
    topic: Optional[str] = None
    difficulty: Optional[str] = None
    card_type: Optional[str] = None
    course_name: Optional[str] = None
    gemini_file_id: Optional[str] = None
    is_active: bool = True


class GeneratedFlashcardCreate(GeneratedFlashcardBase):
    student_id: int


class GeneratedFlashcardUpdate(BaseModel):
    front_content: Optional[str] = None
    back_content: Optional[str] = None
    topic: Optional[str] = None
    difficulty: Optional[str] = None
    card_type: Optional[str] = None
    course_name: Optional[str] = None
    is_active: Optional[bool] = None


class GeneratedFlashcardInDBBase(GeneratedFlashcardBase):
    id: int
    student_id: int
    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True)


class GeneratedFlashcard(GeneratedFlashcardInDBBase):
    pass


# Response schemas for flashcard generation
class GeneratedFlashcardResponse(BaseModel):
    """Response for individual generated flashcard"""
    front_content: str
    back_content: str
    topic: str
    difficulty: str
    card_type: str


class FlashcardGenerationResponse(BaseModel):
    """Response containing multiple generated flashcards"""
    flashcards: List[GeneratedFlashcardResponse]
    total_count: int
    source_topics: List[str]
    saved_flashcard_ids: List[int]
    message: str
