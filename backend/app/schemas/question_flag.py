from datetime import datetime
from typing import List, Optional, Dict, Any, Union
from pydantic import BaseModel, ConfigDict, Field, field_validator, field_serializer
import json

from app.models.question_flag import FlagStatus, FlagReason


class QuestionFlagBase(BaseModel):
    """Base schema for question flags"""
    question_id: int
    reasons: List[str] = Field(..., description="List of flag reasons")
    description: Optional[str] = Field(None, description="Additional description of the issue")


class QuestionFlagCreate(QuestionFlagBase):
    """Schema for creating a question flag"""
    pass


class QuestionFlagUpdate(BaseModel):
    """Schema for updating a question flag"""
    status: Optional[FlagStatus] = None
    admin_notes: Optional[str] = None


class QuestionFlagStatusUpdate(BaseModel):
    """Schema for updating flag status by admin"""
    status: FlagStatus
    admin_notes: Optional[str] = None


class QuestionFlagBulkUpdate(BaseModel):
    """Schema for bulk updating flags"""
    flag_ids: List[int]
    status: FlagStatus
    admin_notes: Optional[str] = None


# Response schemas
class StudentInfo(BaseModel):
    """Basic student information for flag responses"""
    id: int
    full_name: str
    email: str
    
    model_config = ConfigDict(from_attributes=True)


class ReviewerInfo(BaseModel):
    """Basic reviewer information for flag responses"""
    id: int
    full_name: str
    email: str
    
    model_config = ConfigDict(from_attributes=True)


class QuestionInfo(BaseModel):
    """Basic question information for flag responses"""
    id: int
    content: str
    difficulty: str
    course_name: Optional[str] = None
    options: Optional[Dict[str, str]] = None
    answer: Optional[str] = None
    explanation: Optional[str] = None

    model_config = ConfigDict(from_attributes=True)

    @classmethod
    def from_question(cls, question):
        """Create QuestionInfo from Question model"""
        return cls(
            id=question.id,
            content=question.content,
            difficulty=question.difficulty.value if hasattr(question.difficulty, 'value') else str(question.difficulty),
            course_name=question.course_name,
            options=question.options,
            answer=question.answer,
            explanation=question.explanation
        )


class QuestionFlag(BaseModel):
    """Schema for question flag responses"""
    id: int
    question_id: int
    student_id: int
    reasons: List[str] = Field(..., description="List of flag reasons")
    description: Optional[str] = Field(None, description="Additional description of the issue")
    status: FlagStatus
    reviewed_by: Optional[int] = None
    reviewed_at: Optional[datetime] = None
    admin_notes: Optional[str] = None
    created_at: datetime
    updated_at: datetime

    # Nested objects
    student: Optional[StudentInfo] = None
    reviewer: Optional[ReviewerInfo] = None
    question: Optional[QuestionInfo] = None

    model_config = ConfigDict(from_attributes=True)

    @field_validator('reasons', mode='before')
    @classmethod
    def parse_reasons(cls, v):
        """Parse reasons from JSON string if needed"""
        if isinstance(v, str):
            try:
                return json.loads(v)
            except (json.JSONDecodeError, TypeError):
                return []
        elif isinstance(v, list):
            return v
        else:
            return []

    @field_validator('question', mode='before')
    @classmethod
    def parse_question(cls, v):
        """Ensure question data includes all fields"""
        if v is None:
            return None

        # If it's already a QuestionInfo object, return as is
        if isinstance(v, QuestionInfo):
            return v

        # If it's a Question model object, convert it properly
        if hasattr(v, 'id') and hasattr(v, 'content'):
            return QuestionInfo.from_question(v)

        # If it's a dict, create QuestionInfo from it
        if isinstance(v, dict):
            return QuestionInfo(**v)

        return v


class QuestionFlagWithDetails(QuestionFlag):
    """Extended flag schema with full question and user details"""
    pass


class FlagReasonOption(BaseModel):
    """Schema for flag reason options"""
    value: str
    label: str
    description: str


class FlagReasonsResponse(BaseModel):
    """Response schema for available flag reasons"""
    reasons: List[FlagReasonOption]


class FlagStatistics(BaseModel):
    """Schema for flag statistics"""
    total_flags: int
    pending_flags: int
    resolved_flags: int
    dismissed_flags: int
    reason_statistics: Dict[str, int]


class FlagListResponse(BaseModel):
    """Response schema for paginated flag lists"""
    flags: List[QuestionFlag]
    total: int
    page: int
    per_page: int
    total_pages: int


class FlagSubmissionResponse(BaseModel):
    """Response schema for flag submission"""
    success: bool
    message: str
    flag_id: Optional[int] = None


class FlagActionResponse(BaseModel):
    """Response schema for flag actions (resolve, dismiss, etc.)"""
    success: bool
    message: str
    updated_flags: int = 0


# Filter schemas
class FlagFilters(BaseModel):
    """Schema for filtering flags"""
    status: Optional[FlagStatus] = None
    reason: Optional[str] = None
    course_id: Optional[int] = None
    student_id: Optional[int] = None
    question_id: Optional[int] = None
    date_from: Optional[datetime] = None
    date_to: Optional[datetime] = None


class AdminFlagListRequest(BaseModel):
    """Schema for admin flag list requests with filters"""
    page: int = Field(1, ge=1, description="Page number")
    per_page: int = Field(20, ge=1, le=100, description="Items per page")
    filters: Optional[FlagFilters] = None
    sort_by: Optional[str] = Field("created_at", description="Sort field")
    sort_order: Optional[str] = Field("desc", description="Sort order (asc/desc)")


# Validation schemas
class FlagReasonValidation(BaseModel):
    """Schema for validating flag reasons"""
    @classmethod
    def validate_reasons(cls, reasons: List[str]) -> List[str]:
        """Validate that all reasons are valid"""
        valid_reasons = [reason.value for reason in FlagReason]
        invalid_reasons = [reason for reason in reasons if reason not in valid_reasons]
        
        if invalid_reasons:
            raise ValueError(f"Invalid flag reasons: {invalid_reasons}")
        
        return reasons


# Export schemas for easy importing
__all__ = [
    "QuestionFlagCreate",
    "QuestionFlagUpdate", 
    "QuestionFlagStatusUpdate",
    "QuestionFlagBulkUpdate",
    "QuestionFlag",
    "QuestionFlagWithDetails",
    "FlagReasonOption",
    "FlagReasonsResponse",
    "FlagStatistics",
    "FlagListResponse",
    "FlagSubmissionResponse",
    "FlagActionResponse",
    "FlagFilters",
    "AdminFlagListRequest",
    "FlagReasonValidation"
]
