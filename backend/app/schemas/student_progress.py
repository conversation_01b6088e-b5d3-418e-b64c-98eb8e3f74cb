from datetime import datetime
from typing import Dict, List, Optional

from pydantic import BaseModel, ConfigDict


# Student Question Attempt schemas
class StudentQuestionAttemptBase(BaseModel):
    question_id: int
    is_correct: bool
    selected_answer: Optional[str] = None


class StudentQuestionAttemptCreate(StudentQuestionAttemptBase):
    student_id: int


class StudentQuestionAttemptUpdate(BaseModel):
    is_correct: Optional[bool] = None
    selected_answer: Optional[str] = None


class StudentQuestionAttemptInDBBase(StudentQuestionAttemptBase):
    id: int
    student_id: int
    attempt_time: datetime

    model_config = ConfigDict(from_attributes=True)


class StudentQuestionAttempt(StudentQuestionAttemptInDBBase):
    pass


# Student Bookmark schemas
class StudentBookmarkBase(BaseModel):
    question_id: int
    notes: Optional[str] = None


class StudentBookmarkCreate(StudentBookmarkBase):
    student_id: int


class StudentBookmarkUpdate(BaseModel):
    notes: Optional[str] = None


class StudentBookmarkInDBBase(StudentBookmarkBase):
    id: int
    student_id: int
    created_at: datetime

    model_config = ConfigDict(from_attributes=True)


class StudentBookmark(StudentBookmarkInDBBase):
    pass


# Student Exam Attempt schemas
class StudentExamAttemptBase(BaseModel):
    question_id: int
    is_correct: bool
    selected_answer: Optional[str] = None
    time_spent_seconds: Optional[int] = None


class StudentExamAttemptCreate(StudentExamAttemptBase):
    exam_id: int


class StudentExamAttemptInDBBase(StudentExamAttemptBase):
    id: int
    exam_id: int

    model_config = ConfigDict(from_attributes=True)


class StudentExamAttempt(StudentExamAttemptInDBBase):
    pass


# Student Exam schemas
class StudentExamBase(BaseModel):
    course_id: Optional[int] = None
    total_questions: int
    score: Optional[float] = None
    correct_answers: Optional[int] = None
    end_time: Optional[datetime] = None
    # Fields for note-generated content
    is_note_generated: Optional[bool] = False
    note_job_id: Optional[int] = None
    course_name: Optional[str] = None


class StudentExamCreate(StudentExamBase):
    student_id: int

    # Add validation to ensure all required fields are present and of correct type
    @classmethod
    def model_validate(cls, obj, *args, **kwargs):
        # Log the incoming data for debugging
        print(f"Validating StudentExamCreate with data: {obj}")
        return super().model_validate(obj, *args, **kwargs)


class StudentExamUpdate(BaseModel):
    score: Optional[float] = None
    correct_answers: Optional[int] = None
    end_time: Optional[datetime] = None


class StudentExamInDBBase(StudentExamBase):
    id: int
    student_id: int
    start_time: datetime

    model_config = ConfigDict(from_attributes=True)


class StudentExam(StudentExamInDBBase):
    pass


class StudentExamWithAttempts(StudentExam):
    attempts: List[StudentExamAttempt]
