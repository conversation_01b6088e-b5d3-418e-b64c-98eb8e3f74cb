from datetime import datetime
from typing import Optional, List

from pydantic import BaseModel, ConfigDict

from app.models.chat import MessageType, MessageDeliveryStatus


# Chat Room Schemas
class ChatRoomBase(BaseModel):
    student_id: int
    tutor_id: int
    is_active: bool = True


class ChatRoomCreate(ChatRoomBase):
    pass


class ChatRoomUpdate(BaseModel):
    is_active: Optional[bool] = None


class ChatRoomInDBBase(ChatRoomBase):
    id: int
    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True)


class ChatRoom(ChatRoomInDBBase):
    # Include related data
    student_name: Optional[str] = None
    tutor_name: Optional[str] = None
    last_message: Optional[str] = None
    last_message_time: Optional[datetime] = None
    unread_count: Optional[int] = None


# Chat Message Schemas
class ChatMessageBase(BaseModel):
    content: str
    message_type: MessageType = MessageType.TEXT


class ChatMessageCreate(ChatMessageBase):
    chat_room_id: int


class ChatMessageUpdate(BaseModel):
    content: Optional[str] = None
    is_read: Optional[bool] = None
    delivery_status: Optional[MessageDeliveryStatus] = None
    delivered_at: Optional[datetime] = None
    read_at: Optional[datetime] = None


class ChatMessageInDBBase(ChatMessageBase):
    id: int
    sender_id: int
    chat_room_id: int
    is_read: bool
    delivery_status: MessageDeliveryStatus
    created_at: datetime
    updated_at: datetime
    delivered_at: Optional[datetime] = None
    read_at: Optional[datetime] = None

    model_config = ConfigDict(from_attributes=True)


class ChatMessage(ChatMessageInDBBase):
    # Include related data
    sender_name: Optional[str] = None


# User Online Status Schemas
class UserOnlineStatusBase(BaseModel):
    user_id: int
    is_online: bool = False
    connection_id: Optional[str] = None


class UserOnlineStatusCreate(UserOnlineStatusBase):
    pass


class UserOnlineStatusUpdate(BaseModel):
    is_online: Optional[bool] = None
    last_seen: Optional[datetime] = None
    connection_id: Optional[str] = None


class UserOnlineStatusInDBBase(UserOnlineStatusBase):
    id: int
    last_seen: datetime
    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True)


class UserOnlineStatus(UserOnlineStatusInDBBase):
    # Include related data
    user_name: Optional[str] = None


# WebSocket Message Schemas
class WebSocketMessage(BaseModel):
    type: str  # "message", "status", "join", "leave", etc.
    data: dict


class ChatJoinRequest(BaseModel):
    chat_room_id: int


class ChatMessageSend(BaseModel):
    chat_room_id: int
    content: str
    message_type: MessageType = MessageType.TEXT


class OnlineStatusUpdate(BaseModel):
    user_id: int
    is_online: bool
    last_seen: Optional[datetime] = None
