from datetime import datetime
from typing import Dict, List, Optional, Any
from pydantic import BaseModel, Field


# User Behavior Event schemas
class UserBehaviorEventBase(BaseModel):
    event_type: str = Field(..., description="Type of event (mcq_attempt, flashcard_view, etc.)")
    content_type: str = Field(..., description="Type of content (question, flashcard, course)")
    content_id: int = Field(..., description="ID of the content")
    course_id: Optional[int] = None
    topic: Optional[str] = None
    difficulty: Optional[str] = None
    is_correct: Optional[bool] = None
    response_time_seconds: Optional[float] = None
    confidence_level: Optional[int] = Field(None, ge=1, le=5)
    session_id: Optional[str] = None
    device_type: Optional[str] = None
    event_metadata: Optional[Dict[str, Any]] = None


class UserBehaviorEventCreate(UserBehaviorEventBase):
    user_id: int


class UserBehaviorEvent(UserBehaviorEventBase):
    id: int
    user_id: int
    time_of_day: Optional[int] = None
    day_of_week: Optional[int] = None
    created_at: datetime

    class Config:
        from_attributes = True


# User Feature Vector schemas
class UserFeatureVectorBase(BaseModel):
    avg_response_time: float = 0.0
    accuracy_rate: float = 0.0
    study_frequency: float = 0.0
    session_duration_avg: float = 0.0
    preferred_difficulty: Optional[str] = None
    preferred_time_of_day: Optional[int] = None
    preferred_session_length: Optional[int] = None
    topic_performance: Dict[str, float] = Field(default_factory=dict)
    topic_interest: Dict[str, float] = Field(default_factory=dict)
    help_seeking_rate: float = 0.0
    bookmark_rate: float = 0.0
    completion_rate: float = 0.0
    improvement_rate: float = 0.0
    consistency_score: float = 0.0
    challenge_preference: float = 0.5
    active_days_per_week: float = 0.0
    peak_performance_hour: Optional[int] = None
    feature_vector: List[float] = Field(default_factory=list)


class UserFeatureVectorCreate(UserFeatureVectorBase):
    user_id: int


class UserFeatureVector(UserFeatureVectorBase):
    id: int
    user_id: int
    last_updated: datetime
    feature_version: int

    class Config:
        from_attributes = True


# Content Feature Vector schemas
class ContentFeatureVectorBase(BaseModel):
    content_type: str
    content_id: int
    difficulty_score: float = 0.5
    topic: Optional[str] = None
    course_id: Optional[int] = None
    avg_response_time: float = 0.0
    success_rate: float = 0.0
    attempt_count: int = 0
    view_count: int = 0
    bookmark_count: int = 0
    help_request_count: int = 0
    text_complexity: Optional[float] = None
    has_media: bool = False
    word_count: Optional[int] = None
    feature_vector: List[float] = Field(default_factory=list)


class ContentFeatureVectorCreate(ContentFeatureVectorBase):
    pass


class ContentFeatureVector(ContentFeatureVectorBase):
    id: int
    last_updated: datetime
    feature_version: int

    class Config:
        from_attributes = True


# ML Recommendation schemas
class MLRecommendationBase(BaseModel):
    content_type: str
    content_id: Optional[int] = None
    topic: Optional[str] = None
    course_id: Optional[int] = None
    confidence_score: float = Field(..., ge=0.0, le=1.0)
    predicted_performance: float = Field(..., ge=0.0, le=1.0)
    difficulty_match: float = Field(..., ge=0.0, le=1.0)
    interest_score: float = Field(..., ge=0.0, le=1.0)
    model_name: str
    model_version: str
    explanation: Optional[str] = None


class MLRecommendationCreate(MLRecommendationBase):
    user_id: int


class MLRecommendationUpdate(BaseModel):
    is_viewed: Optional[bool] = None
    is_accepted: Optional[bool] = None
    is_completed: Optional[bool] = None
    user_rating: Optional[int] = Field(None, ge=1, le=5)


class MLRecommendation(MLRecommendationBase):
    id: int
    user_id: int
    is_viewed: bool = False
    is_accepted: bool = False
    is_completed: bool = False
    user_rating: Optional[int] = None
    created_at: datetime
    viewed_at: Optional[datetime] = None
    accepted_at: Optional[datetime] = None
    completed_at: Optional[datetime] = None
    expires_at: Optional[datetime] = None

    class Config:
        from_attributes = True


# ML Model Metadata schemas
class MLModelMetadataBase(BaseModel):
    model_name: str
    model_type: str
    version: str
    accuracy: Optional[float] = None
    precision: Optional[float] = None
    recall: Optional[float] = None
    f1_score: Optional[float] = None
    training_data_size: int
    training_duration_seconds: Optional[float] = None
    hyperparameters: Dict[str, Any] = Field(default_factory=dict)
    is_active: bool = False
    is_deployed: bool = False
    model_file_path: Optional[str] = None
    feature_scaler_path: Optional[str] = None


class MLModelMetadataCreate(MLModelMetadataBase):
    pass


class MLModelMetadata(MLModelMetadataBase):
    id: int
    created_at: datetime
    deployed_at: Optional[datetime] = None
    last_retrained: Optional[datetime] = None

    class Config:
        from_attributes = True


# User Learning Session schemas
class UserLearningSessionBase(BaseModel):
    session_id: str
    device_type: Optional[str] = None
    total_questions: int = 0
    correct_answers: int = 0
    total_flashcards: int = 0
    help_requests: int = 0
    bookmarks_added: int = 0
    avg_response_time: Optional[float] = None
    accuracy_rate: Optional[float] = None
    engagement_score: Optional[float] = None
    primary_course_id: Optional[int] = None
    topics_covered: List[str] = Field(default_factory=list)


class UserLearningSessionCreate(UserLearningSessionBase):
    user_id: int


class UserLearningSessionUpdate(BaseModel):
    end_time: Optional[datetime] = None
    total_questions: Optional[int] = None
    correct_answers: Optional[int] = None
    total_flashcards: Optional[int] = None
    help_requests: Optional[int] = None
    bookmarks_added: Optional[int] = None
    avg_response_time: Optional[float] = None
    accuracy_rate: Optional[float] = None
    engagement_score: Optional[float] = None
    topics_covered: Optional[List[str]] = None


class UserLearningSession(UserLearningSessionBase):
    id: int
    user_id: int
    start_time: datetime
    end_time: Optional[datetime] = None

    class Config:
        from_attributes = True


# Request/Response schemas for API endpoints
class BehaviorTrackingRequest(BaseModel):
    event_type: str
    content_type: str
    content_id: int
    course_id: Optional[int] = None
    topic: Optional[str] = None
    difficulty: Optional[str] = None
    is_correct: Optional[bool] = None
    response_time_seconds: Optional[float] = None
    confidence_level: Optional[int] = Field(None, ge=1, le=5)
    session_id: Optional[str] = None
    device_type: Optional[str] = None
    event_metadata: Optional[Dict[str, Any]] = None


class RecommendationRequest(BaseModel):
    content_type: str = "question"
    limit: int = Field(10, ge=1, le=50)
    course_id: Optional[int] = None
    topic: Optional[str] = None
    difficulty: Optional[str] = None


class RecommendationResponse(BaseModel):
    recommendations: List[MLRecommendation]
    total_count: int
    model_info: Dict[str, str]
    user_features: Optional[UserFeatureVector] = None


class LearningInsightsResponse(BaseModel):
    user_features: Optional[UserFeatureVector] = None
    recent_recommendations: List[MLRecommendation] = Field(default_factory=list)
    performance_trends: Dict[str, Any] = Field(default_factory=dict)
    learning_patterns: Dict[str, Any] = Field(default_factory=dict)
    model_confidence: float = 0.0


class SessionStartRequest(BaseModel):
    session_id: str
    device_type: Optional[str] = "web"
    primary_course_id: Optional[int] = None


class SessionEndRequest(BaseModel):
    total_questions: int = 0
    correct_answers: int = 0
    total_flashcards: int = 0
    help_requests: int = 0
    bookmarks_added: int = 0
    topics_covered: List[str] = Field(default_factory=list)


class ModelPerformanceResponse(BaseModel):
    model_name: str
    model_version: str
    accuracy: Optional[float] = None
    precision: Optional[float] = None
    recall: Optional[float] = None
    f1_score: Optional[float] = None
    training_data_size: int
    last_retrained: Optional[datetime] = None
    is_active: bool
    recommendation_count: int
    user_satisfaction: Optional[float] = None


class SimilarUsersResponse(BaseModel):
    similar_users: List[int]
    similarity_scores: List[float]
    common_interests: List[str]
    recommendation_basis: str
