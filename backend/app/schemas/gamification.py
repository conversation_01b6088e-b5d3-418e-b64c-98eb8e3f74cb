from datetime import datetime
from typing import Dict, List, Optional, Any

from pydantic import BaseModel, ConfigDict

from app.models.gamification import BadgeType


# Badge schemas
class BadgeBase(BaseModel):
    name: str
    description: str
    badge_type: BadgeType
    icon_url: Optional[str] = None
    points_value: int = 0


class BadgeCreate(BadgeBase):
    pass


class BadgeUpdate(BaseModel):
    name: Optional[str] = None
    description: Optional[str] = None
    badge_type: Optional[BadgeType] = None
    icon_url: Optional[str] = None
    points_value: Optional[int] = None


class BadgeInDBBase(BadgeBase):
    id: int
    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True)


class Badge(BadgeInDBBase):
    pass


# UserBadge schemas
class UserBadgeBase(BaseModel):
    user_id: int
    badge_id: int


class UserBadgeCreate(UserBadgeBase):
    pass


class UserBadgeInDBBase(UserBadgeBase):
    id: int
    earned_at: datetime

    model_config = ConfigDict(from_attributes=True)


class UserBadge(UserBadgeInDBBase):
    badge: Optional[Badge] = None


# PointsTransaction schemas
class PointsTransactionBase(BaseModel):
    user_id: int
    points: int
    description: str
    transaction_type: str
    reference_id: Optional[int] = None


class PointsTransactionCreate(PointsTransactionBase):
    pass


class PointsTransactionInDBBase(PointsTransactionBase):
    id: int
    created_at: datetime

    model_config = ConfigDict(from_attributes=True)


class PointsTransaction(PointsTransactionInDBBase):
    pass


# UserLevel schemas
class UserLevelBase(BaseModel):
    level_number: int
    name: str
    min_points: int
    max_points: int
    icon_url: Optional[str] = None


class UserLevelCreate(UserLevelBase):
    pass


class UserLevelUpdate(BaseModel):
    level_number: Optional[int] = None
    name: Optional[str] = None
    min_points: Optional[int] = None
    max_points: Optional[int] = None
    icon_url: Optional[str] = None


class UserLevelInDBBase(UserLevelBase):
    id: int
    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True)


class UserLevel(UserLevelInDBBase):
    pass


# UserStreak schemas
class UserStreakBase(BaseModel):
    user_id: int
    current_streak: int = 0
    longest_streak: int = 0


class UserStreakCreate(UserStreakBase):
    pass


class UserStreakUpdate(BaseModel):
    current_streak: Optional[int] = None
    longest_streak: Optional[int] = None
    last_activity_date: Optional[datetime] = None


class UserStreakInDBBase(UserStreakBase):
    id: int
    last_activity_date: datetime
    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True)


class UserStreak(UserStreakInDBBase):
    pass


# Additional schemas for API responses
class UserGamificationProfile(BaseModel):
    total_points: int
    current_level: Dict[str, Any]
    badges: List[Dict[str, Any]]
    streak: Optional[UserStreak] = None
    next_level: Optional[Dict[str, Any]] = None
    points_to_next_level: Optional[int] = None


class LeaderboardEntry(BaseModel):
    user_id: int
    full_name: str
    profile_picture_url: Optional[str] = None
    total_points: int
    badge_count: int
    current_streak: int
    level: Dict[str, Any]
    school_name: Optional[str] = None
    department_name: Optional[str] = None


class Leaderboard(BaseModel):
    entries: List[LeaderboardEntry]
