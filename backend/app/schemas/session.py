from datetime import datetime
from typing import Optional, TYPE_CHECKING

from pydantic import BaseModel, ConfigDict

from app.models.session import SessionStatus, SessionType

if TYPE_CHECKING:
    from app.schemas.user import User
    from app.schemas.course import Course


# Shared properties
class SessionBase(BaseModel):
    title: str
    description: str
    session_type: SessionType
    status: SessionStatus = SessionStatus.SCHEDULED
    start_time: datetime
    end_time: datetime
    location: Optional[str] = None
    video_link: Optional[str] = None
    max_students: int = 1
    is_recurring: bool = False
    recurrence_pattern: Optional[str] = None
    course_id: Optional[int] = None
    tutor_id: int


# Properties to receive via API on creation
class SessionCreate(BaseModel):
    title: str
    description: str
    course_name: Optional[str] = None  # Allow course name input
    session_type: SessionType
    status: SessionStatus = SessionStatus.SCHEDULED
    start_time: datetime
    end_time: datetime
    location: Optional[str] = None
    video_link: Optional[str] = None
    max_students: int = 1
    is_recurring: bool = False
    recurrence_pattern: Optional[str] = None
    course_id: Optional[int] = None  # Optional since we can create from course_name
    tutor_id: int


# Properties to receive via API on update
class SessionUpdate(BaseModel):
    title: Optional[str] = None
    description: Optional[str] = None
    course_name: Optional[str] = None
    session_type: Optional[SessionType] = None
    status: Optional[SessionStatus] = None
    start_time: Optional[datetime] = None
    end_time: Optional[datetime] = None
    location: Optional[str] = None
    video_link: Optional[str] = None
    max_students: Optional[int] = None
    is_recurring: Optional[bool] = None
    recurrence_pattern: Optional[str] = None
    course_id: Optional[int] = None


# Properties shared by models stored in DB
class SessionInDBBase(SessionBase):
    id: int
    student_id: Optional[int] = None
    created_at: datetime
    updated_at: datetime

    model_config = ConfigDict(from_attributes=True)


# Properties to return via API
class Session(SessionInDBBase):
    pass


# Session booking request
class SessionBookingRequest(BaseModel):
    session_id: int
    student_id: int


# Session with relationships - defined after imports to avoid circular dependencies
class SessionWithRelations(SessionInDBBase):
    tutor: Optional['User'] = None
    student: Optional['User'] = None
    course: Optional['Course'] = None


# Resolve forward references after all schemas are defined
def resolve_session_forward_refs():
    """Call this after all schemas are imported to resolve forward references"""
    try:
        from app.schemas.user import User
        from app.schemas.course import Course
        SessionWithRelations.model_rebuild()
    except ImportError:
        # Schemas not yet available, will be resolved later
        pass
