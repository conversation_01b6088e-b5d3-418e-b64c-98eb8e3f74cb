from datetime import datetime
from typing import List, Optional

from sqlalchemy.orm import Session
from sqlalchemy import and_, desc

from app.crud.base import CRUDBase
from app.models.notification import Notification, NotificationType
from app.schemas.notification import NotificationCreate, NotificationUpdate


class CRUDNotification(CRUDBase[Notification, NotificationCreate, NotificationUpdate]):
    def create_notification(
        self,
        db: Session,
        *,
        user_id: int,
        notification_type: NotificationType,
        title: str,
        message: str,
        related_id: Optional[int] = None,
        related_type: Optional[str] = None
    ) -> Notification:
        """Create a new notification for a user"""
        db_obj = Notification(
            user_id=user_id,
            type=notification_type,
            title=title,
            message=message,
            related_id=related_id,
            related_type=related_type,
            is_read=False,
            created_at=datetime.utcnow()
        )
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj

    def get_user_notifications(
        self,
        db: Session,
        *,
        user_id: int,
        skip: int = 0,
        limit: int = 100,
        unread_only: bool = False
    ) -> List[Notification]:
        """Get notifications for a specific user"""
        query = db.query(Notification).filter(Notification.user_id == user_id)
        
        if unread_only:
            query = query.filter(Notification.is_read == False)
        
        return query.order_by(desc(Notification.created_at)).offset(skip).limit(limit).all()

    def get_unread_count(self, db: Session, *, user_id: int) -> int:
        """Get count of unread notifications for a user"""
        return db.query(Notification).filter(
            and_(
                Notification.user_id == user_id,
                Notification.is_read == False
            )
        ).count()

    def mark_as_read(
        self,
        db: Session,
        *,
        notification_id: int,
        user_id: int
    ) -> Optional[Notification]:
        """Mark a notification as read"""
        notification = db.query(Notification).filter(
            and_(
                Notification.id == notification_id,
                Notification.user_id == user_id
            )
        ).first()
        
        if notification and not notification.is_read:
            notification.is_read = True
            notification.read_at = datetime.utcnow()
            db.add(notification)
            db.commit()
            db.refresh(notification)
        
        return notification

    def mark_all_as_read(self, db: Session, *, user_id: int) -> int:
        """Mark all notifications as read for a user"""
        updated_count = db.query(Notification).filter(
            and_(
                Notification.user_id == user_id,
                Notification.is_read == False
            )
        ).update({
            "is_read": True,
            "read_at": datetime.utcnow()
        })
        db.commit()
        return updated_count

    def delete_notification(
        self,
        db: Session,
        *,
        notification_id: int,
        user_id: int
    ) -> bool:
        """Delete a notification (only if it belongs to the user)"""
        notification = db.query(Notification).filter(
            and_(
                Notification.id == notification_id,
                Notification.user_id == user_id
            )
        ).first()
        
        if notification:
            db.delete(notification)
            db.commit()
            return True
        return False

    def delete_old_notifications(
        self,
        db: Session,
        *,
        user_id: int,
        days_old: int = 30
    ) -> int:
        """Delete notifications older than specified days"""
        from datetime import timedelta
        cutoff_date = datetime.utcnow() - timedelta(days=days_old)
        
        deleted_count = db.query(Notification).filter(
            and_(
                Notification.user_id == user_id,
                Notification.created_at < cutoff_date
            )
        ).delete()
        db.commit()
        return deleted_count

    def get_user_notifications_by_types(
        self,
        db: Session,
        *,
        user_id: int,
        notification_types: List[NotificationType],
        skip: int = 0,
        limit: int = 100
    ) -> List[Notification]:
        """Get notifications for a user filtered by types"""
        return db.query(Notification).filter(
            and_(
                Notification.user_id == user_id,
                Notification.type.in_(notification_types)
            )
        ).order_by(desc(Notification.created_at)).offset(skip).limit(limit).all()

    def mark_notifications_read_by_related_id(
        self,
        db: Session,
        *,
        user_id: int,
        related_id: int,
        related_type: str
    ) -> int:
        """Mark notifications as read by related_id and related_type"""
        updated_count = db.query(Notification).filter(
            and_(
                Notification.user_id == user_id,
                Notification.related_id == related_id,
                Notification.related_type == related_type,
                Notification.is_read == False
            )
        ).update({
            "is_read": True,
            "read_at": datetime.utcnow()
        })
        db.commit()
        return updated_count


notification = CRUDNotification(Notification)
