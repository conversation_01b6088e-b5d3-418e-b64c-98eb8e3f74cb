from typing import List, Optional

from sqlalchemy.orm import Session

from app.crud.base import CRUDBase
from app.models.course import Course
from app.schemas.course import CourseCreate, CourseUpdate


class CRUDCourse(CRUDBase[Course, CourseCreate, CourseUpdate]):
    def get_by_code(self, db: Session, *, code: str) -> Optional[Course]:
        """Get course by code"""
        return db.query(Course).filter(Course.code == code).first()

    def get_by_school(
        self, 
        db: Session, 
        *, 
        school_id: int, 
        skip: int = 0, 
        limit: int = 100
    ) -> List[Course]:
        """Get courses by school ID"""
        return (
            db.query(Course)
            .filter(Course.school_id == school_id)
            .offset(skip)
            .limit(limit)
            .all()
        )

    def get_by_department(
        self, 
        db: Session, 
        *, 
        department_id: int, 
        skip: int = 0, 
        limit: int = 100
    ) -> List[Course]:
        """Get courses by department ID"""
        return (
            db.query(Course)
            .filter(Course.department_id == department_id)
            .offset(skip)
            .limit(limit)
            .all()
        )

    def get_active(
        self, 
        db: Session, 
        *, 
        skip: int = 0, 
        limit: int = 100
    ) -> List[Course]:
        """Get active courses only"""
        return (
            db.query(Course)
            .filter(Course.is_active == True)
            .offset(skip)
            .limit(limit)
            .all()
        )


course = CRUDCourse(Course)
