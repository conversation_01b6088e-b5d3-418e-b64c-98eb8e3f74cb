"""
CRUD operations for upload tasks
"""
from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_

from app.crud.base import CRUDBase
from app.models.upload_task import UploadTask, UploadStatus
from app.schemas.upload_task import UploadTaskCreate, UploadTaskUpdate


class CRUDUploadTask(CRUDBase[UploadTask, UploadTaskCreate, UploadTaskUpdate]):
    """CRUD operations for upload tasks"""
    
    def create_upload_task(
        self,
        db: Session,
        *,
        upload_id: str,
        task_id: str,
        student_id: int,
        original_filename: str,
        file_size: int,
        course_name: Optional[str] = None
    ) -> UploadTask:
        """Create a new upload task"""
        upload_task = UploadTask(
            upload_id=upload_id,
            task_id=task_id,
            student_id=student_id,
            original_filename=original_filename,
            file_size=file_size,
            course_name=course_name,
            status=UploadStatus.PENDING
        )
        db.add(upload_task)
        db.commit()
        db.refresh(upload_task)
        return upload_task
    
    def get_by_upload_id(self, db: Session, upload_id: str) -> Optional[UploadTask]:
        """Get upload task by upload ID"""
        return db.query(UploadTask).filter(UploadTask.upload_id == upload_id).first()
    
    def get_by_task_id(self, db: Session, task_id: str) -> Optional[UploadTask]:
        """Get upload task by Celery task ID"""
        return db.query(UploadTask).filter(UploadTask.task_id == task_id).first()
    
    def get_by_student(
        self, 
        db: Session, 
        student_id: int,
        status: Optional[UploadStatus] = None,
        limit: int = 100
    ) -> List[UploadTask]:
        """Get upload tasks for a student"""
        query = db.query(UploadTask).filter(UploadTask.student_id == student_id)
        
        if status:
            query = query.filter(UploadTask.status == status)
        
        return query.order_by(UploadTask.created_at.desc()).limit(limit).all()
    
    def get_active_uploads(self, db: Session, student_id: int) -> List[UploadTask]:
        """Get active uploads for a student"""
        return db.query(UploadTask).filter(
            and_(
                UploadTask.student_id == student_id,
                or_(
                    UploadTask.status == UploadStatus.PENDING,
                    UploadTask.status == UploadStatus.UPLOADING,
                    UploadTask.status == UploadStatus.PROCESSING
                )
            )
        ).order_by(UploadTask.created_at.desc()).all()
    
    def get_recent_uploads(
        self, 
        db: Session, 
        student_id: int, 
        limit: int = 20
    ) -> List[UploadTask]:
        """Get recent uploads for a student"""
        return db.query(UploadTask).filter(
            UploadTask.student_id == student_id
        ).order_by(UploadTask.created_at.desc()).limit(limit).all()
    
    def update_progress(
        self,
        db: Session,
        upload_id: str,
        progress: float,
        bytes_uploaded: Optional[int] = None,
        speed: Optional[float] = None,
        estimated_time: Optional[float] = None
    ) -> Optional[UploadTask]:
        """Update upload progress"""
        upload_task = self.get_by_upload_id(db, upload_id)
        if upload_task:
            upload_task.update_progress(
                progress=progress,
                bytes_uploaded=bytes_uploaded,
                speed=speed,
                estimated_time=estimated_time
            )
            db.commit()
            db.refresh(upload_task)
        return upload_task
    
    def mark_started(self, db: Session, upload_id: str) -> Optional[UploadTask]:
        """Mark upload as started"""
        upload_task = self.get_by_upload_id(db, upload_id)
        if upload_task:
            upload_task.mark_started()
            db.commit()
            db.refresh(upload_task)
        return upload_task
    
    def mark_completed(
        self,
        db: Session,
        upload_id: str,
        result: Optional[Dict[str, Any]] = None,
        gemini_file_id: Optional[str] = None
    ) -> Optional[UploadTask]:
        """Mark upload as completed"""
        upload_task = self.get_by_upload_id(db, upload_id)
        if upload_task:
            upload_task.mark_completed(result=result, gemini_file_id=gemini_file_id)
            db.commit()
            db.refresh(upload_task)
        return upload_task
    
    def mark_failed(
        self,
        db: Session,
        upload_id: str,
        error_message: str,
        error_details: Optional[Dict[str, Any]] = None
    ) -> Optional[UploadTask]:
        """Mark upload as failed"""
        upload_task = self.get_by_upload_id(db, upload_id)
        if upload_task:
            upload_task.mark_failed(
                error_message=error_message,
                error_details=error_details
            )
            db.commit()
            db.refresh(upload_task)
        return upload_task
    
    def mark_cancelled(self, db: Session, upload_id: str) -> Optional[UploadTask]:
        """Mark upload as cancelled"""
        upload_task = self.get_by_upload_id(db, upload_id)
        if upload_task:
            upload_task.mark_cancelled()
            db.commit()
            db.refresh(upload_task)
        return upload_task
    
    def cleanup_old_tasks(
        self, 
        db: Session, 
        days_old: int = 7
    ) -> int:
        """Clean up old completed/failed tasks"""
        from datetime import datetime, timedelta
        
        cutoff_date = datetime.utcnow() - timedelta(days=days_old)
        
        # Delete old completed or failed tasks
        deleted_count = db.query(UploadTask).filter(
            and_(
                UploadTask.completed_at < cutoff_date,
                or_(
                    UploadTask.status == UploadStatus.COMPLETED,
                    UploadTask.status == UploadStatus.FAILED,
                    UploadTask.status == UploadStatus.CANCELLED
                )
            )
        ).delete()
        
        db.commit()
        return deleted_count


# Create instance
upload_task = CRUDUploadTask(UploadTask)
