"""
CRUD operations for the simplified content access sharing system.
"""

from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, desc

from app.crud.base import CRUDBase
from app.models.content_access import ContentAccess, ShareInvitation, PublicShare, ContentType, AccessType
from app.models.user import User
from app.models.question import Question
from app.models.generated_flashcard import GeneratedFlashcard
from app.models.generated_note_explanation import GeneratedNoteExplanation
from app.schemas.content_access import (
    ContentAccessCreate, ContentAccessUpdate,
    ShareInvitationCreate, ShareInvitationUpdate,
    PublicShareCreate, PublicShareUpdate
)


class CRUDContentAccess(CRUDBase[ContentAccess, ContentAccessCreate, ContentAccessUpdate]):
    """CRUD operations for ContentAccess"""
    
    def grant_access(
        self,
        db: Session,
        *,
        content_type: ContentType,
        content_id: int,
        user_id: int,
        shared_by_id: int,
        access_type: AccessType,
        share_token: Optional[str] = None,
        share_message: Optional[str] = None,
        commit: bool = True
    ) -> ContentAccess:
        """Grant a user access to specific content"""
        from sqlalchemy.exc import IntegrityError

        # Check if access already exists (including inactive ones)
        existing = db.query(ContentAccess).filter(
            and_(
                ContentAccess.content_type == content_type,
                ContentAccess.content_id == content_id,
                ContentAccess.user_id == user_id
            )
        ).first()

        if existing:
            # Update existing access
            existing.access_type = access_type
            # Only update share_token if it's different and won't cause conflicts
            if share_token != existing.share_token:
                # Check if this share_token already exists for another record
                existing_token = db.query(ContentAccess).filter(
                    and_(
                        ContentAccess.share_token == share_token,
                        ContentAccess.id != existing.id
                    )
                ).first()

                if not existing_token:
                    existing.share_token = share_token
            existing.share_message = share_message
            existing.is_active = True
            if commit:
                db.commit()
                db.refresh(existing)
            return existing

        # For new records, check if share_token already exists
        final_share_token = share_token
        if share_token:
            existing_token = db.query(ContentAccess).filter(
                ContentAccess.share_token == share_token
            ).first()

            if existing_token:
                # Don't store duplicate share_token, set to None
                final_share_token = None

        # Create new access
        db_obj = ContentAccess(
            content_type=content_type,
            content_id=content_id,
            user_id=user_id,
            shared_by_id=shared_by_id,
            access_type=access_type,
            share_token=final_share_token,
            share_message=share_message,
            can_edit=False,  # Shared content is always read-only
            can_delete=False
        )
        db.add(db_obj)

        try:
            if commit:
                db.commit()
                db.refresh(db_obj)
            return db_obj
        except IntegrityError:
            # Handle race condition - another process created the same access
            db.rollback()
            # Try to get the existing record that was created by another process
            existing = db.query(ContentAccess).filter(
                and_(
                    ContentAccess.content_type == content_type,
                    ContentAccess.content_id == content_id,
                    ContentAccess.user_id == user_id
                )
            ).first()

            if existing:
                # Update the existing record
                existing.access_type = access_type
                existing.share_message = share_message
                existing.is_active = True
                if commit:
                    db.commit()
                    db.refresh(existing)
                return existing
            else:
                # If we still can't find it, re-raise the error
                raise
    
    def get_user_content_access(
        self,
        db: Session,
        *,
        content_type: ContentType,
        content_id: int,
        user_id: int
    ) -> Optional[ContentAccess]:
        """Get user's access to specific content"""
        return db.query(ContentAccess).filter(
            and_(
                ContentAccess.content_type == content_type,
                ContentAccess.content_id == content_id,
                ContentAccess.user_id == user_id,
                ContentAccess.is_active == True
            )
        ).first()

    def remove_user_access(
        self,
        db: Session,
        *,
        content_type: ContentType,
        content_id: int,
        user_id: int
    ) -> bool:
        """Remove user's access to specific content"""
        access = self.get_user_content_access(
            db, content_type=content_type, content_id=content_id, user_id=user_id
        )

        if access:
            access.is_active = False
            db.commit()
            return True
        return False
    
    def get_user_accessible_content(
        self,
        db: Session,
        *,
        user_id: int,
        content_type: Optional[ContentType] = None,
        skip: int = 0,
        limit: int = 100
    ) -> List[ContentAccess]:
        """Get all content accessible to a user"""
        query = db.query(ContentAccess).filter(
            and_(
                ContentAccess.user_id == user_id,
                ContentAccess.is_active == True
            )
        )
        
        if content_type:
            query = query.filter(ContentAccess.content_type == content_type)
        
        return query.order_by(desc(ContentAccess.created_at)).offset(skip).limit(limit).all()
    
    def update_last_accessed(
        self,
        db: Session,
        *,
        content_type: ContentType,
        content_id: int,
        user_id: int
    ) -> Optional[ContentAccess]:
        """Update the last accessed timestamp"""
        access = self.get_user_content_access(
            db, content_type=content_type, content_id=content_id, user_id=user_id
        )
        if access:
            from app.utils.datetime_utils import utc_now
            access.accessed_at = utc_now()
            db.commit()
            db.refresh(access)
        return access


class CRUDShareInvitation(CRUDBase[ShareInvitation, ShareInvitationCreate, ShareInvitationUpdate]):
    """CRUD operations for ShareInvitation"""
    
    def create_invitation(
        self,
        db: Session,
        *,
        content_type: ContentType,
        content_id: int,
        invited_email: str,
        shared_by_id: int,
        share_message: Optional[str] = None,
        content_ids: Optional[List[int]] = None
    ) -> ShareInvitation:
        """Create a new share invitation"""
        import json

        content_ids_json = None
        if content_ids and len(content_ids) >= 1:
            content_ids_json = json.dumps(content_ids)

        db_obj = ShareInvitation(
            content_type=content_type,
            content_id=content_id,
            content_ids=content_ids_json,
            invited_email=invited_email,
            shared_by_id=shared_by_id,
            share_message=share_message
        )
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj
    
    def get_by_token(self, db: Session, *, invitation_token: str) -> Optional[ShareInvitation]:
        """Get invitation by token"""
        return db.query(ShareInvitation).filter(
            ShareInvitation.invitation_token == invitation_token
        ).first()
    
    def accept_invitation(
        self,
        db: Session,
        *,
        invitation_token: str,
        user_id: int
    ) -> Optional[ShareInvitation]:
        """Accept a share invitation"""
        invitation = self.get_by_token(db, invitation_token=invitation_token)
        if not invitation or invitation.is_accepted:
            return None

        # Mark invitation as accepted
        from datetime import datetime, timezone
        invitation.is_accepted = True
        invitation.accepted_at = datetime.now(timezone.utc)
        invitation.accepted_by_id = user_id

        # Grant access to the user for all content items
        content_ids_to_grant = [invitation.content_id]

        # If this is a batch invitation, grant access to all content items
        if invitation.content_ids:
            import json
            try:
                content_ids_to_grant = json.loads(invitation.content_ids)
            except (json.JSONDecodeError, TypeError):
                # Fallback to single content ID if JSON parsing fails
                content_ids_to_grant = [invitation.content_id]

        # Grant access to all content items (without committing each time)
        for content_id in content_ids_to_grant:
            content_access.grant_access(
                db,
                content_type=invitation.content_type,
                content_id=content_id,
                user_id=user_id,
                shared_by_id=invitation.shared_by_id,
                access_type=AccessType.EMAIL_INVITATION,
                share_message=invitation.share_message,
                commit=False  # Don't commit yet, we'll do it once at the end
            )

        # Commit all changes together
        db.commit()
        db.refresh(invitation)
        return invitation


class CRUDPublicShare(CRUDBase[PublicShare, PublicShareCreate, PublicShareUpdate]):
    """CRUD operations for PublicShare"""
    
    def create_public_share(
        self,
        db: Session,
        *,
        content_type: ContentType,
        content_id: int,
        shared_by_id: int,
        share_message: Optional[str] = None,
        content_ids: Optional[List[int]] = None
    ) -> PublicShare:
        """Create or get existing public share"""
        import json
        from sqlalchemy.exc import IntegrityError

        content_ids_json = None
        if content_ids and len(content_ids) >= 1:
            content_ids_json = json.dumps(content_ids)

        # Check if a public share already exists based on the unique constraint
        # The unique constraint is on (content_type, content_id, shared_by_id)
        existing = db.query(PublicShare).filter(
            and_(
                PublicShare.content_type == content_type,
                PublicShare.content_id == content_id,
                PublicShare.shared_by_id == shared_by_id,
                PublicShare.is_active == True
            )
        ).first()

        if existing:
            # Update the existing record with new data if provided
            updated = False
            if share_message and existing.share_message != share_message:
                existing.share_message = share_message
                updated = True
            if content_ids_json and existing.content_ids != content_ids_json:
                existing.content_ids = content_ids_json
                updated = True

            if updated:
                db.commit()
                db.refresh(existing)
            return existing

        # Create new public share
        db_obj = PublicShare(
            content_type=content_type,
            content_id=content_id,
            content_ids=content_ids_json,
            shared_by_id=shared_by_id,
            share_message=share_message
        )
        db.add(db_obj)

        try:
            db.commit()
            db.refresh(db_obj)
            return db_obj
        except IntegrityError:
            # Handle race condition - another process created the same share
            db.rollback()
            # Try to get the existing record that was created by another process
            existing = db.query(PublicShare).filter(
                and_(
                    PublicShare.content_type == content_type,
                    PublicShare.content_id == content_id,
                    PublicShare.shared_by_id == shared_by_id
                )
            ).first()

            if existing:
                # Update the existing record with new data if provided
                updated = False
                if share_message and existing.share_message != share_message:
                    existing.share_message = share_message
                    updated = True
                if content_ids_json and existing.content_ids != content_ids_json:
                    existing.content_ids = content_ids_json
                    updated = True

                # Ensure it's active
                if not existing.is_active:
                    existing.is_active = True
                    updated = True

                if updated:
                    db.commit()
                    db.refresh(existing)
                return existing
            else:
                # If we still can't find it, re-raise the error
                raise
    
    def get_by_token(self, db: Session, *, share_token: str) -> Optional[PublicShare]:
        """Get public share by token"""
        return db.query(PublicShare).filter(
            and_(
                PublicShare.share_token == share_token,
                PublicShare.is_active == True
            )
        ).first()
    
    def access_public_share(
        self,
        db: Session,
        *,
        share_token: str,
        user_id: int
    ) -> Optional[PublicShare]:
        """Access a public share and grant user access"""
        public_share = self.get_by_token(db, share_token=share_token)
        if not public_share:
            return None

        # Increment access count
        public_share.access_count += 1

        # Determine which content IDs to grant access to
        content_ids_to_grant = [public_share.content_id]

        # If this is a batch public share, grant access to all content items
        if public_share.content_ids:
            import json
            try:
                content_ids_to_grant = json.loads(public_share.content_ids)
            except (json.JSONDecodeError, TypeError):
                # Fallback to single content ID if JSON parsing fails
                content_ids_to_grant = [public_share.content_id]

        # Grant access to all content items (without committing each time)
        for content_id in content_ids_to_grant:
            content_access.grant_access(
                db,
                content_type=public_share.content_type,
                content_id=content_id,
                user_id=user_id,
                shared_by_id=public_share.shared_by_id,
                access_type=AccessType.SHARED_LINK,
                share_token=share_token,
                share_message=public_share.share_message,
                commit=False  # Don't commit yet, we'll do it once at the end
            )

        # Commit all changes together
        db.commit()
        db.refresh(public_share)
        return public_share


# Create instances
content_access = CRUDContentAccess(ContentAccess)
share_invitation = CRUDShareInvitation(ShareInvitation)
public_share = CRUDPublicShare(PublicShare)
