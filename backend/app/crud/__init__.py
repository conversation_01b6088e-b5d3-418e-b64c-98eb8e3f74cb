
from app.crud.crud_admin_flashcard import admin_flashcard
from app.crud.crud_course import course
from app.crud.crud_booking_request import booking_request
from app.crud.crud_gemini_file import gemini_file
from app.crud.crud_generated_flashcard import generated_flashcard
from app.crud.crud_generated_note_explanation import generated_note_explanation
from app.crud.crud_student_usage_limit import student_usage_limit

# These imports will be uncommented when the modules are created
# from app.crud.crud_user import user
# from app.crud.crud_school import school
# from app.crud.crud_department import department
from app.crud.crud_question import question
# from app.crud.crud_session import session
# from app.crud.crud_student_progress import student_question_attempt, student_bookmark, student_exam, student_exam_attempt
# from app.crud.crud_gamification import badge, user_badge, points_transaction, user_level, user_streak
# from app.crud.crud_ai_analytics import ai_assistant_interaction
