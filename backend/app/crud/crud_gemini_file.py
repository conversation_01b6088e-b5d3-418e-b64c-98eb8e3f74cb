from typing import List, Optional
from datetime import datetime

from sqlalchemy.orm import Session

from app.crud.base import CRUDBase
from app.models.gemini_file import GeminiFile
from app.schemas.gemini_file import GeminiFileCreate, GeminiFileUpdate


class CRUDGeminiFile(CRUDBase[GeminiFile, GeminiFileCreate, GeminiFileUpdate]):
    def get_by_student(
        self, db: Session, *, student_id: int, skip: int = 0, limit: int = 100
    ) -> List[GeminiFile]:
        """Get Gemini files by student ID, auto-deleting expired files"""
        from datetime import datetime
        current_time = datetime.utcnow()

        # First, mark expired files as deleted
        self._auto_delete_expired_files(db, current_time)

        return (
            db.query(self.model)
            .filter(GeminiFile.student_id == student_id)
            .filter(GeminiFile.is_deleted == False)
            .order_by(GeminiFile.created_at.desc())
            .offset(skip)
            .limit(limit)
            .all()
        )

    def get_by_gemini_file_id(
        self, db: Session, *, gemini_file_id: str
    ) -> Optional[GeminiFile]:
        """Get file by Gemini file ID"""
        return (
            db.query(self.model)
            .filter(GeminiFile.gemini_file_id == gemini_file_id)
            .filter(GeminiFile.is_deleted == False)
            .first()
        )

    def get_expired_files(
        self, db: Session, *, current_time: datetime
    ) -> List[GeminiFile]:
        """Get files that have passed their auto-delete time"""
        return (
            db.query(self.model)
            .filter(GeminiFile.auto_delete_time <= current_time)
            .filter(GeminiFile.is_deleted == False)
            .all()
        )

    def mark_as_deleted(
        self, db: Session, *, gemini_file_id: str
    ) -> Optional[GeminiFile]:
        """Mark a file as deleted"""
        file_obj = self.get_by_gemini_file_id(db, gemini_file_id=gemini_file_id)
        if file_obj:
            file_obj.is_deleted = True
            db.commit()
            db.refresh(file_obj)
        return file_obj

    def get_by_course_name(
        self, db: Session, *, student_id: int, course_name: str, skip: int = 0, limit: int = 100
    ) -> List[GeminiFile]:
        """Get files by student and course name, auto-deleting expired files"""
        from datetime import datetime
        current_time = datetime.utcnow()

        # First, mark expired files as deleted
        self._auto_delete_expired_files(db, current_time)

        return (
            db.query(self.model)
            .filter(GeminiFile.student_id == student_id)
            .filter(GeminiFile.course_name == course_name)
            .filter(GeminiFile.is_deleted == False)
            .order_by(GeminiFile.created_at.desc())
            .offset(skip)
            .limit(limit)
            .all()
        )

    def _auto_delete_expired_files(self, db: Session, current_time: datetime) -> int:
        """
        Automatically mark expired files as deleted in the database.
        This is called whenever files are queried to ensure expired files don't appear.

        Returns:
            Number of files marked as deleted
        """
        try:
            # Find expired files that are not already marked as deleted
            expired_files = (
                db.query(self.model)
                .filter(GeminiFile.auto_delete_time <= current_time)
                .filter(GeminiFile.is_deleted == False)
                .all()
            )

            deleted_count = 0
            for file_obj in expired_files:
                file_obj.is_deleted = True
                deleted_count += 1

            if deleted_count > 0:
                db.commit()
                # Note: We're only marking as deleted in DB here.
                # Actual Gemini deletion happens in background cleanup tasks

            return deleted_count

        except Exception as e:
            db.rollback()
            return 0


gemini_file = CRUDGeminiFile(GeminiFile)
