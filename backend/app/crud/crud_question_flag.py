from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import and_, or_, desc, func

from app.crud.base import CRUDBase
from app.models.question_flag import QuestionFlag, FlagStatus, FlagReason
from app.schemas.question_flag import Question<PERSON>lag<PERSON><PERSON>, QuestionFlagUpdate


class CRUDQuestionFlag(CRUDBase[QuestionFlag, QuestionFlagCreate, QuestionFlagUpdate]):
    """CRUD operations for QuestionFlag"""
    
    def create_flag(
        self,
        db: Session,
        *,
        question_id: int,
        student_id: int,
        reasons: List[str],
        description: Optional[str] = None
    ) -> QuestionFlag:
        """Create a new question flag"""
        import json
        
        flag = QuestionFlag(
            question_id=question_id,
            student_id=student_id,
            reasons=json.dumps(reasons),
            description=description,
            status=FlagStatus.PENDING
        )
        db.add(flag)
        db.commit()
        db.refresh(flag)
        return flag
    
    def get_by_question_and_student(
        self,
        db: Session,
        *,
        question_id: int,
        student_id: int
    ) -> Optional[QuestionFlag]:
        """Get flag by question and student (to prevent duplicates)"""
        return db.query(QuestionFlag).filter(
            and_(
                QuestionFlag.question_id == question_id,
                QuestionFlag.student_id == student_id
            )
        ).first()
    
    def get_flags_by_status(
        self,
        db: Session,
        *,
        status: FlagStatus,
        skip: int = 0,
        limit: int = 100
    ) -> List[QuestionFlag]:
        """Get flags by status with pagination"""
        return db.query(QuestionFlag).filter(
            QuestionFlag.status == status
        ).options(
            joinedload(QuestionFlag.question),
            joinedload(QuestionFlag.student),
            joinedload(QuestionFlag.reviewer)
        ).order_by(desc(QuestionFlag.created_at)).offset(skip).limit(limit).all()
    
    def get_flags_by_question(
        self,
        db: Session,
        *,
        question_id: int,
        skip: int = 0,
        limit: int = 100
    ) -> List[QuestionFlag]:
        """Get all flags for a specific question"""
        return db.query(QuestionFlag).filter(
            QuestionFlag.question_id == question_id
        ).options(
            joinedload(QuestionFlag.student),
            joinedload(QuestionFlag.reviewer)
        ).order_by(desc(QuestionFlag.created_at)).offset(skip).limit(limit).all()
    
    def get_flags_by_student(
        self,
        db: Session,
        *,
        student_id: int,
        skip: int = 0,
        limit: int = 100
    ) -> List[QuestionFlag]:
        """Get all flags submitted by a student"""
        return db.query(QuestionFlag).filter(
            QuestionFlag.student_id == student_id
        ).options(
            joinedload(QuestionFlag.question),
            joinedload(QuestionFlag.reviewer)
        ).order_by(desc(QuestionFlag.created_at)).offset(skip).limit(limit).all()
    
    def get_pending_flags(
        self,
        db: Session,
        *,
        skip: int = 0,
        limit: int = 100
    ) -> List[QuestionFlag]:
        """Get all pending flags for admin review"""
        return self.get_flags_by_status(
            db, status=FlagStatus.PENDING, skip=skip, limit=limit
        )
    
    def get_flags_with_filters(
        self,
        db: Session,
        *,
        status: Optional[FlagStatus] = None,
        reason: Optional[str] = None,
        course_id: Optional[int] = None,
        skip: int = 0,
        limit: int = 100
    ) -> List[QuestionFlag]:
        """Get flags with various filters"""
        query = db.query(QuestionFlag).options(
            joinedload(QuestionFlag.question),
            joinedload(QuestionFlag.student),
            joinedload(QuestionFlag.reviewer)
        )
        
        if status:
            query = query.filter(QuestionFlag.status == status)
        
        if reason:
            query = query.filter(QuestionFlag.reasons.contains(f'"{reason}"'))
        
        if course_id:
            query = query.join(QuestionFlag.question).filter(
                QuestionFlag.question.has(course_id=course_id)
            )
        
        return query.order_by(desc(QuestionFlag.created_at)).offset(skip).limit(limit).all()
    
    def update_flag_status(
        self,
        db: Session,
        *,
        flag_id: int,
        status: FlagStatus,
        reviewer_id: Optional[int] = None,
        admin_notes: Optional[str] = None
    ) -> Optional[QuestionFlag]:
        """Update flag status and reviewer information"""
        from datetime import datetime, timezone
        
        flag = db.query(QuestionFlag).filter(QuestionFlag.id == flag_id).first()
        if not flag:
            return None
        
        flag.status = status
        if reviewer_id:
            flag.reviewed_by = reviewer_id
            flag.reviewed_at = datetime.now(timezone.utc)
        if admin_notes:
            flag.admin_notes = admin_notes
        
        db.commit()
        db.refresh(flag)
        return flag
    
    def get_flag_statistics(self, db: Session) -> Dict[str, Any]:
        """Get statistics about flags"""
        total_flags = db.query(func.count(QuestionFlag.id)).scalar()
        pending_flags = db.query(func.count(QuestionFlag.id)).filter(
            QuestionFlag.status == FlagStatus.PENDING
        ).scalar()
        resolved_flags = db.query(func.count(QuestionFlag.id)).filter(
            QuestionFlag.status == FlagStatus.RESOLVED
        ).scalar()
        dismissed_flags = db.query(func.count(QuestionFlag.id)).filter(
            QuestionFlag.status == FlagStatus.DISMISSED
        ).scalar()
        
        # Get most common reasons
        reason_stats = {}
        flags_with_reasons = db.query(QuestionFlag.reasons).filter(
            QuestionFlag.reasons.isnot(None)
        ).all()
        
        import json
        for flag in flags_with_reasons:
            try:
                reasons = json.loads(flag.reasons)
                for reason in reasons:
                    reason_stats[reason] = reason_stats.get(reason, 0) + 1
            except (json.JSONDecodeError, TypeError):
                continue
        
        return {
            "total_flags": total_flags,
            "pending_flags": pending_flags,
            "resolved_flags": resolved_flags,
            "dismissed_flags": dismissed_flags,
            "reason_statistics": reason_stats
        }
    
    def bulk_update_status(
        self,
        db: Session,
        *,
        flag_ids: List[int],
        status: FlagStatus,
        reviewer_id: Optional[int] = None,
        admin_notes: Optional[str] = None
    ) -> int:
        """Bulk update multiple flags"""
        from datetime import datetime, timezone
        
        update_data = {"status": status}
        if reviewer_id:
            update_data["reviewed_by"] = reviewer_id
            update_data["reviewed_at"] = datetime.now(timezone.utc)
        if admin_notes:
            update_data["admin_notes"] = admin_notes
        
        updated_count = db.query(QuestionFlag).filter(
            QuestionFlag.id.in_(flag_ids)
        ).update(update_data, synchronize_session=False)
        
        db.commit()
        return updated_count
    
    def delete_flag(self, db: Session, *, flag_id: int) -> bool:
        """Delete a flag (admin only)"""
        flag = db.query(QuestionFlag).filter(QuestionFlag.id == flag_id).first()
        if flag:
            db.delete(flag)
            db.commit()
            return True
        return False


question_flag = CRUDQuestionFlag(QuestionFlag)
