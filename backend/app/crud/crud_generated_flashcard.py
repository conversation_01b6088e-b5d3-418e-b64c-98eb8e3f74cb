from typing import List, Optional
from sqlalchemy.orm import Session
from sqlalchemy import and_, or_

from app.crud.base import CRUDBase
from app.models.generated_flashcard import GeneratedFlashcard
from app.schemas.generated_flashcard import GeneratedFlashcardCreate, GeneratedFlashcardUpdate


class CRUDGeneratedFlashcard(CRUDBase[GeneratedFlashcard, GeneratedFlashcardCreate, GeneratedFlashcardUpdate]):
    def get_by_student(
        self,
        db: Session,
        *,
        student_id: int,
        skip: int = 0,
        limit: int = 100,
        active_only: bool = True
    ) -> List[GeneratedFlashcard]:
        """Get flashcards by student ID, ordered by newest first"""
        query = db.query(self.model).filter(self.model.student_id == student_id)

        if active_only:
            query = query.filter(self.model.is_active == True)

        return query.order_by(self.model.created_at.desc()).offset(skip).limit(limit).all()

    def get_by_course_name(
        self,
        db: Session,
        *,
        student_id: int,
        course_name: str,
        skip: int = 0,
        limit: int = 100,
        active_only: bool = True
    ) -> List[GeneratedFlashcard]:
        """Get flashcards by course name for a specific student, ordered by newest first"""
        query = db.query(self.model).filter(
            and_(
                self.model.student_id == student_id,
                self.model.course_name == course_name
            )
        )

        if active_only:
            query = query.filter(self.model.is_active == True)

        return query.order_by(self.model.created_at.desc()).offset(skip).limit(limit).all()

    def get_by_difficulty(
        self,
        db: Session,
        *,
        student_id: int,
        difficulty: str,
        skip: int = 0,
        limit: int = 100,
        active_only: bool = True
    ) -> List[GeneratedFlashcard]:
        """Get flashcards by difficulty for a specific student, ordered by newest first"""
        query = db.query(self.model).filter(
            and_(
                self.model.student_id == student_id,
                self.model.difficulty == difficulty
            )
        )

        if active_only:
            query = query.filter(self.model.is_active == True)

        return query.order_by(self.model.created_at.desc()).offset(skip).limit(limit).all()

    def search(
        self,
        db: Session,
        *,
        student_id: int,
        search_term: str,
        skip: int = 0,
        limit: int = 100,
        active_only: bool = True
    ) -> List[GeneratedFlashcard]:
        """Search flashcards by content for a specific student, ordered by newest first"""
        query = db.query(self.model).filter(self.model.student_id == student_id)

        # Search in front_content, back_content, and topic
        search_filter = or_(
            self.model.front_content.ilike(f"%{search_term}%"),
            self.model.back_content.ilike(f"%{search_term}%"),
            self.model.topic.ilike(f"%{search_term}%")
        )
        query = query.filter(search_filter)

        if active_only:
            query = query.filter(self.model.is_active == True)

        return query.order_by(self.model.created_at.desc()).offset(skip).limit(limit).all()

    def get_by_gemini_file(
        self,
        db: Session,
        *,
        student_id: int,
        gemini_file_id: str,
        skip: int = 0,
        limit: int = 100,
        active_only: bool = True
    ) -> List[GeneratedFlashcard]:
        """Get flashcards generated from a specific Gemini file, ordered by newest first"""
        query = db.query(self.model).filter(
            and_(
                self.model.student_id == student_id,
                self.model.gemini_file_id == gemini_file_id
            )
        )

        if active_only:
            query = query.filter(self.model.is_active == True)

        return query.order_by(self.model.created_at.desc()).offset(skip).limit(limit).all()

    def create_with_student(
        self, 
        db: Session, 
        *, 
        obj_in: GeneratedFlashcardCreate, 
        student_id: int
    ) -> GeneratedFlashcard:
        """Create flashcard with student information"""
        obj_in_data = obj_in.model_dump()
        obj_in_data["student_id"] = student_id
        db_obj = self.model(**obj_in_data)
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj

    def bulk_create(
        self, 
        db: Session, 
        *, 
        objs_in: List[GeneratedFlashcardCreate], 
        student_id: int
    ) -> List[GeneratedFlashcard]:
        """Bulk create flashcards"""
        db_objs = []
        for obj_in in objs_in:
            obj_in_data = obj_in.model_dump()
            obj_in_data["student_id"] = student_id
            db_obj = self.model(**obj_in_data)
            db_objs.append(db_obj)
        
        db.add_all(db_objs)
        db.commit()
        
        for db_obj in db_objs:
            db.refresh(db_obj)
            
        return db_objs


generated_flashcard = CRUDGeneratedFlashcard(GeneratedFlashcard)
