from typing import List, Optional, Dict, Any
from sqlalchemy.orm import Session, joinedload
from sqlalchemy import func, and_, or_

from app.crud.base import CRUDBase
from app.models.admin_flashcard import AdminFlashCard
from app.models.course import Course
from app.schemas.admin_flashcard import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>C<PERSON>, AdminFlashCardUpdate


class CRUDAdminFlashCard(CRUDBase[AdminFlashCard, AdminFlashCardCreate, AdminFlashCardUpdate]):
    def get_by_course(
        self, 
        db: Session, 
        *, 
        course_id: int, 
        skip: int = 0, 
        limit: int = 100,
        active_only: bool = True
    ) -> List[AdminFlashCard]:
        """Get flashcards by course ID"""
        query = db.query(self.model).filter(self.model.course_id == course_id)
        
        if active_only:
            query = query.filter(self.model.is_active == True)
            
        return query.offset(skip).limit(limit).all()

    def get_with_course_info(
        self, 
        db: Session, 
        *, 
        skip: int = 0, 
        limit: int = 100,
        course_id: Optional[int] = None,
        difficulty: Optional[str] = None,
        topic: Optional[str] = None,
        active_only: bool = True
    ) -> List[AdminFlashCard]:
        """Get flashcards with course information"""
        query = db.query(self.model).options(joinedload(self.model.course))
        
        if active_only:
            query = query.filter(self.model.is_active == True)
            
        if course_id:
            query = query.filter(self.model.course_id == course_id)
            
        if difficulty:
            query = query.filter(self.model.difficulty == difficulty)
            
        if topic:
            query = query.filter(self.model.topic.ilike(f"%{topic}%"))
            
        return query.offset(skip).limit(limit).all()

    def search(
        self, 
        db: Session, 
        *, 
        search_term: str,
        skip: int = 0, 
        limit: int = 100,
        course_id: Optional[int] = None
    ) -> List[AdminFlashCard]:
        """Search flashcards by content"""
        query = db.query(self.model).options(joinedload(self.model.course))
        
        # Search in front_content, back_content, and topic
        search_filter = or_(
            self.model.front_content.ilike(f"%{search_term}%"),
            self.model.back_content.ilike(f"%{search_term}%"),
            self.model.topic.ilike(f"%{search_term}%")
        )
        query = query.filter(search_filter)
        
        if course_id:
            query = query.filter(self.model.course_id == course_id)
            
        query = query.filter(self.model.is_active == True)
        
        return query.offset(skip).limit(limit).all()

    def get_stats(self, db: Session) -> Dict[str, Any]:
        """Get flashcard statistics"""
        total = db.query(func.count(self.model.id)).scalar()
        active = db.query(func.count(self.model.id)).filter(self.model.is_active == True).scalar()
        inactive = total - active
        
        # Recent flashcards (last 7 days)
        from datetime import datetime, timedelta
        week_ago = datetime.utcnow() - timedelta(days=7)
        recent = db.query(func.count(self.model.id)).filter(
            self.model.created_at >= week_ago
        ).scalar()
        
        # By course
        by_course = db.query(
            Course.name,
            Course.code,
            func.count(self.model.id).label('count')
        ).join(Course).filter(
            self.model.is_active == True
        ).group_by(Course.id, Course.name, Course.code).all()
        
        # By difficulty
        by_difficulty = db.query(
            self.model.difficulty,
            func.count(self.model.id).label('count')
        ).filter(
            self.model.is_active == True,
            self.model.difficulty.isnot(None)
        ).group_by(self.model.difficulty).all()
        
        return {
            "total": total,
            "active": active,
            "inactive": inactive,
            "recent": recent,
            "by_course": [
                {"course_name": row.name, "course_code": row.code, "count": row.count}
                for row in by_course
            ],
            "by_difficulty": [
                {"difficulty": row.difficulty, "count": row.count}
                for row in by_difficulty
            ]
        }

    def create_with_creator(
        self, 
        db: Session, 
        *, 
        obj_in: AdminFlashCardCreate, 
        created_by: int
    ) -> AdminFlashCard:
        """Create flashcard with creator information"""
        obj_in_data = obj_in.model_dump()
        obj_in_data["created_by"] = created_by
        db_obj = self.model(**obj_in_data)
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj

    def bulk_create(
        self, 
        db: Session, 
        *, 
        objs_in: List[AdminFlashCardCreate], 
        created_by: int
    ) -> List[AdminFlashCard]:
        """Bulk create flashcards"""
        db_objs = []
        for obj_in in objs_in:
            obj_in_data = obj_in.model_dump()
            obj_in_data["created_by"] = created_by
            db_obj = self.model(**obj_in_data)
            db_objs.append(db_obj)
        
        db.add_all(db_objs)
        db.commit()
        
        for db_obj in db_objs:
            db.refresh(db_obj)
            
        return db_objs

    def bulk_update_status(
        self, 
        db: Session, 
        *, 
        flashcard_ids: List[int], 
        is_active: bool
    ) -> int:
        """Bulk update flashcard status"""
        updated_count = db.query(self.model).filter(
            self.model.id.in_(flashcard_ids)
        ).update(
            {"is_active": is_active},
            synchronize_session=False
        )
        db.commit()
        return updated_count

    def bulk_delete(self, db: Session, *, flashcard_ids: List[int]) -> int:
        """Bulk delete flashcards"""
        deleted_count = db.query(self.model).filter(
            self.model.id.in_(flashcard_ids)
        ).delete(synchronize_session=False)
        db.commit()
        return deleted_count


admin_flashcard = CRUDAdminFlashCard(AdminFlashCard)
