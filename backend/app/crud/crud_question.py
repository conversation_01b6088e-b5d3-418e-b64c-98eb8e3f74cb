from typing import List, Optional, Dict, Any

from sqlalchemy.orm import Session

from app.crud.base import CRUDBase
from app.models.question import Question
from app.schemas.question import QuestionCreate, QuestionUpdate


class CRUDQuestion(CRUDBase[Question, QuestionCreate, QuestionUpdate]):
    def get_by_course(
        self, db: Session, *, course_id: int, skip: int = 0, limit: int = 100
    ) -> List[Question]:
        """Get questions by course ID"""
        return (
            db.query(self.model)
            .filter(Question.course_id == course_id)
            .filter(Question.is_active == True)
            .offset(skip)
            .limit(limit)
            .all()
        )

    def get_by_course_name(
        self, db: Session, *, course_name: str, skip: int = 0, limit: int = 100
    ) -> List[Question]:
        """Get questions by course name (for note-generated questions)"""
        return (
            db.query(self.model)
            .filter(Question.course_name == course_name)
            .filter(Question.is_active == True)
            .offset(skip)
            .limit(limit)
            .all()
        )

    def get_by_student(
        self, db: Session, *, student_id: int, skip: int = 0, limit: int = 100
    ) -> List[Question]:
        """Get questions created by a student"""
        return (
            db.query(self.model)
            .filter(Question.created_by_id == student_id)
            .filter(Question.is_active == True)
            .offset(skip)
            .limit(limit)
            .all()
        )

    def get_by_topic(
        self, db: Session, *, topic: str, skip: int = 0, limit: int = 100
    ) -> List[Question]:
        """Get questions by topic"""
        return (
            db.query(self.model)
            .filter(Question.topic == topic)
            .filter(Question.is_active == True)
            .offset(skip)
            .limit(limit)
            .all()
        )

    def get_by_difficulty(
        self, db: Session, *, difficulty: str, skip: int = 0, limit: int = 100
    ) -> List[Question]:
        """Get questions by difficulty level"""
        return (
            db.query(self.model)
            .filter(Question.difficulty == difficulty)
            .filter(Question.is_active == True)
            .offset(skip)
            .limit(limit)
            .all()
        )


question = CRUDQuestion(Question)
