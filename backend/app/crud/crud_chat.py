from typing import List, Optional
from datetime import datetime

from sqlalchemy.orm import Session
from sqlalchemy import and_, or_, desc

from app.crud.base import CRUDBase
from app.models.chat import ChatRoom, ChatMessage, UserOnlineStatus
from app.schemas.chat import (
    ChatRoomCreate, ChatRoomUpdate,
    ChatMessageCreate, ChatMessageUpdate,
    UserOnlineStatusCreate, UserOnlineStatusUpdate
)


class CRUDChatRoom(CRUDBase[ChatRoom, ChatRoomCreate, ChatRoomUpdate]):
    def get_by_participants(
        self, db: Session, *, student_id: int, tutor_id: int
    ) -> Optional[ChatRoom]:
        """Get chat room by student and tutor IDs"""
        return db.query(ChatRoom).filter(
            and_(
                ChatRoom.student_id == student_id,
                ChatRoom.tutor_id == tutor_id
            )
        ).first()

    def get_for_user(
        self, db: Session, *, user_id: int, skip: int = 0, limit: int = 100
    ) -> List[ChatRoom]:
        """Get all chat rooms for a user (as student or tutor)"""
        return db.query(ChatRoom).filter(
            or_(
                ChatRoom.student_id == user_id,
                ChatRoom.tutor_id == user_id
            )
        ).filter(ChatRoom.is_active == True).offset(skip).limit(limit).all()

    def get_for_user_and_room(
        self, db: Session, *, user_id: int, chat_room_id: int
    ) -> Optional[ChatRoom]:
        """Get a specific chat room if user is a participant"""
        return db.query(ChatRoom).filter(
            and_(
                ChatRoom.id == chat_room_id,
                or_(
                    ChatRoom.student_id == user_id,
                    ChatRoom.tutor_id == user_id
                ),
                ChatRoom.is_active == True
            )
        ).first()

    def create_for_booking(
        self, db: Session, *, student_id: int, tutor_id: int
    ) -> ChatRoom:
        """Create a chat room for a booking request"""
        # Check if room already exists
        existing_room = self.get_by_participants(
            db, student_id=student_id, tutor_id=tutor_id
        )
        if existing_room:
            # Reactivate if inactive
            if not existing_room.is_active:
                existing_room.is_active = True
                db.add(existing_room)
                db.commit()
                db.refresh(existing_room)
            return existing_room
        
        # Create new room
        db_obj = ChatRoom(
            student_id=student_id,
            tutor_id=tutor_id,
            is_active=True
        )
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj


class CRUDChatMessage(CRUDBase[ChatMessage, ChatMessageCreate, ChatMessageUpdate]):
    def get_by_chat_room(
        self, db: Session, *, chat_room_id: int, skip: int = 0, limit: int = 100
    ) -> List[ChatMessage]:
        """Get messages for a chat room"""
        return db.query(ChatMessage).filter(
            ChatMessage.chat_room_id == chat_room_id
        ).order_by(desc(ChatMessage.created_at)).offset(skip).limit(limit).all()

    def create_message(
        self, db: Session, *, obj_in: ChatMessageCreate, sender_id: int
    ) -> ChatMessage:
        """Create a new chat message"""
        db_obj = ChatMessage(
            content=obj_in.content,
            message_type=obj_in.message_type,
            sender_id=sender_id,
            chat_room_id=obj_in.chat_room_id,
            is_read=False
        )
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj

    def mark_as_read(
        self, db: Session, *, chat_room_id: int, user_id: int
    ) -> int:
        """Mark all messages in a chat room as read for a user"""
        updated_count = db.query(ChatMessage).filter(
            and_(
                ChatMessage.chat_room_id == chat_room_id,
                ChatMessage.sender_id != user_id,  # Don't mark own messages
                ChatMessage.is_read == False
            )
        ).update({"is_read": True})
        db.commit()
        return updated_count

    def get_unread_count(
        self, db: Session, *, chat_room_id: int, user_id: int
    ) -> int:
        """Get count of unread messages for a user in a chat room"""
        return db.query(ChatMessage).filter(
            and_(
                ChatMessage.chat_room_id == chat_room_id,
                ChatMessage.sender_id != user_id,  # Don't count own messages
                ChatMessage.is_read == False
            )
        ).count()


class CRUDUserOnlineStatus(CRUDBase[UserOnlineStatus, UserOnlineStatusCreate, UserOnlineStatusUpdate]):
    def get_by_user_id(self, db: Session, *, user_id: int) -> Optional[UserOnlineStatus]:
        """Get online status by user ID"""
        return db.query(UserOnlineStatus).filter(
            UserOnlineStatus.user_id == user_id
        ).first()

    def update_status(
        self, db: Session, *, user_id: int, is_online: bool, connection_id: Optional[str] = None
    ) -> UserOnlineStatus:
        """Update user online status"""
        status = self.get_by_user_id(db, user_id=user_id)
        if status:
            status.is_online = is_online
            status.last_seen = datetime.utcnow()
            if connection_id is not None:
                status.connection_id = connection_id
            db.add(status)
        else:
            status = UserOnlineStatus(
                user_id=user_id,
                is_online=is_online,
                last_seen=datetime.utcnow(),
                connection_id=connection_id
            )
            db.add(status)
        
        db.commit()
        db.refresh(status)
        return status

    def get_online_users(self, db: Session, *, user_ids: List[int]) -> List[UserOnlineStatus]:
        """Get online status for multiple users"""
        return db.query(UserOnlineStatus).filter(
            and_(
                UserOnlineStatus.user_id.in_(user_ids),
                UserOnlineStatus.is_online == True
            )
        ).all()


# Create instances
chat_room = CRUDChatRoom(ChatRoom)
chat_message = CRUDChatMessage(ChatMessage)
user_online_status = CRUDUserOnlineStatus(UserOnlineStatus)
