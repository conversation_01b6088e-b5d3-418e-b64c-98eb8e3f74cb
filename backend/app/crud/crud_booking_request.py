from typing import List, Optional
from datetime import datetime

from sqlalchemy.orm import Session, joinedload
from sqlalchemy import and_, or_

from app.crud.base import CRUDBase
from app.models.booking_request import BookingRequest, BookingRequestStatus
from app.models.user import User
from app.models.course import Course
from app.schemas.booking_request import (
    BookingRequestCreate, 
    BookingRequestUpdate, 
    BookingRequestFilters,
    BookingRequestResponse
)


class CRUDBookingRequest(CRUDBase[BookingRequest, BookingRequestCreate, BookingRequestUpdate]):
    def create_for_student(
        self,
        db: Session,
        *,
        obj_in: BookingRequestCreate,
        student_id: int
    ) -> BookingRequest:
        """Create a simple booking request from a student to a tutor"""
        db_obj = BookingRequest(
            student_id=student_id,
            tutor_id=obj_in.tutor_id,
            message=obj_in.message or "I would like to request a tutoring session.",
            course_id=obj_in.course_id,
            status=BookingRequestStatus.PENDING
        )
        db.add(db_obj)
        db.commit()
        db.refresh(db_obj)
        return db_obj

    def get_with_details(self, db: Session, *, id: int) -> Optional[BookingRequest]:
        """Get booking request with student, tutor, and course details"""
        return (
            db.query(BookingRequest)
            .options(
                joinedload(BookingRequest.student),
                joinedload(BookingRequest.tutor),
                joinedload(BookingRequest.course)
            )
            .filter(BookingRequest.id == id)
            .first()
        )

    def get_for_tutor(
        self, 
        db: Session, 
        *, 
        tutor_id: int, 
        skip: int = 0, 
        limit: int = 100,
        status: Optional[BookingRequestStatus] = None
    ) -> List[BookingRequest]:
        """Get booking requests for a specific tutor"""
        query = (
            db.query(BookingRequest)
            .options(
                joinedload(BookingRequest.student),
                joinedload(BookingRequest.course)
            )
            .filter(BookingRequest.tutor_id == tutor_id)
        )
        
        if status:
            query = query.filter(BookingRequest.status == status)
            
        return query.order_by(BookingRequest.created_at.desc()).offset(skip).limit(limit).all()

    def get_for_student(
        self, 
        db: Session, 
        *, 
        student_id: int, 
        skip: int = 0, 
        limit: int = 100,
        status: Optional[BookingRequestStatus] = None
    ) -> List[BookingRequest]:
        """Get booking requests sent by a specific student"""
        query = (
            db.query(BookingRequest)
            .options(
                joinedload(BookingRequest.tutor),
                joinedload(BookingRequest.course)
            )
            .filter(BookingRequest.student_id == student_id)
        )
        
        if status:
            query = query.filter(BookingRequest.status == status)
            
        return query.order_by(BookingRequest.created_at.desc()).offset(skip).limit(limit).all()

    def respond_to_request(
        self, 
        db: Session, 
        *, 
        request_id: int, 
        tutor_id: int,
        response: BookingRequestResponse
    ) -> Optional[BookingRequest]:
        """Tutor responds to a booking request"""
        booking_request = (
            db.query(BookingRequest)
            .filter(
                and_(
                    BookingRequest.id == request_id,
                    BookingRequest.tutor_id == tutor_id,
                    BookingRequest.status == BookingRequestStatus.PENDING
                )
            )
            .first()
        )
        
        if not booking_request:
            return None
            
        booking_request.status = response.status
        booking_request.tutor_response = response.tutor_response
        booking_request.response_date = datetime.utcnow()

        # If accepted, create a chat room
        if response.status == BookingRequestStatus.ACCEPTED:
            from app.services.chat_service import chat_service
            chat_room = chat_service.create_chat_room(
                db,
                student_id=booking_request.student_id,
                tutor_id=booking_request.tutor_id
            )
            booking_request.chat_room_id = chat_room.id

        db.add(booking_request)
        db.commit()
        db.refresh(booking_request)
        return booking_request

    def search_requests(
        self, 
        db: Session, 
        *, 
        filters: BookingRequestFilters,
        skip: int = 0,
        limit: int = 100
    ) -> List[BookingRequest]:
        """Search booking requests with filters"""
        query = (
            db.query(BookingRequest)
            .options(
                joinedload(BookingRequest.student),
                joinedload(BookingRequest.tutor),
                joinedload(BookingRequest.course)
            )
        )

        # Apply filters
        if filters.status:
            query = query.filter(BookingRequest.status == filters.status)
            
        if filters.tutor_id:
            query = query.filter(BookingRequest.tutor_id == filters.tutor_id)
            
        if filters.student_id:
            query = query.filter(BookingRequest.student_id == filters.student_id)
            
        if filters.course_id:
            query = query.filter(BookingRequest.course_id == filters.course_id)
            
        if filters.date_from:
            query = query.filter(BookingRequest.preferred_date >= filters.date_from)
            
        if filters.date_to:
            query = query.filter(BookingRequest.preferred_date <= filters.date_to)

        return query.order_by(BookingRequest.created_at.desc()).offset(skip).limit(limit).all()

    def get_pending_count_for_tutor(self, db: Session, *, tutor_id: int) -> int:
        """Get count of pending booking requests for a tutor"""
        return (
            db.query(BookingRequest)
            .filter(
                and_(
                    BookingRequest.tutor_id == tutor_id,
                    BookingRequest.status == BookingRequestStatus.PENDING
                )
            )
            .count()
        )


booking_request = CRUDBookingRequest(BookingRequest)
