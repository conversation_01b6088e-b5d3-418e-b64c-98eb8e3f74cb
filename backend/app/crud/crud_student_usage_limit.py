from typing import List, Optional
from sqlalchemy.orm import Session
from sqlalchemy import and_

from app.crud.base import CRUDBase
from app.models.student_usage_limit import StudentUsageLimit, UsageType
from app.schemas.usage_limit import UsageLimitCreate, UsageLimitUpdate


class CRUDStudentUsageLimit(CRUDBase[StudentUsageLimit, UsageLimitCreate, UsageLimitUpdate]):
    """CRUD operations for StudentUsageLimit"""
    
    def get_by_student_and_type_and_month(
        self,
        db: Session,
        *,
        student_id: int,
        usage_type: UsageType,
        usage_month: str
    ) -> Optional[StudentUsageLimit]:
        """Get usage limit record by student, type, and month"""
        return db.query(StudentUsageLimit).filter(
            and_(
                StudentUsageLimit.student_id == student_id,
                StudentUsageLimit.usage_type == usage_type,
                StudentUsageLimit.usage_month == usage_month
            )
        ).first()
    
    def get_by_student_and_month(
        self,
        db: Session,
        *,
        student_id: int,
        usage_month: str
    ) -> List[StudentUsageLimit]:
        """Get all usage limit records for a student in a specific month"""
        return db.query(StudentUsageLimit).filter(
            and_(
                StudentUsageLimit.student_id == student_id,
                StudentUsageLimit.usage_month == usage_month
            )
        ).all()
    
    def get_by_student(
        self,
        db: Session,
        *,
        student_id: int,
        skip: int = 0,
        limit: int = 100
    ) -> List[StudentUsageLimit]:
        """Get all usage limit records for a student"""
        return db.query(StudentUsageLimit).filter(
            StudentUsageLimit.student_id == student_id
        ).offset(skip).limit(limit).all()
    
    def get_by_month(
        self,
        db: Session,
        *,
        usage_month: str,
        skip: int = 0,
        limit: int = 100
    ) -> List[StudentUsageLimit]:
        """Get all usage limit records for a specific month"""
        return db.query(StudentUsageLimit).filter(
            StudentUsageLimit.usage_month == usage_month
        ).offset(skip).limit(limit).all()
    
    def increment_usage(
        self,
        db: Session,
        *,
        student_id: int,
        usage_type: UsageType,
        usage_month: str,
        increment: int = 1
    ) -> StudentUsageLimit:
        """Increment usage count for a specific record"""
        record = self.get_by_student_and_type_and_month(
            db, student_id=student_id, usage_type=usage_type, usage_month=usage_month
        )
        
        if record:
            record.usage_count += increment
            db.commit()
            db.refresh(record)
        
        return record
    
    def reset_usage_for_month(
        self,
        db: Session,
        *,
        usage_month: str,
        student_id: Optional[int] = None,
        usage_type: Optional[UsageType] = None
    ) -> int:
        """Reset usage counts for a specific month"""
        query = db.query(StudentUsageLimit).filter(
            StudentUsageLimit.usage_month == usage_month
        )
        
        if student_id:
            query = query.filter(StudentUsageLimit.student_id == student_id)
        
        if usage_type:
            query = query.filter(StudentUsageLimit.usage_type == usage_type)
        
        records = query.all()
        count = len(records)
        
        for record in records:
            record.usage_count = 0
        
        db.commit()
        return count


student_usage_limit = CRUDStudentUsageLimit(StudentUsageLimit)
