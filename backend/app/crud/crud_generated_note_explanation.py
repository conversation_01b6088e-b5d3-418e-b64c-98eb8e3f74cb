from typing import List, Optional

from sqlalchemy.orm import Session

from app.crud.base import CRUDBase
from app.models.generated_note_explanation import GeneratedNoteExplanation
from app.schemas.generated_note_explanation import GeneratedNoteExplanationCreate, GeneratedNoteExplanationUpdate


class CRUDGeneratedNoteExplanation(CRUDBase[GeneratedNoteExplanation, GeneratedNoteExplanationCreate, GeneratedNoteExplanationUpdate]):
    def get_by_student(
        self, db: Session, *, student_id: int, skip: int = 0, limit: int = 100
    ) -> List[GeneratedNoteExplanation]:
        """Get all note explanations for a specific student"""
        return (
            db.query(self.model)
            .filter(GeneratedNoteExplanation.student_id == student_id)
            .filter(GeneratedNoteExplanation.is_active == True)
            .order_by(GeneratedNoteExplanation.created_at.desc())
            .offset(skip)
            .limit(limit)
            .all()
        )

    def get_by_student_and_course(
        self, db: Session, *, student_id: int, course_name: str, skip: int = 0, limit: int = 100
    ) -> List[GeneratedNoteExplanation]:
        """Get note explanations for a specific student and course"""
        return (
            db.query(self.model)
            .filter(GeneratedNoteExplanation.student_id == student_id)
            .filter(GeneratedNoteExplanation.course_name == course_name)
            .filter(GeneratedNoteExplanation.is_active == True)
            .order_by(GeneratedNoteExplanation.created_at.desc())
            .offset(skip)
            .limit(limit)
            .all()
        )

    def get_by_gemini_file(
        self, db: Session, *, gemini_file_id: str, student_id: int
    ) -> Optional[GeneratedNoteExplanation]:
        """Get note explanation by Gemini file ID and student ID"""
        return (
            db.query(self.model)
            .filter(GeneratedNoteExplanation.gemini_file_id == gemini_file_id)
            .filter(GeneratedNoteExplanation.student_id == student_id)
            .filter(GeneratedNoteExplanation.is_active == True)
            .first()
        )

    def deactivate(self, db: Session, *, id: int) -> Optional[GeneratedNoteExplanation]:
        """Soft delete a note explanation by setting is_active to False"""
        explanation = db.query(self.model).filter(self.model.id == id).first()
        if explanation:
            explanation.is_active = False
            db.add(explanation)
            db.commit()
            db.refresh(explanation)
        return explanation


generated_note_explanation = CRUDGeneratedNoteExplanation(GeneratedNoteExplanation)
