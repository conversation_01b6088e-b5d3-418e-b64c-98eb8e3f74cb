from typing import Any, List, Optional

from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session

from app import schemas
from app.api import deps
from app.models.user import User, UserRole
from app.models.booking_request import BookingRequestStatus
from app.crud.crud_booking_request import booking_request
from app.services import user_service
from app.schemas.booking_request import (
    BookingRequest, BookingRequestCreate, BookingRequestUpdate,
    BookingRequestResponse, BookingRequestFilters, BookingRequestListItem
)

router = APIRouter()


@router.get("/", response_model=List[BookingRequest])
def get_booking_requests(
    db: Session = Depends(deps.get_db),
    skip: int = 0,
    limit: int = 100,
    status: Optional[BookingRequestStatus] = Query(None),
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Retrieve booking requests for the current user.
    Students see their sent requests, tutors see their received requests.
    """
    if current_user.role == UserRole.STUDENT:
        requests = booking_request.get_for_student(
            db, student_id=current_user.id, skip=skip, limit=limit, status=status
        )
    elif current_user.role == UserRole.TUTOR:
        requests = booking_request.get_for_tutor(
            db, tutor_id=current_user.id, skip=skip, limit=limit, status=status
        )
    else:
        # Admin can see all requests
        filters = BookingRequestFilters(status=status)
        requests = booking_request.search_requests(
            db, filters=filters, skip=skip, limit=limit
        )
    
    # Convert to response format with names
    result = []
    for req in requests:
        req_dict = {
            **req.__dict__,
            "student_name": req.student.full_name,
            "student_email": req.student.email,
            "tutor_name": req.tutor.full_name,
            "tutor_email": req.tutor.email,
            "course_name": req.course.name if req.course else None
        }
        result.append(req_dict)
    
    return result


@router.post("/", response_model=BookingRequest)
def create_booking_request(
    *,
    db: Session = Depends(deps.get_db),
    request_in: BookingRequestCreate,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Create a simple booking request (students only).
    This is a simplified request that only requires tutor_id and optional course_id/message.
    """
    if current_user.role != UserRole.STUDENT:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only students can create booking requests"
        )

    # Verify tutor exists and is active
    tutor = user_service.get(db, id=request_in.tutor_id)
    if not tutor or tutor.role != UserRole.TUTOR or not tutor.is_active:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Tutor not found or inactive"
        )

    # Check if there's already a pending or accepted request between these users
    existing_request = db.query(booking_request.model).filter(
        booking_request.model.student_id == current_user.id,
        booking_request.model.tutor_id == request_in.tutor_id,
        booking_request.model.status.in_(["pending", "accepted"])
    ).first()

    if existing_request:
        if existing_request.status == "pending":
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="You already have a pending request with this tutor"
            )
        elif existing_request.status == "accepted":
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="You already have an active session with this tutor. Please use the chat to communicate."
            )

    booking_req = booking_request.create_for_student(
        db, obj_in=request_in, student_id=current_user.id
    )

    # Send email notification to tutor
    from app.services.email_service import send_booking_request_notification
    try:
        send_booking_request_notification(
            tutor_email=tutor.email,
            tutor_name=tutor.full_name or tutor.email,
            student_name=current_user.full_name or current_user.email,
            student_email=current_user.email,
            message=booking_req.message or "I would like to request a tutoring session.",
            request_id=booking_req.id
        )
    except Exception as e:
        # Log the error but don't fail the request creation
        import logging
        logger = logging.getLogger(__name__)
        logger.error(f"Failed to send booking request notification email: {str(e)}")

    # Get the request with details for response
    booking_req_with_details = booking_request.get_with_details(db, id=booking_req.id)

    # Convert to response format
    result = {
        **booking_req_with_details.__dict__,
        "student_name": booking_req_with_details.student.full_name,
        "student_email": booking_req_with_details.student.email,
        "tutor_name": booking_req_with_details.tutor.full_name,
        "tutor_email": booking_req_with_details.tutor.email,
        "course_name": booking_req_with_details.course.name if booking_req_with_details.course else None
    }

    return result


@router.get("/{id}", response_model=BookingRequest)
def get_booking_request(
    *,
    db: Session = Depends(deps.get_db),
    id: int,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Get a specific booking request.
    """
    booking_req = booking_request.get_with_details(db, id=id)
    if not booking_req:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Booking request not found"
        )
    
    # Check permissions
    if (current_user.role == UserRole.STUDENT and booking_req.student_id != current_user.id) or \
       (current_user.role == UserRole.TUTOR and booking_req.tutor_id != current_user.id):
        if current_user.role != UserRole.ADMIN:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Not enough permissions"
            )
    
    # Convert to response format
    result = {
        **booking_req.__dict__,
        "student_name": booking_req.student.full_name,
        "student_email": booking_req.student.email,
        "tutor_name": booking_req.tutor.full_name,
        "tutor_email": booking_req.tutor.email,
        "course_name": booking_req.course.name if booking_req.course else None
    }
    
    return result


@router.post("/{id}/respond", response_model=BookingRequest)
def respond_to_booking_request(
    *,
    db: Session = Depends(deps.get_db),
    id: int,
    response: BookingRequestResponse,
    current_user: User = Depends(deps.get_current_tutor_user),
) -> Any:
    """
    Respond to a booking request (tutors only).
    """
    booking_req = booking_request.respond_to_request(
        db, request_id=id, tutor_id=current_user.id, response=response
    )

    if not booking_req:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Booking request not found or already responded to"
        )

    # Create in-app notification for the student
    from app.crud.crud_notification import notification
    from app.models.notification import NotificationType
    from app.api.v1.endpoints.chat import get_connection_manager

    try:
        notification_obj = None
        if response.status.value == "declined":
            # Create notification for rejected booking request
            notification_title = f"Booking Request Declined"
            notification_message = f"Your tutoring request to {current_user.full_name or current_user.email} has been declined."
            if response.tutor_response:
                notification_message += f" Message: {response.tutor_response}"

            notification_obj = notification.create_notification(
                db,
                user_id=booking_req.student_id,
                notification_type=NotificationType.BOOKING_REJECTED,
                title=notification_title,
                message=notification_message,
                related_id=booking_req.id,
                related_type="booking_request"
            )
        elif response.status.value == "accepted":
            # Create notification for accepted booking request
            notification_title = f"Booking Request Accepted"
            notification_message = f"Your tutoring request to {current_user.full_name or current_user.email} has been accepted! You can now start chatting."
            if response.tutor_response:
                notification_message += f" Message: {response.tutor_response}"

            notification_obj = notification.create_notification(
                db,
                user_id=booking_req.student_id,
                notification_type=NotificationType.BOOKING_ACCEPTED,
                title=notification_title,
                message=notification_message,
                related_id=booking_req.id,
                related_type="booking_request"
            )

        # Send real-time notification via WebSocket
        if notification_obj:
            connection_manager = get_connection_manager()
            notification_data = {
                "id": notification_obj.id,
                "type": notification_obj.type.value,
                "title": notification_obj.title,
                "message": notification_obj.message,
                "is_read": notification_obj.is_read,
                "created_at": notification_obj.created_at.isoformat(),
                "related_id": notification_obj.related_id,
                "related_type": notification_obj.related_type,
                "chat_room_id": booking_req.chat_room_id if response.status.value == "accepted" else None
            }

            # Send real-time notification to the student
            import asyncio
            try:
                # Create a new event loop if one doesn't exist
                try:
                    loop = asyncio.get_event_loop()
                except RuntimeError:
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)

                # Send the notification
                loop.create_task(
                    connection_manager.send_notification(booking_req.student_id, notification_data)
                )
            except Exception as ws_error:
                # Log WebSocket error but don't fail the response
                import logging
                logger = logging.getLogger(__name__)
                logger.error(f"Failed to send real-time notification: {str(ws_error)}")

    except Exception as e:
        # Log the error but don't fail the response
        import logging
        logger = logging.getLogger(__name__)
        logger.error(f"Failed to create in-app notification: {str(e)}")

    # Send email notification to student about the response
    from app.services.email_service import send_booking_response_notification
    try:
        send_booking_response_notification(
            student_email=booking_req.student.email,
            student_name=booking_req.student.full_name or booking_req.student.email,
            tutor_name=current_user.full_name or current_user.email,
            tutor_email=current_user.email,
            response_status=response.status.value,
            tutor_message=response.tutor_response or "",
            request_id=booking_req.id
        )
    except Exception as e:
        # Log the error but don't fail the response
        import logging
        logger = logging.getLogger(__name__)
        logger.error(f"Failed to send booking response notification email: {str(e)}")
    
    # Get updated request with details
    booking_req_with_details = booking_request.get_with_details(db, id=booking_req.id)
    
    # Convert to response format
    result = {
        **booking_req_with_details.__dict__,
        "student_name": booking_req_with_details.student.full_name,
        "student_email": booking_req_with_details.student.email,
        "tutor_name": booking_req_with_details.tutor.full_name,
        "tutor_email": booking_req_with_details.tutor.email,
        "course_name": booking_req_with_details.course.name if booking_req_with_details.course else None
    }
    
    return result


@router.get("/tutor/pending-count", response_model=dict)
def get_pending_requests_count(
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_tutor_user),
) -> Any:
    """
    Get count of pending booking requests for the current tutor.
    """
    count = booking_request.get_pending_count_for_tutor(db, tutor_id=current_user.id)
    return {"pending_count": count}
