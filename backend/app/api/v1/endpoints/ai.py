from typing import Any, List

from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session

from app import schemas
from app.models.user import User
from app.models.question import Question
from app.api import deps
from app.services.ai_service import ai_service

router = APIRouter()


@router.post("/generate-questions", response_model=List[schemas.Question])
async def generate_questions(
    *,
    request: schemas.AIQuestionRequest,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_tutor_user),
) -> Any:
    """
    Generate questions using AI based on the provided parameters.
    """
    try:
        # Generate questions using the AI service
        questions = await ai_service.generate_questions(
            topic=request.topic,
            question_type=request.question_type,
            difficulty=request.difficulty,
            num_questions=request.num_questions
        )

        # Convert the generated questions to database models
        db_questions = []
        for q in questions:
            db_question = Question(
                content=q.content,
                question_type=q.question_type,
                difficulty=q.difficulty,
                options=q.options,
                answer=q.answer,
                explanation=q.explanation,
                course_id=request.course_id,
                created_by_id=current_user.id
            )
            db.add(db_question)
            db_questions.append(db_question)

        db.commit()
        for q in db_questions:
            db.refresh(q)

        return db_questions
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to generate questions: {str(e)}"
        )
