import logging
from typing import List, Optional, Dict, Any

from fastapi import APIRouter, Depends, HTTPException, UploadFile, File, Form, Query, status
from fastapi.responses import Response, StreamingResponse
from sqlalchemy.orm import Session

from app.services.gemini_content_generator import gemini_content_generator
from app.services.gemini_files_service import GeminiFilesService
from app.services.usage_limit_service import usage_limit_service
from app.services.ai_assistant_service import ai_assistant_service
from app.api import deps
from app import crud
from app.models.user import User, UserRole
from app.models.gemini_file import GeminiFile
from app.models.student_usage_limit import UsageType
from app.schemas.gemini_file import GeminiFileListResponse
from app.schemas.usage_limit import UsageLimitError, UsageStatsResponse
from app.schemas.ai_assistant import AIAssistantStreamEvent
from app.tasks.monthly_reset import run_monthly_maintenance

logger = logging.getLogger(__name__)

router = APIRouter()

# Initialize services
gemini_files_service = GeminiFilesService()


@router.get("/gemini/files", response_model=GeminiFileListResponse)
def get_uploaded_files(
    skip: int = 0,
    limit: int = 100,
    course_name: Optional[str] = Query(None),
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user)
):
    """
    Get list of uploaded files for the current student.

    - **skip**: Number of items to skip
    - **limit**: Maximum number of items to return
    - **course_name**: Optional filter by course name
    """
    # Check if user is a student
    if current_user.role != UserRole.STUDENT:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only students can access uploaded files"
        )

    try:
        if course_name:
            files = crud.gemini_file.get_by_course_name(
                db, student_id=current_user.id, course_name=course_name, skip=skip, limit=limit
            )
        else:
            files = crud.gemini_file.get_by_student(
                db, student_id=current_user.id, skip=skip, limit=limit
            )

        return GeminiFileListResponse(
            files=files,
            total_count=len(files)
        )

    except Exception as e:
        logger.error(f"Error fetching uploaded files: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to fetch uploaded files"
        )


@router.post("/gemini/upload-pdf", response_model=Dict[str, Any])
async def upload_pdf_to_gemini(
    file: UploadFile = File(...),
    course_name: Optional[str] = Form(None),
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user)
):
    """
    Upload PDF to Gemini Files API.

    - **file**: PDF file to upload (max 30MB)
    - **course_name**: Optional course name for context
    """
    # Check if user is a student
    if current_user.role != UserRole.STUDENT:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only students can upload PDFs"
        )

    # Check usage limit for PDF uploads
    usage_check = usage_limit_service.check_usage_limit(
        db, current_user.id, UsageType.PDF_UPLOAD, current_user
    )

    if not usage_check["allowed"]:
        error_response = usage_limit_service.create_usage_limit_error_response(usage_check)
        raise HTTPException(
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            detail=error_response
        )

    # Validate file type
    if not file.filename.lower().endswith('.pdf'):
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Only PDF files are allowed"
        )

    try:
        # Reset file position after validation
        await file.seek(0)

        # Check for duplicate uploads (same filename, same user, same file size)
        # This prevents true duplicates while allowing re-uploads of different files with same name
        from datetime import datetime, timedelta

        # Import streaming file handler
        from app.utils.streaming_file_handler import streaming_file_handler

        # First check for exact duplicates (same filename and file size)
        file_content = await streaming_file_handler.get_file_content_safe(file)
        file_size = len(file_content)

        existing_file = db.query(GeminiFile).filter(
            GeminiFile.student_id == current_user.id,
            GeminiFile.original_filename == file.filename,
            GeminiFile.file_size == file_size,
            GeminiFile.is_deleted == False
        ).first()

        if existing_file:
            logger.warning(f"Duplicate upload attempt blocked for student {current_user.id}: {file.filename} (size: {file_size})")
            return {
                "gemini_file_id": existing_file.gemini_file_id,
                "original_filename": existing_file.original_filename,
                "file_size": existing_file.file_size,
                "upload_time": existing_file.upload_time.isoformat(),
                "auto_delete_time": existing_file.auto_delete_time.isoformat(),
                "course_name": existing_file.course_name,
                "message": "File already uploaded"
            }

        # Upload to Gemini Files API using the files service
        # Pass the file content we already read to avoid re-reading
        upload_response = await gemini_files_service.upload_pdf_to_gemini(
            file=file,
            student_id=current_user.id,
            db=db,
            course_name=course_name,
            file_content=file_content
        )

        logger.info(f"PDF uploaded to Gemini for student {current_user.id}: {file.filename}")

        # Clean up file content from memory
        del file_content

        # Increment usage count for successful upload
        usage_limit_service.increment_usage(db, current_user.id, UsageType.PDF_UPLOAD)

        return {
            "gemini_file_id": upload_response["gemini_file_id"],
            "original_filename": upload_response["original_filename"],
            "file_size": upload_response["file_size"],
            "upload_time": upload_response["upload_time"].isoformat(),
            "auto_delete_time": upload_response["auto_delete_time"].isoformat(),
            "course_name": upload_response.get("course_name"),
            "message": "Upload successful"
        }

    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Error uploading PDF to Gemini: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to upload PDF"
        )





@router.get("/mcq/my-questions", response_model=List[Dict[str, Any]])
def get_my_generated_questions(
    skip: int = 0,
    limit: int = 100,
    course_name: Optional[str] = Query(None),
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user)
):
    """
    Get all MCQs generated by the current student (from Gemini Files API).
    
    - **course_name**: Optional filter by course name
    """
    # Check if user is a student
    if current_user.role != UserRole.STUDENT:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only students can access their generated questions"
        )

    # Get questions created by this student, ordered by newest first
    from app.models.question import Question, QuestionType

    query = db.query(Question).filter(
        Question.created_by_id == current_user.id,
        Question.question_type == QuestionType.MULTIPLE_CHOICE,
        Question.is_active == True
    )

    if course_name:
        query = query.filter(Question.course_name == course_name)

    questions = query.order_by(Question.created_at.desc()).offset(skip).limit(limit).all()
    
    # Convert to dict format for response
    result = []
    for q in questions:
        result.append({
            "id": q.id,
            "content": q.content,
            "question_type": q.question_type.value,
            "difficulty": q.difficulty.value,
            "topic": q.topic,
            "options": q.options,
            "answer": q.answer,
            "explanation": q.explanation,
            "course_name": q.course_name,
            "created_at": q.created_at.isoformat(),
            "updated_at": q.updated_at.isoformat()
        })
    
    return result





@router.post("/gemini/generate-mcqs", response_model=Dict[str, Any])
async def generate_mcqs_from_gemini_file(
    gemini_file_id: str = Form(...),
    question_count: int = Form(10),
    course_name: Optional[str] = Form(None),
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user)
):
    """
    Generate MCQs from a Gemini file using structured output and save them to database.

    - **gemini_file_id**: Gemini file ID from upload
    - **question_count**: Number of questions to generate (1-30)
    - **course_name**: Optional course context
    """
    # Check if user is a student
    if current_user.role != UserRole.STUDENT:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only students can generate MCQs"
        )

    # Check usage limit for MCQ generation
    usage_check = usage_limit_service.check_usage_limit(
        db, current_user.id, UsageType.MCQ_GENERATION, current_user
    )

    if not usage_check["allowed"]:
        error_response = usage_limit_service.create_usage_limit_error_response(usage_check)
        raise HTTPException(
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            detail=error_response
        )

    # Validate question count (maximum 30 as per user requirements)
    if question_count < 1 or question_count > 30:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Question count must be between 1 and 30"
        )

    try:
        # Get the file info to use filename as course name if not provided
        from app.crud.crud_gemini_file import gemini_file
        gemini_file_obj = gemini_file.get_by_gemini_file_id(db, gemini_file_id=gemini_file_id)

        if not gemini_file_obj:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="File not found or has been deleted"
            )

        # Use filename as course name if no course name provided
        effective_course_name = course_name
        if not effective_course_name:
            # Remove file extension from filename for cleaner course name
            filename = gemini_file_obj.original_filename
            if '.' in filename:
                effective_course_name = filename.rsplit('.', 1)[0]
            else:
                effective_course_name = filename

        # Generate MCQs using Gemini structured output
        mcq_response = await gemini_content_generator.generate_mcqs(
            gemini_file_id=gemini_file_id,
            question_count=question_count,
            student_id=current_user.id,
            course_name=effective_course_name,
            db=db
        )

        # Convert Gemini MCQs to Question model format and save to database
        from app.models.question import Question, QuestionType, DifficultyLevel

        question_ids = []

        for gemini_mcq in mcq_response.questions:
            # Convert options from Gemini format to Question model format
            options_dict = {}
            for i, option in enumerate(gemini_mcq.options):
                option_letter = chr(65 + i)  # A, B, C, D
                options_dict[option_letter] = option.option_text

            # Map difficulty from Gemini format to Question model format
            difficulty_mapping = {
                "easy": DifficultyLevel.EASY,
                "medium": DifficultyLevel.MEDIUM,
                "hard": DifficultyLevel.HARD
            }
            difficulty = difficulty_mapping.get(gemini_mcq.difficulty.value.lower(), DifficultyLevel.MEDIUM)

            # Find the correct answer letter
            correct_answer_letter = "A"
            for i, option in enumerate(gemini_mcq.options):
                if option.is_correct:
                    correct_answer_letter = chr(65 + i)
                    break

            # Create Question object
            question = Question(
                content=gemini_mcq.question_text,
                question_type=QuestionType.MULTIPLE_CHOICE,
                difficulty=difficulty,
                topic=gemini_mcq.topic,
                options=options_dict,
                answer=correct_answer_letter,
                explanation=gemini_mcq.explanation,
                course_name=effective_course_name,
                created_by_id=current_user.id,
                is_active=True
            )

            db.add(question)
            db.flush()  # Flush to get the ID
            question_ids.append(question.id)

        db.commit()  # Commit all questions

        logger.info(f"Saved {len(question_ids)} MCQs to database for student {current_user.id}")

        # Increment usage count for successful MCQ generation
        usage_limit_service.increment_usage(db, current_user.id, UsageType.MCQ_GENERATION)

        # Return both the generated MCQs and the saved question IDs
        return {
            "questions": [q.dict() for q in mcq_response.questions],
            "total_count": mcq_response.total_count,
            "source_topics": mcq_response.source_topics,
            "saved_question_ids": question_ids,
            "message": f"Generated and saved {len(question_ids)} MCQs successfully"
        }

    except ValueError as e:
        # ValueError exceptions from the service already have user-friendly messages
        error_message = str(e)
        logger.warning(f"MCQ generation failed with user error: {error_message}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=error_message
        )
    except Exception as e:
        logger.error(f"Error generating MCQs from Gemini file: {str(e)}")
        db.rollback()  # Rollback on error

        # Convert technical errors to user-friendly messages
        error_str = str(e).lower()
        if "timeout" in error_str or "503" in error_str:
            user_message = "The AI service is temporarily busy. Please try again in a few minutes."
        elif "429" in error_str or "quota" in error_str or "rate limit" in error_str:
            user_message = "Too many requests at the moment. Please wait a moment and try again."
        elif "network" in error_str or "connection" in error_str:
            user_message = "Network connection issue. Please check your internet connection and try again."
        elif "file not found" in error_str or "404" in error_str:
            user_message = "The uploaded file could not be found. Please upload the file again."
        elif "permission" in error_str or "403" in error_str:
            user_message = "Access denied. Please make sure you have permission to access this file."
        else:
            user_message = "Unable to generate MCQs at this time. Please try again later or contact support if the problem persists."

        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=user_message
        )


@router.post("/gemini/generate-flashcards", response_model=Dict[str, Any])
async def generate_flashcards_from_gemini_file(
    gemini_file_id: str = Form(...),
    card_count: int = Form(10),
    course_name: Optional[str] = Form(None),
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user)
):
    """
    Generate flashcards from a Gemini file using structured output and save them to database.

    - **gemini_file_id**: Gemini file ID from upload
    - **card_count**: Number of flashcards to generate (1-30)
    - **course_name**: Optional course context
    """
    # Check if user is a student
    if current_user.role != UserRole.STUDENT:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only students can generate flashcards"
        )

    # Check usage limit for flashcard generation
    usage_check = usage_limit_service.check_usage_limit(
        db, current_user.id, UsageType.FLASHCARD_GENERATION, current_user
    )

    if not usage_check["allowed"]:
        error_response = usage_limit_service.create_usage_limit_error_response(usage_check)
        raise HTTPException(
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            detail=error_response
        )

    # Validate card count
    if card_count < 1 or card_count > 30:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Card count must be between 1 and 30"
        )

    try:
        # Get the Gemini file to check ownership and get course name
        gemini_file_record = crud.gemini_file.get_by_gemini_file_id(db, gemini_file_id=gemini_file_id)
        if not gemini_file_record:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="File not found"
            )

        # Check if the file belongs to the current user
        if gemini_file_record.student_id != current_user.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="You can only generate flashcards from your own files"
            )

        # Use course name from file if not provided
        effective_course_name = course_name or gemini_file_record.course_name or gemini_file_record.original_filename

        # Generate flashcards using Gemini structured output
        flashcard_response = await gemini_content_generator.generate_flashcards(
            gemini_file_id=gemini_file_id,
            card_count=card_count,
            student_id=current_user.id,
            course_name=effective_course_name,
            db=db
        )

        # Convert Gemini flashcards to GeneratedFlashcard model format and save to database
        from app.models.generated_flashcard import GeneratedFlashcard
        from app.schemas.generated_flashcard import GeneratedFlashcardCreate

        flashcard_ids = []

        for gemini_flashcard in flashcard_response.flashcards:
            # Create GeneratedFlashcard object
            flashcard_create = GeneratedFlashcardCreate(
                front_content=gemini_flashcard.front_content,
                back_content=gemini_flashcard.back_content,
                topic=gemini_flashcard.topic,
                difficulty=gemini_flashcard.difficulty.value.lower() if hasattr(gemini_flashcard.difficulty, 'value') else gemini_flashcard.difficulty.lower(),
                card_type=gemini_flashcard.card_type,
                course_name=effective_course_name,
                gemini_file_id=gemini_file_id,
                student_id=current_user.id,
                is_active=True
            )

            flashcard = crud.generated_flashcard.create(db, obj_in=flashcard_create)
            flashcard_ids.append(flashcard.id)

        logger.info(f"Saved {len(flashcard_ids)} flashcards to database for student {current_user.id}")

        # Increment usage count for successful flashcard generation
        usage_limit_service.increment_usage(db, current_user.id, UsageType.FLASHCARD_GENERATION)

        # Return both the generated flashcards and the saved flashcard IDs
        return {
            "flashcards": [f.dict() for f in flashcard_response.flashcards],
            "total_count": flashcard_response.total_count,
            "source_topics": flashcard_response.source_topics,
            "saved_flashcard_ids": flashcard_ids,
            "message": f"Generated and saved {len(flashcard_ids)} flashcards successfully"
        }

    except ValueError as e:
        # ValueError exceptions from the service already have user-friendly messages
        error_message = str(e)
        logger.warning(f"Flashcard generation failed with user error: {error_message}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=error_message
        )
    except Exception as e:
        logger.error(f"Error generating flashcards from Gemini file: {str(e)}")
        db.rollback()  # Rollback on error

        # Convert technical errors to user-friendly messages
        error_str = str(e).lower()
        if "timeout" in error_str or "503" in error_str:
            user_message = "The AI service is temporarily busy. Please try again in a few minutes."
        elif "429" in error_str or "quota" in error_str or "rate limit" in error_str:
            user_message = "Too many requests at the moment. Please wait a moment and try again."
        elif "network" in error_str or "connection" in error_str:
            user_message = "Network connection issue. Please check your internet connection and try again."
        elif "file not found" in error_str or "404" in error_str:
            user_message = "The uploaded file could not be found. Please upload the file again."
        elif "permission" in error_str or "403" in error_str:
            user_message = "Access denied. Please make sure you have permission to access this file."
        else:
            user_message = "Unable to generate flashcards at this time. Please try again later or contact support if the problem persists."

        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=user_message
        )


@router.get("/generated-flashcards", response_model=List[Dict[str, Any]])
async def get_generated_flashcards(
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    course_name: Optional[str] = Query(None),
    difficulty: Optional[str] = Query(None),
    flashcard_ids: Optional[str] = Query(None, description="Comma-separated list of flashcard IDs")
):
    """
    Get generated flashcards for the current student.

    - **skip**: Number of flashcards to skip
    - **limit**: Maximum number of flashcards to return
    - **course_name**: Filter by course name
    - **difficulty**: Filter by difficulty (easy, medium, hard)
    - **flashcard_ids**: Specific flashcard IDs to retrieve
    """
    # Check if user is a student
    if current_user.role != UserRole.STUDENT:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only students can access generated flashcards"
        )

    try:
        if flashcard_ids:
            # Get specific flashcards by IDs, ordered by newest first
            id_list = [int(id.strip()) for id in flashcard_ids.split(',') if id.strip().isdigit()]
            from app.models.generated_flashcard import GeneratedFlashcard
            flashcards = db.query(GeneratedFlashcard).filter(
                GeneratedFlashcard.id.in_(id_list),
                GeneratedFlashcard.student_id == current_user.id,
                GeneratedFlashcard.is_active == True
            ).order_by(GeneratedFlashcard.created_at.desc()).all()
        elif course_name:
            flashcards = crud.generated_flashcard.get_by_course_name(
                db, student_id=current_user.id, course_name=course_name, skip=skip, limit=limit
            )
        elif difficulty:
            flashcards = crud.generated_flashcard.get_by_difficulty(
                db, student_id=current_user.id, difficulty=difficulty, skip=skip, limit=limit
            )
        else:
            flashcards = crud.generated_flashcard.get_by_student(
                db, student_id=current_user.id, skip=skip, limit=limit
            )

        # Convert to dict format for response
        flashcard_dicts = []
        for flashcard in flashcards:
            flashcard_dicts.append({
                "id": flashcard.id,
                "front_content": flashcard.front_content,
                "back_content": flashcard.back_content,
                "topic": flashcard.topic,
                "difficulty": flashcard.difficulty,
                "card_type": flashcard.card_type,
                "course_name": flashcard.course_name,
                "created_at": flashcard.created_at.isoformat(),
                "updated_at": flashcard.updated_at.isoformat()
            })

        return flashcard_dicts

    except Exception as e:
        logger.error(f"Error retrieving generated flashcards: {str(e)}")
        # Convert technical errors to user-friendly messages
        error_str = str(e).lower()
        if "timeout" in error_str:
            user_message = "Request timed out. Please try again."
        elif "connection" in error_str or "network" in error_str:
            user_message = "Network connection issue. Please check your internet connection and try again."
        elif "database" in error_str or "sql" in error_str:
            user_message = "Database temporarily unavailable. Please try again in a few minutes."
        else:
            user_message = "Unable to load flashcards at this time. Please try again later."

        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=user_message
        )


@router.delete("/gemini/files/{file_id}")
async def delete_gemini_file(
    file_id: str,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user)
):
    """
    Delete a file from Gemini Files API.

    - **file_id**: Gemini file ID to delete
    """
    # Check if user is a student
    if current_user.role != UserRole.STUDENT:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only students can delete files"
        )

    try:
        # Ensure file_id has the correct format for Gemini API
        if not file_id.startswith('files/'):
            file_id = f"files/{file_id}"

        # Delete from Gemini Files API and update database
        success = await gemini_files_service.delete_file_from_gemini(file_id, db)

        if not success:
            raise HTTPException(
                status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
                detail="Failed to delete file from Gemini"
            )

        logger.info(f"Gemini file deleted by student {current_user.id}: {file_id}")

        return {
            "message": "File deleted successfully",
            "gemini_file_id": file_id
        }

    except ValueError as e:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(e)
        )
    except Exception as e:
        logger.error(f"Error deleting Gemini file: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to delete file"
        )


@router.post("/gemini/cleanup-expired")
async def cleanup_expired_gemini_files(
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user)
):
    """
    Trigger cleanup of expired Gemini files.
    """
    # Check if user is a student
    if current_user.role != UserRole.STUDENT:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only students can trigger cleanup"
        )

    try:
        # Cleanup expired files using the service directly
        cleanup_result = await gemini_files_service.cleanup_expired_files(db)

        logger.info(f"Gemini file cleanup triggered by student {current_user.id}")

        return {
            "message": "Cleanup completed",
            "deleted_count": cleanup_result.get("deleted_count", 0),
            "failed_count": cleanup_result.get("failed_count", 0),
            "total_processed": cleanup_result.get("total_processed", 0)
        }

    except Exception as e:
        logger.error(f"Error during Gemini file cleanup: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to cleanup files"
        )


@router.post("/gemini/admin/cleanup-all")
async def admin_cleanup_all_files(
    current_user: User = Depends(deps.get_current_admin_user)
):
    """
    Admin endpoint to trigger comprehensive cleanup of expired files and duplicates.
    """
    try:
        from app.tasks import cleanup_expired_gemini_files_task, cleanup_duplicate_files_task

        # Trigger both cleanup tasks
        expired_task = cleanup_expired_gemini_files_task.delay()
        duplicate_task = cleanup_duplicate_files_task.delay()

        logger.info(f"Admin cleanup tasks triggered by user {current_user.id}")

        return {
            "message": "Cleanup tasks triggered successfully",
            "expired_files_task_id": expired_task.id,
            "duplicate_files_task_id": duplicate_task.id
        }

    except Exception as e:
        logger.error(f"Error triggering admin cleanup: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to trigger admin cleanup"
        )


@router.post("/gemini/generate-note-explanation", response_model=Dict[str, Any])
async def generate_note_explanation_from_gemini_file(
    gemini_file_id: str = Form(...),
    course_name: Optional[str] = Form(None),
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user)
):
    """
    Generate comprehensive note explanation from a Gemini file using structured output and save to database.

    - **gemini_file_id**: Gemini file ID from upload
    - **course_name**: Optional course context
    """
    # Check if user is a student
    if current_user.role != UserRole.STUDENT:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only students can generate note explanations"
        )

    # Check usage limit for summary generation
    usage_check = usage_limit_service.check_usage_limit(
        db, current_user.id, UsageType.SUMMARY_GENERATION, current_user
    )

    if not usage_check["allowed"]:
        error_response = usage_limit_service.create_usage_limit_error_response(usage_check)
        raise HTTPException(
            status_code=status.HTTP_429_TOO_MANY_REQUESTS,
            detail=error_response
        )

    try:
        # Check if file exists and belongs to user
        gemini_file_record = crud.gemini_file.get_by_gemini_file_id(
            db, gemini_file_id=gemini_file_id
        )

        # Verify the file belongs to the current user
        if gemini_file_record and gemini_file_record.student_id != current_user.id:
            gemini_file_record = None

        if not gemini_file_record:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="File not found or access denied"
            )

        # Use course name from file if not provided
        effective_course_name = course_name or gemini_file_record.course_name or gemini_file_record.original_filename

        # Generate note explanation using Gemini structured output
        explanation_response = await gemini_content_generator.generate_note_explanation(
            gemini_file_id=gemini_file_id,
            student_id=current_user.id,
            course_name=effective_course_name,
            db=db
        )

        # Convert Gemini explanation to GeneratedNoteExplanation model format and save to database
        from app.models.generated_note_explanation import GeneratedNoteExplanation
        from app.schemas.generated_note_explanation import GeneratedNoteExplanationCreate

        # Create GeneratedNoteExplanation object
        explanation_create = GeneratedNoteExplanationCreate(
            title=explanation_response.explanation.title,
            overview=explanation_response.explanation.overview,
            detailed_explanation=explanation_response.explanation.detailed_explanation,
            comprehensive_summary=explanation_response.explanation.comprehensive_summary,
            key_concepts=explanation_response.explanation.key_concepts,
            main_topics=explanation_response.explanation.main_topics,
            word_count=explanation_response.explanation.word_count,
            course_name=effective_course_name,
            gemini_file_id=gemini_file_id,
            original_filename=gemini_file_record.original_filename,
            student_id=current_user.id,
            is_active=True
        )

        explanation = crud.generated_note_explanation.create(db, obj_in=explanation_create)

        # Increment usage count for successful summary generation
        usage_limit_service.increment_usage(db, current_user.id, UsageType.SUMMARY_GENERATION)

        return {
            "explanation": {
                "title": explanation_response.explanation.title,
                "overview": explanation_response.explanation.overview,
                "detailed_explanation": explanation_response.explanation.detailed_explanation,
                "comprehensive_summary": explanation_response.explanation.comprehensive_summary,
                "key_concepts": explanation_response.explanation.key_concepts,
                "main_topics": explanation_response.explanation.main_topics,
                "word_count": explanation_response.explanation.word_count
            },
            "source_info": explanation_response.source_info,
            "saved_explanation_id": explanation.id,
            "message": "Note explanation generated successfully"
        }

    except HTTPException:
        raise
    except ValueError as e:
        # ValueError exceptions from the service already have user-friendly messages
        error_message = str(e)
        logger.warning(f"Note explanation generation failed with user error: {error_message}")
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=error_message
        )
    except Exception as e:
        logger.error(f"Error generating note explanation from Gemini file: {str(e)}")
        db.rollback()  # Rollback on error

        # Convert technical errors to user-friendly messages
        error_str = str(e).lower()
        if "timeout" in error_str or "503" in error_str:
            user_message = "The AI service is temporarily busy. Please try again in a few minutes."
        elif "429" in error_str or "quota" in error_str or "rate limit" in error_str:
            user_message = "Too many requests at the moment. Please wait a moment and try again."
        elif "network" in error_str or "connection" in error_str:
            user_message = "Network connection issue. Please check your internet connection and try again."
        elif "file not found" in error_str or "404" in error_str:
            user_message = "The uploaded file could not be found. Please upload the file again."
        elif "invalid" in error_str and "escape" in error_str:
            user_message = "The document contains formatting that cannot be processed. Please try with a simpler document."
        elif "permission" in error_str or "403" in error_str:
            user_message = "Access denied. Please make sure you have permission to access this file."
        else:
            user_message = "Unable to generate note explanation at this time. Please try again later or contact support if the problem persists."

        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=user_message
        )


@router.get("/generated-note-explanations", response_model=List[Dict[str, Any]])
async def get_generated_note_explanations(
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
    skip: int = Query(0, ge=0),
    limit: int = Query(100, ge=1, le=1000),
    course_name: Optional[str] = Query(None),
    explanation_id: Optional[int] = Query(None, description="Specific explanation ID to retrieve")
):
    """
    Get generated note explanations for the current student.

    - **skip**: Number of explanations to skip
    - **limit**: Maximum number of explanations to return
    - **course_name**: Filter by course name
    - **explanation_id**: Specific explanation ID to retrieve
    """
    # Check if user is a student
    if current_user.role != UserRole.STUDENT:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only students can access generated note explanations"
        )

    try:
        if explanation_id:
            # Get specific explanation by ID
            from app.models.generated_note_explanation import GeneratedNoteExplanation
            explanation = db.query(GeneratedNoteExplanation).filter(
                GeneratedNoteExplanation.id == explanation_id,
                GeneratedNoteExplanation.student_id == current_user.id,
                GeneratedNoteExplanation.is_active == True
            ).first()

            if not explanation:
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Note explanation not found"
                )

            explanations = [explanation]
        elif course_name:
            explanations = crud.generated_note_explanation.get_by_student_and_course(
                db, student_id=current_user.id, course_name=course_name, skip=skip, limit=limit
            )
        else:
            explanations = crud.generated_note_explanation.get_by_student(
                db, student_id=current_user.id, skip=skip, limit=limit
            )

        # Convert to response format
        result = []
        for explanation in explanations:
            result.append({
                "id": explanation.id,
                "title": explanation.title,
                "overview": explanation.overview,
                "detailed_explanation": explanation.detailed_explanation,
                "comprehensive_summary": explanation.comprehensive_summary,
                "key_concepts": explanation.key_concepts,
                "main_topics": explanation.main_topics,
                "word_count": explanation.word_count,
                "course_name": explanation.course_name,
                "original_filename": explanation.original_filename,
                "gemini_file_id": explanation.gemini_file_id,
                "created_at": explanation.created_at.isoformat(),
                "updated_at": explanation.updated_at.isoformat()
            })

        return result

    except HTTPException:
        raise
    except Exception as e:
        logger.error(f"Error retrieving generated note explanations: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve note explanations"
        )


@router.get("/usage-stats", response_model=UsageStatsResponse)
def get_usage_statistics(
    month: Optional[str] = Query(None, description="Month in YYYY-MM format (defaults to current month)"),
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user)
):
    """
    Get usage statistics for the current student.

    - **month**: Optional month in YYYY-MM format (defaults to current month)
    """
    # Check if user is a student
    if current_user.role != UserRole.STUDENT:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only students can access usage statistics"
        )

    try:
        # Get usage statistics for all usage types
        usage_stats = usage_limit_service.get_all_usage_stats(
            db, current_user.id, month
        )

        effective_month = month or usage_limit_service.get_current_month()

        return UsageStatsResponse(
            student_id=current_user.id,
            month=effective_month,
            usage_stats=usage_stats
        )

    except Exception as e:
        logger.error(f"Error retrieving usage statistics: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to retrieve usage statistics"
        )


@router.post("/admin/reset-usage-limits")
def reset_usage_limits(
    student_id: Optional[int] = Query(None, description="Specific student ID to reset (admin only)"),
    usage_type: Optional[str] = Query(None, description="Specific usage type to reset"),
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_admin_user)
):
    """
    Reset usage limits (admin only).

    - **student_id**: Optional specific student ID to reset
    - **usage_type**: Optional specific usage type to reset
    """
    try:
        # Convert string usage_type to enum if provided
        usage_type_enum = None
        if usage_type:
            try:
                usage_type_enum = UsageType(usage_type)
            except ValueError:
                raise HTTPException(
                    status_code=status.HTTP_400_BAD_REQUEST,
                    detail=f"Invalid usage type: {usage_type}. Valid types: {[t.value for t in UsageType]}"
                )

        # Reset usage limits
        reset_count = usage_limit_service.reset_monthly_usage(
            db, student_id=student_id, usage_type=usage_type_enum
        )

        return {
            "message": f"Reset usage limits for {reset_count} records",
            "reset_count": reset_count,
            "student_id": student_id,
            "usage_type": usage_type
        }

    except Exception as e:
        logger.error(f"Error resetting usage limits: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to reset usage limits"
        )


@router.post("/admin/monthly-maintenance")
def run_monthly_maintenance_task(
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_admin_user)
):
    """
    Run monthly maintenance tasks (admin only).
    This includes resetting usage limits and cleaning up old records.
    """
    try:
        logger.info(f"Monthly maintenance triggered by admin user: {current_user.email}")

        # Run the monthly maintenance
        result = run_monthly_maintenance()

        return {
            "message": "Monthly maintenance completed",
            "result": result
        }

    except Exception as e:
        logger.error(f"Error running monthly maintenance: {str(e)}")
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to run monthly maintenance"
        )


@router.post("/gemini/stream/document-chat")
async def stream_document_chat(
    gemini_file_id: str = Form(...),
    query: str = Form(...),
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user)
) -> StreamingResponse:
    """
    Stream chat responses about a specific document using Gemini Files API.

    - **gemini_file_id**: Gemini file ID to chat about
    - **query**: The question to ask about the document
    """
    # Check if user is a student
    if current_user.role != UserRole.STUDENT:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only students can chat with documents"
        )

    async def event_generator():
        try:
            async for event in ai_assistant_service.stream_document_chat(
                gemini_file_id=gemini_file_id,
                query=query,
                user_id=current_user.id,
                db=db
            ):
                yield f"data: {event.model_dump_json()}\n\n"
        except Exception as e:
            error_event = AIAssistantStreamEvent(
                type="error",
                error=f"Failed to stream document chat: {str(e)}"
            )
            yield f"data: {error_event.model_dump_json()}\n\n"

    return StreamingResponse(
        event_generator(),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
        }
    )
