from typing import Any, List

from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session

from app import schemas
from app.api import deps
from app.models.user import User
from app.crud.crud_notification import notification
from app.schemas.notification import (
    Notification, NotificationCreate, NotificationUpdate,
    NotificationMarkRead, NotificationBulkAction
)

router = APIRouter()


@router.get("/", response_model=List[Notification])
def get_notifications(
    db: Session = Depends(deps.get_db),
    skip: int = 0,
    limit: int = 100,
    unread_only: bool = Query(False, description="Get only unread notifications"),
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Retrieve notifications for the current user.
    """
    notifications = notification.get_user_notifications(
        db, user_id=current_user.id, skip=skip, limit=limit, unread_only=unread_only
    )

    # Enhance notifications with chat room information for booking-related notifications
    enhanced_notifications = []
    for notif in notifications:
        chat_room_id = None

        # If this is a booking acceptance notification, get the chat room ID
        if (notif.type.value == "BOOKING_ACCEPTED" and
            notif.related_type == "booking_request" and
            notif.related_id):

            from app.crud.crud_booking_request import booking_request
            booking_req = booking_request.get(db, id=notif.related_id)
            if booking_req and booking_req.chat_room_id:
                chat_room_id = booking_req.chat_room_id

        # Create enhanced notification response
        enhanced_notif = Notification.from_db_with_chat_room(notif, chat_room_id)
        enhanced_notifications.append(enhanced_notif)

    return enhanced_notifications


@router.get("/unread-count", response_model=dict)
def get_unread_count(
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Get count of unread notifications for the current user.
    """
    count = notification.get_unread_count(db, user_id=current_user.id)
    return {"unread_count": count}


@router.post("/mark-read/{notification_id}", response_model=Notification)
def mark_notification_as_read(
    *,
    db: Session = Depends(deps.get_db),
    notification_id: int,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Mark a specific notification as read.
    """
    updated_notification = notification.mark_as_read(
        db, notification_id=notification_id, user_id=current_user.id
    )
    
    if not updated_notification:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Notification not found"
        )
    
    return updated_notification


@router.post("/mark-all-read", response_model=dict)
def mark_all_notifications_as_read(
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Mark all notifications as read for the current user.
    """
    updated_count = notification.mark_all_as_read(db, user_id=current_user.id)
    return {"updated_count": updated_count}


@router.delete("/{notification_id}", response_model=dict)
def delete_notification(
    *,
    db: Session = Depends(deps.get_db),
    notification_id: int,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Delete a specific notification.
    """
    deleted = notification.delete_notification(
        db, notification_id=notification_id, user_id=current_user.id
    )
    
    if not deleted:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Notification not found"
        )
    
    return {"message": "Notification deleted successfully"}


@router.post("/bulk-action", response_model=dict)
def bulk_notification_action(
    *,
    db: Session = Depends(deps.get_db),
    action_data: NotificationBulkAction,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Perform bulk actions on notifications.
    """
    if action_data.action == "mark_all_read":
        updated_count = notification.mark_all_as_read(db, user_id=current_user.id)
        return {"message": f"Marked {updated_count} notifications as read"}
    
    elif action_data.action == "delete_old":
        days_old = action_data.days_old or 30
        deleted_count = notification.delete_old_notifications(
            db, user_id=current_user.id, days_old=days_old
        )
        return {"message": f"Deleted {deleted_count} old notifications"}
    
    else:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid action. Supported actions: mark_all_read, delete_old"
        )


# Admin-only endpoint for creating notifications
@router.post("/", response_model=Notification)
def create_notification(
    *,
    db: Session = Depends(deps.get_db),
    notification_in: NotificationCreate,
    current_user: User = Depends(deps.get_current_admin_user),
) -> Any:
    """
    Create a new notification (admin only).
    """
    new_notification = notification.create_notification(
        db,
        user_id=notification_in.user_id,
        notification_type=notification_in.type,
        title=notification_in.title,
        message=notification_in.message,
        related_id=notification_in.related_id,
        related_type=notification_in.related_type
    )
    return new_notification
