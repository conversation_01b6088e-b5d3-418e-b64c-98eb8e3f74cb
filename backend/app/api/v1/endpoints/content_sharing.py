"""
API endpoints for the simplified content sharing system.
"""

from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, status
from sqlalchemy.orm import Session

from app.api import deps
from app.crud.crud_content_access import content_access, share_invitation, public_share
from app.models.content_access import ContentType, AccessType
from app.models.user import User
from app.models.question import Question
from app.models.generated_flashcard import GeneratedFlashcard
from app.models.generated_note_explanation import GeneratedNoteExplanation
from app.schemas.content_access import (
    ShareContentRequest, ShareContentResponse,
    AcceptInvitationRequest, AcceptInvitationResponse,
    AccessPublicShareRequest, AccessPublicShareResponse,
    SharedContentItem, MySharedContentSummary, SharedWithMeSummary
)
from app.services.email_service import send_content_share_invitation
from app.core.config import settings

router = APIRouter()


def get_content_data(db: Session, content_type: ContentType, content_id: int) -> Optional[Dict[str, Any]]:
    """Get the actual content data based on type and ID"""
    if content_type == ContentType.MCQ:
        content = db.query(Question).filter(Question.id == content_id).first()
        if content:
            return {
                "id": content.id,
                "content": content.content,
                "question_type": content.question_type,
                "difficulty": content.difficulty,
                "topic": content.topic,
                "options": content.options,
                "answer": content.answer,
                "explanation": content.explanation,
                "course_name": content.course_name,
                "created_by_id": content.created_by_id
            }
    elif content_type == ContentType.FLASHCARD:
        content = db.query(GeneratedFlashcard).filter(GeneratedFlashcard.id == content_id).first()
        if content:
            return {
                "id": content.id,
                "front_content": content.front_content,
                "back_content": content.back_content,
                "topic": content.topic,
                "difficulty": content.difficulty,
                "card_type": content.card_type,
                "course_name": content.course_name,
                "student_id": content.student_id
            }
    elif content_type == ContentType.SUMMARY:
        content = db.query(GeneratedNoteExplanation).filter(GeneratedNoteExplanation.id == content_id).first()
        if content:
            return {
                "id": content.id,
                "title": content.title,
                "overview": content.overview,
                "detailed_explanation": content.detailed_explanation,
                "comprehensive_summary": content.comprehensive_summary,
                "key_concepts": content.key_concepts,
                "main_topics": content.main_topics,
                "course_name": content.course_name,
                "student_id": content.student_id
            }
    return None


def get_content_owner_id(db: Session, content_type: ContentType, content_id: int) -> Optional[int]:
    """Get the owner ID of the content"""
    if content_type == ContentType.MCQ:
        content = db.query(Question).filter(Question.id == content_id).first()
        return content.created_by_id if content else None
    elif content_type == ContentType.FLASHCARD:
        content = db.query(GeneratedFlashcard).filter(GeneratedFlashcard.id == content_id).first()
        return content.student_id if content else None
    elif content_type == ContentType.SUMMARY:
        content = db.query(GeneratedNoteExplanation).filter(GeneratedNoteExplanation.id == content_id).first()
        return content.student_id if content else None
    return None


@router.post("/share", response_model=ShareContentResponse)
def share_content(
    *,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
    request: ShareContentRequest
):
    """Share content with other users via email invitations and/or public link"""
    
    # Verify the user owns this content
    owner_id = get_content_owner_id(db, request.content_type, request.content_id)
    if not owner_id or owner_id != current_user.id:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="You can only share content that you own"
        )
    
    # Determine which content IDs to share
    content_ids_to_share = []
    if request.content_ids:
        # Multiple content IDs provided
        content_ids_to_share = request.content_ids
        print(f"DEBUG: Sharing multiple content IDs: {content_ids_to_share}")
    else:
        # Single content ID (backward compatibility)
        content_ids_to_share = [request.content_id]
        print(f"DEBUG: Sharing single content ID: {request.content_id}")

    print(f"DEBUG: Total content IDs to share: {len(content_ids_to_share)}")

    # Verify all content exists and user owns it
    for content_id in content_ids_to_share:
        content_data = get_content_data(db, request.content_type, content_id)
        if not content_data:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail=f"Content with ID {content_id} not found"
            )

        # Verify ownership
        owner_id = get_content_owner_id(db, request.content_type, content_id)
        if owner_id != current_user.id:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail=f"You don't own content with ID {content_id}"
            )

    # Validate that at least one sharing method is selected
    invited_emails = request.invited_emails or []
    if not invited_emails and not request.create_public_link:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Please add at least one email address or enable public link sharing"
        )
    
    invitations_sent = []
    invitations_failed = []
    public_share_url = None
    
    # Create a single public share link for all content items
    if request.create_public_link:
        public_share_obj = public_share.create_public_share(
            db,
            content_type=request.content_type,
            content_id=content_ids_to_share[0],  # Primary content ID
            content_ids=content_ids_to_share,  # All content IDs for batch sharing
            shared_by_id=current_user.id,
            share_message=request.share_message
        )
        public_share_url = f"{settings.FRONTEND_URL}/share/{public_share_obj.share_token}"
    
    # Send email invitations (one batch invitation per email)
    for email in invited_emails:
        try:
            # Check if user exists
            existing_user = db.query(User).filter(User.email == email).first()

            # Create a single batch invitation for all content items
            invitation = share_invitation.create_invitation(
                db,
                content_type=request.content_type,
                content_id=content_ids_to_share[0],  # Primary content ID
                content_ids=content_ids_to_share,  # Always pass all content IDs
                invited_email=email,
                shared_by_id=current_user.id,
                share_message=request.share_message
            )

            # Get content data for email (use first item for title/course)
            first_content_data = get_content_data(db, request.content_type, content_ids_to_share[0])
            content_title = first_content_data.get("content", first_content_data.get("title", f"{request.content_type.value} content"))
            course_name = first_content_data.get("course_name", "Unknown Course")

            # Adjust title for batch sharing
            if len(content_ids_to_share) > 1:
                content_title = f"{len(content_ids_to_share)} {request.content_type.value}s from {course_name}"

            send_content_share_invitation(
                invited_email=email,
                sharer_name=current_user.full_name,
                sharer_email=current_user.email,
                content_title=content_title,
                content_type=request.content_type.value,
                course_name=course_name,
                share_message=request.share_message or "",
                invitation_token=invitation.invitation_token,
                is_existing_user=existing_user is not None
            )

            invitations_sent.append(email)

        except Exception as e:
            print(f"Failed to send invitation to {email}: {e}")
            invitations_failed.append(email)
    
    # Generate appropriate success message
    content_count = len(content_ids_to_share)
    content_label = f"{content_count} {request.content_type.value}{'s' if content_count > 1 else ''}"

    if public_share_url and len(invitations_sent) > 0:
        message = f"Successfully shared {content_label} with {len(invitations_sent)} users and created public link"
    elif public_share_url:
        message = f"Successfully created public share link for {content_label}"
    else:
        message = f"Successfully shared {content_label} with {len(invitations_sent)} users"

    return ShareContentResponse(
        content_type=request.content_type,
        content_id=request.content_id,
        content_ids=content_ids_to_share,
        invitations_sent=invitations_sent,
        invitations_failed=invitations_failed,
        public_share_url=public_share_url,
        message=message
    )


@router.post("/accept-invitation", response_model=AcceptInvitationResponse)
def accept_invitation(
    *,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
    request: AcceptInvitationRequest
):
    """Accept a share invitation"""
    
    invitation = share_invitation.accept_invitation(
        db, invitation_token=request.invitation_token, user_id=current_user.id
    )
    
    if not invitation:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Invalid or expired invitation"
        )
    
    # Generate redirect URL based on content type
    redirect_url = None
    if invitation.content_type == ContentType.MCQ:
        redirect_url = f"{settings.FRONTEND_URL}/mcq-practice"
    elif invitation.content_type == ContentType.FLASHCARD:
        redirect_url = f"{settings.FRONTEND_URL}/flashcards"
    elif invitation.content_type == ContentType.SUMMARY:
        redirect_url = f"{settings.FRONTEND_URL}/summaries"
    
    return AcceptInvitationResponse(
        success=True,
        message="Successfully accepted invitation",
        content_type=invitation.content_type,
        content_id=invitation.content_id,
        redirect_url=redirect_url
    )


@router.post("/access-public", response_model=AccessPublicShareResponse)
def access_public_share(
    *,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
    request: AccessPublicShareRequest
):
    """Access content via public share link"""
    
    public_share_obj = public_share.access_public_share(
        db, share_token=request.share_token, user_id=current_user.id
    )
    
    if not public_share_obj:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Invalid or expired share link"
        )
    
    # Parse content_ids if available
    content_ids = None
    if public_share_obj.content_ids:
        import json
        try:
            content_ids = json.loads(public_share_obj.content_ids)
        except (json.JSONDecodeError, TypeError):
            content_ids = [public_share_obj.content_id]

    # Generate redirect URL based on content type
    redirect_url = None
    if public_share_obj.content_type == ContentType.MCQ:
        redirect_url = f"{settings.FRONTEND_URL}/mcq-practice"
    elif public_share_obj.content_type == ContentType.FLASHCARD:
        redirect_url = f"{settings.FRONTEND_URL}/flashcards"
    elif public_share_obj.content_type == ContentType.SUMMARY:
        redirect_url = f"{settings.FRONTEND_URL}/summaries"

    return AccessPublicShareResponse(
        success=True,
        message="Successfully accessed shared content",
        content_type=public_share_obj.content_type,
        content_id=public_share_obj.content_id,
        content_ids=content_ids,
        redirect_url=redirect_url
    )


@router.get("/my-shares", response_model=List[MySharedContentSummary])
def get_my_shared_content(
    *,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
    skip: int = 0,
    limit: int = 100
):
    """Get content that I have shared with others"""

    # Get all public shares by this user
    public_shares = db.query(public_share.model).filter(
        public_share.model.shared_by_id == current_user.id
    ).offset(skip).limit(limit).all()

    summaries = []
    for share in public_shares:
        content_data = get_content_data(db, share.content_type, share.content_id)
        if content_data:
            # Count invitations for this content
            invitation_count = db.query(share_invitation.model).filter(
                share_invitation.model.content_type == share.content_type,
                share_invitation.model.content_id == share.content_id,
                share_invitation.model.shared_by_id == current_user.id
            ).count()

            accepted_count = db.query(share_invitation.model).filter(
                share_invitation.model.content_type == share.content_type,
                share_invitation.model.content_id == share.content_id,
                share_invitation.model.shared_by_id == current_user.id,
                share_invitation.model.is_accepted == True
            ).count()

            content_title = content_data.get("content", content_data.get("title", f"{share.content_type.value} content"))

            summaries.append(MySharedContentSummary(
                content_type=share.content_type,
                content_id=share.content_id,
                content_title=content_title,
                course_name=content_data.get("course_name"),
                total_invitations=invitation_count,
                accepted_invitations=accepted_count,
                public_link_accesses=share.access_count,
                has_public_link=True,
                created_at=share.created_at
            ))

    return summaries


@router.get("/shared-with-me", response_model=List[SharedWithMeSummary])
def get_shared_with_me(
    *,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
    skip: int = 0,
    limit: int = 100
):
    """Get content that has been shared with me"""

    # Get all content access for this user
    user_access = content_access.get_user_accessible_content(
        db, user_id=current_user.id, skip=skip, limit=limit
    )

    summaries = []
    for access in user_access:
        content_data = get_content_data(db, access.content_type, access.content_id)
        if content_data:
            # Get sharer info
            sharer = db.query(User).filter(User.id == access.shared_by_id).first()
            if sharer:
                content_title = content_data.get("content", content_data.get("title", f"{access.content_type.value} content"))

                summaries.append(SharedWithMeSummary(
                    content_type=access.content_type,
                    content_id=access.content_id,
                    content_title=content_title,
                    course_name=content_data.get("course_name"),
                    shared_by_name=sharer.full_name,
                    shared_by_email=sharer.email,
                    share_message=access.share_message,
                    shared_at=access.created_at,
                    last_accessed=access.accessed_at
                ))

    return summaries


@router.delete("/remove-access/{content_type}/{content_id}")
def remove_shared_content_access(
    *,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
    content_type: ContentType,
    content_id: int
):
    """Remove user's access to shared content (delete their own copy)"""

    # Check if user has shared access to this content
    user_access = content_access.get_user_content_access(
        db, content_type=content_type, content_id=content_id, user_id=current_user.id
    )

    if not user_access:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="You don't have access to this content or it doesn't exist"
        )

    # Check if user owns the content (they can't remove access to their own content)
    owner_id = get_content_owner_id(db, content_type, content_id)
    if owner_id == current_user.id:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="You cannot remove access to content you own. Use the sharing settings to manage access."
        )

    # Remove the access
    content_access.remove_user_access(
        db, content_type=content_type, content_id=content_id, user_id=current_user.id
    )

    return {"message": "Access removed successfully"}


@router.get("/content/{content_type}/{content_id}")
def get_shared_content_data(
    *,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
    content_type: ContentType,
    content_id: int
):
    """Get content data if user has access to it (either owns it or has shared access)"""

    # Check if user owns the content
    owner_id = get_content_owner_id(db, content_type, content_id)
    if owner_id == current_user.id:
        # User owns the content, return it
        content_data = get_content_data(db, content_type, content_id)
        if content_data:
            return content_data
        else:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Content not found"
            )

    # Check if user has shared access to the content
    user_access = content_access.get_user_content_access(
        db, content_type=content_type, content_id=content_id, user_id=current_user.id
    )

    if user_access:
        # User has shared access, return the content
        content_data = get_content_data(db, content_type, content_id)
        if content_data:
            return content_data
        else:
            raise HTTPException(
                status_code=status.HTTP_404_NOT_FOUND,
                detail="Content not found"
            )

    # User doesn't have access
    raise HTTPException(
        status_code=status.HTTP_403_FORBIDDEN,
        detail="You don't have access to this content"
    )
