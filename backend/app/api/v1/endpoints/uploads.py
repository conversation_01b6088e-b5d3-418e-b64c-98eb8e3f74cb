"""
Upload management endpoints - Direct upload to Gemini (no Celery)
"""
from typing import Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, UploadFile, File, Form
from sqlalchemy.orm import Session

from app.api import deps
from app.models.user import User
from app.services.gemini_files_service import GeminiFilesService

router = APIRouter()


@router.post("/start", response_model=Dict[str, Any])
async def start_upload(
    *,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
    file: UploadFile = File(...),
    course_name: Optional[str] = Form(None)
):
    """
    Upload PDF directly to Gemini Files API
    """
    # Validate file
    if not file.filename.lower().endswith('.pdf'):
        raise HTTPException(status_code=400, detail="Only PDF files are allowed")

    if file.size > 30 * 1024 * 1024:  # 30MB limit
        raise HTTPException(status_code=400, detail="File size must be less than 30MB")

    try:
        # Initialize Gemini Files service
        gemini_service = GeminiFilesService()

        # Upload directly to Gemini
        upload_result = await gemini_service.upload_pdf_to_gemini(
            file=file,
            student_id=current_user.id,
            db=db,
            course_name=course_name
        )

        return {
            "status": "success",
            "message": "File uploaded successfully",
            "gemini_file_id": upload_result.get("gemini_file_id"),
            "original_filename": upload_result.get("original_filename"),
            "file_size": upload_result.get("file_size"),
            "upload_time": upload_result.get("upload_time"),
            "auto_delete_time": upload_result.get("auto_delete_time"),
            "course_name": upload_result.get("course_name")
        }

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Upload failed: {str(e)}"
        )


@router.get("/list", response_model=Dict[str, Any])
async def list_uploads(
    *,
    current_user: User = Depends(deps.get_current_active_user),
    limit: int = 50
):
    """
    Legacy endpoint for compatibility - returns empty list since we use direct uploads
    """
    # Parameters are kept for API compatibility but not used
    _ = current_user, limit

    return {
        "uploads": [],
        "total_count": 0,
        "message": "Direct upload system - no background tracking"
    }


# Note: With direct upload, we no longer need complex upload tracking endpoints
# The upload happens immediately and returns success/failure directly
