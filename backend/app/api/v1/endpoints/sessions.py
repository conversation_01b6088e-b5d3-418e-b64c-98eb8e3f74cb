from typing import Any, List

from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session

from app import schemas
from app.models.user import User, UserRole
from app.models.session import SessionStatus
from app.api import deps
from app.services import session_service

router = APIRouter()


@router.get("", response_model=List[schemas.SessionWithRelations])
@router.get("/", response_model=List[schemas.SessionWithRelations])
def read_sessions(
    db: Session = Depends(deps.get_db),
    skip: int = 0,
    limit: int = 100,
    course_id: int = None,
    tutor_id: int = None,
    status: SessionStatus = None,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Retrieve sessions with optional filtering.
    """
    # Auto-update expired sessions before fetching
    session_service.update_expired_sessions(db)

    sessions = session_service.get_multi(
        db,
        skip=skip,
        limit=limit,
        course_id=course_id,
        tutor_id=tutor_id,
        status=status
    )
    return sessions


@router.post("", response_model=schemas.Session)
@router.post("/", response_model=schemas.Session)
def create_session(
    *,
    db: Session = Depends(deps.get_db),
    session_in: schemas.SessionCreate,
    current_user: User = Depends(deps.get_current_tutor_user),
) -> Any:
    """
    Create new session.
    """
    # Ensure the tutor_id matches the current user if they're a tutor
    if current_user.role == UserRole.TUTOR and session_in.tutor_id != current_user.id:
        raise HTTPException(
            status_code=400,
            detail="Tutor ID must match the current user"
        )

    session = session_service.create(db, obj_in=session_in)
    return session


@router.get("/{id}", response_model=schemas.SessionWithRelations)
def read_session(
    *,
    db: Session = Depends(deps.get_db),
    id: int,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Get session by ID.
    """
    session = session_service.get(db, id=id)
    if not session:
        raise HTTPException(status_code=404, detail="Session not found")
    return session


@router.put("/{id}", response_model=schemas.SessionWithRelations)
def update_session(
    *,
    db: Session = Depends(deps.get_db),
    id: int,
    session_in: schemas.SessionUpdate,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Update a session.
    """
    session = session_service.get(db, id=id)
    if not session:
        raise HTTPException(status_code=404, detail="Session not found")

    # Only the tutor who created the session or an admin can update it
    if session.tutor_id != current_user.id and current_user.role != UserRole.ADMIN:
        raise HTTPException(status_code=403, detail="Not enough permissions")

    session = session_service.update(db, db_obj=session, obj_in=session_in)
    return session


@router.delete("/{id}", response_model=schemas.SessionWithRelations)
def delete_session(
    *,
    db: Session = Depends(deps.get_db),
    id: int,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Delete a session.
    """
    session = session_service.get(db, id=id)
    if not session:
        raise HTTPException(status_code=404, detail="Session not found")

    # Only the tutor who created the session or an admin can delete it
    if session.tutor_id != current_user.id and current_user.role != UserRole.ADMIN:
        raise HTTPException(status_code=403, detail="Not enough permissions")

    session = session_service.delete(db, id=id)
    return session


@router.post("/update-expired", response_model=dict)
def update_expired_sessions(
    *,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Manually update expired sessions to completed status.
    """
    updated_count = session_service.update_expired_sessions(db)
    return {"message": f"Updated {updated_count} expired sessions"}


@router.post("/book", response_model=schemas.SessionWithRelations)
def book_session(
    *,
    db: Session = Depends(deps.get_db),
    booking: schemas.SessionBookingRequest,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Book a session for a student.
    """
    # Ensure the student_id matches the current user if they're a student
    if current_user.role == UserRole.STUDENT and booking.student_id != current_user.id:
        raise HTTPException(
            status_code=400,
            detail="Student ID must match the current user"
        )

    session = session_service.get(db, id=booking.session_id)
    if not session:
        raise HTTPException(status_code=404, detail="Session not found")

    # Check if session is already booked
    if session.student_id is not None:
        raise HTTPException(status_code=400, detail="Session is already booked")

    # Book the session
    session.student_id = booking.student_id
    db.add(session)
    db.commit()
    db.refresh(session)

    # Return session with relationships loaded
    return session_service.get(db, id=session.id)
