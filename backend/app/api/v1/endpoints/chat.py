from typing import Any, List, Dict
import json
import asyncio
from datetime import datetime, timezone
from contextlib import contextmanager

from fastapi import APIRout<PERSON>, Depends, HTTPException, status, WebSocket, WebSocketDisconnect
from sqlalchemy.orm import Session

from app.api import deps
from app.models.user import User
from app.services.chat_service import chat_service
from app.schemas.chat import (
    ChatMessageCreate,
    ChatJoinRequest, ChatMessageSend, OnlineStatusUpdate
)


@contextmanager
def get_db_session():
    """Context manager for database sessions in WebSocket operations"""
    db_gen = deps.get_db()
    db = next(db_gen)
    try:
        yield db
    finally:
        db.close()


router = APIRouter()


# WebSocket connection manager
class ConnectionManager:
    def __init__(self):
        self.active_connections: Dict[int, WebSocket] = {}  # user_id -> websocket
        self.chat_room_connections: Dict[int, List[int]] = {}  # chat_room_id -> [user_ids]
        self.max_connections_per_user = 10  # Increased limit for multiple tabs/devices
        self.user_connection_count: Dict[int, int] = {}  # user_id -> connection_count

    async def connect(self, websocket: WebSocket, user_id: int):
        # Close existing connection if any (only allow one active connection per user)
        if user_id in self.active_connections:
            try:
                old_websocket = self.active_connections[user_id]
                await old_websocket.close(code=1000, reason="New connection")
            except Exception as e:
                print(f"🔥 Error closing old connection for user {user_id}: {e}")
            # Clean up the old connection completely
            self.disconnect(user_id)

        try:
            # Add connection timeout
            await asyncio.wait_for(websocket.accept(), timeout=10.0)
            self.active_connections[user_id] = websocket
            self.user_connection_count[user_id] = 1  # Reset to 1 for the new connection
            print(f"🔥 User {user_id} connected successfully. Active connections: {list(self.active_connections.keys())}")
            return True
        except asyncio.TimeoutError:
            print(f"🔥 Connection timeout for user {user_id}")
            return False
        except Exception as e:
            print(f"🔥 Failed to accept connection for user {user_id}: {e}")
            return False

    def disconnect(self, user_id: int):
        if user_id in self.active_connections:
            del self.active_connections[user_id]
            print(f"🔥 Removed user {user_id} from active connections")

        # Decrease connection count
        if user_id in self.user_connection_count:
            self.user_connection_count[user_id] = max(0, self.user_connection_count[user_id] - 1)
            if self.user_connection_count[user_id] == 0:
                del self.user_connection_count[user_id]

        # Remove from all chat rooms - create a copy to avoid iteration issues
        for room_id, users in list(self.chat_room_connections.items()):
            if user_id in users:
                users.remove(user_id)
                print(f"🔥 Removed user {user_id} from chat room {room_id}")

        print(f"🔥 User {user_id} disconnected. Active connections: {list(self.active_connections.keys())}")

    async def send_personal_message(self, message: str, user_id: int):
        print(f"🔥 SEND_PERSONAL_MESSAGE: user_id={user_id}")
        if user_id in self.active_connections:
            websocket = self.active_connections[user_id]
            try:
                print(f"🔥 SENDING MESSAGE TO USER {user_id}: {message[:100]}...")
                # Add timeout to prevent hanging
                await asyncio.wait_for(websocket.send_text(message), timeout=5.0)
                print(f"🔥 MESSAGE SENT SUCCESSFULLY TO USER {user_id}")
                return True
            except asyncio.TimeoutError:
                print(f"🔥 TIMEOUT sending message to user {user_id}")
                # Remove the broken connection immediately
                self.disconnect(user_id)
                return False
            except Exception as e:
                print(f"🔥 ERROR sending message to user {user_id}: {e}")
                # Remove the broken connection immediately
                self.disconnect(user_id)
                return False
        else:
            print(f"🔥 USER {user_id} NOT IN ACTIVE CONNECTIONS: {list(self.active_connections.keys())}")
            return False

    async def join_chat_room(self, user_id: int, chat_room_id: int):
        print(f"🔥 JOIN_CHAT_ROOM: user_id={user_id}, chat_room_id={chat_room_id}")
        if chat_room_id not in self.chat_room_connections:
            self.chat_room_connections[chat_room_id] = []
            print(f"🔥 CREATED NEW CHAT ROOM CONNECTION LIST FOR ROOM {chat_room_id}")

        if user_id not in self.chat_room_connections[chat_room_id]:
            self.chat_room_connections[chat_room_id].append(user_id)
            print(f"🔥 ADDED USER {user_id} TO CHAT ROOM {chat_room_id}")
        else:
            print(f"🔥 USER {user_id} ALREADY IN CHAT ROOM {chat_room_id}")

        print(f"🔥 CHAT ROOM {chat_room_id} NOW HAS USERS: {self.chat_room_connections[chat_room_id]}")

    async def leave_chat_room(self, user_id: int, chat_room_id: int):
        if chat_room_id in self.chat_room_connections:
            if user_id in self.chat_room_connections[chat_room_id]:
                self.chat_room_connections[chat_room_id].remove(user_id)

    async def broadcast_to_chat_room(self, message: str, chat_room_id: int, exclude_user: int = None):
        print(f"🔥 BROADCAST_TO_CHAT_ROOM: room_id={chat_room_id}, exclude_user={exclude_user}")
        print(f"🔥 CHAT_ROOM_CONNECTIONS: {self.chat_room_connections}")
        print(f"🔥 ACTIVE_CONNECTIONS: {list(self.active_connections.keys())}")

        if chat_room_id in self.chat_room_connections:
            # Create a copy of the user list to avoid iteration issues
            users_to_notify = list(self.chat_room_connections[chat_room_id])
            print(f"🔥 USERS_TO_NOTIFY: {users_to_notify}")

            # Send to all users in the chat room who are actively connected
            for user_id in users_to_notify:
                if exclude_user and user_id == exclude_user:
                    print(f"🔥 EXCLUDING USER: {user_id}")
                    continue

                # Double-check user is still actively connected
                if user_id in self.active_connections:
                    print(f"🔥 SENDING MESSAGE TO ACTIVE USER: {user_id}")
                    await self.send_personal_message(message, user_id)
                else:
                    print(f"🔥 USER {user_id} NOT IN ACTIVE CONNECTIONS, REMOVING FROM CHAT ROOM")
                    # Clean up stale chat room connection
                    self.chat_room_connections[chat_room_id].remove(user_id)
        else:
            print(f"🔥 CHAT ROOM {chat_room_id} NOT FOUND IN CONNECTIONS")

    async def broadcast_online_status(self, user_id: int, is_online: bool):
        message = json.dumps({
            "type": "online_status",
            "data": {
                "user_id": user_id,
                "is_online": is_online,
                "timestamp": datetime.now(timezone.utc).isoformat()
            }
        })

        # Send to all connected users - create a copy to avoid iteration issues
        connected_users = list(self.active_connections.keys())
        for connected_user_id in connected_users:
            if connected_user_id != user_id:
                await self.send_personal_message(message, connected_user_id)

    async def send_notification(self, user_id: int, notification_data: dict):
        """Send a real-time notification to a specific user"""
        if user_id in self.active_connections:
            message = json.dumps({
                "type": "notification",
                "data": notification_data
            })
            await self.send_personal_message(message, user_id)
            return True
        return False

    async def broadcast_notification(self, notification_data: dict, target_user_ids: list = None):
        """Broadcast a notification to multiple users or all connected users"""
        message = json.dumps({
            "type": "notification",
            "data": notification_data
        })

        if target_user_ids:
            # Send to specific users
            for user_id in target_user_ids:
                if user_id in self.active_connections:
                    await self.send_personal_message(message, user_id)
        else:
            # Send to all connected users
            for user_id in self.active_connections:
                await self.send_personal_message(message, user_id)


# Global connection manager instance
manager = ConnectionManager()

# Export the manager for use in other modules
def get_connection_manager() -> ConnectionManager:
    """Get the global WebSocket connection manager"""
    return manager


async def deliver_offline_messages(user_id: int):
    """Deliver all pending offline messages to a user when they come online"""
    try:
        with get_db_session() as delivery_db:
            # Get all undelivered messages for the user
            undelivered_messages = chat_service.get_undelivered_messages_for_user(
                delivery_db, user_id=user_id
            )

            if not undelivered_messages:
                print(f"🔥 No offline messages to deliver for user {user_id}")
                return

            print(f"🔥 Delivering {len(undelivered_messages)} offline messages to user {user_id}")

            # Group messages by chat room for better organization
            messages_by_room = {}
            for message in undelivered_messages:
                room_id = message.chat_room_id
                if room_id not in messages_by_room:
                    messages_by_room[room_id] = []
                messages_by_room[room_id].append(message)

            # Deliver messages room by room
            delivered_message_ids = []
            for room_id, room_messages in messages_by_room.items():
                for message in room_messages:
                    # Get sender info
                    sender = delivery_db.query(User).filter(User.id == message.sender_id).first()
                    sender_name = sender.full_name if sender else "Unknown"
                    sender_profile_picture_url = sender.profile_picture_url if sender else None

                    # Create message data
                    message_data = {
                        "type": "offline_message",
                        "data": {
                            "id": message.id,
                            "content": message.content,
                            "sender_id": message.sender_id,
                            "sender_name": sender_name,
                            "sender_profile_picture_url": sender_profile_picture_url,
                            "chat_room_id": message.chat_room_id,
                            "created_at": message.created_at.isoformat(),
                            "message_type": message.message_type,
                            "is_offline_delivery": True
                        }
                    }

                    # Send message to user
                    message_json = json.dumps(message_data)
                    await manager.send_personal_message(message_json, user_id)
                    delivered_message_ids.append(message.id)

                    print(f"🔥 Delivered offline message {message.id} to user {user_id}")

            # Mark all delivered messages as delivered
            if delivered_message_ids:
                delivered_count = chat_service.mark_multiple_messages_delivered(
                    delivery_db, message_ids=delivered_message_ids, user_id=user_id
                )
                print(f"🔥 Marked {delivered_count} messages as delivered for user {user_id}")

    except Exception as e:
        print(f"🔥 Error delivering offline messages to user {user_id}: {e}")
        import traceback
        traceback.print_exc()


async def mark_message_delivered_for_online_users(message_id: int, chat_room_id: int, sender_id: int):
    """Mark a message as delivered for online users in the chat room"""
    try:
        with get_db_session() as delivery_db:
            # Get chat room participants
            room_participants = chat_service.get_chat_room_participants(
                delivery_db, chat_room_id=chat_room_id
            )

            if not room_participants:
                return

            # Get the other participant (not the sender)
            other_user_id = None
            if room_participants["student"] and room_participants["student"]["id"] != sender_id:
                other_user_id = room_participants["student"]["id"]
            elif room_participants["tutor"] and room_participants["tutor"]["id"] != sender_id:
                other_user_id = room_participants["tutor"]["id"]

            if not other_user_id:
                return

            # Check if the other user is online
            if other_user_id in manager.active_connections:
                # Mark message as delivered
                delivered = chat_service.mark_message_delivered(
                    delivery_db, message_id=message_id, user_id=other_user_id
                )
                if delivered:
                    print(f"🔥 Marked message {message_id} as delivered to online user {other_user_id}")

    except Exception as e:
        print(f"🔥 Error marking message {message_id} as delivered: {e}")


async def mark_chat_room_messages_delivered(chat_room_id: int, user_id: int):
    """Mark all undelivered messages in a chat room as delivered for a user"""
    try:
        with get_db_session() as delivery_db:
            # Get undelivered messages for this chat room and user
            undelivered_messages = chat_service.get_undelivered_messages_for_chat_room(
                delivery_db, chat_room_id=chat_room_id, user_id=user_id
            )

            if not undelivered_messages:
                print(f"🔥 No undelivered messages in chat room {chat_room_id} for user {user_id}")
                return

            # Mark all as delivered
            message_ids = [msg.id for msg in undelivered_messages]
            delivered_count = chat_service.mark_multiple_messages_delivered(
                delivery_db, message_ids=message_ids, user_id=user_id
            )

            print(f"🔥 Marked {delivered_count} messages as delivered in chat room {chat_room_id} for user {user_id}")

    except Exception as e:
        print(f"🔥 Error marking chat room messages as delivered: {e}")


@router.websocket("/ws/{user_id}")
async def websocket_endpoint(websocket: WebSocket, user_id: int):
    """WebSocket endpoint for real-time chat with timeout protection"""
    # Accept the connection first
    connection_accepted = await manager.connect(websocket, user_id)

    if not connection_accepted:
        print(f"Connection rejected for user {user_id}: connection failed")
        return

    try:
        # Update user online status using separate session with timeout
        try:
            with get_db_session() as online_db:
                chat_service.update_user_online_status(
                    online_db, user_id=user_id, is_online=True, connection_id=str(user_id)
                )
        except Exception as e:
            print(f"🔥 Error updating online status for user {user_id}: {e}")
            # Continue anyway - don't fail the connection for this

        # Broadcast online status
        await manager.broadcast_online_status(user_id, True)

        # Deliver pending offline messages
        await deliver_offline_messages(user_id)
        
        while True:
            try:
                # Receive message from client with timeout
                data = await asyncio.wait_for(websocket.receive_text(), timeout=300.0)  # 5 minute timeout
                message_data = json.loads(data)
                message_type = message_data.get("type")
            except asyncio.TimeoutError:
                print(f"🔥 WebSocket timeout for user {user_id} - closing connection")
                break
            except json.JSONDecodeError as e:
                print(f"🔥 Invalid JSON from user {user_id}: {e}")
                continue
            except Exception as e:
                print(f"🔥 Error receiving message from user {user_id}: {e}")
                break
            
            if message_type == "join_chat":
                # Join a chat room
                chat_room_id = message_data.get("chat_room_id")
                print(f"🔥 RECEIVED JOIN_CHAT: user_id={user_id}, chat_room_id={chat_room_id}")
                if chat_room_id:
                    await manager.join_chat_room(user_id, chat_room_id)

                    # Send confirmation
                    response = json.dumps({
                        "type": "joined_chat",
                        "data": {"chat_room_id": chat_room_id}
                    })
                    print(f"🔥 SENDING JOIN CONFIRMATION TO USER {user_id}")
                    await manager.send_personal_message(response, user_id)

                    # Mark undelivered messages in this chat room as delivered
                    await mark_chat_room_messages_delivered(chat_room_id, user_id)
            
            elif message_type == "send_message":
                # Send a message to chat room
                chat_room_id = message_data.get("chat_room_id")
                content = message_data.get("content")
                print(f"🔥 RECEIVED SEND_MESSAGE: user_id={user_id}, chat_room_id={chat_room_id}, content='{content}'")

                if chat_room_id and content:
                    try:
                        # Use separate database session for this operation
                        with get_db_session() as msg_db:
                            print(f"🔥 SAVING MESSAGE TO DATABASE...")
                            # Save message to database
                            message = chat_service.send_message(
                                msg_db,
                                chat_room_id=chat_room_id,
                                sender_id=user_id,
                                content=content
                            )
                            print(f"🔥 MESSAGE SAVED: id={message.id}")

                            # Get sender info
                            sender = msg_db.query(User).filter(User.id == user_id).first()
                            sender_name = sender.full_name if sender else "Unknown"
                            sender_profile_picture_url = sender.profile_picture_url if sender else None
                            print(f"🔥 SENDER INFO: {sender_name}")

                            # Create message data for broadcast
                            message_data_broadcast = {
                                "id": message.id,
                                "content": message.content,
                                "sender_id": message.sender_id,
                                "sender_name": sender_name,
                                "sender_profile_picture_url": sender_profile_picture_url,
                                "chat_room_id": message.chat_room_id,
                                "created_at": message.created_at.isoformat(),
                                "message_type": message.message_type
                            }

                            # Broadcast to chat room immediately for real-time feel
                            response = json.dumps({
                                "type": "new_message",
                                "data": message_data_broadcast
                            })
                            print(f"🔥 BROADCASTING MESSAGE IMMEDIATELY: {response[:200]}...")

                            # Broadcast to ALL users in chat room (including sender for confirmation)
                            await manager.broadcast_to_chat_room(response, chat_room_id, exclude_user=None)
                            print(f"🔥 REAL-TIME MESSAGE BROADCAST COMPLETE")

                            # Mark message as delivered for online recipients (async)
                            await mark_message_delivered_for_online_users(message.id, chat_room_id, user_id)
                    except Exception as e:
                        print(f"ERROR: Failed to send message: {e}")
                        import traceback
                        traceback.print_exc()

                        # Send error response to sender
                        error_response = json.dumps({
                            "type": "message_error",
                            "data": {"error": "Failed to send message", "chat_room_id": chat_room_id}
                        })
                        await manager.send_personal_message(error_response, user_id)
            
            elif message_type == "mark_read":
                # Mark messages as read
                chat_room_id = message_data.get("chat_room_id")
                if chat_room_id:
                    # Use separate database session for this operation
                    with get_db_session() as read_db:
                        chat_service.mark_messages_as_read(
                            read_db, chat_room_id=chat_room_id, user_id=user_id
                        )

                        # Send confirmation
                        response = json.dumps({
                            "type": "messages_marked_read",
                            "data": {"chat_room_id": chat_room_id}
                        })
                        await manager.send_personal_message(response, user_id)
            
            elif message_type == "message_delivered":
                # Mark a message as delivered
                message_id = message_data.get("message_id")
                if message_id:
                    try:
                        with get_db_session() as delivery_db:
                            delivered = chat_service.mark_message_delivered(
                                delivery_db, message_id=message_id, user_id=user_id
                            )
                            if delivered:
                                print(f"🔥 Message {message_id} marked as delivered by user {user_id}")
                    except Exception as e:
                        print(f"🔥 Error marking message {message_id} as delivered: {e}")

            elif message_type == "message_read":
                # Mark a message as read
                message_id = message_data.get("message_id")
                if message_id:
                    try:
                        with get_db_session() as read_db:
                            read_success = chat_service.mark_message_read(
                                read_db, message_id=message_id, user_id=user_id
                            )
                            if read_success:
                                print(f"🔥 Message {message_id} marked as read by user {user_id}")
                    except Exception as e:
                        print(f"🔥 Error marking message {message_id} as read: {e}")

            elif message_type == "ping":
                # Heartbeat ping - respond with pong
                pong_response = json.dumps({
                    "type": "pong",
                    "data": {"timestamp": datetime.now(timezone.utc).isoformat()}
                })
                await manager.send_personal_message(pong_response, user_id)

            elif message_type == "leave_chat":
                # Leave a chat room
                chat_room_id = message_data.get("chat_room_id")
                if chat_room_id:
                    await manager.leave_chat_room(user_id, chat_room_id)

                    # Send confirmation
                    response = json.dumps({
                        "type": "left_chat",
                        "data": {"chat_room_id": chat_room_id}
                    })
                    await manager.send_personal_message(response, user_id)
    
    except WebSocketDisconnect:
        pass
    except Exception as e:
        print(f"WebSocket error for user {user_id}: {e}")
    finally:
        # Clean up
        manager.disconnect(user_id)

        # Update user offline status using separate session
        try:
            with get_db_session() as offline_db:
                chat_service.update_user_online_status(
                    offline_db, user_id=user_id, is_online=False
                )

            # Broadcast offline status
            await manager.broadcast_online_status(user_id, False)
        except Exception as e:
            print(f"Error updating offline status: {e}")


# REST API endpoints for chat
@router.get("/rooms", response_model=List[Dict])
def get_chat_rooms(
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
    skip: int = 0,
    limit: int = 100,
) -> Any:
    """Get chat rooms for current user"""
    return chat_service.get_user_chat_rooms(
        db, user_id=current_user.id, skip=skip, limit=limit
    )


@router.get("/rooms/{chat_room_id}/messages", response_model=List[Dict])
def get_chat_messages(
    *,
    db: Session = Depends(deps.get_db),
    chat_room_id: int,
    current_user: User = Depends(deps.get_current_active_user),
    skip: int = 0,
    limit: int = 100,
) -> Any:
    """Get messages for a chat room"""
    try:
        return chat_service.get_chat_messages(
            db, chat_room_id=chat_room_id, user_id=current_user.id, skip=skip, limit=limit
        )
    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail=str(e))


@router.post("/rooms/{chat_room_id}/messages", response_model=Dict)
def send_message(
    *,
    db: Session = Depends(deps.get_db),
    chat_room_id: int,
    message: ChatMessageSend,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """Send a message to a chat room (REST API fallback)"""
    try:
        msg = chat_service.send_message(
            db,
            chat_room_id=chat_room_id,
            sender_id=current_user.id,
            content=message.content,
            message_type=message.message_type
        )

        return {
            "id": msg.id,
            "content": msg.content,
            "sender_id": msg.sender_id,
            "chat_room_id": msg.chat_room_id,
            "created_at": msg.created_at.isoformat(),
            "message_type": msg.message_type
        }
    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail=str(e))


@router.get("/rooms/{chat_room_id}/participant/{user_id}/profile", response_model=Dict)
def get_chat_participant_profile(
    *,
    db: Session = Depends(deps.get_db),
    chat_room_id: int,
    user_id: int,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """Get profile details for a chat participant"""
    try:
        # Verify that the current user is part of this chat room
        chat_room = chat_service.get_chat_room(db, chat_room_id=chat_room_id, user_id=current_user.id)
        if not chat_room:
            raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail="Access denied to this chat room")

        # Get the participant's profile
        participant_profile = chat_service.get_chat_participant_profile(
            db, chat_room_id=chat_room_id, user_id=user_id, requesting_user_id=current_user.id
        )

        if not participant_profile:
            raise HTTPException(status_code=status.HTTP_404_NOT_FOUND, detail="Participant profile not found")

        return participant_profile

    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail=str(e))


@router.post("/rooms/{chat_room_id}/mark-read")
def mark_messages_read(
    *,
    db: Session = Depends(deps.get_db),
    chat_room_id: int,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """Mark all messages in a chat room as read"""
    try:
        count = chat_service.mark_messages_as_read(
            db, chat_room_id=chat_room_id, user_id=current_user.id
        )
        return {"marked_read": count}
    except ValueError as e:
        raise HTTPException(status_code=status.HTTP_403_FORBIDDEN, detail=str(e))
