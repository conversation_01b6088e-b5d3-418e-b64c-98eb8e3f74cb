from typing import Any, List

from fastapi import APIRouter, Body, Depends, HTTPException, UploadFile, File
from fastapi.encoders import jsonable_encoder
from sqlalchemy.orm import Session

from app import schemas
from app.models.user import User, UserRole
from app.api import deps
from app.services import user_service
from app.services.cloudinary_service import CloudinaryService

router = APIRouter()


@router.get("", response_model=List[schemas.User])
@router.get("/", response_model=List[schemas.User])
def read_users(
    db: Session = Depends(deps.get_db),
    skip: int = 0,
    limit: int = 100,
    current_user: User = Depends(deps.get_current_admin_user),
) -> Any:
    """
    Retrieve users.
    """
    users = user_service.get_multi(db, skip=skip, limit=limit)
    return users


@router.get("/me", response_model=schemas.User)
def read_user_me(
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Get current user.
    """
    return current_user


@router.put("/me", response_model=schemas.User)
def update_user_me(
    *,
    db: Session = Depends(deps.get_db),
    password: str = Body(None),
    full_name: str = Body(None),
    email: str = Body(None),
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Update own user.
    """
    current_user_data = jsonable_encoder(current_user)
    user_in = schemas.UserUpdate(**current_user_data)
    if password is not None:
        user_in.password = password
    if full_name is not None:
        user_in.full_name = full_name
    if email is not None:
        user_in.email = email
    user = user_service.update(db, db_obj=current_user, obj_in=user_in)
    return user


@router.post("/me/profile-picture", response_model=schemas.User)
async def upload_profile_picture(
    *,
    db: Session = Depends(deps.get_db),
    profile_picture: UploadFile = File(...),
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Upload profile picture for current user.
    """
    # Validate file type
    if not profile_picture.content_type or not profile_picture.content_type.startswith('image/'):
        raise HTTPException(
            status_code=400,
            detail="File must be an image"
        )

    # Validate file size (max 5MB)
    max_size = 5 * 1024 * 1024  # 5MB
    if profile_picture.size and profile_picture.size > max_size:
        raise HTTPException(
            status_code=400,
            detail="File size must be less than 5MB"
        )

    try:
        # Initialize Cloudinary service
        cloudinary_service = CloudinaryService()

        # Upload to Cloudinary with profile picture optimizations
        upload_result = await cloudinary_service.upload_image(
            profile_picture,
            folder=f"profile_pictures/{current_user.id}",
            public_id=f"user_{current_user.id}_profile",
            transformation=cloudinary_service.get_optimized_transformations()["profile_picture"]
        )

        # Update user with new profile picture URL
        user_update = schemas.UserUpdate(profile_picture_url=upload_result["url"])
        updated_user = user_service.update(db, db_obj=current_user, obj_in=user_update)

        return updated_user

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to upload profile picture: {str(e)}"
        )


@router.get("/{user_id}", response_model=schemas.User)
def read_user_by_id(
    user_id: int,
    current_user: User = Depends(deps.get_current_active_user),
    db: Session = Depends(deps.get_db),
) -> Any:
    """
    Get a specific user by id.
    """
    user = user_service.get(db, id=user_id)
    if user == current_user:
        return user
    if not current_user.role == UserRole.ADMIN:
        raise HTTPException(
            status_code=400, detail="The user doesn't have enough privileges"
        )
    return user


@router.put("/{user_id}", response_model=schemas.User)
def update_user(
    *,
    db: Session = Depends(deps.get_db),
    user_id: int,
    user_in: schemas.UserUpdate,
    current_user: User = Depends(deps.get_current_admin_user),
) -> Any:
    """
    Update a user.
    """
    user = user_service.get(db, id=user_id)
    if not user:
        raise HTTPException(
            status_code=404,
            detail="The user with this id does not exist in the system",
        )
    user = user_service.update(db, db_obj=user, obj_in=user_in)
    return user
