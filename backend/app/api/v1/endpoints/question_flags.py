from typing import List, Optional, Dict, Any
from fastapi import APIRouter, Depends, HTTPException, status, Query
from sqlalchemy.orm import Session

from app.api import deps
from app.models.user import User, UserRole
from app.models.question_flag import FlagReasonDisplayNames, FlagStatus
from app.crud.crud_question_flag import question_flag
from app.schemas.question_flag import (
    QuestionFlagCreate,
    QuestionFlagUpdate,
    QuestionFlagStatusUpdate,
    QuestionFlagBulkUpdate,
    QuestionFlag,
    FlagReasonsResponse,
    FlagStatistics,
    FlagListResponse,
    FlagSubmissionResponse,
    FlagActionResponse,
    AdminFlagListRequest
)

router = APIRouter()


@router.get("/reasons", response_model=FlagReasonsResponse)
def get_flag_reasons():
    """
    Get available flag reasons for students to choose from.
    """
    reasons = FlagReasonDisplayNames.get_all_reasons()
    return FlagReasonsResponse(reasons=reasons)


@router.post("/", response_model=FlagSubmissionResponse)
def submit_question_flag(
    *,
    db: Session = Depends(deps.get_db),
    flag_data: QuestionFlagCreate,
    current_user: User = Depends(deps.get_current_active_user)
):
    """
    Submit a flag for a question.
    Students can flag questions for various issues.
    """
    # Only students can submit flags
    if current_user.role != UserRole.STUDENT:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only students can flag questions"
        )
    
    # Check if student has already flagged this question
    existing_flag = question_flag.get_by_question_and_student(
        db, question_id=flag_data.question_id, student_id=current_user.id
    )
    
    if existing_flag:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="You have already flagged this question"
        )
    
    # Validate reasons
    if not flag_data.reasons:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="At least one reason must be provided"
        )
    
    try:
        # Create the flag
        new_flag = question_flag.create_flag(
            db,
            question_id=flag_data.question_id,
            student_id=current_user.id,
            reasons=flag_data.reasons,
            description=flag_data.description
        )
        
        return FlagSubmissionResponse(
            success=True,
            message="Question flagged successfully. Thank you for your feedback!",
            flag_id=new_flag.id
        )
        
    except Exception as e:
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Failed to submit flag. Please try again."
        )


@router.get("/my-flags", response_model=FlagListResponse)
def get_my_flags(
    *,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
    page: int = Query(1, ge=1),
    per_page: int = Query(20, ge=1, le=100)
):
    """
    Get flags submitted by the current student.
    """
    if current_user.role != UserRole.STUDENT:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only students can view their flags"
        )
    
    skip = (page - 1) * per_page
    flags = question_flag.get_flags_by_student(
        db, student_id=current_user.id, skip=skip, limit=per_page
    )
    
    # Get total count for pagination
    total = len(question_flag.get_flags_by_student(db, student_id=current_user.id, skip=0, limit=1000))
    total_pages = (total + per_page - 1) // per_page
    
    return FlagListResponse(
        flags=flags,
        total=total,
        page=page,
        per_page=per_page,
        total_pages=total_pages
    )


# Admin endpoints
@router.post("/admin/list", response_model=FlagListResponse)
def get_all_flags_admin(
    *,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
    request: AdminFlagListRequest
):
    """
    Get all flags with filters (admin only).
    """
    if current_user.role != UserRole.ADMIN:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only admins can view all flags"
        )
    
    page = request.page or 1
    per_page = request.per_page or 20
    skip = (page - 1) * per_page
    
    # Apply filters
    filters = request.filters or {}
    status_filter = None
    if filters.status:
        try:
            status_filter = FlagStatus(filters.status)
        except ValueError:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail=f"Invalid status: {filters.status}"
            )
    
    flags = question_flag.get_flags_with_filters(
        db,
        status=status_filter,
        reason=filters.reason,
        course_id=filters.course_id,
        skip=skip,
        limit=per_page
    )
    
    # Get total count for pagination
    total_flags = question_flag.get_flags_with_filters(
        db,
        status=status_filter,
        reason=filters.reason,
        course_id=filters.course_id,
        skip=0,
        limit=1000
    )
    total = len(total_flags)
    total_pages = (total + per_page - 1) // per_page
    
    return FlagListResponse(
        flags=flags,
        total=total,
        page=page,
        per_page=per_page,
        total_pages=total_pages
    )


@router.get("/admin/statistics", response_model=FlagStatistics)
def get_flag_statistics_admin(
    *,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user)
):
    """
    Get flag statistics (admin only).
    """
    if current_user.role != UserRole.ADMIN:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only admins can view flag statistics"
        )
    
    stats = question_flag.get_flag_statistics(db)
    return FlagStatistics(**stats)


@router.put("/{flag_id}/status", response_model=FlagActionResponse)
def update_flag_status(
    *,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
    flag_id: int,
    status_update: QuestionFlagStatusUpdate
):
    """
    Update flag status (admin only).
    """
    if current_user.role != UserRole.ADMIN:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only admins can update flag status"
        )
    
    updated_flag = question_flag.update_flag_status(
        db,
        flag_id=flag_id,
        status=status_update.status,
        reviewer_id=current_user.id,
        admin_notes=status_update.admin_notes
    )
    
    if not updated_flag:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Flag not found"
        )
    
    return FlagActionResponse(
        success=True,
        message=f"Flag status updated to {status_update.status.value}",
        updated_flags=1
    )


@router.put("/admin/bulk-update", response_model=FlagActionResponse)
def bulk_update_flag_status(
    *,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
    bulk_update: QuestionFlagBulkUpdate
):
    """
    Bulk update flag status (admin only).
    """
    if current_user.role != UserRole.ADMIN:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only admins can bulk update flags"
        )
    
    updated_count = question_flag.bulk_update_status(
        db,
        flag_ids=bulk_update.flag_ids,
        status=bulk_update.status,
        reviewer_id=current_user.id,
        admin_notes=bulk_update.admin_notes
    )
    
    return FlagActionResponse(
        success=True,
        message=f"Updated {updated_count} flags to {bulk_update.status.value}",
        updated_flags=updated_count
    )


@router.get("/question/{question_id}", response_model=FlagListResponse)
def get_question_flags(
    *,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
    question_id: int,
    page: int = Query(1, ge=1),
    per_page: int = Query(20, ge=1, le=100)
):
    """
    Get all flags for a specific question (admin only).
    """
    if current_user.role != UserRole.ADMIN:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only admins can view question flags"
        )
    
    skip = (page - 1) * per_page
    flags = question_flag.get_flags_by_question(
        db, question_id=question_id, skip=skip, limit=per_page
    )
    
    # Get total count for pagination
    total_flags = question_flag.get_flags_by_question(
        db, question_id=question_id, skip=0, limit=1000
    )
    total = len(total_flags)
    total_pages = (total + per_page - 1) // per_page
    
    return FlagListResponse(
        flags=flags,
        total=total,
        page=page,
        per_page=per_page,
        total_pages=total_pages
    )


@router.delete("/{flag_id}", response_model=FlagActionResponse)
def delete_flag(
    *,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
    flag_id: int
):
    """
    Delete a flag (admin only).
    """
    if current_user.role != UserRole.ADMIN:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only admins can delete flags"
        )
    
    success = question_flag.delete_flag(db, flag_id=flag_id)
    
    if not success:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Flag not found"
        )
    
    return FlagActionResponse(
        success=True,
        message="Flag deleted successfully"
    )


@router.put("/{flag_id}/notes", response_model=FlagActionResponse)
def update_flag_notes(
    *,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
    flag_id: int,
    request: dict
):
    """
    Update flag admin notes (admin only).
    """
    if current_user.role != UserRole.ADMIN:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Only admins can update flag notes"
        )

    flag = question_flag.get(db=db, id=flag_id)
    if not flag:
        raise HTTPException(
            status_code=status.HTTP_404_NOT_FOUND,
            detail="Flag not found"
        )

    admin_notes = request.get("admin_notes", "")

    updated_flag = question_flag.update(
        db=db,
        db_obj=flag,
        obj_in={"admin_notes": admin_notes}
    )

    return FlagActionResponse(
        success=True,
        message="Flag notes updated successfully"
    )
