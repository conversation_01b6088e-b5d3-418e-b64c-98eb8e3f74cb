from typing import Any, List

from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session

from app import schemas
from app.models.user import User
from app.api import deps
from app.services import school_service

router = APIRouter()


@router.get("", response_model=List[schemas.School])
@router.get("/", response_model=List[schemas.School])
def read_schools(
    db: Session = Depends(deps.get_db),
    skip: int = 0,
    limit: int = 100,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Retrieve schools.
    """
    schools = school_service.get_multi(db, skip=skip, limit=limit)
    return schools


@router.get("/public", response_model=List[schemas.School])
def read_schools_public(
    db: Session = Depends(deps.get_db),
    skip: int = 0,
    limit: int = 100,
) -> Any:
    """
    Retrieve schools without authentication (for registration).
    """
    schools = school_service.get_multi(db, skip=skip, limit=limit)
    return schools


@router.post("", response_model=schemas.School)
@router.post("/", response_model=schemas.School)
def create_school(
    *,
    db: Session = Depends(deps.get_db),
    school_in: schemas.SchoolCreate,
    current_user: User = Depends(deps.get_current_admin_user),
) -> Any:
    """
    Create new school.
    """
    school = school_service.create(db, obj_in=school_in)
    return school


@router.get("/{id}", response_model=schemas.School)
def read_school(
    *,
    db: Session = Depends(deps.get_db),
    id: int,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Get school by ID.
    """
    school = school_service.get(db, id=id)
    if not school:
        raise HTTPException(status_code=404, detail="School not found")
    return school


@router.put("/{id}", response_model=schemas.School)
def update_school(
    *,
    db: Session = Depends(deps.get_db),
    id: int,
    school_in: schemas.SchoolUpdate,
    current_user: User = Depends(deps.get_current_admin_user),
) -> Any:
    """
    Update a school.
    """
    school = school_service.get(db, id=id)
    if not school:
        raise HTTPException(status_code=404, detail="School not found")
    school = school_service.update(db, db_obj=school, obj_in=school_in)
    return school


@router.delete("/{id}", response_model=schemas.School)
def delete_school(
    *,
    db: Session = Depends(deps.get_db),
    id: int,
    current_user: User = Depends(deps.get_current_admin_user),
) -> Any:
    """
    Delete a school.
    """
    school = school_service.get(db, id=id)
    if not school:
        raise HTTPException(status_code=404, detail="School not found")
    school = school_service.delete(db, id=id)
    return school
