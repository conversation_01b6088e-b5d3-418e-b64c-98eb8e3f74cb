from typing import Any, Dict, List, Optional

from fastapi import APIRouter, Depends, HTTPException
from fastapi.responses import StreamingResponse
from sqlalchemy.orm import Session

from app import schemas
from app.models.user import User
from app.api import deps
from app.services.ai_assistant_service import ai_assistant_service
from app.services.ai_analytics_service import get_common_queries, get_user_engagement_metrics
from app.models.ai_analytics import InteractionType

router = APIRouter()


@router.post("/ask", response_model=schemas.AIAssistantResponse)
async def ask_assistant(
    *,
    query: schemas.AssistantQuery,
    db: Session = Depends(deps.get_db),
) -> Any:
    """
    Ask the AI assistant a question.
    """
    try:
        response = await ai_assistant_service.ask_general_assistant(
            query=query.query,
            user_id=None,
            db=db
        )
        return response
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get response from AI assistant: {str(e)}"
        )


@router.post("/stream/ask")
async def stream_ask_assistant(
    *,
    query: schemas.AssistantQuery,
    db: Session = Depends(deps.get_db),
) -> StreamingResponse:
    """
    Stream responses from the AI assistant.
    """
    async def event_generator():
        try:
            async for event in ai_assistant_service.stream_general_assistant(
                query=query.query,
                user_id=None,
                db=db
            ):
                yield f"data: {event.model_dump_json()}\n\n"
        except Exception as e:
            error_event = schemas.AIAssistantStreamEvent(
                type="error",
                error=f"Failed to stream response from AI assistant: {str(e)}"
            )
            yield f"data: {error_event.model_dump_json()}\n\n"

    return StreamingResponse(
        event_generator(),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
        }
    )


@router.get("/practice-help/{question_id}", response_model=schemas.AIAssistantResponse)
async def get_practice_help(
    *,
    question_id: int,
    db: Session = Depends(deps.get_db),
) -> Any:
    """
    Get AI help for a specific question in practice mode.
    """
    try:
        response = await ai_assistant_service.get_practice_help(question_id, db)
        return response
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get practice help: {str(e)}"
        )


@router.get("/stream/practice-help/{question_id}")
async def stream_practice_help(
    *,
    question_id: int,
    db: Session = Depends(deps.get_db),
) -> StreamingResponse:
    """
    Stream AI help for a specific question in practice mode.
    """
    async def event_generator():
        try:
            async for event in ai_assistant_service.stream_practice_help(question_id, db):
                yield f"data: {event.model_dump_json()}\n\n"
        except Exception as e:
            error_event = schemas.AIAssistantStreamEvent(
                type="error",
                error=f"Failed to stream practice help: {str(e)}"
            )
            yield f"data: {error_event.model_dump_json()}\n\n"

    return StreamingResponse(
        event_generator(),
        media_type="text/event-stream",
        headers={
            "Cache-Control": "no-cache",
            "Connection": "keep-alive",
        }
    )


@router.get("/analytics/common-queries", response_model=List[Dict[str, Any]])
async def get_ai_common_queries(
    *,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_admin_user),
    limit: int = 10,
    days: int = 30,
    interaction_type: Optional[InteractionType] = None,
) -> Any:
    """
    Get the most common queries asked to the AI assistant.
    Admin only.
    """
    return get_common_queries(
        db=db,
        limit=limit,
        days=days,
        interaction_type=interaction_type
    )


@router.get("/analytics/engagement", response_model=Dict[str, Any])
async def get_ai_engagement_metrics(
    *,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_admin_user),
    days: int = 30,
) -> Any:
    """
    Get user engagement metrics for the AI assistant.
    Admin only.
    """
    return get_user_engagement_metrics(db=db, days=days)
