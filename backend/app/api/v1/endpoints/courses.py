from typing import Any, List

from fastapi import APIRouter, Depends, HTTPException
from sqlalchemy.orm import Session

from app import schemas
from app.models.user import User
from app.api import deps
from app.services import course_service

router = APIRouter()


@router.get("", response_model=List[schemas.Course])
@router.get("/", response_model=List[schemas.Course])
def read_courses(
    db: Session = Depends(deps.get_db),
    skip: int = 0,
    limit: int = 100,
    school_id: int = None,
    department_id: int = None,
    search: str = None,
    prioritize_user_department: bool = False,
    prioritize_enrolled: bool = False,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Retrieve courses with optional filtering by school or department.

    - If search is provided, returns courses matching the search query
    - If school_id is provided, returns courses for that school
    - If department_id is provided, returns courses for that department
    - If prioritize_enrolled is True, returns all courses but prioritizes those the user is enrolled in
    - If prioritize_user_department is True, returns all courses but prioritizes those from the user's department
    - Otherwise, returns all courses
    """
    if search:
        courses = course_service.search_courses(db, search_query=search, skip=skip, limit=limit)
    elif school_id:
        courses = course_service.get_by_school(db, school_id=school_id, skip=skip, limit=limit)
    elif department_id:
        courses = course_service.get_by_department(db, department_id=department_id, skip=skip, limit=limit)
    elif prioritize_enrolled:
        courses = course_service.get_prioritized_enrolled_courses(
            db, user_id=current_user.id, skip=skip, limit=limit
        )
    elif prioritize_user_department and hasattr(current_user, 'department_id') and current_user.department_id:
        courses = course_service.get_prioritized_by_department(
            db, user_department_id=current_user.department_id, skip=skip, limit=limit
        )
    else:
        courses = course_service.get_multi(db, skip=skip, limit=limit)
    return courses


@router.post("", response_model=schemas.Course)
@router.post("/", response_model=schemas.Course)
def create_course(
    *,
    db: Session = Depends(deps.get_db),
    course_in: schemas.CourseCreate,
    current_user: User = Depends(deps.get_current_admin_user),
) -> Any:
    """
    Create new course.
    """
    course = course_service.create(db, obj_in=course_in)
    return course


@router.get("/enrolled", response_model=List[schemas.Course])
def read_enrolled_courses(
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Get courses that the current user has interacted with through:
    - Enrollment
    - Taking exams
    - Practice sessions (question attempts)
    - Flashcard usage
    - Other learning activities
    """
    courses = course_service.get_enrolled_courses_for_user(db, user_id=current_user.id)
    return courses


@router.get("/{id}", response_model=schemas.Course)
def read_course(
    *,
    db: Session = Depends(deps.get_db),
    id: int,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Get course by ID.
    """
    course = course_service.get(db, id=id)
    if not course:
        raise HTTPException(status_code=404, detail="Course not found")
    return course


@router.put("/{id}", response_model=schemas.Course)
def update_course(
    *,
    db: Session = Depends(deps.get_db),
    id: int,
    course_in: schemas.CourseUpdate,
    current_user: User = Depends(deps.get_current_admin_user),
) -> Any:
    """
    Update a course.
    """
    course = course_service.get(db, id=id)
    if not course:
        raise HTTPException(status_code=404, detail="Course not found")
    course = course_service.update(db, db_obj=course, obj_in=course_in)
    return course


@router.delete("/{id}", response_model=schemas.Course)
def delete_course(
    *,
    db: Session = Depends(deps.get_db),
    id: int,
    current_user: User = Depends(deps.get_current_admin_user),
) -> Any:
    """
    Delete a course.
    """
    course = course_service.get(db, id=id)
    if not course:
        raise HTTPException(status_code=404, detail="Course not found")
    course = course_service.delete(db, id=id)
    return course


@router.post("/{id}/enroll", response_model=schemas.Course)
def enroll_in_course(
    *,
    db: Session = Depends(deps.get_db),
    id: int,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Enroll current user in a course.
    """
    course = course_service.get(db, id=id)
    if not course:
        raise HTTPException(status_code=404, detail="Course not found")

    # Get the full user object from the database
    user = db.query(User).filter(User.id == current_user.id).first()
    if not user:
        raise HTTPException(status_code=404, detail="User not found")

    # Check if user is already enrolled using the user_course association table directly
    from app.models.user import user_course

    existing_enrollment = db.query(user_course).filter(
        user_course.c.user_id == user.id,
        user_course.c.course_id == course.id
    ).first()

    if existing_enrollment:
        raise HTTPException(status_code=400, detail="User already enrolled in this course")

    # Enroll user using the association table directly
    db.execute(
        user_course.insert().values(
            user_id=user.id,
            course_id=course.id
        )
    )
    db.commit()
    db.refresh(course)

    return course
