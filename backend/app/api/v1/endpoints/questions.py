from typing import Any, List

from fastapi import APIRouter, Depends, HTTPException, UploadFile, File, Form
from fastapi.responses import Response
from sqlalchemy.orm import Session

from app import schemas
from app.models.user import User, UserRole
from app.models.question import DifficultyLevel, QuestionType
from app.api import deps
from app.services import question_service
from app.services.csv_question_service import csv_question_service
from app.services.cloudinary_service import cloudinary_service

router = APIRouter()


@router.get("", response_model=List[schemas.Question])
@router.get("/", response_model=List[schemas.Question])
def read_questions(
    db: Session = Depends(deps.get_db),
    skip: int = 0,
    limit: int = 100,
    course_id: int = None,
    difficulty: DifficultyLevel = None,
    question_type: QuestionType = None,
    topic: str = None,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Retrieve questions with optional filtering.
    """
    questions = question_service.get_multi(
        db,
        skip=skip,
        limit=limit,
        course_id=course_id,
        difficulty=difficulty,
        question_type=question_type,
        topic=topic
    )
    return questions


@router.post("", response_model=schemas.Question)
@router.post("/", response_model=schemas.Question)
def create_question(
    *,
    db: Session = Depends(deps.get_db),
    question_in: schemas.QuestionCreate,
    current_user: User = Depends(deps.get_current_tutor_user),
) -> Any:
    """
    Create new question.
    """
    question = question_service.create(
        db,
        obj_in=question_in,
        created_by_id=current_user.id
    )
    return question


@router.get("/{id}", response_model=schemas.Question)
def read_question(
    *,
    db: Session = Depends(deps.get_db),
    id: int,
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Get question by ID.
    """
    question = question_service.get(db, id=id)
    if not question:
        raise HTTPException(status_code=404, detail="Question not found")
    return question


@router.post("/by-ids", response_model=List[schemas.Question])
def get_questions_by_ids(
    *,
    db: Session = Depends(deps.get_db),
    question_ids: List[int],
    current_user: User = Depends(deps.get_current_active_user),
) -> Any:
    """
    Get multiple questions by their IDs.
    """
    questions = []
    for question_id in question_ids:
        question = question_service.get(db, id=question_id)
        if question:
            questions.append(question)
    return questions


@router.put("/{id}", response_model=schemas.Question)
def update_question(
    *,
    db: Session = Depends(deps.get_db),
    id: int,
    question_in: schemas.QuestionUpdate,
    current_user: User = Depends(deps.get_current_tutor_user),
) -> Any:
    """
    Update a question.
    """
    question = question_service.get(db, id=id)
    if not question:
        raise HTTPException(status_code=404, detail="Question not found")

    # Only the creator or an admin can update the question
    if question.created_by_id != current_user.id and current_user.role != UserRole.ADMIN:
        raise HTTPException(status_code=403, detail="Not enough permissions")

    question = question_service.update(db, db_obj=question, obj_in=question_in)
    return question


@router.get("/{id}/usage-stats")
def get_question_usage_stats(
    *,
    db: Session = Depends(deps.get_db),
    id: int,
    current_user: User = Depends(deps.get_current_tutor_user),
) -> Any:
    """
    Get usage statistics for a question (how many student attempts, bookmarks, etc.).
    Useful for admins to understand the impact of deleting a question.
    """
    question = question_service.get(db, id=id)
    if not question:
        raise HTTPException(status_code=404, detail="Question not found")

    # Only the creator or an admin can view usage stats
    if question.created_by_id != current_user.id and current_user.role != UserRole.ADMIN:
        raise HTTPException(status_code=403, detail="Not enough permissions")

    stats = question_service.get_question_usage_stats(db, id=id)
    return {
        "question_id": id,
        "usage_stats": stats,
        "warning": "Deleting this question will remove all related student progress data." if stats["total_references"] > 0 else None
    }


@router.delete("/{id}", response_model=schemas.Question)
def delete_question(
    *,
    db: Session = Depends(deps.get_db),
    id: int,
    current_user: User = Depends(deps.get_current_tutor_user),
) -> Any:
    """
    Delete a question and all related student progress data.
    """
    question = question_service.get(db, id=id)
    if not question:
        raise HTTPException(status_code=404, detail="Question not found")

    # Only the creator or an admin can delete the question
    if question.created_by_id != current_user.id and current_user.role != UserRole.ADMIN:
        raise HTTPException(status_code=403, detail="Not enough permissions")

    try:
        question = question_service.delete(db, id=id)
        if not question:
            raise HTTPException(status_code=404, detail="Question not found")
        return question
    except ValueError as e:
        raise HTTPException(status_code=400, detail=str(e))
    except Exception as e:
        raise HTTPException(status_code=500, detail="An error occurred while deleting the question")


@router.post("/upload-pdf", response_model=List[schemas.Question])
async def upload_pdf_for_questions(
    *,
    db: Session = Depends(deps.get_db),
    file: UploadFile = File(...),
    course_id: int,
    question_type: QuestionType,
    difficulty: DifficultyLevel,
    num_questions: int = 5,
    current_user: User = Depends(deps.get_current_tutor_user),
) -> Any:
    """
    DEPRECATED: Use the student tools Gemini Files API instead.
    This endpoint has been replaced with the new Gemini AI-powered PDF processing.
    """
    raise HTTPException(
        status_code=status.HTTP_410_GONE,
        detail="PDF upload for question generation has been replaced with Gemini AI processing. Please use the student tools section."
    )


@router.post("/upload-csv", response_model=schemas.question.CSVUploadResponse)
async def upload_csv_questions(
    *,
    db: Session = Depends(deps.get_db),
    file: UploadFile = File(...),
    course_id: int = Form(...),
    question_type: QuestionType = Form(...),
    current_user: User = Depends(deps.get_current_admin_user),
) -> Any:
    """
    Upload a CSV file containing questions for bulk import.
    Only admins can upload CSV files.
    """
    # Validate file
    is_valid, error_message = await csv_question_service.validate_csv_file(file)
    if not is_valid:
        raise HTTPException(status_code=400, detail=error_message)

    # Validate course exists first
    course_errors = await csv_question_service.validate_course(db, course_id)
    if course_errors:
        return schemas.question.CSVUploadResponse(
            success=False,
            total_rows=0,
            created_questions=0,
            validation_errors=course_errors,
            creation_errors=[],
            questions=[]
        )

    # Parse CSV content
    rows, validation_errors = await csv_question_service.parse_csv_content(file, course_id, question_type)

    if validation_errors and not rows:
        # If there are validation errors and no valid rows, return error
        return schemas.question.CSVUploadResponse(
            success=False,
            total_rows=0,
            created_questions=0,
            validation_errors=validation_errors,
            creation_errors=[],
            questions=[]
        )

    # Create questions
    created_questions, creation_errors = await csv_question_service.create_questions_from_csv(
        db, rows, current_user.id
    )

    success = len(created_questions) > 0 and len(creation_errors) == 0

    return schemas.question.CSVUploadResponse(
        success=success,
        total_rows=len(rows),
        created_questions=len(created_questions),
        validation_errors=validation_errors,
        creation_errors=creation_errors,
        questions=created_questions
    )


@router.get("/csv-template")
async def download_csv_template(
    question_type: QuestionType,
    current_user: User = Depends(deps.get_current_active_user),
) -> Response:
    """
    Download a CSV template for bulk question upload.
    Available to all authenticated users for reference.
    """
    template_content = csv_question_service.generate_csv_template(question_type)

    filename = f"questions_template_{question_type.value}.csv"
    return Response(
        content=template_content,
        media_type="text/csv",
        headers={"Content-Disposition": f"attachment; filename={filename}"}
    )


@router.post("/{question_id}/upload-image")
async def upload_question_image(
    question_id: int,
    file: UploadFile = File(...),
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_admin_user),
) -> Any:
    """
    Upload an image for a specific question.
    Only admins can upload question images.
    """
    # Check if question exists
    question = question_service.get(db, id=question_id)
    if not question:
        raise HTTPException(
            status_code=404,
            detail="Question not found"
        )

    try:
        # Upload image to Cloudinary
        upload_result = await cloudinary_service.upload_image(
            file,
            folder=f"questions/{question_id}",
            transformation=cloudinary_service.get_optimized_transformations()["question_display"]
        )

        # Update question with image URL
        question_update = schemas.question.QuestionUpdate(media_url=upload_result["url"])
        updated_question = question_service.update(db, db_obj=question, obj_in=question_update)

        return {
            "message": "Image uploaded successfully",
            "question_id": question_id,
            "image_url": upload_result["url"],
            "image_metadata": upload_result
        }

    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to upload image: {str(e)}"
        )


@router.post("/bulk-upload-images")
async def bulk_upload_question_images(
    files: List[UploadFile] = File(...),
    question_ids: List[int] = Form(...),
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_admin_user),
) -> Any:
    """
    Upload images for multiple questions in bulk.
    Files and question_ids should be in the same order.
    Only admins can upload question images.
    """
    if len(files) != len(question_ids):
        raise HTTPException(
            status_code=400,
            detail="Number of files must match number of question IDs"
        )

    results = []

    for file, question_id in zip(files, question_ids):
        try:
            # Check if question exists
            question = question_service.get(db, id=question_id)
            if not question:
                results.append({
                    "question_id": question_id,
                    "filename": file.filename,
                    "success": False,
                    "error": "Question not found"
                })
                continue

            # Upload image to Cloudinary
            upload_result = await cloudinary_service.upload_image(
                file,
                folder=f"questions/{question_id}",
                transformation=cloudinary_service.get_optimized_transformations()["question_display"]
            )

            # Update question with image URL
            question_update = schemas.question.QuestionUpdate(media_url=upload_result["url"])
            question_service.update(db, db_obj=question, obj_in=question_update)

            results.append({
                "question_id": question_id,
                "filename": file.filename,
                "success": True,
                "image_url": upload_result["url"],
                "image_metadata": upload_result
            })

        except Exception as e:
            results.append({
                "question_id": question_id,
                "filename": file.filename,
                "success": False,
                "error": str(e)
            })

    successful_uploads = sum(1 for r in results if r["success"])

    return {
        "message": f"Processed {len(files)} files. {successful_uploads} successful uploads.",
        "results": results,
        "total_files": len(files),
        "successful_uploads": successful_uploads,
        "failed_uploads": len(files) - successful_uploads
    }


@router.post("/upload-csv-with-images", response_model=schemas.question.CSVUploadResponse)
async def upload_csv_questions_with_images(
    *,
    db: Session = Depends(deps.get_db),
    csv_file: UploadFile = File(...),
    course_id: int = Form(...),
    question_type: QuestionType = Form(...),
    image_files: List[UploadFile] = File(default=[]),
    image_mapping: str = Form(default="{}"),  # JSON string mapping image filenames to question indices
    current_user: User = Depends(deps.get_current_admin_user),
) -> Any:
    """
    Upload a CSV file containing questions with optional images for bulk import.
    Only admins can upload CSV files with images.

    image_mapping should be a JSON string like: {"image1.jpg": "1", "image2.png": "What is the capital?"}
    where keys are image filenames and values are either question row numbers (1-based) or question content.
    """
    # Validate CSV file
    is_valid, error_message = await csv_question_service.validate_csv_file(csv_file)
    if not is_valid:
        raise HTTPException(status_code=400, detail=error_message)

    # Validate course exists first
    course_errors = await csv_question_service.validate_course(db, course_id)
    if course_errors:
        return schemas.question.CSVUploadResponse(
            success=False,
            total_rows=0,
            created_questions=0,
            validation_errors=course_errors,
            creation_errors=[],
            questions=[]
        )

    # Parse CSV content
    rows, validation_errors = await csv_question_service.parse_csv_content(csv_file, course_id, question_type)

    if validation_errors and not rows:
        return schemas.question.CSVUploadResponse(
            success=False,
            total_rows=0,
            created_questions=0,
            validation_errors=validation_errors,
            creation_errors=[],
            questions=[]
        )

    # Parse image mapping
    try:
        import json
        parsed_image_mapping = json.loads(image_mapping) if image_mapping != "{}" else {}
    except json.JSONDecodeError:
        return schemas.question.CSVUploadResponse(
            success=False,
            total_rows=len(rows),
            created_questions=0,
            validation_errors=["Invalid image mapping JSON format"],
            creation_errors=[],
            questions=[]
        )

    # Create questions with images
    created_questions, creation_errors = await csv_question_service.create_questions_with_images(
        db, rows, current_user.id, image_files, parsed_image_mapping
    )

    success = len(created_questions) > 0 and len(creation_errors) == 0

    return schemas.question.CSVUploadResponse(
        success=success,
        total_rows=len(rows),
        created_questions=len(created_questions),
        validation_errors=validation_errors,
        creation_errors=creation_errors,
        questions=created_questions
    )
