from typing import Any, List, Optional
from fastapi import APIRouter, Depends, HTTPException, Query, BackgroundTasks
from sqlalchemy.orm import Session

from app import schemas
from app.models.user import User
from app.api import deps
from app.services.ml_recommendation_service import ml_recommendation_service
from app.models.ml_recommendations import MLRecommendation, UserLearningSession, UserFeatureVector

router = APIRouter()


@router.post("/track-behavior", response_model=schemas.UserBehaviorEvent)
def track_user_behavior(
    *,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_user),
    request: schemas.BehaviorTrackingRequest,
    background_tasks: BackgroundTasks,
) -> Any:
    """
    Track user behavior for ML model training.
    """
    try:
        event = ml_recommendation_service.track_user_behavior(
            db=db,
            user_id=current_user.id,
            event_type=request.event_type,
            content_type=request.content_type,
            content_id=request.content_id,
            course_id=request.course_id,
            topic=request.topic,
            difficulty=request.difficulty,
            is_correct=request.is_correct,
            response_time_seconds=request.response_time_seconds,
            confidence_level=request.confidence_level,
            session_id=request.session_id,
            device_type=request.device_type,
            event_metadata=request.event_metadata
        )
        
        return event
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to track behavior: {str(e)}"
        )


@router.get("/recommendations", response_model=schemas.RecommendationResponse)
def get_ml_recommendations(
    *,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_user),
    content_type: str = Query("question", description="Type of content to recommend"),
    limit: int = Query(10, ge=1, le=50, description="Number of recommendations"),
    course_id: Optional[int] = Query(None, description="Filter by course ID"),
) -> Any:
    """
    Get ML-generated personalized recommendations.
    """
    try:
        recommendations = ml_recommendation_service.get_user_recommendations(
            db=db,
            user_id=current_user.id,
            content_type=content_type,
            limit=limit
        )
        
        # Get user features for additional context
        user_features = db.query(UserFeatureVector).filter(
            UserFeatureVector.user_id == current_user.id
        ).first()
        
        return schemas.RecommendationResponse(
            recommendations=recommendations,
            total_count=len(recommendations),
            model_info={
                "type": "hybrid_ml",
                "version": "1.0",
                "description": "Combines collaborative filtering and content-based recommendations"
            },
            user_features=user_features
        )
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get recommendations: {str(e)}"
        )


@router.get("/learning-insights", response_model=schemas.LearningInsightsResponse)
def get_learning_insights(
    *,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_user),
) -> Any:
    """
    Get comprehensive learning insights based on ML analysis.
    """
    try:
        # Get user features
        user_features = db.query(UserFeatureVector).filter(
            UserFeatureVector.user_id == current_user.id
        ).first()
        
        # Get recent recommendations
        recent_recommendations = db.query(MLRecommendation).filter(
            MLRecommendation.user_id == current_user.id
        ).order_by(MLRecommendation.created_at.desc()).limit(5).all()
        
        # Calculate performance trends
        performance_trends = {}
        learning_patterns = {}
        model_confidence = 0.0
        
        if user_features:
            # Performance trends
            performance_trends = {
                "accuracy_trend": user_features.accuracy_rate,
                "response_time_trend": user_features.avg_response_time,
                "study_frequency": user_features.study_frequency,
                "consistency": user_features.consistency_score,
                "improvement_rate": user_features.improvement_rate
            }
            
            # Learning patterns
            learning_patterns = {
                "preferred_difficulty": user_features.preferred_difficulty,
                "preferred_time": user_features.preferred_time_of_day,
                "study_pattern": "consistent" if user_features.consistency_score > 0.7 else "irregular",
                "challenge_preference": user_features.challenge_preference,
                "help_seeking": "high" if user_features.help_seeking_rate > 0.3 else "low",
                "topic_strengths": user_features.topic_performance
            }
            
            # Model confidence based on data availability
            model_confidence = min(user_features.feature_version * 0.1, 1.0)
        
        return schemas.LearningInsightsResponse(
            user_features=user_features,
            recent_recommendations=recent_recommendations,
            performance_trends=performance_trends,
            learning_patterns=learning_patterns,
            model_confidence=model_confidence
        )
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get learning insights: {str(e)}"
        )


@router.post("/session/start", response_model=schemas.UserLearningSession)
def start_learning_session(
    *,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_user),
    request: schemas.SessionStartRequest,
) -> Any:
    """
    Start a new learning session for tracking.
    """
    try:
        session = UserLearningSession(
            user_id=current_user.id,
            session_id=request.session_id,
            device_type=request.device_type,
            primary_course_id=request.primary_course_id
        )
        
        db.add(session)
        db.commit()
        db.refresh(session)
        
        return session
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to start session: {str(e)}"
        )


@router.post("/session/{session_id}/end", response_model=schemas.UserLearningSession)
def end_learning_session(
    *,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_user),
    session_id: str,
    request: schemas.SessionEndRequest,
) -> Any:
    """
    End a learning session and update metrics.
    """
    try:
        session = db.query(UserLearningSession).filter(
            UserLearningSession.session_id == session_id,
            UserLearningSession.user_id == current_user.id
        ).first()
        
        if not session:
            raise HTTPException(
                status_code=404,
                detail="Learning session not found"
            )
        
        # Update session metrics
        from datetime import datetime, timezone
        session.end_time = datetime.now(timezone.utc)
        session.total_questions = request.total_questions
        session.correct_answers = request.correct_answers
        session.total_flashcards = request.total_flashcards
        session.help_requests = request.help_requests
        session.bookmarks_added = request.bookmarks_added
        session.topics_covered = request.topics_covered
        
        # Calculate derived metrics
        if request.total_questions > 0:
            session.accuracy_rate = request.correct_answers / request.total_questions
        
        # Calculate engagement score
        engagement_factors = [
            min(request.total_questions / 10, 1.0),  # Questions attempted
            min(request.total_flashcards / 5, 1.0),  # Flashcards reviewed
            max(0, 1 - request.help_requests / 10),  # Help requests (inverse)
            min(request.bookmarks_added / 3, 1.0),   # Bookmarks added
        ]
        session.engagement_score = sum(engagement_factors) / len(engagement_factors)
        
        db.commit()
        db.refresh(session)
        
        return session
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to end session: {str(e)}"
        )


@router.post("/recommendations/{recommendation_id}/feedback")
def provide_recommendation_feedback(
    *,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_user),
    recommendation_id: int,
    feedback: schemas.MLRecommendationUpdate,
) -> Any:
    """
    Provide feedback on a recommendation for model improvement.
    """
    try:
        recommendation = db.query(MLRecommendation).filter(
            MLRecommendation.id == recommendation_id,
            MLRecommendation.user_id == current_user.id
        ).first()
        
        if not recommendation:
            raise HTTPException(
                status_code=404,
                detail="Recommendation not found"
            )
        
        # Update recommendation with feedback
        from datetime import datetime, timezone
        now = datetime.now(timezone.utc)
        
        if feedback.is_viewed is not None:
            recommendation.is_viewed = feedback.is_viewed
            if feedback.is_viewed and not recommendation.viewed_at:
                recommendation.viewed_at = now
        
        if feedback.is_accepted is not None:
            recommendation.is_accepted = feedback.is_accepted
            if feedback.is_accepted and not recommendation.accepted_at:
                recommendation.accepted_at = now
        
        if feedback.is_completed is not None:
            recommendation.is_completed = feedback.is_completed
            if feedback.is_completed and not recommendation.completed_at:
                recommendation.completed_at = now
        
        if feedback.user_rating is not None:
            recommendation.user_rating = feedback.user_rating
        
        db.commit()
        
        return {"message": "Feedback recorded successfully"}
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to record feedback: {str(e)}"
        )


@router.post("/recommendations/refresh", response_model=schemas.RecommendationResponse)
def refresh_recommendations(
    *,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_user),
    content_type: str = Query("question", description="Type of content to recommend"),
) -> Any:
    """
    Force refresh recommendations for the current user.
    """
    try:
        recommendations = ml_recommendation_service.refresh_user_recommendations(
            db=db,
            user_id=current_user.id,
            content_type=content_type
        )

        # Get user features for additional context
        user_features = db.query(UserFeatureVector).filter(
            UserFeatureVector.user_id == current_user.id
        ).first()

        return schemas.RecommendationResponse(
            recommendations=recommendations,
            total_count=len(recommendations),
            model_info={
                "type": "hybrid_ml_refreshed",
                "version": "1.0",
                "description": "Freshly generated recommendations based on latest user activity"
            },
            user_features=user_features
        )
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to refresh recommendations: {str(e)}"
        )


@router.get("/user-features", response_model=schemas.UserFeatureVector)
def get_user_features(
    *,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_user),
) -> Any:
    """
    Get the current user's feature vector and learning characteristics.
    """
    try:
        user_features = db.query(UserFeatureVector).filter(
            UserFeatureVector.user_id == current_user.id
        ).first()

        if not user_features:
            raise HTTPException(
                status_code=404,
                detail="User features not found. Please interact with the system to generate features."
            )

        return user_features
    except HTTPException:
        # Re-raise HTTPExceptions (like 404) without modification
        raise
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get user features: {str(e)}"
        )


@router.get("/model-performance", response_model=List[schemas.ModelPerformanceResponse])
def get_model_performance(
    *,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_admin_user),  # Admin only
) -> Any:
    """
    Get performance metrics for all ML models (Admin only).
    """
    try:
        from app.models.ml_recommendations import MLModelMetadata
        from sqlalchemy import func
        
        models = db.query(MLModelMetadata).all()
        
        performance_data = []
        for model in models:
            # Get recommendation count for this model
            rec_count = db.query(func.count(MLRecommendation.id)).filter(
                MLRecommendation.model_name == model.model_name
            ).scalar() or 0
            
            # Calculate user satisfaction from ratings
            avg_rating = db.query(func.avg(MLRecommendation.user_rating)).filter(
                MLRecommendation.model_name == model.model_name,
                MLRecommendation.user_rating.isnot(None)
            ).scalar()
            
            user_satisfaction = (avg_rating / 5.0) if avg_rating else None
            
            performance_data.append(schemas.ModelPerformanceResponse(
                model_name=model.model_name,
                model_version=model.version,
                accuracy=model.accuracy,
                precision=model.precision,
                recall=model.recall,
                f1_score=model.f1_score,
                training_data_size=model.training_data_size,
                last_retrained=model.last_retrained,
                is_active=model.is_active,
                recommendation_count=rec_count,
                user_satisfaction=user_satisfaction
            ))
        
        return performance_data
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to get model performance: {str(e)}"
        )


@router.post("/retrain-models")
def trigger_model_retraining(
    *,
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_admin_user),  # Admin only
    background_tasks: BackgroundTasks,
) -> Any:
    """
    Trigger retraining of ML models (Admin only).
    """
    try:
        # Add retraining task to background
        background_tasks.add_task(
            _retrain_models_background,
            db
        )
        
        return {"message": "Model retraining initiated in background"}
    except Exception as e:
        raise HTTPException(
            status_code=500,
            detail=f"Failed to trigger retraining: {str(e)}"
        )


def _retrain_models_background(db: Session):
    """Background task for model retraining"""
    try:
        # This would implement the actual model retraining logic
        # For now, we'll just log that retraining was triggered
        import logging
        logger = logging.getLogger(__name__)
        logger.info("Model retraining initiated")
        
        # In a real implementation, this would:
        # 1. Collect recent user behavior data
        # 2. Retrain collaborative filtering models
        # 3. Update content-based recommendation models
        # 4. Evaluate model performance
        # 5. Deploy new models if they perform better
        
    except Exception as e:
        import logging
        logger = logging.getLogger(__name__)
        logger.error(f"Model retraining failed: {str(e)}")
