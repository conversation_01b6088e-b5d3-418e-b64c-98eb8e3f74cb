from fastapi import APIRouter

from app.api.v1.endpoints import auth, users, schools, departments, courses, questions, sessions, tutors, ai, student_progress, ai_assistant, gamification, student_tools, debug, admin, admin_flashcards, booking_requests, ml_recommendations, chat, notifications, uploads, content_sharing, question_flags

api_router = APIRouter()
api_router.include_router(auth.router, prefix="/auth", tags=["authentication"])
api_router.include_router(admin.router, prefix="/admin", tags=["admin"])
api_router.include_router(admin_flashcards.router, prefix="/admin/flashcards", tags=["admin-flashcards"])
api_router.include_router(users.router, prefix="/users", tags=["users"])
api_router.include_router(schools.router, prefix="/schools", tags=["schools"])
api_router.include_router(departments.router, prefix="/departments", tags=["departments"])
api_router.include_router(courses.router, prefix="/courses", tags=["courses"])
api_router.include_router(questions.router, prefix="/questions", tags=["questions"])
api_router.include_router(sessions.router, prefix="/sessions", tags=["sessions"])
api_router.include_router(tutors.router, prefix="/tutors", tags=["tutors"])
api_router.include_router(ai.router, prefix="/ai", tags=["ai"])
api_router.include_router(ai_assistant.router, prefix="/ai-assistant", tags=["ai-assistant"])
api_router.include_router(student_tools.router, prefix="/student-tools", tags=["student-tools"])
api_router.include_router(student_progress.router, prefix="/student-progress", tags=["student-progress"])
api_router.include_router(gamification.router, prefix="/gamification", tags=["gamification"])
api_router.include_router(booking_requests.router, prefix="/booking-requests", tags=["booking-requests"])
api_router.include_router(chat.router, prefix="/chat", tags=["chat"])
api_router.include_router(ml_recommendations.router, prefix="/ml-recommendations", tags=["ml-recommendations"])
api_router.include_router(notifications.router, prefix="/notifications", tags=["notifications"])
api_router.include_router(uploads.router, prefix="/uploads", tags=["uploads"])
api_router.include_router(content_sharing.router, prefix="/content-sharing", tags=["content-sharing"])
api_router.include_router(question_flags.router, prefix="/question-flags", tags=["question-flags"])
api_router.include_router(debug.router, prefix="/debug", tags=["debug"])
