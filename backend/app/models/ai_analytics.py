import enum
from datetime import datetime, timezone
from typing import Dict, Optional

from sqlalchemy import <PERSON><PERSON><PERSON>, <PERSON>umn, DateTime, Enum, Foreign<PERSON>ey, Integer, Text, JSON
from sqlalchemy.orm import relationship

from app.db.base_class import Base

# Use timezone-aware UTC now function to replace deprecated datetime.utcnow
def utc_now():
    return datetime.now(timezone.utc)


class InteractionType(str, enum.Enum):
    """Type of AI assistant interaction."""
    GENERAL = "general"
    PRACTICE = "practice"
    COURSE = "course"


class AIAssistantInteraction(Base):
    """Model for tracking AI assistant interactions."""
    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey("user.id"), nullable=True)
    query = Column(Text, nullable=False)
    response = Column(Text, nullable=False)
    interaction_type = Column(Enum(InteractionType), nullable=False)
    interaction_metadata = Column(JSON, nullable=True)  # Renamed from metadata to avoid conflict
    course_id = Column(<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>("course.id"), nullable=True)
    question_id = Column(Integer, ForeignKey("question.id"), nullable=True)
    response_time_ms = Column(Integer, nullable=True)
    success = Column(Boolean, default=True)
    timestamp = Column(DateTime, default=utc_now)
