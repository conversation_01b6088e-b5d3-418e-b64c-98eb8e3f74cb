from datetime import datetime, timezone
from typing import Optional, List, TYPE_CHECKING

from sqlalchemy import ForeignKey
from sqlalchemy.orm import Mapped, mapped_column, relationship

from app.db.base_class import Base

if TYPE_CHECKING:
    from app.models.user import User
    from app.models.question import Question
    from app.models.course import Course

# Use timezone-aware UTC now function to replace deprecated datetime.utcnow
def utc_now():
    return datetime.now(timezone.utc)


class StudentQuestionAttempt(Base):
    """Model to track student attempts at questions"""
    id: Mapped[int] = mapped_column(primary_key=True, index=True)
    student_id: Mapped[int] = mapped_column(ForeignKey("user.id"))
    question_id: Mapped[int] = mapped_column(ForeignKey("question.id"))
    is_correct: Mapped[bool]
    selected_answer: Mapped[Optional[str]] = mapped_column(nullable=True)
    attempt_time: Mapped[datetime] = mapped_column(default=utc_now)

    # Relationships - temporarily commented out to fix import issues
    # student = relationship("User", back_populates="question_attempts")
    # question = relationship("Question", back_populates="student_attempts")


class StudentBookmark(Base):
    """Model to track student bookmarks"""
    id: Mapped[int] = mapped_column(primary_key=True, index=True)
    student_id: Mapped[int] = mapped_column(ForeignKey("user.id"))
    question_id: Mapped[int] = mapped_column(ForeignKey("question.id"))
    created_at: Mapped[datetime] = mapped_column(default=utc_now)
    notes: Mapped[Optional[str]] = mapped_column(nullable=True)

    # Relationships - temporarily commented out to fix import issues
    # student = relationship("User", back_populates="bookmarks")
    # question = relationship("Question", back_populates="bookmarks")


class StudentExam(Base):
    """Model to track student exam sessions"""
    id: Mapped[int] = mapped_column(primary_key=True, index=True)
    student_id: Mapped[int] = mapped_column(ForeignKey("user.id"))
    course_id: Mapped[Optional[int]] = mapped_column(ForeignKey("course.id"), nullable=True)
    start_time: Mapped[datetime] = mapped_column(default=utc_now)
    end_time: Mapped[Optional[datetime]] = mapped_column(nullable=True)
    score: Mapped[Optional[float]] = mapped_column(nullable=True)
    total_questions: Mapped[int]
    correct_answers: Mapped[Optional[int]] = mapped_column(nullable=True)

    # Fields for note-generated content
    is_note_generated: Mapped[bool] = mapped_column(default=False)
    note_job_id: Mapped[Optional[int]] = mapped_column(nullable=True)
    course_name: Mapped[Optional[str]] = mapped_column(nullable=True)

    # Relationships - temporarily commented out to fix import issues
    # student = relationship("User", back_populates="exams")
    # course = relationship("Course", back_populates="exams")  # This one was already commented out in course.py
    attempts = relationship("StudentExamAttempt", back_populates="exam")


class StudentExamAttempt(Base):
    """Model to track individual question attempts within an exam"""
    id: Mapped[int] = mapped_column(primary_key=True, index=True)
    exam_id: Mapped[int] = mapped_column(ForeignKey("studentexam.id"))
    question_id: Mapped[int] = mapped_column(ForeignKey("question.id"))
    is_correct: Mapped[bool]
    selected_answer: Mapped[Optional[str]] = mapped_column(nullable=True)
    time_spent_seconds: Mapped[Optional[int]] = mapped_column(nullable=True)  # Time spent on this question

    # Relationships - temporarily commented out to fix import issues
    exam = relationship("StudentExam", back_populates="attempts")
    # question = relationship("Question", back_populates="exam_attempts")  # This was already commented out in question.py
