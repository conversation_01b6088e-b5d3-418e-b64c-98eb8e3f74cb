from datetime import datetime, timezone
from typing import List, Optional, TYPE_CHECKING

from sqlalchemy import Foreign<PERSON>ey
from sqlalchemy.orm import Mapped, mapped_column, relationship

from app.db.base_class import Base
from app.models.user import user_course

# Use timezone-aware UTC now function to replace deprecated datetime.utcnow
def utc_now():
    return datetime.now(timezone.utc)

if TYPE_CHECKING:
    from app.models.user import User
    from app.models.question import Question
    from app.models.session import Session
    from app.models.department import Department
    from app.models.ai_analytics import AIAssistantInteraction
    from app.models.tutor import TutorProfile
    from app.models.admin_flashcard import AdminFlashCard


class Course(Base):
    id: Mapped[int] = mapped_column(primary_key=True, index=True)
    name: Mapped[str] = mapped_column(index=True)
    description: Mapped[str]
    code: Mapped[str] = mapped_column(index=True)
    is_active: Mapped[bool] = mapped_column(default=True)
    created_at: Mapped[datetime] = mapped_column(default=utc_now)
    updated_at: Mapped[datetime] = mapped_column(default=utc_now, onupdate=utc_now)

    # Relationships
    school_id: Mapped[int] = mapped_column(ForeignKey("school.id"))
    school: Mapped["School"] = relationship("School", back_populates="courses")

    department_id: Mapped[Optional[int]] = mapped_column(ForeignKey("department.id"), nullable=True)
    department: Mapped[Optional["Department"]] = relationship("Department", back_populates="courses")

    users: Mapped[List["User"]] = relationship(secondary=user_course, back_populates="courses")
    questions: Mapped[List["Question"]] = relationship("Question", back_populates="course")
    sessions: Mapped[List["Session"]] = relationship("Session", back_populates="course")

    # Tutors who can teach this course
    tutors: Mapped[List["TutorProfile"]] = relationship(
        "TutorProfile",
        secondary="tutor_specialization",
        back_populates="specializations"
    )
    # exams: Mapped[List["StudentExam"]] = relationship("StudentExam", back_populates="course")  # Temporarily removed
    # AI interactions removed to fix circular dependency

    # Student tools relationships removed - now using course_name instead of course_id

    # Admin flashcards
    admin_flashcards: Mapped[List["AdminFlashCard"]] = relationship("AdminFlashCard", back_populates="course")

    # Booking requests
    booking_requests: Mapped[List["BookingRequest"]] = relationship("BookingRequest", back_populates="course")
