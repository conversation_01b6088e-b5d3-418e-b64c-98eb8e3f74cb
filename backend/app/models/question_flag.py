from datetime import datetime, timezone
from enum import Enum
from typing import TYPE_CHECKING, List
from sqlalchemy import <PERSON><PERSON><PERSON>, Integer, String, DateTime, Text, Boolean
from sqlalchemy.orm import Mapped, mapped_column, relationship
from sqlalchemy import Enum as SQLAlchemyEnum

from app.db.base_class import Base

# Use timezone-aware UTC now function
def utc_now():
    return datetime.now(timezone.utc)

if TYPE_CHECKING:
    from app.models.user import User
    from app.models.question import Question


class FlagReason(str, Enum):
    """Reasons for flagging a question"""
    WRONG_ANSWER = "wrong_answer"
    INCORRECT_SPELLING = "incorrect_spelling"
    NOT_VISIBLE = "not_visible"
    NO_DIAGRAM = "no_diagram"
    INCORRECT_RENDERING = "incorrect_rendering"
    NO_OPTIONS = "no_options"
    UNCLEAR_QUESTION = "unclear_question"
    DUPLICATE_QUESTION = "duplicate_question"
    INAPPROPRIATE_CONTENT = "inappropriate_content"
    OTHER = "other"


class FlagStatus(str, Enum):
    """Status of a flagged question"""
    PENDING = "pending"
    UNDER_REVIEW = "under_review"
    RESOLVED = "resolved"
    DISMISSED = "dismissed"


class QuestionFlag(Base):
    """Model to track flagged questions by students"""
    __tablename__ = "question_flag"
    
    id: Mapped[int] = mapped_column(primary_key=True, index=True)
    
    # Question and user associations
    question_id: Mapped[int] = mapped_column(ForeignKey("question.id"), index=True)
    student_id: Mapped[int] = mapped_column(ForeignKey("user.id"), index=True)
    
    # Flag details
    reasons: Mapped[str] = mapped_column(Text)  # JSON array of selected reasons
    description: Mapped[str] = mapped_column(Text, nullable=True)  # Optional additional description
    
    # Status tracking
    status: Mapped[FlagStatus] = mapped_column(
        SQLAlchemyEnum(FlagStatus), 
        default=FlagStatus.PENDING,
        index=True
    )
    
    # Admin handling
    reviewed_by: Mapped[int] = mapped_column(ForeignKey("user.id"), nullable=True)
    reviewed_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), nullable=True)
    admin_notes: Mapped[str] = mapped_column(Text, nullable=True)
    
    # Timestamps
    created_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), default=utc_now)
    updated_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), default=utc_now, onupdate=utc_now)
    
    # Relationships
    question = relationship("Question", back_populates="flags")
    student = relationship("User", foreign_keys=[student_id], back_populates="submitted_flags")
    reviewer = relationship("User", foreign_keys=[reviewed_by], back_populates="reviewed_flags")
    
    def __repr__(self):
        return f"<QuestionFlag(id={self.id}, question_id={self.question_id}, status={self.status})>"
    
    @property
    def is_pending(self) -> bool:
        """Check if the flag is still pending"""
        return self.status == FlagStatus.PENDING
    
    @property
    def is_resolved(self) -> bool:
        """Check if the flag has been resolved"""
        return self.status == FlagStatus.RESOLVED
    
    @property
    def reason_list(self) -> List[str]:
        """Get the list of reasons from JSON string"""
        import json
        try:
            return json.loads(self.reasons) if self.reasons else []
        except (json.JSONDecodeError, TypeError):
            return []
    
    def set_reasons(self, reason_list: List[str]) -> None:
        """Set the reasons as JSON string"""
        import json
        self.reasons = json.dumps(reason_list)
    
    def add_reason(self, reason: str) -> None:
        """Add a reason to the existing list"""
        current_reasons = self.reason_list
        if reason not in current_reasons:
            current_reasons.append(reason)
            self.set_reasons(current_reasons)
    
    def remove_reason(self, reason: str) -> None:
        """Remove a reason from the existing list"""
        current_reasons = self.reason_list
        if reason in current_reasons:
            current_reasons.remove(reason)
            self.set_reasons(current_reasons)


class FlagReasonDisplayNames:
    """Display names for flag reasons"""
    DISPLAY_NAMES = {
        FlagReason.WRONG_ANSWER: "Wrong Answer",
        FlagReason.INCORRECT_SPELLING: "Incorrect Spelling",
        FlagReason.NOT_VISIBLE: "Content Not Visible",
        FlagReason.NO_DIAGRAM: "Missing Diagram",
        FlagReason.INCORRECT_RENDERING: "Incorrect Math/Text Rendering",
        FlagReason.NO_OPTIONS: "Missing Answer Options",
        FlagReason.UNCLEAR_QUESTION: "Unclear Question",
        FlagReason.DUPLICATE_QUESTION: "Duplicate Question",
        FlagReason.INAPPROPRIATE_CONTENT: "Inappropriate Content",
        FlagReason.OTHER: "Other"
    }
    
    @classmethod
    def get_display_name(cls, reason: FlagReason) -> str:
        """Get display name for a reason"""
        return cls.DISPLAY_NAMES.get(reason, reason.value)
    
    @classmethod
    def get_all_reasons(cls) -> List[dict]:
        """Get all reasons with their display names"""
        return [
            {
                "value": reason.value,
                "label": cls.get_display_name(reason),
                "description": cls._get_description(reason)
            }
            for reason in FlagReason
        ]
    
    @classmethod
    def _get_description(cls, reason: FlagReason) -> str:
        """Get description for a reason"""
        descriptions = {
            FlagReason.WRONG_ANSWER: "The correct answer is marked incorrectly",
            FlagReason.INCORRECT_SPELLING: "There are spelling or grammar errors",
            FlagReason.NOT_VISIBLE: "Question content is not displaying properly",
            FlagReason.NO_DIAGRAM: "Question references a diagram that is missing",
            FlagReason.INCORRECT_RENDERING: "Mathematical expressions or formatting is broken",
            FlagReason.NO_OPTIONS: "Answer choices are missing or incomplete",
            FlagReason.UNCLEAR_QUESTION: "Question is confusing or ambiguous",
            FlagReason.DUPLICATE_QUESTION: "This question appears to be a duplicate",
            FlagReason.INAPPROPRIATE_CONTENT: "Content is inappropriate or offensive",
            FlagReason.OTHER: "Other issue not listed above"
        }
        return descriptions.get(reason, "")
