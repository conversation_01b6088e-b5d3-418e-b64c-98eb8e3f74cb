from datetime import datetime, timezone
from typing import List, Optional, TYPE_CHECKING
from enum import Enum

from sqlalchemy import <PERSON><PERSON><PERSON>, Table, Column, Integer
from sqlalchemy.orm import Mapped, mapped_column, relationship

from app.db.base_class import Base

# Use timezone-aware UTC now function to replace deprecated datetime.utcnow
def utc_now():
    return datetime.now(timezone.utc)

if TYPE_CHECKING:
    from app.models.user import User


class BadgeType(str, Enum):
    # Achievement Badges
    COURSE_COMPLETION = "course_completion"
    EXAM_EXCELLENCE = "exam_excellence"
    PRACTICE_MASTER = "practice_master"
    FIRST_PERFECT_SCORE = "first_perfect_score"
    QUESTION_MASTER = "question_master"
    QUICK_LEARNER = "quick_learner"
    CONSISTENT_LEARNER = "consistent_learner"

    # Streak Badges
    STREAK = "streak"
    STREAK_WARRIOR = "streak_warrior"
    STREAK_LEGEND = "streak_legend"
    STREAK_IMMORTAL = "streak_immortal"

    # Speed Badges
    SPEED_DEMON = "speed_demon"
    LIGHTNING_FAST = "lightning_fast"
    FLASH_MASTER = "flash_master"

    # Accuracy Badges
    SHARPSHOOTER = "sharpshooter"
    PRECISION_MASTER = "precision_master"
    PERFECTIONIST = "perfectionist"

    # Progress Badges
    EARLY_BIRD = "early_bird"
    NIGHT_OWL = "night_owl"
    WEEKEND_WARRIOR = "weekend_warrior"
    COMEBACK_KID = "comeback_kid"

    # Social Badges
    HELPFUL_PEER = "helpful_peer"
    MENTOR = "mentor"
    COMMUNITY_CHAMPION = "community_champion"

    # Special Badges
    FIRST_LOGIN = "first_login"
    PROFILE_COMPLETE = "profile_complete"
    EXPLORER = "explorer"
    COLLECTOR = "collector"
    OVERACHIEVER = "overachiever"

    # Milestone Badges
    HUNDRED_CLUB = "hundred_club"
    THOUSAND_CLUB = "thousand_club"
    ELITE_CLUB = "elite_club"
    LEGENDARY_CLUB = "legendary_club"


class Badge(Base):
    """Model for achievement badges that can be earned by users"""
    id: Mapped[int] = mapped_column(primary_key=True, index=True)
    name: Mapped[str]
    description: Mapped[str]
    badge_type: Mapped[BadgeType]
    icon_url: Mapped[Optional[str]] = mapped_column(nullable=True)
    points_value: Mapped[int] = mapped_column(default=0)
    created_at: Mapped[datetime] = mapped_column(default=utc_now)
    updated_at: Mapped[datetime] = mapped_column(default=utc_now, onupdate=utc_now)

    # Relationships
    user_badges: Mapped[List["UserBadge"]] = relationship("UserBadge", back_populates="badge")


class UserBadge(Base):
    """Model to track badges earned by users"""
    id: Mapped[int] = mapped_column(primary_key=True, index=True)
    user_id: Mapped[int] = mapped_column(ForeignKey("user.id"))
    badge_id: Mapped[int] = mapped_column(ForeignKey("badge.id"))
    earned_at: Mapped[datetime] = mapped_column(default=utc_now)
    
    # Relationships
    user = relationship("User", back_populates="badges")
    badge = relationship("Badge", back_populates="user_badges")


class PointsTransaction(Base):
    """Model to track points earned by users"""
    id: Mapped[int] = mapped_column(primary_key=True, index=True)
    user_id: Mapped[int] = mapped_column(ForeignKey("user.id"))
    points: Mapped[int]
    description: Mapped[str]
    transaction_type: Mapped[str]  # e.g., "exam_completion", "practice_session", "badge_earned"
    reference_id: Mapped[Optional[int]] = mapped_column(nullable=True)  # ID of related entity (exam, practice session, etc.)
    created_at: Mapped[datetime] = mapped_column(default=utc_now)
    
    # Relationships
    user = relationship("User", back_populates="points_transactions")


class UserLevel(Base):
    """Model for user experience levels"""
    id: Mapped[int] = mapped_column(primary_key=True, index=True)
    level_number: Mapped[int]
    name: Mapped[str]
    min_points: Mapped[int]
    max_points: Mapped[int]
    icon_url: Mapped[Optional[str]] = mapped_column(nullable=True)
    created_at: Mapped[datetime] = mapped_column(default=utc_now)
    updated_at: Mapped[datetime] = mapped_column(default=utc_now, onupdate=utc_now)


class UserStreak(Base):
    """Model to track user learning streaks"""
    id: Mapped[int] = mapped_column(primary_key=True, index=True)
    user_id: Mapped[int] = mapped_column(ForeignKey("user.id"))
    current_streak: Mapped[int] = mapped_column(default=0)
    longest_streak: Mapped[int] = mapped_column(default=0)
    last_activity_date: Mapped[datetime] = mapped_column(default=utc_now)
    created_at: Mapped[datetime] = mapped_column(default=utc_now)
    updated_at: Mapped[datetime] = mapped_column(default=utc_now, onupdate=utc_now)
    
    # Relationships
    user = relationship("User", back_populates="streak")
