"""
Upload task tracking model for background uploads
"""
from datetime import datetime
from enum import Enum
from typing import Optional, Dict, Any
from sqlalchemy import <PERSON>umn, Integer, String, DateTime, <PERSON>olean, Float, JSON, Text, ForeignKey
from sqlalchemy.orm import Mapped, mapped_column, relationship
from sqlalchemy.sql import func

from app.db.base_class import Base


class UploadStatus(str, Enum):
    """Upload task status"""
    PENDING = "pending"
    UPLOADING = "uploading"
    PROCESSING = "processing"
    COMPLETED = "completed"
    FAILED = "failed"
    CANCELLED = "cancelled"


class UploadTask(Base):
    """Model to track background upload tasks"""
    __tablename__ = "upload_task"
    
    id: Mapped[int] = mapped_column(primary_key=True, index=True)
    
    # Task identification
    task_id: Mapped[str] = mapped_column(String(255), unique=True, index=True)  # Celery task ID
    upload_id: Mapped[str] = mapped_column(String(255), unique=True, index=True)  # Frontend upload ID
    
    # User association
    student_id: Mapped[int] = mapped_column(ForeignKey("user.id"))
    
    # File information
    original_filename: Mapped[str] = mapped_column(String(500))
    file_size: Mapped[int]
    course_name: Mapped[Optional[str]] = mapped_column(String(255), nullable=True)
    
    # Task status and progress
    status: Mapped[UploadStatus] = mapped_column(String(50), default=UploadStatus.PENDING)
    progress: Mapped[float] = mapped_column(Float, default=0.0)  # 0-100
    
    # Timing information
    created_at: Mapped[datetime] = mapped_column(DateTime, default=func.now())
    started_at: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True)
    completed_at: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True)
    
    # Progress tracking
    bytes_uploaded: Mapped[int] = mapped_column(Integer, default=0)
    upload_speed: Mapped[Optional[float]] = mapped_column(Float, nullable=True)  # bytes per second
    estimated_time_remaining: Mapped[Optional[float]] = mapped_column(Float, nullable=True)  # seconds
    
    # Results and errors
    result: Mapped[Optional[Dict[str, Any]]] = mapped_column(JSON, nullable=True)
    error_message: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    error_details: Mapped[Optional[Dict[str, Any]]] = mapped_column(JSON, nullable=True)
    
    # Gemini file information (populated after successful upload)
    gemini_file_id: Mapped[Optional[str]] = mapped_column(String(255), nullable=True)
    
    # Additional metadata
    extra_metadata: Mapped[Optional[Dict[str, Any]]] = mapped_column(JSON, nullable=True)
    
    # Relationships
    student = relationship("User")
    
    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for API responses"""
        return {
            "id": self.upload_id,
            "task_id": self.task_id,
            "fileName": self.original_filename,
            "fileSize": self.file_size,
            "courseName": self.course_name,
            "status": self.status.value,
            "progress": self.progress,
            "startTime": int(self.created_at.timestamp() * 1000) if self.created_at else None,
            "completedTime": int(self.completed_at.timestamp() * 1000) if self.completed_at else None,
            "speed": self.upload_speed or 0,
            "estimatedTimeRemaining": self.estimated_time_remaining,
            "error": self.error_message,
            "result": self.result,
            "geminiFileId": self.gemini_file_id,
            "bytesUploaded": self.bytes_uploaded
        }
    
    def update_progress(
        self, 
        progress: float, 
        bytes_uploaded: int = None, 
        speed: float = None,
        estimated_time: float = None
    ):
        """Update progress information"""
        self.progress = min(100.0, max(0.0, progress))
        
        if bytes_uploaded is not None:
            self.bytes_uploaded = bytes_uploaded
            
        if speed is not None:
            self.upload_speed = speed
            
        if estimated_time is not None:
            self.estimated_time_remaining = estimated_time
            
        # Update status based on progress
        if self.progress >= 100.0 and self.status == UploadStatus.UPLOADING:
            self.status = UploadStatus.PROCESSING
    
    def mark_started(self):
        """Mark task as started"""
        self.status = UploadStatus.UPLOADING
        self.started_at = func.now()
    
    def mark_completed(self, result: Dict[str, Any] = None, gemini_file_id: str = None):
        """Mark task as completed"""
        self.status = UploadStatus.COMPLETED
        self.progress = 100.0
        self.completed_at = func.now()
        
        if result:
            self.result = result
            
        if gemini_file_id:
            self.gemini_file_id = gemini_file_id
    
    def mark_failed(self, error_message: str, error_details: Dict[str, Any] = None):
        """Mark task as failed"""
        self.status = UploadStatus.FAILED
        self.completed_at = func.now()
        self.error_message = error_message
        
        if error_details:
            self.error_details = error_details
    
    def mark_cancelled(self):
        """Mark task as cancelled"""
        self.status = UploadStatus.CANCELLED
        self.completed_at = func.now()
