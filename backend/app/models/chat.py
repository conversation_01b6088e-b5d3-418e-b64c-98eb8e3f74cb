from datetime import datetime
from enum import Enum
from typing import Optional

from sqlalchemy import Foreign<PERSON>ey, Text, Boolean, Enum as SQLAlchemyEnum
from sqlalchemy.orm import Mapped, mapped_column, relationship

from app.db.base_class import Base


class MessageType(str, Enum):
    TEXT = "text"
    IMAGE = "image"
    FILE = "file"
    SYSTEM = "system"


class MessageDeliveryStatus(str, Enum):
    """Message delivery status"""
    SENT = "sent"           # Message saved to database
    DELIVERED = "delivered" # Message delivered to recipient's client
    READ = "read"          # Message marked as read by recipient


class ChatRoom(Base):
    """Model for chat rooms between tutors and students"""
    id: Mapped[int] = mapped_column(primary_key=True, index=True)
    
    # Participants
    student_id: Mapped[int] = mapped_column(ForeignKey("user.id"))
    tutor_id: Mapped[int] = mapped_column(ForeignKey("user.id"))
    
    # Room details
    is_active: Mapped[bool] = mapped_column(<PERSON><PERSON><PERSON>, default=True)
    
    # Timestamps
    created_at: Mapped[datetime] = mapped_column(default=datetime.utcnow)
    updated_at: Mapped[datetime] = mapped_column(default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    student = relationship("User", foreign_keys=[student_id], back_populates="student_chat_rooms")
    tutor = relationship("User", foreign_keys=[tutor_id], back_populates="tutor_chat_rooms")
    messages = relationship("ChatMessage", back_populates="chat_room", cascade="all, delete-orphan")
    booking_requests = relationship("BookingRequest", back_populates="chat_room", foreign_keys="BookingRequest.chat_room_id")


class ChatMessage(Base):
    """Model for messages in chat rooms"""
    id: Mapped[int] = mapped_column(primary_key=True, index=True)

    # Message details
    content: Mapped[str] = mapped_column(Text)
    message_type: Mapped[MessageType] = mapped_column(default=MessageType.TEXT)

    # Sender and room
    sender_id: Mapped[int] = mapped_column(ForeignKey("user.id"))
    chat_room_id: Mapped[int] = mapped_column(ForeignKey("chatroom.id"))

    # Message status
    is_read: Mapped[bool] = mapped_column(Boolean, default=False)
    delivery_status: Mapped[MessageDeliveryStatus] = mapped_column(
        SQLAlchemyEnum(MessageDeliveryStatus),
        default=MessageDeliveryStatus.SENT,
        index=True
    )

    # Timestamps
    created_at: Mapped[datetime] = mapped_column(default=datetime.utcnow)
    updated_at: Mapped[datetime] = mapped_column(default=datetime.utcnow, onupdate=datetime.utcnow)
    delivered_at: Mapped[Optional[datetime]] = mapped_column(nullable=True)
    read_at: Mapped[Optional[datetime]] = mapped_column(nullable=True)

    # Relationships
    sender = relationship("User", back_populates="sent_messages")
    chat_room = relationship("ChatRoom", back_populates="messages")


class UserOnlineStatus(Base):
    """Model for tracking user online status"""
    id: Mapped[int] = mapped_column(primary_key=True, index=True)
    
    # User details
    user_id: Mapped[int] = mapped_column(ForeignKey("user.id"), unique=True)
    
    # Status details
    is_online: Mapped[bool] = mapped_column(Boolean, default=False)
    last_seen: Mapped[datetime] = mapped_column(default=datetime.utcnow)
    
    # WebSocket connection info
    connection_id: Mapped[Optional[str]] = mapped_column(nullable=True)
    
    # Timestamps
    created_at: Mapped[datetime] = mapped_column(default=datetime.utcnow)
    updated_at: Mapped[datetime] = mapped_column(default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    user = relationship("User", back_populates="online_status")
