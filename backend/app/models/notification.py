from datetime import datetime
from enum import Enum
from typing import Optional, TYPE_CHECKING

from sqlalchemy import Enum as SQLAlchemyEnum, ForeignKey, Text
from sqlalchemy.orm import Mapped, mapped_column, relationship

from app.db.base_class import Base

if TYPE_CHECKING:
    from app.models.user import User


class NotificationType(str, Enum):
    """Types of notifications"""
    BOOKING_REQUEST = "booking_request"
    BOOKING_ACCEPTED = "booking_accepted"
    BOOKING_REJECTED = "booking_rejected"
    BOOKING_CANCELLED = "booking_cancelled"
    SYSTEM = "system"
    ACHIEVEMENT = "achievement"
    REMINDER = "reminder"
    CONTENT_SHARED = "CONTENT_SHARED"
    CONTENT_SHARE_ACCEPTED = "CONTENT_SHARE_ACCEPTED"
    CONTENT_SHARE_REJECTED = "CONTENT_SHARE_REJECTED"


class Notification(Base):
    """Model for user notifications"""
    id: Mapped[int] = mapped_column(primary_key=True, index=True)
    
    # User who receives the notification
    user_id: Mapped[int] = mapped_column(ForeignKey("user.id"), index=True)
    
    # Notification details
    type: Mapped[NotificationType] = mapped_column(SQLAlchemyEnum(NotificationType))
    title: Mapped[str] = mapped_column(index=True)
    message: Mapped[str] = mapped_column(Text)
    
    # Status
    is_read: Mapped[bool] = mapped_column(default=False, index=True)
    
    # Optional reference to related objects
    related_id: Mapped[Optional[int]] = mapped_column(nullable=True)  # e.g., booking_request_id
    related_type: Mapped[Optional[str]] = mapped_column(nullable=True)  # e.g., "booking_request"
    
    # Timestamps
    created_at: Mapped[datetime] = mapped_column(default=datetime.utcnow, index=True)
    read_at: Mapped[Optional[datetime]] = mapped_column(nullable=True)
    
    # Relationships
    user = relationship("User", back_populates="notifications")
