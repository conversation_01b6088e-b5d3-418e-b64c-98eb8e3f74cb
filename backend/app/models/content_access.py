"""
Simplified content access models for sharing system.
Instead of duplicating content, we grant users access to existing content with metadata.
"""

from datetime import datetime, timezone
from enum import Enum
from typing import Optional, TYPE_CHECKING
import secrets

from sqlalchemy import Enum as SQLAlchemyEnum, <PERSON><PERSON><PERSON>, String, Text, <PERSON>olean, UniqueConstraint
from sqlalchemy.orm import Mapped, mapped_column, relationship

from app.db.base_class import Base

# Use timezone-aware UTC now function
def utc_now():
    return datetime.now(timezone.utc)

if TYPE_CHECKING:
    from app.models.user import User


class ContentType(str, Enum):
    """Types of content that can be shared"""
    MCQ = "MCQ"  # Questions from Question model
    FLASHCARD = "FLASHCARD"  # GeneratedFlashcard model
    SUMMARY = "SUMMARY"  # GeneratedNoteExplanation model


class AccessType(str, Enum):
    """How the user got access to this content"""
    SHARED_LINK = "SHARED_LINK"  # Via public share link
    EMAIL_INVITATION = "EMAIL_INVITATION"  # Via email invitation
    DIRECT_SHARE = "DIRECT_SHARE"  # Directly shared by owner


def generate_share_token() -> str:
    """Generate a secure random token for sharing"""
    return secrets.token_urlsafe(32)


class ContentAccess(Base):
    """
    Model for granting users access to existing content.
    This replaces the complex SharedContent system with a simple access permission model.
    """
    __tablename__ = "content_access"
    
    id: Mapped[int] = mapped_column(primary_key=True, index=True)
    
    # Content reference
    content_type: Mapped[ContentType] = mapped_column(SQLAlchemyEnum(ContentType))
    content_id: Mapped[int]  # ID of the actual content (question.id, generated_flashcard.id, etc.)
    
    # Access control
    user_id: Mapped[int] = mapped_column(ForeignKey("user.id"))  # User who has access
    shared_by_id: Mapped[int] = mapped_column(ForeignKey("user.id"))  # User who shared the content
    access_type: Mapped[AccessType] = mapped_column(SQLAlchemyEnum(AccessType))
    
    # Sharing metadata
    share_token: Mapped[Optional[str]] = mapped_column(String(64), nullable=True, index=True)
    share_message: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    
    # Permissions
    can_edit: Mapped[bool] = mapped_column(Boolean, default=False)  # Always False for shared content
    can_delete: Mapped[bool] = mapped_column(Boolean, default=False)  # Always False for shared content
    
    # Status
    is_active: Mapped[bool] = mapped_column(Boolean, default=True)
    accessed_at: Mapped[Optional[datetime]] = mapped_column(nullable=True)  # Last time user accessed this content
    
    # Timestamps
    created_at: Mapped[datetime] = mapped_column(default=utc_now)
    updated_at: Mapped[datetime] = mapped_column(default=utc_now, onupdate=utc_now)
    
    # Relationships
    user = relationship("User", foreign_keys=[user_id], back_populates="content_access")
    shared_by = relationship("User", foreign_keys=[shared_by_id])
    
    # Constraints
    __table_args__ = (
        UniqueConstraint('content_type', 'content_id', 'user_id', name='unique_content_access_per_user'),
    )


class ShareInvitation(Base):
    """
    Model for tracking email invitations to access content.
    Much simpler than the previous system.
    """
    __tablename__ = "content_share_invitation"
    
    id: Mapped[int] = mapped_column(primary_key=True, index=True)
    
    # Content reference
    content_type: Mapped[ContentType] = mapped_column(SQLAlchemyEnum(ContentType))
    content_id: Mapped[int]
    content_ids: Mapped[Optional[str]] = mapped_column(Text, nullable=True)  # JSON array of content IDs for batch sharing
    
    # Invitation details
    invited_email: Mapped[str] = mapped_column(String(255), index=True)
    shared_by_id: Mapped[int] = mapped_column(ForeignKey("user.id"))
    invitation_token: Mapped[str] = mapped_column(String(64), unique=True, default=generate_share_token, index=True)
    share_message: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    
    # Status tracking
    is_accepted: Mapped[bool] = mapped_column(Boolean, default=False)
    accepted_at: Mapped[Optional[datetime]] = mapped_column(nullable=True)
    accepted_by_id: Mapped[Optional[int]] = mapped_column(ForeignKey("user.id"), nullable=True)
    
    # Timestamps
    created_at: Mapped[datetime] = mapped_column(default=utc_now)
    expires_at: Mapped[Optional[datetime]] = mapped_column(nullable=True)  # Optional expiration
    
    # Relationships
    shared_by = relationship("User", foreign_keys=[shared_by_id])
    accepted_by = relationship("User", foreign_keys=[accepted_by_id])


class PublicShare(Base):
    """
    Model for public share links that anyone can access.
    """
    __tablename__ = "public_share"

    id: Mapped[int] = mapped_column(primary_key=True, index=True)

    # Content reference
    content_type: Mapped[ContentType] = mapped_column(SQLAlchemyEnum(ContentType))
    content_id: Mapped[int]
    content_ids: Mapped[Optional[str]] = mapped_column(Text, nullable=True)  # JSON array of content IDs for batch sharing

    # Share details
    shared_by_id: Mapped[int] = mapped_column(ForeignKey("user.id"))
    share_token: Mapped[str] = mapped_column(String(64), unique=True, default=generate_share_token, index=True)
    share_message: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    
    # Settings
    is_active: Mapped[bool] = mapped_column(Boolean, default=True)
    access_count: Mapped[int] = mapped_column(default=0)  # Track how many times it's been accessed
    
    # Timestamps
    created_at: Mapped[datetime] = mapped_column(default=utc_now)
    expires_at: Mapped[Optional[datetime]] = mapped_column(nullable=True)  # Optional expiration
    
    # Relationships
    shared_by = relationship("User", foreign_keys=[shared_by_id])
    
    # Constraints
    __table_args__ = (
        UniqueConstraint('content_type', 'content_id', 'shared_by_id', name='unique_public_share_per_content'),
    )
