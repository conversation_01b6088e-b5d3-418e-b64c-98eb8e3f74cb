from datetime import datetime, timezone
from typing import TYPE_CHECKING

from sqlalchemy import <PERSON><PERSON><PERSON>
from sqlalchemy.orm import Mapped, mapped_column, relationship

from app.db.base_class import Base

# Use timezone-aware UTC now function to replace deprecated datetime.utcnow
def utc_now():
    return datetime.now(timezone.utc)

if TYPE_CHECKING:
    from app.models.user import User


class RefreshToken(Base):
    """Refresh token for JWT authentication"""
    __tablename__ = "refresh_token"

    id: Mapped[int] = mapped_column(primary_key=True, index=True)
    token: Mapped[str] = mapped_column(unique=True, index=True)
    user_id: Mapped[int] = mapped_column(ForeignKey("user.id"))
    expires_at: Mapped[datetime] = mapped_column()
    is_revoked: Mapped[bool] = mapped_column(default=False)
    created_at: Mapped[datetime] = mapped_column(default=utc_now)
    updated_at: Mapped[datetime] = mapped_column(default=utc_now, onupdate=utc_now)
    
    # Relationships
    user: Mapped["User"] = relationship("User", back_populates="refresh_tokens")
    
    @property
    def is_expired(self) -> bool:
        """Check if the refresh token is expired"""
        # Ensure both datetimes are timezone-aware for comparison
        now = datetime.now(timezone.utc)
        expires_at = self.expires_at

        # If expires_at is naive, assume it's UTC
        if expires_at.tzinfo is None:
            expires_at = expires_at.replace(tzinfo=timezone.utc)

        return now > expires_at
    
    @property
    def is_valid(self) -> bool:
        """Check if the refresh token is valid (not expired and not revoked)"""
        return not self.is_expired and not self.is_revoked
