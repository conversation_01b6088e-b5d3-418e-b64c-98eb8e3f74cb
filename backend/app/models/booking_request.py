from datetime import datetime
from enum import Enum
from typing import Optional

from sqlalchemy import Enum as SQLAlchemyEnum, ForeignKey, Text
from sqlalchemy.orm import Mapped, mapped_column, relationship

from app.db.base_class import Base


class BookingRequestStatus(str, Enum):
    PENDING = "pending"
    ACCEPTED = "accepted"
    DECLINED = "declined"
    CANCELLED = "cancelled"


class BookingRequest(Base):
    """Model for student booking requests to tutors"""
    id: Mapped[int] = mapped_column(primary_key=True, index=True)

    # Request details - simplified for button-based requests
    message: Mapped[Optional[str]] = mapped_column(Text, nullable=True)  # Optional message from student
    
    # Status and response
    status: Mapped[BookingRequestStatus] = mapped_column(
        SQLAlchemyEnum(BookingRequestStatus), 
        default=BookingRequestStatus.PENDING
    )
    tutor_response: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    response_date: Mapped[Optional[datetime]] = mapped_column(nullable=True)

    # Chat room created when request is accepted
    chat_room_id: Mapped[Optional[int]] = mapped_column(ForeignKey("chatroom.id"), nullable=True)

    # Timestamps
    created_at: Mapped[datetime] = mapped_column(default=datetime.utcnow)
    updated_at: Mapped[datetime] = mapped_column(default=datetime.utcnow, onupdate=datetime.utcnow)
    
    # Relationships
    student_id: Mapped[int] = mapped_column(ForeignKey("user.id"))
    student = relationship("User", foreign_keys=[student_id], back_populates="sent_booking_requests")
    
    tutor_id: Mapped[int] = mapped_column(ForeignKey("user.id"))
    tutor = relationship("User", foreign_keys=[tutor_id], back_populates="received_booking_requests")
    
    course_id: Mapped[Optional[int]] = mapped_column(ForeignKey("course.id"), nullable=True)
    course = relationship("Course", back_populates="booking_requests")

    # Chat room relationship
    chat_room = relationship("ChatRoom", back_populates="booking_requests", foreign_keys=[chat_room_id])

    # Optional: Link to created session if accepted
    session_id: Mapped[Optional[int]] = mapped_column(ForeignKey("session.id"), nullable=True)
    session = relationship("Session", back_populates="booking_request")
