from datetime import datetime, timezone
from typing import Optional, TYPE_CHECKING

from sqlalchemy import ForeignKey, Text, String
from sqlalchemy.orm import Mapped, mapped_column, relationship

from app.db.base_class import Base

# Use timezone-aware UTC now function
def utc_now():
    return datetime.now(timezone.utc)

if TYPE_CHECKING:
    from app.models.user import User


class GeneratedFlashcard(Base):
    """Model for flashcards generated from uploaded notes using Gemini"""
    __tablename__ = "generated_flashcard"
    
    id: Mapped[int] = mapped_column(primary_key=True, index=True)
    
    # Content
    front_content: Mapped[str] = mapped_column(Text)
    back_content: Mapped[str] = mapped_column(Text)
    topic: Mapped[Optional[str]] = mapped_column(String(255), nullable=True)
    difficulty: Mapped[Optional[str]] = mapped_column(String(50), nullable=True)  # easy, medium, hard
    card_type: Mapped[Optional[str]] = mapped_column(String(100), nullable=True)  # definition, concept, formula, etc.
    
    # User association
    student_id: Mapped[int] = mapped_column(ForeignKey("user.id"))
    
    # Source information
    course_name: Mapped[Optional[str]] = mapped_column(String(255), nullable=True)
    gemini_file_id: Mapped[Optional[str]] = mapped_column(String(255), nullable=True)  # Reference to source file
    
    # Metadata
    is_active: Mapped[bool] = mapped_column(default=True)
    created_at: Mapped[datetime] = mapped_column(default=utc_now)
    updated_at: Mapped[datetime] = mapped_column(default=utc_now, onupdate=utc_now)
    
    # Relationships
    student = relationship("User", back_populates="generated_flashcards")
