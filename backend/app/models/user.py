from datetime import datetime, timezone, date
from enum import Enum
from typing import List, Optional, TYPE_CHECKING

from sqlalchemy import Column, Enum as SQLAlchemyEnum, <PERSON><PERSON>ey, Integer, Table, Date
from sqlalchemy.orm import Mapped, mapped_column, relationship

from app.db.base_class import Base

# Use timezone-aware UTC now function to replace deprecated datetime.utcnow
def utc_now():
    return datetime.now(timezone.utc)

if TYPE_CHECKING:
    from app.models.school import School
    from app.models.session import Session
    from app.models.course import Course
    from app.models.question import Question
    from app.models.department import Department
    from app.models.student_progress import StudentQuestionAttempt, StudentBookmark, StudentExam
    from app.models.ai_analytics import AIAssistantInteraction
    from app.models.gamification import UserBadge, PointsTransaction, UserStreak

    from app.models.tutor import TutorProfile
    from app.models.booking_request import BookingRequest
    from app.models.notification import Notification
    from app.models.gemini_file import GeminiFile
    from app.models.generated_flashcard import GeneratedFlashcard
    from app.models.generated_note_explanation import GeneratedNoteExplanation
    from app.models.student_usage_limit import StudentUsageLimit
    from app.models.refresh_token import RefreshToken
    from app.models.shared_content import SharedContent, SharedContentAccess


class UserRole(str, Enum):
    STUDENT = "student"
    TUTOR = "tutor"
    ADMIN = "admin"


# Association tables
user_course = Table(
    "user_course",
    Base.metadata,
    Column("user_id", Integer, ForeignKey("user.id"), primary_key=True),
    Column("course_id", Integer, ForeignKey("course.id"), primary_key=True),
)


class Gender(str, Enum):
    MALE = "male"
    FEMALE = "female"
    OTHER = "other"


class User(Base):
    id: Mapped[int] = mapped_column(primary_key=True, index=True)
    email: Mapped[str] = mapped_column(unique=True, index=True)
    hashed_password: Mapped[str]
    full_name: Mapped[str]
    other_name: Mapped[Optional[str]] = mapped_column(nullable=True)
    role: Mapped[UserRole] = mapped_column(SQLAlchemyEnum(UserRole))
    is_active: Mapped[bool] = mapped_column(default=True)

    # Email verification fields
    email_verified: Mapped[bool] = mapped_column(default=False)
    email_verification_token: Mapped[Optional[str]] = mapped_column(nullable=True)
    email_verification_token_expires: Mapped[Optional[datetime]] = mapped_column(nullable=True)

    # Password reset fields
    password_reset_token: Mapped[Optional[str]] = mapped_column(nullable=True)
    password_reset_token_expires: Mapped[Optional[datetime]] = mapped_column(nullable=True)

    # Terms and conditions acceptance
    terms_accepted: Mapped[bool] = mapped_column(default=False)
    terms_accepted_at: Mapped[Optional[datetime]] = mapped_column(nullable=True)

    # Profile fields
    gender: Mapped[Optional[Gender]] = mapped_column(SQLAlchemyEnum(Gender), nullable=True)
    phone_number: Mapped[Optional[str]] = mapped_column(nullable=True)
    department: Mapped[Optional[str]] = mapped_column(nullable=True)  # Legacy field, kept for backward compatibility
    level: Mapped[Optional[str]] = mapped_column(nullable=True)
    date_of_birth: Mapped[Optional[date]] = mapped_column(Date, nullable=True)
    state_of_origin: Mapped[Optional[str]] = mapped_column(nullable=True)
    profile_picture_url: Mapped[Optional[str]] = mapped_column(nullable=True)
    profile_completed: Mapped[bool] = mapped_column(default=False)

    created_at: Mapped[datetime] = mapped_column(default=utc_now)
    updated_at: Mapped[datetime] = mapped_column(default=utc_now, onupdate=utc_now)

    # Relationships
    institution_id: Mapped[Optional[int]] = mapped_column(ForeignKey("school.id"), nullable=True)
    institution: Mapped[Optional["School"]] = relationship("School", back_populates="users")

    department_id: Mapped[Optional[int]] = mapped_column(ForeignKey("department.id"), nullable=True)
    department_obj: Mapped[Optional["Department"]] = relationship("Department", back_populates="users")

    courses: Mapped[List["Course"]] = relationship(secondary=user_course, back_populates="users")

    # Student-specific relationships
    student_sessions: Mapped[List["Session"]] = relationship(
        "Session",
        foreign_keys="[Session.student_id]",
        back_populates="student"
    )

    # Tutor-specific relationships
    tutor_sessions: Mapped[List["Session"]] = relationship(
        "Session",
        foreign_keys="[Session.tutor_id]",
        back_populates="tutor"
    )

    # Tutor profile relationship
    tutor_profile: Mapped[Optional["TutorProfile"]] = relationship(
        "TutorProfile",
        back_populates="user",
        uselist=False
    )

    questions: Mapped[List["Question"]] = relationship("Question", back_populates="created_by")

    # Gemini file uploads
    gemini_files: Mapped[List["GeminiFile"]] = relationship("GeminiFile", back_populates="student")

    # Generated flashcards
    generated_flashcards: Mapped[List["GeneratedFlashcard"]] = relationship("GeneratedFlashcard", back_populates="student")

    # Generated note explanations
    generated_note_explanations: Mapped[List["GeneratedNoteExplanation"]] = relationship("GeneratedNoteExplanation", back_populates="student")

    # Booking request relationships
    sent_booking_requests: Mapped[List["BookingRequest"]] = relationship(
        "BookingRequest",
        foreign_keys="BookingRequest.student_id",
        back_populates="student"
    )
    received_booking_requests: Mapped[List["BookingRequest"]] = relationship(
        "BookingRequest",
        foreign_keys="BookingRequest.tutor_id",
        back_populates="tutor"
    )

    # Chat relationships
    student_chat_rooms: Mapped[List["ChatRoom"]] = relationship(
        "ChatRoom",
        foreign_keys="ChatRoom.student_id",
        back_populates="student"
    )
    tutor_chat_rooms: Mapped[List["ChatRoom"]] = relationship(
        "ChatRoom",
        foreign_keys="ChatRoom.tutor_id",
        back_populates="tutor"
    )
    sent_messages: Mapped[List["ChatMessage"]] = relationship(
        "ChatMessage",
        back_populates="sender"
    )
    online_status: Mapped[Optional["UserOnlineStatus"]] = relationship(
        "UserOnlineStatus",
        back_populates="user",
        uselist=False
    )

    # Notifications
    notifications: Mapped[List["Notification"]] = relationship(
        "Notification",
        back_populates="user",
        order_by="desc(Notification.created_at)"
    )

    # New simplified content access relationships
    content_access: Mapped[List["ContentAccess"]] = relationship(
        "ContentAccess",
        foreign_keys="ContentAccess.user_id",
        back_populates="user"
    )

    # Student progress tracking - temporarily removed to fix import issues
    # question_attempts: Mapped[List["StudentQuestionAttempt"]] = relationship("StudentQuestionAttempt", back_populates="student")
    # bookmarks: Mapped[List["StudentBookmark"]] = relationship("StudentBookmark", back_populates="student")
    # exams: Mapped[List["StudentExam"]] = relationship("StudentExam", back_populates="student")

    # AI assistant interactions removed to fix circular dependency

    # Gamification relationships
    badges: Mapped[List["UserBadge"]] = relationship("UserBadge", back_populates="user")
    points_transactions: Mapped[List["PointsTransaction"]] = relationship("PointsTransaction", back_populates="user")
    streak: Mapped[Optional["UserStreak"]] = relationship("UserStreak", back_populates="user", uselist=False)

    # Student tools relationships - temporarily removed to fix import issues
    # notes: Mapped[List["StudentNote"]] = relationship("StudentNote", back_populates="student")
    # mcq_generation_jobs: Mapped[List["MCQGenerationJob"]] = relationship("MCQGenerationJob", back_populates="student")
    # flash_cards: Mapped[List["FlashCard"]] = relationship("FlashCard", back_populates="student")
    # flash_card_generation_jobs: Mapped[List["FlashCardGenerationJob"]] = relationship("FlashCardGenerationJob", back_populates="student")
    # summaries: Mapped[List["NoteSummary"]] = relationship("NoteSummary", back_populates="student")
    # summary_generation_jobs: Mapped[List["SummaryGenerationJob"]] = relationship("SummaryGenerationJob", back_populates="student")

    # Usage limits for students
    usage_limits = relationship("StudentUsageLimit", back_populates="student")

    # Refresh tokens for JWT authentication
    refresh_tokens: Mapped[List["RefreshToken"]] = relationship("RefreshToken", back_populates="user")

    # Question flagging relationships
    submitted_flags: Mapped[List["QuestionFlag"]] = relationship(
        "QuestionFlag",
        foreign_keys="QuestionFlag.student_id",
        back_populates="student"
    )
    reviewed_flags: Mapped[List["QuestionFlag"]] = relationship(
        "QuestionFlag",
        foreign_keys="QuestionFlag.reviewed_by",
        back_populates="reviewer"
    )

    # AI Recommendation relationships - temporarily removed to fix import issues
    # learning_profile: Mapped[Optional["UserLearningProfile"]] = relationship("UserLearningProfile", back_populates="user", uselist=False)
    # learning_streak: Mapped[Optional["LearningStreak"]] = relationship("LearningStreak", back_populates="user", uselist=False)

    # Calculated properties for gamification
    @property
    def total_points(self) -> int:
        """Calculate total points from all transactions"""
        if not hasattr(self, "_total_points"):
            self._total_points = sum(transaction.points for transaction in self.points_transactions)
        return self._total_points

    @property
    def current_level(self) -> Optional[dict]:
        """Get the user's current level based on total points"""
        from app.services.gamification_service import get_level_for_points
        return get_level_for_points(self.total_points)
