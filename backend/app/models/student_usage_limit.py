from datetime import datetime, timezone
from enum import Enum
from typing import TYPE_CHECKING

from sqlalchemy import <PERSON><PERSON><PERSON>, Integer, String, DateTime
from sqlalchemy.orm import Mapped, mapped_column, relationship
from sqlalchemy import Enum as SQLAlchemyEnum

from app.db.base_class import Base

# Use timezone-aware UTC now function
def utc_now():
    return datetime.now(timezone.utc)

if TYPE_CHECKING:
    from app.models.user import User


class UsageType(str, Enum):
    """Types of usage that can be tracked and limited"""
    PDF_UPLOAD = "pdf_upload"
    MCQ_GENERATION = "mcq_generation"
    FLASHCARD_GENERATION = "flashcard_generation"
    SUMMARY_GENERATION = "summary_generation"


class StudentUsageLimit(Base):
    """Model to track student usage limits per month"""
    __tablename__ = "student_usage_limit"
    
    id: Mapped[int] = mapped_column(primary_key=True, index=True)
    
    # User association
    student_id: Mapped[int] = mapped_column(ForeignKey("user.id"), index=True)
    
    # Usage tracking
    usage_type: Mapped[UsageType] = mapped_column(SQLAlchemyEnum(UsageType), index=True)
    usage_count: Mapped[int] = mapped_column(Integer, default=0)
    usage_limit: Mapped[int] = mapped_column(Integer, default=5)  # Default limit of 5 per month
    
    # Time period tracking (year-month format: YYYY-MM)
    usage_month: Mapped[str] = mapped_column(String(7), index=True)  # Format: "2024-01"
    
    # Timestamps
    created_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), default=utc_now)
    updated_at: Mapped[datetime] = mapped_column(DateTime(timezone=True), default=utc_now, onupdate=utc_now)
    
    # Relationships
    student = relationship("User", back_populates="usage_limits")
    
    def __repr__(self):
        return f"<StudentUsageLimit(student_id={self.student_id}, type={self.usage_type}, count={self.usage_count}/{self.usage_limit}, month={self.usage_month})>"
    
    @property
    def is_limit_reached(self) -> bool:
        """Check if the usage limit has been reached"""
        return self.usage_count >= self.usage_limit
    
    @property
    def remaining_usage(self) -> int:
        """Get remaining usage count"""
        return max(0, self.usage_limit - self.usage_count)
