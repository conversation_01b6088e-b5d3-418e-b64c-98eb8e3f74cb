from datetime import datetime
from enum import Enum
from typing import Optional

from sqlalchemy import Enum as SQLAlchemyEnum, ForeignKey
from sqlalchemy.orm import Mapped, mapped_column, relationship

from app.db.base_class import Base


class SessionType(str, Enum):
    ONLINE = "online"
    IN_PERSON = "in_person"


class SessionStatus(str, Enum):
    SCHEDULED = "scheduled"
    IN_PROGRESS = "in_progress"
    COMPLETED = "completed"
    CANCELLED = "cancelled"


class Session(Base):
    id: Mapped[int] = mapped_column(primary_key=True, index=True)
    title: Mapped[str]
    description: Mapped[str]
    session_type: Mapped[SessionType] = mapped_column(SQLAlchemyEnum(SessionType))
    status: Mapped[SessionStatus] = mapped_column(SQLAlchemyEnum(SessionStatus), default=SessionStatus.SCHEDULED)
    start_time: Mapped[datetime]
    end_time: Mapped[datetime]
    location: Mapped[Optional[str]] = mapped_column(nullable=True)  # For in-person sessions
    video_link: Mapped[Optional[str]] = mapped_column(nullable=True)  # For online sessions
    max_students: Mapped[int] = mapped_column(default=1)
    is_recurring: Mapped[bool] = mapped_column(default=False)
    recurrence_pattern: Mapped[Optional[str]] = mapped_column(nullable=True)  # e.g., "weekly", "biweekly"
    created_at: Mapped[datetime] = mapped_column(default=datetime.utcnow)
    updated_at: Mapped[datetime] = mapped_column(default=datetime.utcnow, onupdate=datetime.utcnow)

    # Relationships
    course_id: Mapped[int] = mapped_column(ForeignKey("course.id"))
    course = relationship("Course", back_populates="sessions")

    tutor_id: Mapped[int] = mapped_column(ForeignKey("user.id"))
    tutor = relationship("User", foreign_keys=[tutor_id], back_populates="tutor_sessions")

    student_id: Mapped[Optional[int]] = mapped_column(ForeignKey("user.id"), nullable=True)
    student = relationship("User", foreign_keys=[student_id], back_populates="student_sessions")

    # Booking request relationship (if session was created from a booking request)
    booking_request = relationship("BookingRequest", back_populates="session", uselist=False)
