from datetime import datetime, timezone
from typing import Optional, TYPE_CHECKING

from sqlalchemy import Foreign<PERSON>ey
from sqlalchemy.orm import Mapped, mapped_column, relationship

from app.db.base_class import Base

# Use timezone-aware UTC now function
def utc_now():
    return datetime.now(timezone.utc)

if TYPE_CHECKING:
    from app.models.user import User


class GeminiFile(Base):
    """Model to track files uploaded to Gemini Files API"""
    id: Mapped[int] = mapped_column(primary_key=True, index=True)
    
    # File identification
    gemini_file_id: Mapped[str] = mapped_column(unique=True, index=True)  # Gemini's file ID
    original_filename: Mapped[str]
    file_size: Mapped[int]
    mime_type: Mapped[str]
    
    # User association
    student_id: Mapped[int] = mapped_column(ForeignKey("user.id"))
    
    # Content metadata
    course_name: Mapped[Optional[str]] = mapped_column(nullable=True)
    
    # Lifecycle management
    upload_time: Mapped[datetime] = mapped_column(default=utc_now)
    auto_delete_time: Mapped[datetime]  # When we should delete from Gemini
    is_deleted: Mapped[bool] = mapped_column(default=False)
    
    # Timestamps
    created_at: Mapped[datetime] = mapped_column(default=utc_now)
    updated_at: Mapped[datetime] = mapped_column(default=utc_now, onupdate=utc_now)
    
    # Relationships
    student = relationship("User", back_populates="gemini_files")
