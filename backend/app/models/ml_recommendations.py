from datetime import datetime, timezone
from enum import Enum
from typing import Dict, List, Optional, TYPE_CHECKING

from sqlalchemy import <PERSON><PERSON><PERSON>, JSON, Float, Integer, String, Boolean, DateTime, Text
from sqlalchemy.orm import Mapped, mapped_column, relationship
from sqlalchemy import Enum as SQLAlchemyEnum

from app.db.base_class import Base

# Use timezone-aware UTC now function
def utc_now():
    return datetime.now(timezone.utc)

if TYPE_CHECKING:
    from app.models.user import User
    from app.models.course import Course
    from app.models.question import Question


class UserBehaviorEvent(Base):
    """Track all user interactions for ML training"""
    __tablename__ = "user_behavior_event"
    
    id: Mapped[int] = mapped_column(primary_key=True, index=True)
    user_id: Mapped[int] = mapped_column(ForeignKey("user.id"), index=True)
    
    # Event details
    event_type: Mapped[str] = mapped_column(String(50), index=True)  # mcq_attempt, flashcard_view, etc.
    content_type: Mapped[str] = mapped_column(String(50))  # question, flashcard, course
    content_id: Mapped[int] = mapped_column(Integer)  # ID of the content
    
    # Context
    course_id: Mapped[Optional[int]] = mapped_column(ForeignKey("course.id"), nullable=True)
    topic: Mapped[Optional[str]] = mapped_column(String(255), nullable=True)
    difficulty: Mapped[Optional[str]] = mapped_column(String(20), nullable=True)
    
    # Performance metrics
    is_correct: Mapped[Optional[bool]] = mapped_column(Boolean, nullable=True)
    response_time_seconds: Mapped[Optional[float]] = mapped_column(Float, nullable=True)
    confidence_level: Mapped[Optional[int]] = mapped_column(Integer, nullable=True)  # 1-5
    
    # Session context
    session_id: Mapped[Optional[str]] = mapped_column(String(255), nullable=True)
    device_type: Mapped[Optional[str]] = mapped_column(String(50), nullable=True)
    time_of_day: Mapped[Optional[int]] = mapped_column(Integer, nullable=True)  # Hour 0-23
    day_of_week: Mapped[Optional[int]] = mapped_column(Integer, nullable=True)  # 0-6
    
    # Additional metadata
    event_metadata: Mapped[Optional[Dict]] = mapped_column(JSON, nullable=True)
    
    # Timestamp
    created_at: Mapped[datetime] = mapped_column(DateTime, default=utc_now, index=True)
    
    # Relationships
    user = relationship("User")
    course = relationship("Course")


class UserFeatureVector(Base):
    """Store computed feature vectors for users"""
    __tablename__ = "user_feature_vector"
    
    id: Mapped[int] = mapped_column(primary_key=True, index=True)
    user_id: Mapped[int] = mapped_column(ForeignKey("user.id"), unique=True)
    
    # Learning behavior features
    avg_response_time: Mapped[float] = mapped_column(Float, default=0.0)
    accuracy_rate: Mapped[float] = mapped_column(Float, default=0.0)
    study_frequency: Mapped[float] = mapped_column(Float, default=0.0)  # sessions per week
    session_duration_avg: Mapped[float] = mapped_column(Float, default=0.0)  # minutes
    
    # Preference features
    preferred_difficulty: Mapped[Optional[str]] = mapped_column(String(20), nullable=True)
    preferred_time_of_day: Mapped[Optional[int]] = mapped_column(Integer, nullable=True)
    preferred_session_length: Mapped[Optional[int]] = mapped_column(Integer, nullable=True)
    
    # Performance features by topic (JSON storing topic -> performance mapping)
    topic_performance: Mapped[Dict] = mapped_column(JSON, default=dict)
    topic_interest: Mapped[Dict] = mapped_column(JSON, default=dict)
    
    # Engagement features
    help_seeking_rate: Mapped[float] = mapped_column(Float, default=0.0)
    bookmark_rate: Mapped[float] = mapped_column(Float, default=0.0)
    completion_rate: Mapped[float] = mapped_column(Float, default=0.0)
    
    # Learning progression features
    improvement_rate: Mapped[float] = mapped_column(Float, default=0.0)
    consistency_score: Mapped[float] = mapped_column(Float, default=0.0)
    challenge_preference: Mapped[float] = mapped_column(Float, default=0.5)  # 0=easy, 1=hard
    
    # Temporal features
    active_days_per_week: Mapped[float] = mapped_column(Float, default=0.0)
    peak_performance_hour: Mapped[Optional[int]] = mapped_column(Integer, nullable=True)
    
    # Feature vector (for similarity calculations)
    feature_vector: Mapped[List[float]] = mapped_column(JSON, default=list)
    
    # Metadata
    last_updated: Mapped[datetime] = mapped_column(DateTime, default=utc_now)
    feature_version: Mapped[int] = mapped_column(Integer, default=1)
    
    # Relationships
    user = relationship("User")


class ContentFeatureVector(Base):
    """Store computed feature vectors for content (questions, flashcards)"""
    __tablename__ = "content_feature_vector"
    
    id: Mapped[int] = mapped_column(primary_key=True, index=True)
    content_type: Mapped[str] = mapped_column(String(50), index=True)  # question, flashcard
    content_id: Mapped[int] = mapped_column(Integer, index=True)
    
    # Content characteristics
    difficulty_score: Mapped[float] = mapped_column(Float, default=0.5)
    topic: Mapped[Optional[str]] = mapped_column(String(255), nullable=True)
    course_id: Mapped[Optional[int]] = mapped_column(ForeignKey("course.id"), nullable=True)
    
    # Performance statistics
    avg_response_time: Mapped[float] = mapped_column(Float, default=0.0)
    success_rate: Mapped[float] = mapped_column(Float, default=0.0)
    attempt_count: Mapped[int] = mapped_column(Integer, default=0)
    
    # Engagement metrics
    view_count: Mapped[int] = mapped_column(Integer, default=0)
    bookmark_count: Mapped[int] = mapped_column(Integer, default=0)
    help_request_count: Mapped[int] = mapped_column(Integer, default=0)
    
    # Content features
    text_complexity: Mapped[Optional[float]] = mapped_column(Float, nullable=True)
    has_media: Mapped[bool] = mapped_column(Boolean, default=False)
    word_count: Mapped[Optional[int]] = mapped_column(Integer, nullable=True)
    
    # Feature vector
    feature_vector: Mapped[List[float]] = mapped_column(JSON, default=list)
    
    # Metadata
    last_updated: Mapped[datetime] = mapped_column(DateTime, default=utc_now)
    feature_version: Mapped[int] = mapped_column(Integer, default=1)
    
    # Relationships
    course = relationship("Course")


class MLRecommendation(Base):
    """Store ML-generated recommendations"""
    __tablename__ = "ml_recommendation"
    
    id: Mapped[int] = mapped_column(primary_key=True, index=True)
    user_id: Mapped[int] = mapped_column(ForeignKey("user.id"), index=True)
    
    # Recommendation details
    content_type: Mapped[str] = mapped_column(String(50))  # question, flashcard, topic
    content_id: Mapped[Optional[int]] = mapped_column(Integer, nullable=True)
    topic: Mapped[Optional[str]] = mapped_column(String(255), nullable=True)
    course_id: Mapped[Optional[int]] = mapped_column(ForeignKey("course.id"), nullable=True)
    
    # ML model outputs
    confidence_score: Mapped[float] = mapped_column(Float)  # 0-1
    predicted_performance: Mapped[float] = mapped_column(Float)  # Expected success rate
    difficulty_match: Mapped[float] = mapped_column(Float)  # How well difficulty matches user
    interest_score: Mapped[float] = mapped_column(Float)  # Predicted user interest
    
    # Recommendation metadata
    model_name: Mapped[str] = mapped_column(String(100))
    model_version: Mapped[str] = mapped_column(String(50))
    explanation: Mapped[Optional[str]] = mapped_column(Text, nullable=True)
    
    # User interaction
    is_viewed: Mapped[bool] = mapped_column(Boolean, default=False)
    is_accepted: Mapped[bool] = mapped_column(Boolean, default=False)
    is_completed: Mapped[bool] = mapped_column(Boolean, default=False)
    user_rating: Mapped[Optional[int]] = mapped_column(Integer, nullable=True)  # 1-5
    
    # Timestamps
    created_at: Mapped[datetime] = mapped_column(DateTime, default=utc_now)
    viewed_at: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True)
    accepted_at: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True)
    completed_at: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True)
    expires_at: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True)
    
    # Relationships
    user = relationship("User")
    course = relationship("Course")


class MLModelMetadata(Base):
    """Store metadata about trained ML models"""
    __tablename__ = "ml_model_metadata"
    
    id: Mapped[int] = mapped_column(primary_key=True, index=True)
    
    # Model identification
    model_name: Mapped[str] = mapped_column(String(100), unique=True)
    model_type: Mapped[str] = mapped_column(String(50))  # collaborative_filtering, content_based, hybrid
    version: Mapped[str] = mapped_column(String(50))
    
    # Model performance
    accuracy: Mapped[Optional[float]] = mapped_column(Float, nullable=True)
    precision: Mapped[Optional[float]] = mapped_column(Float, nullable=True)
    recall: Mapped[Optional[float]] = mapped_column(Float, nullable=True)
    f1_score: Mapped[Optional[float]] = mapped_column(Float, nullable=True)
    
    # Training metadata
    training_data_size: Mapped[int] = mapped_column(Integer)
    training_duration_seconds: Mapped[Optional[float]] = mapped_column(Float, nullable=True)
    hyperparameters: Mapped[Dict] = mapped_column(JSON, default=dict)
    
    # Model status
    is_active: Mapped[bool] = mapped_column(Boolean, default=False)
    is_deployed: Mapped[bool] = mapped_column(Boolean, default=False)
    
    # File paths
    model_file_path: Mapped[Optional[str]] = mapped_column(String(500), nullable=True)
    feature_scaler_path: Mapped[Optional[str]] = mapped_column(String(500), nullable=True)
    
    # Timestamps
    created_at: Mapped[datetime] = mapped_column(DateTime, default=utc_now)
    deployed_at: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True)
    last_retrained: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True)


class UserLearningSession(Base):
    """Track learning sessions for ML analysis"""
    __tablename__ = "user_learning_session"
    
    id: Mapped[int] = mapped_column(primary_key=True, index=True)
    user_id: Mapped[int] = mapped_column(ForeignKey("user.id"), index=True)
    session_id: Mapped[str] = mapped_column(String(255), unique=True)
    
    # Session metadata
    start_time: Mapped[datetime] = mapped_column(DateTime, default=utc_now)
    end_time: Mapped[Optional[datetime]] = mapped_column(DateTime, nullable=True)
    device_type: Mapped[Optional[str]] = mapped_column(String(50), nullable=True)
    
    # Session metrics
    total_questions: Mapped[int] = mapped_column(Integer, default=0)
    correct_answers: Mapped[int] = mapped_column(Integer, default=0)
    total_flashcards: Mapped[int] = mapped_column(Integer, default=0)
    help_requests: Mapped[int] = mapped_column(Integer, default=0)
    bookmarks_added: Mapped[int] = mapped_column(Integer, default=0)
    
    # Performance metrics
    avg_response_time: Mapped[Optional[float]] = mapped_column(Float, nullable=True)
    accuracy_rate: Mapped[Optional[float]] = mapped_column(Float, nullable=True)
    engagement_score: Mapped[Optional[float]] = mapped_column(Float, nullable=True)
    
    # Context
    primary_course_id: Mapped[Optional[int]] = mapped_column(ForeignKey("course.id"), nullable=True)
    topics_covered: Mapped[List[str]] = mapped_column(JSON, default=list)
    
    # Relationships
    user = relationship("User")
    course = relationship("Course")
