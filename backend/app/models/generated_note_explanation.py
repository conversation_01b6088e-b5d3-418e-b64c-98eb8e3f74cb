from datetime import datetime, timezone
from typing import Optional, TYPE_CHECKING

from sqlalchemy import Foreign<PERSON>ey, String, Text, JSON
from sqlalchemy.orm import Mapped, mapped_column, relationship

from app.db.base_class import Base

# Use timezone-aware UTC now function to replace deprecated datetime.utcnow
def utc_now():
    return datetime.now(timezone.utc)

if TYPE_CHECKING:
    from app.models.user import User


class GeneratedNoteExplanation(Base):
    """Model for note explanations generated from uploaded notes using Gemini"""
    __tablename__ = "generated_note_explanation"
    
    id: Mapped[int] = mapped_column(primary_key=True, index=True)
    
    # Content
    title: Mapped[str] = mapped_column(String(500))
    overview: Mapped[str] = mapped_column(Text)
    detailed_explanation: Mapped[str] = mapped_column(Text)  # Comprehensive explanation of the entire document
    comprehensive_summary: Mapped[str] = mapped_column(Text)
    key_concepts: Mapped[dict] = mapped_column(JSON)  # JSON array of key concepts
    main_topics: Mapped[dict] = mapped_column(JSON)  # JSON array of main topics
    word_count: Mapped[Optional[int]] = mapped_column(nullable=True)
    
    # User association
    student_id: Mapped[int] = mapped_column(ForeignKey("user.id"))
    
    # Source information
    course_name: Mapped[Optional[str]] = mapped_column(String(255), nullable=True)
    gemini_file_id: Mapped[Optional[str]] = mapped_column(String(255), nullable=True)  # Reference to source file
    original_filename: Mapped[Optional[str]] = mapped_column(String(255), nullable=True)
    
    # Metadata
    is_active: Mapped[bool] = mapped_column(default=True)
    created_at: Mapped[datetime] = mapped_column(default=utc_now)
    updated_at: Mapped[datetime] = mapped_column(default=utc_now, onupdate=utc_now)
    
    # Relationships
    student = relationship("User", back_populates="generated_note_explanations")
