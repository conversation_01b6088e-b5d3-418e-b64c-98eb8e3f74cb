"""
Timeout middleware to prevent hanging requests
"""
import asyncio
import time
from typing import Callable

from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.responses import JSONResponse

import logging

logger = logging.getLogger(__name__)


class TimeoutMiddleware(BaseHTTPMiddleware):
    """
    Middleware to add timeout protection to all requests
    """
    
    def __init__(
        self,
        app,
        timeout_seconds: float = 30.0,
        exclude_paths: list = None
    ):
        super().__init__(app)
        self.timeout_seconds = timeout_seconds
        self.exclude_paths = exclude_paths or [
            "/api/v1/chat/ws",  # WebSocket endpoints
            "/api/v1/health",   # Health checks
            "/health",          # Simple health checks
        ]

    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        # Skip timeout for excluded paths
        if any(request.url.path.startswith(path) for path in self.exclude_paths):
            return await call_next(request)

        # Skip timeout for WebSocket upgrades
        if request.headers.get("upgrade") == "websocket":
            return await call_next(request)

        start_time = time.time()
        
        try:
            # Apply timeout to the request
            response = await asyncio.wait_for(
                call_next(request),
                timeout=self.timeout_seconds
            )
            
            # Log slow requests
            elapsed = time.time() - start_time
            if elapsed > 10.0:  # Log requests taking more than 10 seconds
                logger.warning(
                    f"Slow request: {request.method} {request.url.path} took {elapsed:.2f}s"
                )
            
            return response
            
        except asyncio.TimeoutError:
            elapsed = time.time() - start_time
            logger.error(
                f"Request timeout: {request.method} {request.url.path} "
                f"exceeded {self.timeout_seconds}s (actual: {elapsed:.2f}s)"
            )
            
            return JSONResponse(
                status_code=504,
                content={
                    "detail": "Request timeout - the server took too long to respond",
                    "timeout_seconds": self.timeout_seconds,
                    "path": str(request.url.path)
                }
            )
            
        except Exception as e:
            elapsed = time.time() - start_time
            logger.error(
                f"Request error: {request.method} {request.url.path} "
                f"failed after {elapsed:.2f}s: {str(e)}"
            )
            raise


class DatabaseTimeoutMiddleware(BaseHTTPMiddleware):
    """
    Middleware to add database operation timeout protection
    """
    
    def __init__(self, app, db_timeout_seconds: float = 20.0):
        super().__init__(app)
        self.db_timeout_seconds = db_timeout_seconds

    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        # Add database timeout header for downstream services
        request.state.db_timeout = self.db_timeout_seconds
        
        try:
            response = await call_next(request)
            return response
        except Exception as e:
            # Log database-related errors
            if "timeout" in str(e).lower() or "connection" in str(e).lower():
                logger.error(
                    f"Database timeout/connection error for {request.method} "
                    f"{request.url.path}: {str(e)}"
                )
            raise
