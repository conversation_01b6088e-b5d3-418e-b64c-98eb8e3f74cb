"""
Middleware to configure file upload limits and memory management for FastAPI.
"""
import asyncio
import time
from starlette.middleware.base import BaseHTTPMiddleware
from fastapi import Fast<PERSON><PERSON>, Request, Response
from starlette.responses import JSONResponse
import logging

logger = logging.getLogger(__name__)

class FileUploadConfig(BaseHTTPMiddleware):
    """
    Middleware to configure file upload limits and memory management for FastAPI.

    This middleware sets the maximum file upload size and adds timeout protection
    for file upload operations to prevent hanging and memory leaks.
    """

    def __init__(
        self,
        app: FastAPI,
        max_upload_size: int = 30 * 1024 * 1024,  # 30MB default
        upload_timeout: float = 120.0,  # 2 minutes timeout for uploads
    ):
        super().__init__(app)
        self.max_upload_size = max_upload_size
        self.upload_timeout = upload_timeout
        logger.info(f"File upload limit set to {self.max_upload_size / (1024 * 1024):.1f}MB")
        logger.info(f"File upload timeout set to {self.upload_timeout}s")

    async def dispatch(self, request: Request, call_next):
        """
        Process the request and set upload limits with timeout protection.

        Args:
            request: The incoming request
            call_next: The next middleware or route handler

        Returns:
            The response from the next middleware or route handler
        """
        # Set the maximum upload size in the request state
        request.state.max_upload_size = self.max_upload_size
        request.state.upload_timeout = self.upload_timeout

        # Check if this is a file upload request
        content_type = request.headers.get("content-type", "")
        is_file_upload = "multipart/form-data" in content_type

        if is_file_upload:
            start_time = time.time()
            try:
                # Apply timeout to file upload requests
                response = await asyncio.wait_for(
                    call_next(request),
                    timeout=self.upload_timeout
                )

                # Log slow uploads
                elapsed = time.time() - start_time
                if elapsed > 30.0:  # Log uploads taking more than 30 seconds
                    logger.warning(
                        f"Slow file upload: {request.method} {request.url.path} took {elapsed:.2f}s"
                    )

                return response

            except asyncio.TimeoutError:
                elapsed = time.time() - start_time
                logger.error(
                    f"File upload timeout: {request.method} {request.url.path} "
                    f"exceeded {self.upload_timeout}s (actual: {elapsed:.2f}s)"
                )

                return JSONResponse(
                    status_code=408,
                    content={
                        "detail": "File upload timeout - upload took too long to complete",
                        "timeout_seconds": self.upload_timeout,
                        "path": str(request.url.path)
                    }
                )
        else:
            # Continue processing non-upload requests normally
            response = await call_next(request)
            return response
