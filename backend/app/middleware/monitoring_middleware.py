"""
Monitoring middleware to track request metrics and detect hanging issues
"""
import time
import asyncio
from typing import Callable

from fastapi import Request, Response
from starlette.middleware.base import BaseHTTPMiddleware

import logging

logger = logging.getLogger(__name__)


class MonitoringMiddleware(BaseHTTPMiddleware):
    """
    Middleware to monitor request performance and detect potential hanging issues
    """
    
    def __init__(self, app):
        super().__init__(app)
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        # Skip monitoring for health check endpoints to avoid recursion
        if request.url.path in ["/health", "/api/v1/health", "/api/v1/health/issues"]:
            return await call_next(request)
        
        start_time = time.time()
        
        try:
            # Process the request
            response = await call_next(request)
            
            # Calculate duration
            duration = time.time() - start_time
            
            # Record metrics
            await self._record_request_metrics(
                request, response, duration
            )
            
            # Log slow requests
            if duration > 5.0:  # Log requests taking more than 5 seconds
                logger.warning(
                    f"Slow request: {request.method} {request.url.path} "
                    f"took {duration:.2f}s (status: {response.status_code})"
                )
            
            return response
            
        except Exception as e:
            duration = time.time() - start_time
            
            # Record error metrics
            await self._record_error_metrics(
                request, e, duration
            )
            
            logger.error(
                f"Request error: {request.method} {request.url.path} "
                f"failed after {duration:.2f}s: {str(e)}"
            )
            
            raise
    
    async def _record_request_metrics(self, request: Request, response: Response, duration: float):
        """Record request metrics in health monitor"""
        try:
            from app.services.health_monitor import health_monitor
            
            health_monitor.record_request(
                duration=duration,
                path=request.url.path,
                status_code=response.status_code
            )
            
        except Exception as e:
            # Don't fail the request if monitoring fails
            logger.debug(f"Failed to record request metrics: {str(e)}")
    
    async def _record_error_metrics(self, request: Request, error: Exception, duration: float):
        """Record error metrics in health monitor"""
        try:
            from app.services.health_monitor import health_monitor
            
            health_monitor.record_request(
                duration=duration,
                path=request.url.path,
                status_code=500  # Assume 500 for unhandled exceptions
            )
            
        except Exception as e:
            # Don't fail the request if monitoring fails
            logger.debug(f"Failed to record error metrics: {str(e)}")


class HangingDetectionMiddleware(BaseHTTPMiddleware):
    """
    Middleware to detect and handle hanging requests
    """
    
    def __init__(self, app, hanging_threshold: float = 60.0):
        super().__init__(app)
        self.hanging_threshold = hanging_threshold
        self.active_requests = {}
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        # Skip for WebSocket and health check endpoints
        if (request.url.path.startswith("/api/v1/chat/ws") or
            request.headers.get("upgrade") == "websocket" or
            request.url.path in ["/health", "/api/v1/health", "/api/v1/health/issues"]):
            return await call_next(request)
        
        request_id = id(request)
        start_time = time.time()
        
        # Track active request
        self.active_requests[request_id] = {
            "start_time": start_time,
            "path": request.url.path,
            "method": request.method
        }
        
        try:
            # Start hanging detection task
            hanging_task = asyncio.create_task(
                self._detect_hanging_request(request_id)
            )
            
            # Process the request
            response = await call_next(request)
            
            # Cancel hanging detection
            hanging_task.cancel()
            
            return response
            
        except Exception as e:
            # Cancel hanging detection
            if 'hanging_task' in locals():
                hanging_task.cancel()
            raise
            
        finally:
            # Remove from active requests
            self.active_requests.pop(request_id, None)
    
    async def _detect_hanging_request(self, request_id: int):
        """Detect if a request is hanging"""
        try:
            await asyncio.sleep(self.hanging_threshold)
            
            # If we reach here, the request is hanging
            request_info = self.active_requests.get(request_id)
            if request_info:
                elapsed = time.time() - request_info["start_time"]
                logger.error(
                    f"HANGING REQUEST DETECTED: {request_info['method']} "
                    f"{request_info['path']} has been running for {elapsed:.2f}s"
                )
                
                # Log active requests for debugging
                active_count = len(self.active_requests)
                if active_count > 1:
                    logger.warning(f"Total active requests: {active_count}")
                    for rid, info in list(self.active_requests.items())[:5]:  # Log first 5
                        req_elapsed = time.time() - info["start_time"]
                        logger.warning(
                            f"  Active: {info['method']} {info['path']} "
                            f"({req_elapsed:.2f}s)"
                        )
                
        except asyncio.CancelledError:
            # Request completed normally
            pass
        except Exception as e:
            logger.error(f"Error in hanging detection: {str(e)}")


class ResourceMonitoringMiddleware(BaseHTTPMiddleware):
    """
    Middleware to monitor resource usage during requests
    """
    
    def __init__(self, app, memory_threshold_mb: float = 500.0):
        super().__init__(app)
        self.memory_threshold_mb = memory_threshold_mb
    
    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        # Skip for health checks and WebSocket
        if (request.url.path.startswith("/api/v1/chat/ws") or
            request.headers.get("upgrade") == "websocket" or
            request.url.path in ["/health", "/api/v1/health", "/api/v1/health/issues"]):
            return await call_next(request)
        
        try:
            import psutil
            process = psutil.Process()
            
            # Get memory usage before request
            memory_before = process.memory_info().rss / 1024 / 1024  # MB
            
            # Process request
            response = await call_next(request)
            
            # Get memory usage after request
            memory_after = process.memory_info().rss / 1024 / 1024  # MB
            memory_diff = memory_after - memory_before
            
            # Log high memory usage
            if memory_diff > self.memory_threshold_mb:
                logger.warning(
                    f"High memory usage: {request.method} {request.url.path} "
                    f"used {memory_diff:.2f}MB (before: {memory_before:.2f}MB, "
                    f"after: {memory_after:.2f}MB)"
                )
            
            return response
            
        except ImportError:
            # psutil not available, skip monitoring
            return await call_next(request)
        except Exception as e:
            logger.debug(f"Resource monitoring error: {str(e)}")
            return await call_next(request)
