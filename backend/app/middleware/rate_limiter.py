import time
from typing import Call<PERSON>, Dict, Optional, Tuple

from fastapi import <PERSON><PERSON><PERSON>, Request, Response
from starlette.middleware.base import BaseHTTPMiddleware
from starlette.status import HTTP_429_TOO_MANY_REQUESTS


class RateLimiter(BaseHTTPMiddleware):
    def __init__(
        self,
        app: FastAPI,
        limit: int = 100,  # Number of requests
        window: int = 60,  # Time window in seconds
    ):
        super().__init__(app)
        self.limit = limit
        self.window = window
        self.requests: Dict[str, Tuple[int, float]] = {}  # IP -> (count, start_time)

    def _get_key(self, request: Request) -> str:
        """Get a key for rate limiting (IP address)."""
        forwarded = request.headers.get("X-Forwarded-For")
        if forwarded:
            return forwarded.split(",")[0].strip()

        # Handle test client where client might be None
        if request.client is None:
            return "test-client"

        return request.client.host or ""

    def _is_rate_limited(self, key: str) -> bool:
        """Check if a key is rate limited."""
        now = time.time()

        # Get current count and start time for the key
        count, start_time = self.requests.get(key, (0, now))

        # Reset if window has passed
        if now - start_time > self.window:
            count = 0
            start_time = now

        # Increment count
        count += 1
        self.requests[key] = (count, start_time)

        # Check if rate limited
        return count > self.limit

    async def dispatch(self, request: Request, call_next: Callable) -> Response:
        # Skip rate limiting for WebSocket connections and chat endpoints
        if (request.url.path.startswith("/api/v1/chat/ws") or
            request.headers.get("upgrade") == "websocket"):
            return await call_next(request)

        key = self._get_key(request)

        # Apply standard rate limiting
        if self._is_rate_limited(key):
            return Response(
                content="Rate limit exceeded",
                status_code=HTTP_429_TOO_MANY_REQUESTS,
                headers={"Retry-After": str(self.window)},
            )

        return await call_next(request)

    def _is_rate_limited_custom(self, key: str, custom_limit: int) -> bool:
        """Check if a key is rate limited with a custom limit."""
        now = time.time()

        # Get current count and start time for the key
        count, start_time = self.requests.get(key, (0, now))

        # Reset if window has passed
        if now - start_time > self.window:
            count = 0
            start_time = now

        # Increment count
        count += 1
        self.requests[key] = (count, start_time)

        # Check if rate limited with custom limit
        return count > custom_limit
