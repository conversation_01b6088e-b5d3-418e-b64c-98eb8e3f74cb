import logging
# Heavy imports - lazy loaded for faster startup
from datetime import datetime, timezone, timedelta
from typing import Dict, <PERSON>, Optional, Tuple, Any
from sqlalchemy.orm import Session
from sqlalchemy import func, desc, and_, or_
import asyncio
from concurrent.futures import ThreadPoolExecutor

# ML imports with fallback
try:
    from sklearn.preprocessing import StandardScaler, LabelEncoder, MinMaxScaler
    from sklearn.feature_extraction.text import TfidfVectorizer
    from sklearn.decomposition import PCA
    from sklearn.cluster import KMeans
    SKLEARN_AVAILABLE = True
except ImportError:
    SKLEARN_AVAILABLE = False
    logging.warning("scikit-learn not available. Some ML features will be limited.")

from app.models.ml_recommendations import (
    UserBehaviorEvent, UserFeatureVector, ContentFeatureVector,
    MLRecommendation, UserLearningSession
)
from app.models.question import Question
from app.models.student_progress import StudentQuestionAttempt
from app.models.student_tools import FlashCard
from app.models.course import Course
from app.models.user import User

logger = logging.getLogger(__name__)


class MLDataPipeline:
    """Machine Learning data pipeline for feature engineering and data preprocessing"""
    
    def __init__(self):
        self.logger = logger
        # Lazy initialization - these will be created when first needed
        self.feature_scaler = None
        self.label_encoder = None
        self.text_vectorizer = None
        
        # Feature engineering parameters
        self.user_feature_dim = 50
        self.content_feature_dim = 30
        self.min_interactions_for_features = 5
        
    async def process_user_data(self, db: Session, user_id: int) -> Optional[UserFeatureVector]:
        """Process and extract features for a specific user"""
        try:
            # Get user's behavior events from last 60 days
            cutoff_date = datetime.now(timezone.utc) - timedelta(days=60)
            
            events = db.query(UserBehaviorEvent).filter(
                UserBehaviorEvent.user_id == user_id,
                UserBehaviorEvent.created_at >= cutoff_date
            ).all()
            
            if len(events) < self.min_interactions_for_features:
                self.logger.info(f"User {user_id} has insufficient data for feature extraction")
                return None
            
            # Extract features
            features = await self._extract_user_features(db, user_id, events)
            
            # Get or create user feature vector
            user_features = db.query(UserFeatureVector).filter(
                UserFeatureVector.user_id == user_id
            ).first()
            
            if not user_features:
                user_features = UserFeatureVector(user_id=user_id)
                db.add(user_features)
            
            # Update features
            for key, value in features.items():
                if hasattr(user_features, key):
                    setattr(user_features, key, value)
            
            user_features.last_updated = datetime.now(timezone.utc)
            user_features.feature_version += 1
            
            db.commit()
            db.refresh(user_features)
            
            return user_features
            
        except Exception as e:
            self.logger.error(f"Error processing user data for user {user_id}: {str(e)}")
            return None
    
    async def _extract_user_features(
        self, 
        db: Session, 
        user_id: int, 
        events: List[UserBehaviorEvent]
    ) -> Dict[str, Any]:
        """Extract comprehensive features from user behavior events"""
        
        # Convert events to DataFrame for easier analysis
        event_data = []
        for event in events:
            event_data.append({
                'event_type': event.event_type,
                'content_type': event.content_type,
                'content_id': event.content_id,
                'course_id': event.course_id,
                'topic': event.topic,
                'difficulty': event.difficulty,
                'is_correct': event.is_correct,
                'response_time': event.response_time_seconds,
                'confidence_level': event.confidence_level,
                'time_of_day': event.time_of_day,
                'day_of_week': event.day_of_week,
                'created_at': event.created_at,
                'session_id': event.session_id
            })
        
        df = pd.DataFrame(event_data)
        features = {}
        
        # Basic performance metrics
        mcq_events = df[df['event_type'] == 'mcq_attempt']
        if len(mcq_events) > 0:
            features['accuracy_rate'] = mcq_events['is_correct'].mean() if mcq_events['is_correct'].notna().any() else 0.0
            features['avg_response_time'] = mcq_events['response_time'].mean() if mcq_events['response_time'].notna().any() else 0.0
            
            # Response time variability
            if mcq_events['response_time'].notna().any():
                features['response_time_std'] = mcq_events['response_time'].std()
                features['response_time_consistency'] = 1.0 / (1.0 + features['response_time_std'])
        
        # Study patterns
        features['study_frequency'] = self._calculate_study_frequency(df)
        features['session_duration_avg'] = self._calculate_avg_session_duration(df)
        features['consistency_score'] = self._calculate_consistency_score(df)
        
        # Time-based preferences
        features['preferred_time_of_day'] = self._find_preferred_time(df)
        features['preferred_day_pattern'] = self._analyze_day_patterns(df)
        
        # Content preferences
        features['preferred_difficulty'] = self._find_preferred_difficulty(df)
        features['topic_performance'] = self._calculate_topic_performance(df)
        features['topic_interest'] = self._calculate_topic_interest(df)
        
        # Learning behavior patterns
        features['help_seeking_rate'] = self._calculate_help_seeking_rate(df)
        features['bookmark_rate'] = self._calculate_bookmark_rate(df)
        features['completion_rate'] = self._calculate_completion_rate(df)
        
        # Advanced behavioral features
        features['improvement_rate'] = self._calculate_improvement_rate(df)
        features['challenge_preference'] = self._calculate_challenge_preference(df)
        features['learning_velocity'] = self._calculate_learning_velocity(df)
        
        # Engagement metrics
        features['engagement_score'] = self._calculate_engagement_score(df)
        features['active_days_per_week'] = self._calculate_active_days(df)
        
        # Generate feature vector for similarity calculations
        features['feature_vector'] = self._generate_feature_vector(features)
        
        return features
    
    def _calculate_study_frequency(self, df: pd.DataFrame) -> float:
        """Calculate study frequency (sessions per week)"""
        if len(df) == 0:
            return 0.0
        
        unique_sessions = df['session_id'].nunique() if 'session_id' in df.columns else 1
        date_range = (df['created_at'].max() - df['created_at'].min()).days + 1
        weeks = max(date_range / 7.0, 1.0)
        
        return unique_sessions / weeks
    
    def _calculate_avg_session_duration(self, df: pd.DataFrame) -> float:
        """Calculate average session duration in minutes"""
        if len(df) == 0 or 'session_id' not in df.columns:
            return 0.0
        
        session_durations = []
        for session_id in df['session_id'].unique():
            if pd.isna(session_id):
                continue
            
            session_events = df[df['session_id'] == session_id]
            if len(session_events) > 1:
                duration = (session_events['created_at'].max() - 
                           session_events['created_at'].min()).total_seconds() / 60
                session_durations.append(min(duration, 180))  # Cap at 3 hours
        
        return np.mean(session_durations) if session_durations else 0.0
    
    def _calculate_consistency_score(self, df: pd.DataFrame) -> float:
        """Calculate how consistently the user studies"""
        if len(df) == 0:
            return 0.0
        
        study_days = df['created_at'].dt.date.nunique()
        total_days = (df['created_at'].max() - df['created_at'].min()).days + 1
        
        return study_days / total_days if total_days > 0 else 0.0
    
    def _find_preferred_time(self, df: pd.DataFrame) -> Optional[int]:
        """Find user's preferred time of day for studying"""
        if 'time_of_day' not in df.columns or df['time_of_day'].isna().all():
            return None
        
        time_counts = df['time_of_day'].value_counts()
        return int(time_counts.index[0]) if len(time_counts) > 0 else None
    
    def _analyze_day_patterns(self, df: pd.DataFrame) -> Dict[str, float]:
        """Analyze study patterns by day of week"""
        if 'day_of_week' not in df.columns:
            return {}
        
        day_counts = df['day_of_week'].value_counts()
        total_events = len(df)
        
        return {str(day): count / total_events for day, count in day_counts.items()}
    
    def _find_preferred_difficulty(self, df: pd.DataFrame) -> Optional[str]:
        """Find user's preferred difficulty level"""
        if 'difficulty' not in df.columns or df['difficulty'].isna().all():
            return None
        
        difficulty_counts = df['difficulty'].value_counts()
        return difficulty_counts.index[0] if len(difficulty_counts) > 0 else None
    
    def _calculate_topic_performance(self, df: pd.DataFrame) -> Dict[str, float]:
        """Calculate performance by topic"""
        if 'topic' not in df.columns or 'is_correct' not in df.columns:
            return {}
        
        topic_performance = {}
        for topic in df['topic'].dropna().unique():
            topic_events = df[df['topic'] == topic]
            if len(topic_events) > 0 and topic_events['is_correct'].notna().any():
                topic_performance[topic] = topic_events['is_correct'].mean()
        
        return topic_performance
    
    def _calculate_topic_interest(self, df: pd.DataFrame) -> Dict[str, float]:
        """Calculate interest level by topic based on engagement"""
        if 'topic' not in df.columns:
            return {}
        
        topic_interest = {}
        total_events = len(df)
        
        for topic in df['topic'].dropna().unique():
            topic_events = len(df[df['topic'] == topic])
            topic_interest[topic] = topic_events / total_events
        
        return topic_interest
    
    def _calculate_help_seeking_rate(self, df: pd.DataFrame) -> float:
        """Calculate rate of help-seeking behavior"""
        if len(df) == 0:
            return 0.0
        
        help_events = len(df[df['event_type'] == 'help_request'])
        return help_events / len(df)
    
    def _calculate_bookmark_rate(self, df: pd.DataFrame) -> float:
        """Calculate rate of bookmarking behavior"""
        if len(df) == 0:
            return 0.0
        
        bookmark_events = len(df[df['event_type'] == 'bookmark_add'])
        return bookmark_events / len(df)
    
    def _calculate_completion_rate(self, df: pd.DataFrame) -> float:
        """Calculate completion rate for activities"""
        if len(df) == 0:
            return 0.0
        
        # Approximate completion based on session patterns
        if 'session_id' not in df.columns:
            return 0.5  # Default assumption
        
        completed_sessions = 0
        for session_id in df['session_id'].unique():
            if pd.isna(session_id):
                continue
            
            session_events = df[df['session_id'] == session_id]
            # Consider session completed if it has multiple events and reasonable duration
            if len(session_events) >= 3:
                completed_sessions += 1
        
        total_sessions = df['session_id'].nunique()
        return completed_sessions / total_sessions if total_sessions > 0 else 0.0
    
    def _calculate_improvement_rate(self, df: pd.DataFrame) -> float:
        """Calculate learning improvement rate over time"""
        if len(df) == 0 or 'is_correct' not in df.columns:
            return 0.0
        
        # Sort by time and calculate rolling accuracy
        df_sorted = df.sort_values('created_at')
        mcq_events = df_sorted[df_sorted['event_type'] == 'mcq_attempt']
        
        if len(mcq_events) < 10:  # Need sufficient data
            return 0.0
        
        # Calculate improvement using linear trend
        mcq_events = mcq_events.reset_index(drop=True)
        mcq_events['rolling_accuracy'] = mcq_events['is_correct'].rolling(window=5, min_periods=1).mean()
        
        # Simple linear trend calculation
        x = np.arange(len(mcq_events))
        y = mcq_events['rolling_accuracy'].values
        
        if len(x) > 1:
            slope = np.polyfit(x, y, 1)[0]
            return max(0.0, slope)  # Only positive improvement
        
        return 0.0
    
    def _calculate_challenge_preference(self, df: pd.DataFrame) -> float:
        """Calculate preference for challenging content"""
        if 'difficulty' not in df.columns:
            return 0.5  # Neutral
        
        difficulty_map = {'easy': 0.2, 'medium': 0.5, 'hard': 0.8}
        difficulty_scores = []
        
        for difficulty in df['difficulty'].dropna():
            if difficulty.lower() in difficulty_map:
                difficulty_scores.append(difficulty_map[difficulty.lower()])
        
        return np.mean(difficulty_scores) if difficulty_scores else 0.5
    
    def _calculate_learning_velocity(self, df: pd.DataFrame) -> float:
        """Calculate how quickly user learns new concepts"""
        if len(df) == 0:
            return 0.0
        
        # Approximate learning velocity based on accuracy improvement and frequency
        accuracy = df['is_correct'].mean() if 'is_correct' in df.columns else 0.5
        frequency = self._calculate_study_frequency(df)
        
        return min(accuracy * frequency / 7.0, 1.0)  # Normalize to 0-1
    
    def _calculate_engagement_score(self, df: pd.DataFrame) -> float:
        """Calculate overall engagement score"""
        if len(df) == 0:
            return 0.0
        
        # Combine multiple engagement indicators
        factors = []
        
        # Event frequency
        factors.append(min(len(df) / 100.0, 1.0))  # Normalize to events per period
        
        # Session consistency
        factors.append(self._calculate_consistency_score(df))
        
        # Interaction diversity
        event_types = df['event_type'].nunique()
        factors.append(min(event_types / 5.0, 1.0))  # Normalize to expected types
        
        # Response time (faster = more engaged, but not too fast)
        if 'response_time' in df.columns and df['response_time'].notna().any():
            avg_time = df['response_time'].mean()
            # Optimal range: 10-60 seconds
            if 10 <= avg_time <= 60:
                factors.append(1.0)
            elif avg_time < 10:
                factors.append(0.7)  # Too fast, might be guessing
            else:
                factors.append(max(0.3, 1.0 - (avg_time - 60) / 120))  # Decreasing engagement
        
        return np.mean(factors) if factors else 0.0
    
    def _calculate_active_days(self, df: pd.DataFrame) -> float:
        """Calculate active days per week"""
        if len(df) == 0:
            return 0.0
        
        unique_days = df['created_at'].dt.date.nunique()
        total_days = (df['created_at'].max() - df['created_at'].min()).days + 1
        weeks = max(total_days / 7.0, 1.0)
        
        return min(unique_days / weeks, 7.0)  # Cap at 7 days per week
    
    def _generate_feature_vector(self, features: Dict[str, Any]) -> List[float]:
        """Generate a fixed-size feature vector for similarity calculations"""
        vector = []
        
        # Core performance features
        vector.extend([
            features.get('accuracy_rate', 0.0),
            features.get('avg_response_time', 0.0) / 100.0,  # Normalize
            features.get('study_frequency', 0.0) / 10.0,  # Normalize
            features.get('session_duration_avg', 0.0) / 60.0,  # Normalize
            features.get('consistency_score', 0.0),
            features.get('engagement_score', 0.0),
            features.get('improvement_rate', 0.0),
            features.get('learning_velocity', 0.0),
            features.get('challenge_preference', 0.5),
            features.get('help_seeking_rate', 0.0),
            features.get('bookmark_rate', 0.0),
            features.get('completion_rate', 0.0),
            features.get('active_days_per_week', 0.0) / 7.0,  # Normalize
        ])
        
        # Time preferences (one-hot encoded)
        preferred_time = features.get('preferred_time_of_day')
        for hour in [6, 12, 18, 22]:  # Morning, noon, evening, night
            vector.append(1.0 if preferred_time == hour else 0.0)
        
        # Difficulty preference (one-hot encoded)
        preferred_diff = features.get('preferred_difficulty', '').lower()
        for diff in ['easy', 'medium', 'hard']:
            vector.append(1.0 if preferred_diff == diff else 0.0)
        
        # Topic performance (top 5 topics)
        topic_perf = features.get('topic_performance', {})
        sorted_topics = sorted(topic_perf.items(), key=lambda x: x[1], reverse=True)[:5]
        for i in range(5):
            if i < len(sorted_topics):
                vector.append(sorted_topics[i][1])
            else:
                vector.append(0.0)
        
        # Pad or truncate to fixed size
        while len(vector) < self.user_feature_dim:
            vector.append(0.0)
        
        return vector[:self.user_feature_dim]
    
    async def process_content_features(self, db: Session, content_type: str, content_id: int) -> Optional[ContentFeatureVector]:
        """Process and extract features for content items"""
        try:
            # Get content-specific data
            if content_type == 'question':
                content_item = db.query(Question).filter(Question.id == content_id).first()
            elif content_type == 'flashcard':
                content_item = db.query(FlashCard).filter(FlashCard.id == content_id).first()
            else:
                return None
            
            if not content_item:
                return None
            
            # Get interaction data for this content
            interactions = db.query(UserBehaviorEvent).filter(
                UserBehaviorEvent.content_type == content_type,
                UserBehaviorEvent.content_id == content_id
            ).all()
            
            # Extract content features
            features = await self._extract_content_features(content_item, interactions)
            
            # Get or create content feature vector
            content_features = db.query(ContentFeatureVector).filter(
                ContentFeatureVector.content_type == content_type,
                ContentFeatureVector.content_id == content_id
            ).first()
            
            if not content_features:
                content_features = ContentFeatureVector(
                    content_type=content_type,
                    content_id=content_id
                )
                db.add(content_features)
            
            # Update features
            for key, value in features.items():
                if hasattr(content_features, key):
                    setattr(content_features, key, value)
            
            content_features.last_updated = datetime.now(timezone.utc)
            content_features.feature_version += 1
            
            db.commit()
            db.refresh(content_features)
            
            return content_features
            
        except Exception as e:
            self.logger.error(f"Error processing content features for {content_type} {content_id}: {str(e)}")
            return None
    
    async def _extract_content_features(self, content_item: Any, interactions: List[UserBehaviorEvent]) -> Dict[str, Any]:
        """Extract features from content and its interactions"""
        features = {}
        
        # Basic content properties
        if hasattr(content_item, 'topic'):
            features['topic'] = content_item.topic
        if hasattr(content_item, 'course_id'):
            features['course_id'] = content_item.course_id
        if hasattr(content_item, 'difficulty'):
            features['difficulty_score'] = self._map_difficulty_to_score(str(content_item.difficulty))
        
        # Content analysis
        if hasattr(content_item, 'content'):
            text_features = self._analyze_text_complexity(content_item.content)
            features.update(text_features)
        
        if hasattr(content_item, 'media_url') and content_item.media_url:
            features['has_media'] = True
        else:
            features['has_media'] = False
        
        # Interaction-based features
        if interactions:
            features.update(self._analyze_content_interactions(interactions))
        else:
            # Default values for new content
            features.update({
                'avg_response_time': 0.0,
                'success_rate': 0.5,
                'attempt_count': 0,
                'view_count': 0,
                'bookmark_count': 0,
                'help_request_count': 0
            })
        
        # Generate feature vector
        features['feature_vector'] = self._generate_content_feature_vector(features)
        
        return features
    
    def _map_difficulty_to_score(self, difficulty: str) -> float:
        """Map difficulty string to numerical score"""
        difficulty_map = {
            'easy': 0.2,
            'medium': 0.5,
            'hard': 0.8,
            'expert': 1.0
        }
        return difficulty_map.get(difficulty.lower(), 0.5)
    
    def _analyze_text_complexity(self, text: str) -> Dict[str, Any]:
        """Analyze text complexity features"""
        if not text:
            return {'text_complexity': 0.0, 'word_count': 0}
        
        words = text.split()
        word_count = len(words)
        
        # Simple complexity metrics
        avg_word_length = np.mean([len(word) for word in words]) if words else 0
        sentence_count = text.count('.') + text.count('!') + text.count('?')
        avg_sentence_length = word_count / max(sentence_count, 1)
        
        # Normalize complexity score
        complexity = min((avg_word_length / 10 + avg_sentence_length / 20) / 2, 1.0)
        
        return {
            'text_complexity': complexity,
            'word_count': word_count
        }
    
    def _analyze_content_interactions(self, interactions: List[UserBehaviorEvent]) -> Dict[str, Any]:
        """Analyze user interactions with content"""
        features = {}
        
        # Basic interaction counts
        features['attempt_count'] = len(interactions)
        features['view_count'] = len([i for i in interactions if i.event_type in ['question_view', 'flashcard_view']])
        features['bookmark_count'] = len([i for i in interactions if i.event_type == 'bookmark_add'])
        features['help_request_count'] = len([i for i in interactions if i.event_type == 'help_request'])
        
        # Performance metrics
        correct_attempts = [i for i in interactions if i.is_correct is True]
        features['success_rate'] = len(correct_attempts) / len(interactions) if interactions else 0.0
        
        # Response time analysis
        response_times = [i.response_time_seconds for i in interactions if i.response_time_seconds is not None]
        features['avg_response_time'] = np.mean(response_times) if response_times else 0.0
        
        return features
    
    def _generate_content_feature_vector(self, features: Dict[str, Any]) -> List[float]:
        """Generate feature vector for content"""
        vector = []
        
        # Basic features
        vector.extend([
            features.get('difficulty_score', 0.5),
            features.get('text_complexity', 0.0),
            features.get('word_count', 0) / 100.0,  # Normalize
            1.0 if features.get('has_media', False) else 0.0,
            features.get('success_rate', 0.0),
            features.get('avg_response_time', 0.0) / 100.0,  # Normalize
            features.get('attempt_count', 0) / 100.0,  # Normalize
            features.get('view_count', 0) / 100.0,  # Normalize
            features.get('bookmark_count', 0) / 10.0,  # Normalize
            features.get('help_request_count', 0) / 10.0,  # Normalize
        ])
        
        # Pad to fixed size
        while len(vector) < self.content_feature_dim:
            vector.append(0.0)
        
        return vector[:self.content_feature_dim]
    
    async def batch_process_users(self, db: Session, user_ids: List[int]) -> List[UserFeatureVector]:
        """Process multiple users in batch"""
        results = []
        
        with ThreadPoolExecutor(max_workers=4) as executor:
            futures = [
                executor.submit(self.process_user_data, db, user_id)
                for user_id in user_ids
            ]
            
            for future in futures:
                try:
                    result = future.result()
                    if result:
                        results.append(result)
                except Exception as e:
                    self.logger.error(f"Error in batch processing: {str(e)}")
        
        return results


# Create service instance
ml_data_pipeline = MLDataPipeline()
