"""
Service for handling content sharing operations and data enrichment.
"""

from typing import List, Dict, Any, Optional
from sqlalchemy.orm import Session

from app.crud.crud_content_access import content_access
from app.models.content_access import ContentType
from app.models.user import User
from app.models.question import Question
from app.models.generated_flashcard import GeneratedFlashcard
from app.models.generated_note_explanation import GeneratedNoteExplanation
from app.schemas.content_access import SharedContentItem


class ContentSharingService:
    """Service for enriching content with sharing metadata"""
    
    def __init__(self, db: Session):
        self.db = db
    
    def get_mcqs_with_sharing_info(
        self, 
        user_id: int, 
        course_name: Optional[str] = None,
        skip: int = 0,
        limit: int = 100
    ) -> List[SharedContentItem]:
        """Get MCQs (both owned and shared) with sharing metadata"""
        
        items = []
        
        # Get user's own MCQs
        query = self.db.query(Question).filter(Question.created_by_id == user_id)
        if course_name:
            query = query.filter(Question.course_name == course_name)
        
        own_mcqs = query.offset(skip).limit(limit).all()
        
        for mcq in own_mcqs:
            items.append(SharedContentItem(
                content_type=ContentType.MCQ,
                content_id=mcq.id,
                content_data={
                    "id": mcq.id,
                    "content": mcq.content,
                    "question_type": mcq.question_type,
                    "difficulty": mcq.difficulty,
                    "topic": mcq.topic,
                    "options": mcq.options,
                    "answer": mcq.answer,
                    "explanation": mcq.explanation,
                    "course_name": mcq.course_name,
                    "created_at": mcq.created_at
                },
                is_shared=False,
                can_edit=True,
                can_delete=True,
                tags=[]
            ))
        
        # Get shared MCQs
        shared_access = content_access.get_user_accessible_content(
            self.db, user_id=user_id, content_type=ContentType.MCQ
        )
        
        for access in shared_access:
            mcq = self.db.query(Question).filter(Question.id == access.content_id).first()
            if mcq:
                # Get sharer info
                sharer = self.db.query(User).filter(User.id == access.shared_by_id).first()
                
                items.append(SharedContentItem(
                    content_type=ContentType.MCQ,
                    content_id=mcq.id,
                    content_data={
                        "id": mcq.id,
                        "content": mcq.content,
                        "question_type": mcq.question_type,
                        "difficulty": mcq.difficulty,
                        "topic": mcq.topic,
                        "options": mcq.options,
                        "answer": mcq.answer,
                        "explanation": mcq.explanation,
                        "course_name": mcq.course_name,
                        "created_at": mcq.created_at
                    },
                    is_shared=True,
                    shared_by_name=sharer.full_name if sharer else "Unknown",
                    shared_by_email=sharer.email if sharer else "",
                    share_message=access.share_message,
                    shared_at=access.created_at,
                    can_edit=False,
                    can_delete=False,
                    tags=["shared", "read-only"]
                ))
        
        return items
    
    def get_flashcards_with_sharing_info(
        self, 
        user_id: int, 
        course_name: Optional[str] = None,
        skip: int = 0,
        limit: int = 100
    ) -> List[SharedContentItem]:
        """Get flashcards (both owned and shared) with sharing metadata"""
        
        items = []
        
        # Get user's own flashcards
        query = self.db.query(GeneratedFlashcard).filter(GeneratedFlashcard.student_id == user_id)
        if course_name:
            query = query.filter(GeneratedFlashcard.course_name == course_name)
        
        own_flashcards = query.offset(skip).limit(limit).all()
        
        for flashcard in own_flashcards:
            items.append(SharedContentItem(
                content_type=ContentType.FLASHCARD,
                content_id=flashcard.id,
                content_data={
                    "id": flashcard.id,
                    "front_content": flashcard.front_content,
                    "back_content": flashcard.back_content,
                    "topic": flashcard.topic,
                    "difficulty": flashcard.difficulty,
                    "card_type": flashcard.card_type,
                    "course_name": flashcard.course_name,
                    "created_at": flashcard.created_at
                },
                is_shared=False,
                can_edit=True,
                can_delete=True,
                tags=[]
            ))
        
        # Get shared flashcards
        shared_access = content_access.get_user_accessible_content(
            self.db, user_id=user_id, content_type=ContentType.FLASHCARD
        )
        
        for access in shared_access:
            flashcard = self.db.query(GeneratedFlashcard).filter(GeneratedFlashcard.id == access.content_id).first()
            if flashcard:
                # Get sharer info
                sharer = self.db.query(User).filter(User.id == access.shared_by_id).first()
                
                items.append(SharedContentItem(
                    content_type=ContentType.FLASHCARD,
                    content_id=flashcard.id,
                    content_data={
                        "id": flashcard.id,
                        "front_content": flashcard.front_content,
                        "back_content": flashcard.back_content,
                        "topic": flashcard.topic,
                        "difficulty": flashcard.difficulty,
                        "card_type": flashcard.card_type,
                        "course_name": flashcard.course_name,
                        "created_at": flashcard.created_at
                    },
                    is_shared=True,
                    shared_by_name=sharer.full_name if sharer else "Unknown",
                    shared_by_email=sharer.email if sharer else "",
                    share_message=access.share_message,
                    shared_at=access.created_at,
                    can_edit=False,
                    can_delete=False,
                    tags=["shared", "read-only"]
                ))
        
        return items
    
    def get_summaries_with_sharing_info(
        self, 
        user_id: int, 
        course_name: Optional[str] = None,
        skip: int = 0,
        limit: int = 100
    ) -> List[SharedContentItem]:
        """Get summaries (both owned and shared) with sharing metadata"""
        
        items = []
        
        # Get user's own summaries
        query = self.db.query(GeneratedNoteExplanation).filter(GeneratedNoteExplanation.student_id == user_id)
        if course_name:
            query = query.filter(GeneratedNoteExplanation.course_name == course_name)
        
        own_summaries = query.offset(skip).limit(limit).all()
        
        for summary in own_summaries:
            items.append(SharedContentItem(
                content_type=ContentType.SUMMARY,
                content_id=summary.id,
                content_data={
                    "id": summary.id,
                    "title": summary.title,
                    "overview": summary.overview,
                    "detailed_explanation": summary.detailed_explanation,
                    "comprehensive_summary": summary.comprehensive_summary,
                    "key_concepts": summary.key_concepts,
                    "main_topics": summary.main_topics,
                    "course_name": summary.course_name,
                    "created_at": summary.created_at
                },
                is_shared=False,
                can_edit=True,
                can_delete=True,
                tags=[]
            ))
        
        # Get shared summaries
        shared_access = content_access.get_user_accessible_content(
            self.db, user_id=user_id, content_type=ContentType.SUMMARY
        )
        
        for access in shared_access:
            summary = self.db.query(GeneratedNoteExplanation).filter(GeneratedNoteExplanation.id == access.content_id).first()
            if summary:
                # Get sharer info
                sharer = self.db.query(User).filter(User.id == access.shared_by_id).first()
                
                items.append(SharedContentItem(
                    content_type=ContentType.SUMMARY,
                    content_id=summary.id,
                    content_data={
                        "id": summary.id,
                        "title": summary.title,
                        "overview": summary.overview,
                        "detailed_explanation": summary.detailed_explanation,
                        "comprehensive_summary": summary.comprehensive_summary,
                        "key_concepts": summary.key_concepts,
                        "main_topics": summary.main_topics,
                        "course_name": summary.course_name,
                        "created_at": summary.created_at
                    },
                    is_shared=True,
                    shared_by_name=sharer.full_name if sharer else "Unknown",
                    shared_by_email=sharer.email if sharer else "",
                    share_message=access.share_message,
                    shared_at=access.created_at,
                    can_edit=False,
                    can_delete=False,
                    tags=["shared", "read-only"]
                ))
        
        return items
    
    def mark_content_accessed(self, user_id: int, content_type: ContentType, content_id: int):
        """Mark content as accessed by user"""
        content_access.update_last_accessed(
            self.db, 
            content_type=content_type, 
            content_id=content_id, 
            user_id=user_id
        )
