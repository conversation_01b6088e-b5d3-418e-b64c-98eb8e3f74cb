import logging
import uuid
import base64
from typing import Optional, Dict, Any
from fastapi import UploadFile
import redis
from app.core.config import settings

logger = logging.getLogger(__name__)


class RedisFileService:
    """
    Service for transferring file content between API and worker services via Redis.
    Perfect for Railway deployment where services are isolated.
    """
    
    def __init__(self):
        """Initialize Redis connection."""
        try:
            # Parse Redis URL for Railway compatibility
            if settings.REDIS_URL.startswith('redis://'):
                self.redis_client = redis.from_url(settings.REDIS_URL, decode_responses=False)
            else:
                # Fallback for local development
                self.redis_client = redis.Redis(host='localhost', port=6379, db=0, decode_responses=False)
            
            # Test connection
            self.redis_client.ping()
            logger.info("Redis file service initialized successfully")
            
        except Exception as e:
            logger.error(f"Failed to initialize Redis file service: {str(e)}")
            raise
    
    async def store_file_content(
        self, 
        file: UploadFile, 
        student_id: int,
        ttl_seconds: int = 3600  # 1 hour TTL
    ) -> Dict[str, Any]:
        """
        Store file content in Redis with automatic expiration.
        
        Args:
            file: The uploaded file
            student_id: ID of the student
            ttl_seconds: Time to live in seconds (default: 1 hour)
            
        Returns:
            Dictionary with file information and Redis key
        """
        try:
            # Generate unique file ID
            file_id = str(uuid.uuid4())
            
            # Read file content
            file_content = await file.read()
            file_size = len(file_content)
            
            # Reset file pointer for potential reuse
            await file.seek(0)
            
            # Encode content as base64 for Redis storage
            encoded_content = base64.b64encode(file_content)
            
            # Create Redis key
            redis_key = f"file:{student_id}:{file_id}"
            
            # Store file metadata and content in Redis hash
            file_data = {
                'content': encoded_content,
                'filename': file.filename or 'unknown.pdf',
                'size': file_size,
                'content_type': file.content_type or 'application/pdf',
                'student_id': student_id,
                'created_at': str(int(__import__('time').time()))
            }
            
            # Store in Redis with TTL
            pipe = self.redis_client.pipeline()
            pipe.hset(redis_key, mapping=file_data)
            pipe.expire(redis_key, ttl_seconds)
            pipe.execute()
            
            logger.info(f"Stored file in Redis: {redis_key} ({file_size} bytes, TTL: {ttl_seconds}s)")
            
            return {
                "file_id": file_id,
                "redis_key": redis_key,
                "original_filename": file.filename,
                "file_size": file_size,
                "student_id": student_id,
                "ttl_seconds": ttl_seconds
            }
            
        except Exception as e:
            logger.error(f"Error storing file in Redis: {str(e)}")
            raise
    
    def get_file_content(self, file_id: str, student_id: int) -> Optional[bytes]:
        """
        Retrieve file content from Redis.
        
        Args:
            file_id: Unique file identifier
            student_id: ID of the student
            
        Returns:
            File content as bytes if found, None otherwise
        """
        try:
            redis_key = f"file:{student_id}:{file_id}"
            
            # Get file data from Redis
            file_data = self.redis_client.hgetall(redis_key)
            
            if not file_data:
                logger.warning(f"File not found in Redis: {redis_key}")
                return None
            
            # Decode base64 content
            encoded_content = file_data.get(b'content')
            if not encoded_content:
                logger.error(f"No content found for file: {redis_key}")
                return None
            
            content = base64.b64decode(encoded_content)
            
            logger.info(f"Retrieved file from Redis: {redis_key} ({len(content)} bytes)")
            return content
            
        except Exception as e:
            logger.error(f"Error retrieving file from Redis: {str(e)}")
            return None
    
    def get_file_metadata(self, file_id: str, student_id: int) -> Optional[Dict[str, Any]]:
        """
        Get file metadata from Redis.
        
        Args:
            file_id: Unique file identifier
            student_id: ID of the student
            
        Returns:
            File metadata dictionary if found, None otherwise
        """
        try:
            redis_key = f"file:{student_id}:{file_id}"
            
            # Get metadata (excluding content)
            metadata = self.redis_client.hmget(
                redis_key, 
                'filename', 'size', 'content_type', 'student_id', 'created_at'
            )
            
            if not any(metadata):
                return None
            
            return {
                'filename': metadata[0].decode() if metadata[0] else None,
                'size': int(metadata[1]) if metadata[1] else 0,
                'content_type': metadata[2].decode() if metadata[2] else None,
                'student_id': int(metadata[3]) if metadata[3] else None,
                'created_at': metadata[4].decode() if metadata[4] else None
            }
            
        except Exception as e:
            logger.error(f"Error getting file metadata from Redis: {str(e)}")
            return None
    
    def delete_file(self, file_id: str, student_id: int) -> bool:
        """
        Delete file from Redis.
        
        Args:
            file_id: Unique file identifier
            student_id: ID of the student
            
        Returns:
            True if deleted successfully, False otherwise
        """
        try:
            redis_key = f"file:{student_id}:{file_id}"
            deleted = self.redis_client.delete(redis_key)
            
            if deleted:
                logger.info(f"Deleted file from Redis: {redis_key}")
                return True
            else:
                logger.warning(f"File not found for deletion: {redis_key}")
                return False
                
        except Exception as e:
            logger.error(f"Error deleting file from Redis: {str(e)}")
            return False
    
    def cleanup_expired_files(self) -> int:
        """
        Clean up expired files (Redis handles this automatically with TTL).
        This method is mainly for monitoring purposes.
        
        Returns:
            Number of active file keys (for monitoring)
        """
        try:
            # Count active file keys
            pattern = "file:*"
            keys = self.redis_client.keys(pattern)
            active_count = len(keys)
            
            logger.info(f"Active file keys in Redis: {active_count}")
            return active_count
            
        except Exception as e:
            logger.error(f"Error during Redis cleanup check: {str(e)}")
            return 0
    
    def extend_file_ttl(self, file_id: str, student_id: int, additional_seconds: int = 3600) -> bool:
        """
        Extend the TTL of a file in Redis.
        
        Args:
            file_id: Unique file identifier
            student_id: ID of the student
            additional_seconds: Additional seconds to add to TTL
            
        Returns:
            True if TTL extended successfully, False otherwise
        """
        try:
            redis_key = f"file:{student_id}:{file_id}"
            
            # Get current TTL
            current_ttl = self.redis_client.ttl(redis_key)
            if current_ttl == -2:  # Key doesn't exist
                logger.warning(f"Cannot extend TTL - file not found: {redis_key}")
                return False
            
            # Set new TTL
            new_ttl = max(additional_seconds, current_ttl + additional_seconds)
            success = self.redis_client.expire(redis_key, new_ttl)
            
            if success:
                logger.info(f"Extended TTL for file: {redis_key} (new TTL: {new_ttl}s)")
            
            return success
            
        except Exception as e:
            logger.error(f"Error extending file TTL: {str(e)}")
            return False


# Create singleton instance
redis_file_service = RedisFileService()
