"""
OpenAI service for document processing, MCQ generation, flashcard creation, and summarization.
High-performance alternative to Gemini with excellent quality.
Enhanced with GPT-4o vision capabilities for comprehensive PDF understanding.
"""

import logging
import time
import base64
import json
import io
import concurrent.futures
from typing import List, Dict, Any, Optional, Union, Tuple
from pathlib import Path

# Heavy imports - lazy loaded for faster startup
# OpenAI and pdf2image will be imported when needed

from app.core.config import settings

logger = logging.getLogger(__name__)


class OpenAIService:
    """Service for interacting with OpenAI API for document processing."""

    def __init__(self):
        """Initialize the OpenAI service."""
        # Lazy initialization - client will be created when first needed
        self.client = None
        self.model_name = "gpt-4o"  # Best model for document understanding
        self.fast_model = "gpt-4o-mini"  # Faster model for simple tasks

        # Rate limiting (OpenAI has generous limits)
        self.last_request_time = 0
        self.min_request_interval = 0.1  # Very fast - 10 requests per second

    def _get_client(self):
        """Get OpenAI client, initializing it lazily if needed."""
        if self.client is None:
            import openai
            from openai import OpenAI
            self.client = OpenAI(api_key=settings.OPENAI_API_KEY)
        return self.client

    def _wait_for_rate_limit(self):
        """Ensure minimum interval between requests."""
        current_time = time.time()
        time_since_last = current_time - self.last_request_time

        if time_since_last < self.min_request_interval:
            wait_time = self.min_request_interval - time_since_last
            time.sleep(wait_time)

        self.last_request_time = time.time()

    def convert_doc_to_images(self, file_path: str) -> List[Any]:
        """Convert PDF document to images using pdf2image."""
        try:
            images = convert_from_path(file_path)
            return images
        except (PDFInfoNotInstalledError, PDFPageCountError, PDFSyntaxError) as e:
            logger.error(f"Error converting PDF to images: {str(e)}")
            raise
        except Exception as e:
            logger.error(f"Unexpected error converting PDF to images: {str(e)}")
            raise

    def get_img_uri(self, img) -> str:
        """Convert PIL image to base64 data URI for OpenAI API."""
        try:
            png_buffer = io.BytesIO()
            img.save(png_buffer, format="PNG")
            png_buffer.seek(0)

            base64_png = base64.b64encode(png_buffer.read()).decode('utf-8')
            data_uri = f"data:image/png;base64,{base64_png}"
            return data_uri
        except Exception as e:
            logger.error(f"Error converting image to data URI: {str(e)}")
            raise

    def analyze_image(self, data_uri: str) -> str:
        """Analyze a single PDF page image using GPT-4o vision."""
        try:
            self._wait_for_rate_limit()

            system_prompt = '''
You will be provided with an image of a PDF page or a slide. Your goal is to deliver a detailed and engaging presentation about the content you see, using clear and accessible language suitable for a 101-level audience.

If there is an identifiable title, start by stating the title to provide context for your audience.

Describe visual elements in detail:

- **Diagrams**: Explain each component and how they interact. For example, "The process begins with X, which then leads to Y and results in Z."

- **Tables**: Break down the information logically. For instance, "Product A costs X dollars, while Product B is priced at Y dollars."

Focus on the content itself rather than the format:

- **DO NOT** include terms referring to the content format.

- **DO NOT** mention the content type. Instead, directly discuss the information presented.

Keep your explanation comprehensive yet concise:

- Be exhaustive in describing the content, as your audience cannot see the image.

- Exclude irrelevant details such as page numbers or the position of elements on the image.

Use clear and accessible language:

- Explain technical terms or concepts in simple language appropriate for a 101-level audience.

Engage with the content:

- Interpret and analyze the information where appropriate, offering insights to help the audience understand its significance.

------

If there is an identifiable title, present the output in the following format:

{TITLE}

{Content description}

If there is no clear title, simply provide the content description.
'''

            response = self.client.chat.completions.create(
                model="gpt-4o",
                messages=[
                    {"role": "system", "content": system_prompt},
                    {
                        "role": "user",
                        "content": [
                            {
                                "type": "image_url",
                                "image_url": {
                                    "url": data_uri
                                }
                            }
                        ]
                    },
                ],
                max_tokens=500,
                temperature=0,
                top_p=0.1
            )
            return response.choices[0].message.content

        except Exception as e:
            logger.error(f"Error analyzing image with GPT-4o: {str(e)}")
            raise

    def analyze_doc_image(self, img) -> str:
        """Analyze a single document image."""
        try:
            img_uri = self.get_img_uri(img)
            data = self.analyze_image(img_uri)
            return data
        except Exception as e:
            logger.error(f"Error in analyze_doc_image: {str(e)}")
            raise

    def extract_text_from_pdf(self, file_path: str) -> str:
        """
        Enhanced PDF text extraction using OpenAI GPT-4o vision capabilities.
        Converts PDF to images and analyzes each page comprehensively.
        """
        try:
            logger.info("Using OpenAI GPT-4o for enhanced PDF processing with vision capabilities")

            # Convert PDF to images
            images = self.convert_doc_to_images(file_path)
            logger.info(f"Converted PDF to {len(images)} images")

            # Process images with concurrent execution for better performance
            pages_description = []

            # Use concurrent processing with ThreadPoolExecutor
            with concurrent.futures.ThreadPoolExecutor(max_workers=4) as executor:
                # Skip first page as it's usually just an intro (following the guide)
                images_to_process = images[1:] if len(images) > 1 else images
                futures = [
                    executor.submit(self.analyze_doc_image, img)
                    for img in images_to_process
                ]

                # Process with progress tracking
                with tqdm(total=len(futures), desc="Analyzing PDF pages") as pbar:
                    for future in concurrent.futures.as_completed(futures):
                        try:
                            result = future.result()
                            pages_description.append(result)
                            pbar.update(1)
                        except Exception as e:
                            logger.warning(f"Failed to process one page: {str(e)}")
                            pbar.update(1)

            # Combine all page descriptions into comprehensive text
            if pages_description:
                combined_text = "\n\n--- PAGE BREAK ---\n\n".join(pages_description)
                logger.info(f"Successfully extracted {len(combined_text)} characters using OpenAI vision")
                return combined_text
            else:
                logger.warning("No content could be extracted from PDF images")
                return "No content could be extracted from the PDF"

        except Exception as e:
            logger.error(f"Error in enhanced PDF extraction: {str(e)}")
            raise

    def extract_text_from_pdf_content(self, pdf_content: bytes) -> str:
        """
        Enhanced PDF text extraction from bytes using OpenAI GPT-4o vision capabilities.
        Converts PDF content to images and analyzes each page comprehensively.
        """
        try:
            logger.info("Using OpenAI GPT-4o for enhanced PDF content processing with vision capabilities")

            # Convert PDF content to images
            images = self.convert_pdf_content_to_images(pdf_content)
            logger.info(f"Converted PDF content to {len(images)} images")

            # Process images with concurrent execution for better performance
            pages_description = []

            # Use concurrent processing with ThreadPoolExecutor
            with concurrent.futures.ThreadPoolExecutor(max_workers=4) as executor:
                # Skip first page as it's usually just an intro (following the guide)
                images_to_process = images[1:] if len(images) > 1 else images
                futures = [
                    executor.submit(self.analyze_doc_image, img)
                    for img in images_to_process
                ]

                # Process with progress tracking
                with tqdm(total=len(futures), desc="Analyzing PDF pages") as pbar:
                    for future in concurrent.futures.as_completed(futures):
                        try:
                            result = future.result()
                            pages_description.append(result)
                            pbar.update(1)
                        except Exception as e:
                            logger.warning(f"Failed to process one page: {str(e)}")
                            pbar.update(1)

            # Combine all page descriptions into comprehensive text
            if pages_description:
                combined_text = "\n\n--- PAGE BREAK ---\n\n".join(pages_description)
                logger.info(f"Successfully extracted {len(combined_text)} characters using OpenAI vision")
                return combined_text
            else:
                logger.warning("No content could be extracted from PDF images")
                return "No content could be extracted from the PDF"

        except Exception as e:
            logger.error(f"Error in enhanced PDF content extraction: {str(e)}")
            raise

    def convert_pdf_content_to_images(self, pdf_content: bytes) -> List[Any]:
        """
        Convert PDF content (bytes) to images using pdf2image.
        """
        try:
            from pdf2image import convert_from_bytes

            # Convert PDF bytes to images
            images = convert_from_bytes(
                pdf_content,
                dpi=200,  # High DPI for better text recognition
                fmt='PNG',
                thread_count=4,
                use_pdftocairo=True  # Better quality
            )

            logger.info(f"Successfully converted PDF content to {len(images)} images")
            return images

        except Exception as e:
            logger.error(f"Error converting PDF content to images: {str(e)}")
            raise

    def process_pdf_complete(self, file_path: str) -> Dict[str, Any]:
        """
        Complete PDF processing pipeline following the OpenAI guide.
        Returns structured data suitable for RAG applications.
        """
        try:
            logger.info(f"Starting complete PDF processing for {file_path}")

            # Extract comprehensive content using vision
            extracted_text = self.extract_text_from_pdf(file_path)

            # Convert to images for metadata
            images = self.convert_doc_to_images(file_path)

            # Create document structure
            doc_data = {
                "filename": Path(file_path).name,
                "text": extracted_text,
                "page_count": len(images),
                "processing_method": "openai_gpt4o_vision",
                "content_chunks": self._chunk_content_by_page(extracted_text),
                "metadata": {
                    "total_characters": len(extracted_text),
                    "estimated_tokens": len(extracted_text.split()) * 1.3,
                    "vision_processed": True,
                    "concurrent_processing": True
                }
            }

            logger.info(f"Successfully processed PDF: {doc_data['metadata']}")
            return doc_data

        except Exception as e:
            logger.error(f"Error in complete PDF processing: {str(e)}")
            raise

    def _chunk_content_by_page(self, content: str) -> List[Dict[str, Any]]:
        """
        Chunk content logically by page breaks as recommended in the guide.
        """
        try:
            # Split by page breaks
            pages = content.split("--- PAGE BREAK ---")
            chunks = []

            for i, page_content in enumerate(pages):
                if page_content.strip():
                    chunk = {
                        "content": page_content.strip(),
                        "chunk_index": i,
                        "chunk_type": "page",
                        "token_count": len(page_content.split()) * 1.3,
                        "page_number": i + 1
                    }
                    chunks.append(chunk)

            logger.info(f"Created {len(chunks)} content chunks")
            return chunks

        except Exception as e:
            logger.error(f"Error chunking content: {str(e)}")
            return [{"content": content, "chunk_index": 0, "chunk_type": "full", "token_count": len(content.split()) * 1.3}]



    def generate_mcqs(self, content: str, course_name: Optional[str], question_count: int) -> List[Dict]:
        """Generate high-quality MCQs using OpenAI."""
        try:
            self._wait_for_rate_limit()

            course_context = f" for the {course_name} course" if course_name else ""

            prompt = f"""Generate exactly {question_count} high-quality multiple-choice questions{course_context} based on the following content.

REQUIREMENTS:
1. Create diverse, challenging questions that test understanding
2. Each question must have exactly 4 options (A, B, C, D)
3. Include clear explanations for correct answers
4. Vary difficulty levels (easy, medium, hard)
5. Focus on key concepts and practical applications
6. Return ONLY valid JSON - no markdown or extra text

JSON FORMAT:
{{
  "questions": [
    {{
      "content": "Clear, specific question?",
      "question_type": "multiple_choice",
      "difficulty": "easy|medium|hard",
      "topic": "Specific topic from content",
      "option_A": "First option",
      "option_B": "Second option",
      "option_C": "Third option",
      "option_D": "Fourth option",
      "answer": "A|B|C|D",
      "explanation": "Clear explanation why this answer is correct"
    }}
  ]
}}

CONTENT:
{content[:6000]}

Generate exactly {question_count} questions in valid JSON format."""

            response = self.client.chat.completions.create(
                model=self.model_name,
                messages=[
                    {"role": "system", "content": "You are an expert educator who creates high-quality multiple-choice questions. Always return valid JSON."},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.3,
                max_tokens=4000,
                response_format={"type": "json_object"}
            )

            result = json.loads(response.choices[0].message.content)
            questions = result.get("questions", [])

            logger.info(f"Generated {len(questions)} MCQs using OpenAI")
            return questions[:question_count]  # Ensure exact count

        except Exception as e:
            logger.error(f"Error generating MCQs with OpenAI: {str(e)}")
            raise


    def generate_flashcards(self, content: str, course_name: Optional[str], card_count: int) -> List[Dict]:
        """Generate high-quality flashcards using OpenAI."""
        try:
            self._wait_for_rate_limit()

            course_context = f" for the {course_name} course" if course_name else ""

            prompt = f"""Generate exactly {card_count} high-quality flashcards{course_context} based on the following content.

REQUIREMENTS:
1. Create concise, focused questions on the front
2. Provide comprehensive but clear answers on the back
3. Cover key concepts, definitions, and important facts
4. Vary difficulty and question types
5. Return ONLY valid JSON - no markdown or extra text

JSON FORMAT:
{{
  "flashcards": [
    {{
      "front": "Clear, concise question or prompt",
      "back": "Comprehensive but clear answer",
      "difficulty": "easy|medium|hard",
      "topic": "Specific topic from content",
      "card_type": "definition|concept|fact|application"
    }}
  ]
}}

CONTENT:
{content[:6000]}

Generate exactly {card_count} flashcards in valid JSON format."""

            response = self.client.chat.completions.create(
                model=self.model_name,
                messages=[
                    {"role": "system", "content": "You are an expert educator who creates effective flashcards for learning. Always return valid JSON."},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.3,
                max_tokens=3000,
                response_format={"type": "json_object"}
            )

            result = json.loads(response.choices[0].message.content)
            flashcards = result.get("flashcards", [])

            logger.info(f"Generated {len(flashcards)} flashcards using OpenAI")
            return flashcards[:card_count]  # Ensure exact count

        except Exception as e:
            logger.error(f"Error generating flashcards with OpenAI: {str(e)}")
            raise

    def summarize_notes(self, content: str, course_name: Optional[str]) -> Dict[str, Any]:
        """Generate comprehensive note summary using OpenAI."""
        try:
            self._wait_for_rate_limit()

            course_context = f" for the {course_name} course" if course_name else ""

            prompt = f"""Create a comprehensive summary of these notes{course_context}.

REQUIREMENTS:
1. Provide a clear, structured summary
2. Identify key topics and concepts
3. Extract important definitions and facts
4. Highlight main learning objectives
5. Return ONLY valid JSON - no markdown or extra text

JSON FORMAT:
{{
  "summary": {{
    "title": "Main topic or title",
    "overview": "Brief overview paragraph",
    "key_topics": ["topic1", "topic2", "topic3"],
    "main_concepts": [
      {{
        "concept": "Concept name",
        "definition": "Clear definition",
        "importance": "Why this is important"
      }}
    ],
    "learning_objectives": ["objective1", "objective2"],
    "difficulty_level": "beginner|intermediate|advanced",
    "estimated_study_time": "X hours"
  }}
}}

CONTENT:
{content[:8000]}

Generate a comprehensive summary in valid JSON format."""

            response = self.client.chat.completions.create(
                model=self.model_name,
                messages=[
                    {"role": "system", "content": "You are an expert educator who creates comprehensive, structured summaries of educational content. Always return valid JSON."},
                    {"role": "user", "content": prompt}
                ],
                temperature=0.2,
                max_tokens=2000,
                response_format={"type": "json_object"}
            )

            result = json.loads(response.choices[0].message.content)
            summary = result.get("summary", {})

            logger.info("Generated comprehensive summary using OpenAI")
            return summary

        except Exception as e:
            logger.error(f"Error generating summary with OpenAI: {str(e)}")
            raise

    # Legacy methods for compatibility
    async def chat_completion(
        self,
        messages: List[Dict[str, str]],
        model: str = "gpt-4o-mini",
        temperature: float = 0.7,
        max_tokens: Optional[int] = None
    ) -> Any:
        """Generate a chat completion using OpenAI API."""
        try:
            response = self.client.chat.completions.create(
                model=model,
                messages=messages,
                temperature=temperature,
                max_tokens=max_tokens
            )
            return response
        except Exception as e:
            logger.error(f"Error calling OpenAI API: {str(e)}")
            raise

    async def generate_embeddings(self, text: str) -> List[float]:
        """Generate embeddings for a text using OpenAI API."""
        try:
            response = self.client.embeddings.create(
                model="text-embedding-3-small",
                input=text
            )
            return response.data[0].embedding
        except Exception as e:
            logger.error(f"Error generating embeddings: {str(e)}")
            raise


# Create a singleton instance
openai_service = OpenAIService()
