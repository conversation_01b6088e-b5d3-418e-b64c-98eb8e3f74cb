import logging
from datetime import datetime, timezone
from typing import Dict, List, Optional

from sqlalchemy.orm import Session
from sqlalchemy import func, desc

from app.models.ai_analytics import AIAssistantInteraction, InteractionType

logger = logging.getLogger(__name__)


def create_interaction(
    db: Session,  # Kept for API compatibility
    *,
    query: str,
    response: str,
    interaction_type: InteractionType,
    metadata: Optional[Dict] = None,
    response_time_ms: Optional[int] = None,
    success: bool = True,
    **kwargs  # Catch all other parameters for backward compatibility
) -> AIAssistantInteraction:
    """
    Log an AI assistant interaction without storing it in the database.
    This is a performance optimization to avoid database operations.

    Args:
        query: The query or question asked
        response: The response provided by the assistant
        interaction_type: Type of interaction (general, practice, etc.)
        metadata: Additional metadata about the interaction
        response_time_ms: Response time in milliseconds
        success: Whether the interaction was successful
        **kwargs: Additional parameters for backward compatibility

    Returns:
        A dummy interaction record
    """
    # For performance reasons, we'll just log the interaction without storing it
    # This avoids database errors and speeds up the response
    logger.info(f"AI Interaction - Type: {interaction_type}, Query: {query[:50]}..., Success: {success}, Time: {response_time_ms}ms")

    # Return a dummy object without touching the database
    return AIAssistantInteraction(
        id=-1,
        query=query,
        response=response[:100] + "...",  # Truncate for memory efficiency
        interaction_type=interaction_type,
        interaction_metadata=metadata or {},
        success=success,
        timestamp=datetime.now(timezone.utc)
    )


def get_common_queries(
    db: Session,
    *,
    limit: int = 10,
    days: int = 30,
    interaction_type: Optional[InteractionType] = None
) -> List[Dict]:
    """
    Get the most common queries asked to the AI assistant.

    Args:
        db: Database session
        limit: Maximum number of results to return
        days: Number of days to look back
        interaction_type: Filter by interaction type

    Returns:
        List of common queries with their counts
    """
    try:
        query = db.query(
            AIAssistantInteraction.query,
            func.count(AIAssistantInteraction.id).label("count")
        ).filter(
            AIAssistantInteraction.timestamp >= func.now() - func.interval(f"{days} days")
        )

        if interaction_type:
            query = query.filter(AIAssistantInteraction.interaction_type == interaction_type)

        result = query.group_by(
            AIAssistantInteraction.query
        ).order_by(
            desc("count")
        ).limit(limit).all()

        return [{"query": item[0], "count": item[1]} for item in result]
    except Exception as e:
        logger.error(f"Error getting common queries: {str(e)}")
        return []


def get_user_engagement_metrics(
    db: Session,
    *,
    days: int = 30
) -> Dict:
    """
    Get user engagement metrics for the AI assistant.

    Args:
        db: Database session
        days: Number of days to look back

    Returns:
        Dictionary with engagement metrics
    """
    try:
        # Total interactions
        total_interactions = db.query(func.count(AIAssistantInteraction.id)).filter(
            AIAssistantInteraction.timestamp >= func.now() - func.interval(f"{days} days")
        ).scalar() or 0

        # Unique users
        unique_users = db.query(func.count(func.distinct(AIAssistantInteraction.user_id))).filter(
            AIAssistantInteraction.timestamp >= func.now() - func.interval(f"{days} days"),
            AIAssistantInteraction.user_id.isnot(None)
        ).scalar() or 0

        # Average interactions per user
        avg_interactions_per_user = total_interactions / unique_users if unique_users > 0 else 0

        # Success rate
        success_count = db.query(func.count(AIAssistantInteraction.id)).filter(
            AIAssistantInteraction.timestamp >= func.now() - func.interval(f"{days} days"),
            AIAssistantInteraction.success == True
        ).scalar() or 0

        success_rate = (success_count / total_interactions) * 100 if total_interactions > 0 else 0

        # Interactions by type
        interactions_by_type = db.query(
            AIAssistantInteraction.interaction_type,
            func.count(AIAssistantInteraction.id).label("count")
        ).filter(
            AIAssistantInteraction.timestamp >= func.now() - func.interval(f"{days} days")
        ).group_by(
            AIAssistantInteraction.interaction_type
        ).all()

        interactions_by_type_dict = {str(item[0]): item[1] for item in interactions_by_type}

        return {
            "total_interactions": total_interactions,
            "unique_users": unique_users,
            "avg_interactions_per_user": avg_interactions_per_user,
            "success_rate": success_rate,
            "interactions_by_type": interactions_by_type_dict
        }
    except Exception as e:
        logger.error(f"Error getting user engagement metrics: {str(e)}")
        return {
            "total_interactions": 0,
            "unique_users": 0,
            "avg_interactions_per_user": 0,
            "success_rate": 0,
            "interactions_by_type": {}
        }
