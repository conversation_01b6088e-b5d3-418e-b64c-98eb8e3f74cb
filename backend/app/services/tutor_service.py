from typing import List, Optional
from decimal import Decimal
from datetime import datetime, timedelta

from sqlalchemy.orm import Session, joinedload
from sqlalchemy import func, and_, or_

from app.models.tutor import TutorProfile, TutorReview
from app.models.user import User, UserRole
from app.models.course import Course
from app.models.session import Session as TutorSession, SessionStatus
from app.schemas import tutor as tutor_schemas
from app.crud.base import CRUDBase


class TutorProfileService(CRUDBase[TutorProfile, tutor_schemas.TutorProfileCreate, tutor_schemas.TutorProfileUpdate]):
    def __init__(self):
        super().__init__(TutorProfile)

    def get_by_user_id(self, db: Session, user_id: int) -> Optional[TutorProfile]:
        """Get tutor profile by user ID"""
        return db.query(TutorProfile).filter(TutorProfile.user_id == user_id).first()

    def create_for_user(self, db: Session, *, obj_in: tutor_schemas.TutorProfileCreate, user_id: int) -> TutorProfile:
        """Create tutor profile for a specific user"""
        # Create the profile with basic fields first
        db_obj = TutorProfile(
            user_id=user_id,
            bio=obj_in.bio,
            experience_years=obj_in.experience_years,
            hourly_rate=obj_in.hourly_rate,
            is_available=obj_in.is_available,
            preferred_session_type=obj_in.preferred_session_type,
            max_students_per_session=obj_in.max_students_per_session,
            linkedin_url=obj_in.linkedin_url,
            website_url=obj_in.website_url,
        )

        # Try to set new fields if they exist in the database
        try:
            if hasattr(obj_in, 'location'):
                db_obj.location = obj_in.location
            if hasattr(obj_in, 'gender'):
                db_obj.gender = obj_in.gender
            if hasattr(obj_in, 'languages') and obj_in.languages:
                db_obj.languages = ','.join(obj_in.languages)
            if hasattr(obj_in, 'custom_specializations') and obj_in.custom_specializations:
                db_obj.custom_specializations = ','.join(obj_in.custom_specializations)
        except Exception as e:
            # If the columns don't exist, just continue without them
            print(f"Warning: Could not set new fields: {e}")
        db.add(db_obj)
        db.flush()  # Get the ID

        # Add specializations
        if obj_in.specialization_ids:
            courses = db.query(Course).filter(Course.id.in_(obj_in.specialization_ids)).all()
            db_obj.specializations = courses

        # Mark the user's profile as completed when creating tutor profile
        user = db.query(User).filter(User.id == user_id).first()
        if user:
            user.profile_completed = True
            db.add(user)

        db.commit()
        db.refresh(db_obj)
        return db_obj

    def update_with_specializations(
        self, db: Session, *, db_obj: TutorProfile, obj_in: tutor_schemas.TutorProfileUpdate
    ) -> TutorProfile:
        """Update tutor profile including specializations"""
        # Update basic fields
        update_data = obj_in.model_dump(exclude_unset=True, exclude={"specialization_ids", "languages"})
        for field, value in update_data.items():
            if hasattr(db_obj, field):  # Only set if the field exists
                setattr(db_obj, field, value)

        # Handle new fields separately with error handling
        try:
            if hasattr(obj_in, 'location') and obj_in.location is not None:
                db_obj.location = obj_in.location
            if hasattr(obj_in, 'gender') and obj_in.gender is not None:
                db_obj.gender = obj_in.gender
            if hasattr(obj_in, 'languages') and obj_in.languages is not None:
                db_obj.languages = ','.join(obj_in.languages) if obj_in.languages else None
            if hasattr(obj_in, 'custom_specializations') and obj_in.custom_specializations is not None:
                db_obj.custom_specializations = ','.join(obj_in.custom_specializations) if obj_in.custom_specializations else None
        except Exception as e:
            print(f"Warning: Could not update new fields: {e}")

        # Update specializations if provided
        if obj_in.specialization_ids is not None:
            courses = db.query(Course).filter(Course.id.in_(obj_in.specialization_ids)).all()
            db_obj.specializations = courses

        db.commit()
        db.refresh(db_obj)
        return db_obj

    def get_with_details(self, db: Session, id: int) -> Optional[TutorProfile]:
        """Get tutor profile with user and specializations"""
        return (
            db.query(TutorProfile)
            .options(
                joinedload(TutorProfile.user),
                joinedload(TutorProfile.specializations)
            )
            .filter(TutorProfile.id == id)
            .first()
        )

    def search_tutors(
        self, 
        db: Session, 
        *, 
        filters: tutor_schemas.TutorSearchFilters,
        skip: int = 0,
        limit: int = 100
    ) -> List[TutorProfile]:
        """Search tutors with filters"""
        query = (
            db.query(TutorProfile)
            .join(User)
            .options(
                joinedload(TutorProfile.user),
                joinedload(TutorProfile.specializations)
            )
            .filter(User.role == UserRole.TUTOR)
            .filter(User.is_active == True)
        )

        # Apply filters
        if filters.is_available:
            query = query.filter(TutorProfile.is_available == True)

        if filters.min_rating is not None:
            query = query.filter(TutorProfile.average_rating >= filters.min_rating)

        if filters.max_hourly_rate is not None:
            query = query.filter(TutorProfile.hourly_rate <= filters.max_hourly_rate)

        if filters.session_type:
            if filters.session_type in ['online', 'in_person']:
                query = query.filter(
                    or_(
                        TutorProfile.preferred_session_type == filters.session_type,
                        TutorProfile.preferred_session_type == 'both'
                    )
                )

        if filters.experience_years_min is not None:
            query = query.filter(TutorProfile.experience_years >= filters.experience_years_min)

        if filters.course_ids:
            query = query.join(TutorProfile.specializations).filter(
                Course.id.in_(filters.course_ids)
            )

        return query.offset(skip).limit(limit).all()

    def get_dashboard_stats(self, db: Session, tutor_profile_id: int) -> tutor_schemas.TutorDashboardStats:
        """Get dashboard statistics for a tutor"""
        # Get basic session counts
        total_sessions = (
            db.query(func.count(TutorSession.id))
            .filter(TutorSession.tutor_id == tutor_profile_id)
            .scalar()
        )

        completed_sessions = (
            db.query(func.count(TutorSession.id))
            .filter(
                and_(
                    TutorSession.tutor_id == tutor_profile_id,
                    TutorSession.status == SessionStatus.COMPLETED
                )
            )
            .scalar()
        )

        upcoming_sessions = (
            db.query(func.count(TutorSession.id))
            .filter(
                and_(
                    TutorSession.tutor_id == tutor_profile_id,
                    TutorSession.status == SessionStatus.SCHEDULED,
                    TutorSession.start_time > datetime.utcnow()
                )
            )
            .scalar()
        )

        # Get unique students count
        total_students = (
            db.query(func.count(func.distinct(TutorSession.student_id)))
            .filter(
                and_(
                    TutorSession.tutor_id == tutor_profile_id,
                    TutorSession.student_id.isnot(None)
                )
            )
            .scalar()
        )

        # Get tutor profile for rating info
        tutor_profile = db.query(TutorProfile).filter(TutorProfile.id == tutor_profile_id).first()

        # Calculate this month's sessions
        start_of_month = datetime.utcnow().replace(day=1, hour=0, minute=0, second=0, microsecond=0)
        this_month_sessions = (
            db.query(func.count(TutorSession.id))
            .filter(
                and_(
                    TutorSession.tutor_id == tutor_profile_id,
                    TutorSession.created_at >= start_of_month
                )
            )
            .scalar()
        )

        return tutor_schemas.TutorDashboardStats(
            total_sessions=total_sessions or 0,
            completed_sessions=completed_sessions or 0,
            upcoming_sessions=upcoming_sessions or 0,
            total_students=total_students or 0,
            average_rating=tutor_profile.average_rating if tutor_profile else None,
            total_reviews=tutor_profile.total_reviews if tutor_profile else 0,
            total_earnings=None,  # To be implemented with payment system
            this_month_sessions=this_month_sessions or 0,
            this_month_earnings=None,  # To be implemented with payment system
        )


    def get_tutor_earnings(self, db: Session, tutor_profile_id: int) -> dict:
        """Get earnings data for a tutor"""
        # For now, return mock data since payment system is not implemented
        # TODO: Implement real earnings calculation when payment system is ready

        # Get basic session stats for calculation
        total_sessions = (
            db.query(func.count(TutorSession.id))
            .filter(TutorSession.tutor_id == tutor_profile_id)
            .filter(TutorSession.status == SessionStatus.COMPLETED)
            .scalar() or 0
        )

        # Get tutor profile for hourly rate
        tutor_profile = db.query(TutorProfile).filter(TutorProfile.id == tutor_profile_id).first()
        hourly_rate = float(tutor_profile.hourly_rate) if tutor_profile and tutor_profile.hourly_rate else 50.0

        # Calculate estimated earnings (assuming 1 hour per session)
        estimated_total = total_sessions * hourly_rate

        # Calculate this month's data
        start_of_month = datetime.utcnow().replace(day=1, hour=0, minute=0, second=0, microsecond=0)
        this_month_sessions = (
            db.query(func.count(TutorSession.id))
            .filter(TutorSession.tutor_id == tutor_profile_id)
            .filter(TutorSession.status == SessionStatus.COMPLETED)
            .filter(TutorSession.created_at >= start_of_month)
            .scalar() or 0
        )

        this_month_earnings = this_month_sessions * hourly_rate

        # Calculate last month's data
        last_month_start = (start_of_month - timedelta(days=1)).replace(day=1)
        last_month_sessions = (
            db.query(func.count(TutorSession.id))
            .filter(TutorSession.tutor_id == tutor_profile_id)
            .filter(TutorSession.status == SessionStatus.COMPLETED)
            .filter(TutorSession.created_at >= last_month_start)
            .filter(TutorSession.created_at < start_of_month)
            .scalar() or 0
        )

        last_month_earnings = last_month_sessions * hourly_rate

        # Calculate growth
        monthly_growth = 0.0
        if last_month_earnings > 0:
            monthly_growth = ((this_month_earnings - last_month_earnings) / last_month_earnings) * 100

        return {
            "total_earnings": estimated_total,
            "this_month_earnings": this_month_earnings,
            "last_month_earnings": last_month_earnings,
            "pending_payments": 0.0,  # TODO: Implement when payment system is ready
            "completed_sessions": total_sessions,
            "average_session_rate": hourly_rate,
            "monthly_growth": monthly_growth,
            "recent_payments": []  # TODO: Implement when payment system is ready
        }

    def get_tutor_analytics(self, db: Session, tutor_profile_id: int) -> dict:
        """Get analytics data for a tutor"""

        # Session statistics
        total_sessions = (
            db.query(func.count(TutorSession.id))
            .filter(TutorSession.tutor_id == tutor_profile_id)
            .scalar() or 0
        )

        completed_sessions = (
            db.query(func.count(TutorSession.id))
            .filter(TutorSession.tutor_id == tutor_profile_id)
            .filter(TutorSession.status == SessionStatus.COMPLETED)
            .scalar() or 0
        )

        cancelled_sessions = (
            db.query(func.count(TutorSession.id))
            .filter(TutorSession.tutor_id == tutor_profile_id)
            .filter(TutorSession.status == SessionStatus.CANCELLED)
            .scalar() or 0
        )

        completion_rate = (completed_sessions / total_sessions * 100) if total_sessions > 0 else 0

        # Student statistics
        total_students = (
            db.query(func.count(func.distinct(TutorSession.student_id)))
            .filter(TutorSession.tutor_id == tutor_profile_id)
            .filter(TutorSession.student_id.isnot(None))
            .scalar() or 0
        )

        # Active students (had a session in the last 30 days)
        thirty_days_ago = datetime.utcnow() - timedelta(days=30)
        active_students = (
            db.query(func.count(func.distinct(TutorSession.student_id)))
            .filter(TutorSession.tutor_id == tutor_profile_id)
            .filter(TutorSession.student_id.isnot(None))
            .filter(TutorSession.created_at >= thirty_days_ago)
            .scalar() or 0
        )

        retention_rate = (active_students / total_students * 100) if total_students > 0 else 0

        # Course performance
        course_performance = (
            db.query(
                Course.name.label('course'),
                func.count(TutorSession.id).label('sessions'),
                func.count(func.distinct(TutorSession.student_id)).label('students'),
                func.coalesce(func.avg(TutorReview.rating), 0).label('avg_rating')
            )
            .join(TutorSession, Course.id == TutorSession.course_id)
            .outerjoin(TutorReview, TutorSession.id == TutorReview.session_id)
            .filter(TutorSession.tutor_id == tutor_profile_id)
            .group_by(Course.id, Course.name)
            .all()
        )

        # Convert to list of dictionaries
        course_data = []
        for course in course_performance:
            course_data.append({
                'course': course.course,
                'sessions': course.sessions,
                'students': course.students,
                'avg_rating': float(course.avg_rating) if course.avg_rating else 0.0,
                'earnings': 0.0  # TODO: Calculate when payment system is implemented
            })

        return {
            "session_stats": {
                "total_sessions": total_sessions,
                "completed_sessions": completed_sessions,
                "cancelled_sessions": cancelled_sessions,
                "completion_rate": completion_rate
            },
            "student_stats": {
                "total_students": total_students,
                "active_students": active_students,
                "retention_rate": retention_rate
            },
            "course_performance": course_data,
            "time_slot_analysis": [
                {"time_slot": "9:00 AM - 12:00 PM", "sessions": 15, "utilization": 75},
                {"time_slot": "12:00 PM - 3:00 PM", "sessions": 20, "utilization": 85},
                {"time_slot": "3:00 PM - 6:00 PM", "sessions": 25, "utilization": 90},
                {"time_slot": "6:00 PM - 9:00 PM", "sessions": 18, "utilization": 80}
            ],  # TODO: Calculate real time slot data
            "monthly_trends": []  # TODO: Calculate monthly trends
        }


class TutorReviewService(CRUDBase[TutorReview, tutor_schemas.TutorReviewCreate, tutor_schemas.TutorReviewUpdate]):
    def __init__(self):
        super().__init__(TutorReview)

    def create_review(
        self, 
        db: Session, 
        *, 
        obj_in: tutor_schemas.TutorReviewCreate, 
        student_id: int
    ) -> TutorReview:
        """Create a review and update tutor's average rating"""
        # Create the review
        db_obj = TutorReview(
            tutor_profile_id=obj_in.tutor_profile_id,
            student_id=student_id,
            session_id=obj_in.session_id,
            rating=obj_in.rating,
            comment=obj_in.comment,
        )
        db.add(db_obj)
        db.flush()

        # Update tutor's average rating
        self._update_tutor_rating(db, obj_in.tutor_profile_id)

        db.commit()
        db.refresh(db_obj)
        return db_obj

    def _update_tutor_rating(self, db: Session, tutor_profile_id: int):
        """Update tutor's average rating and review count"""
        # Calculate new average rating
        result = (
            db.query(
                func.avg(TutorReview.rating).label('avg_rating'),
                func.count(TutorReview.id).label('total_reviews')
            )
            .filter(TutorReview.tutor_profile_id == tutor_profile_id)
            .first()
        )

        # Update tutor profile
        tutor_profile = db.query(TutorProfile).filter(TutorProfile.id == tutor_profile_id).first()
        if tutor_profile:
            tutor_profile.average_rating = result.avg_rating
            tutor_profile.total_reviews = result.total_reviews

    def get_reviews_for_tutor(
        self,
        db: Session,
        tutor_profile_id: int,
        skip: int = 0,
        limit: int = 100
    ) -> List[TutorReview]:
        """Get reviews for a specific tutor"""
        return (
            db.query(TutorReview)
            .options(joinedload(TutorReview.student))
            .filter(TutorReview.tutor_profile_id == tutor_profile_id)
            .order_by(TutorReview.created_at.desc())
            .offset(skip)
            .limit(limit)
            .all()
        )

    def get_review_by_student_and_tutor(
        self,
        db: Session,
        student_id: int,
        tutor_profile_id: int
    ) -> Optional[TutorReview]:
        """Check if a student has already reviewed a specific tutor"""
        return (
            db.query(TutorReview)
            .filter(
                and_(
                    TutorReview.student_id == student_id,
                    TutorReview.tutor_profile_id == tutor_profile_id
                )
            )
            .first()
        )


# Service instances
tutor_profile_service = TutorProfileService()
tutor_review_service = TutorReviewService()
