from datetime import datetime, timezone
from typing import Optional, Dict, Any
from sqlalchemy.orm import Session
from sqlalchemy import and_

from app.models.student_usage_limit import StudentUsageLimit, UsageType
from app.models.user import User, UserRole
import logging

logger = logging.getLogger(__name__)


class UsageLimitService:
    """Service for managing student usage limits"""
    
    DEFAULT_MONTHLY_LIMIT = 5
    
    @staticmethod
    def get_current_month() -> str:
        """Get current month in YYYY-MM format"""
        return datetime.now(timezone.utc).strftime("%Y-%m")

    @staticmethod
    def calculate_reset_date(month: str) -> str:
        """Calculate the reset date for the next month"""
        try:
            year = int(month[:4])
            current_month = int(month[5:])

            # Calculate next month
            if current_month == 12:
                next_year = year + 1
                next_month = 1
            else:
                next_year = year
                next_month = current_month + 1

            return f"{next_year}-{next_month:02d}-01"
        except (Value<PERSON>rror, IndexError):
            # Fallback to next month from current date
            now = datetime.now(timezone.utc)
            if now.month == 12:
                return f"{now.year + 1}-01-01"
            else:
                return f"{now.year}-{now.month + 1:02d}-01"

    @staticmethod
    def get_usage_type_display_name(usage_type: UsageType) -> str:
        """Get user-friendly display name for usage type"""
        display_names = {
            UsageType.PDF_UPLOAD: "PDF upload",
            UsageType.MCQ_GENERATION: "MCQ generation",
            UsageType.FLASHCARD_GENERATION: "flashcard generation",
            UsageType.SUMMARY_GENERATION: "summary generation"
        }
        return display_names.get(usage_type, "usage")
    
    @staticmethod
    def get_or_create_usage_record(
        db: Session,
        student_id: int,
        usage_type: UsageType,
        month: Optional[str] = None
    ) -> StudentUsageLimit:
        """
        Get or create a usage record for a student for the current month
        
        Args:
            db: Database session
            student_id: Student user ID
            usage_type: Type of usage to track
            month: Month in YYYY-MM format (defaults to current month)
            
        Returns:
            StudentUsageLimit record
        """
        if month is None:
            month = UsageLimitService.get_current_month()
        
        # Try to get existing record
        usage_record = db.query(StudentUsageLimit).filter(
            and_(
                StudentUsageLimit.student_id == student_id,
                StudentUsageLimit.usage_type == usage_type,
                StudentUsageLimit.usage_month == month
            )
        ).first()
        
        # Create new record if it doesn't exist
        if not usage_record:
            usage_record = StudentUsageLimit(
                student_id=student_id,
                usage_type=usage_type,
                usage_count=0,
                usage_limit=UsageLimitService.DEFAULT_MONTHLY_LIMIT,
                usage_month=month
            )
            db.add(usage_record)
            db.commit()
            db.refresh(usage_record)
            logger.info(f"Created new usage record for student {student_id}, type {usage_type}, month {month}")
        
        return usage_record
    
    @staticmethod
    def check_usage_limit(
        db: Session,
        student_id: int,
        usage_type: UsageType,
        user: Optional[User] = None
    ) -> Dict[str, Any]:
        """
        Check if student has reached their usage limit for the current month
        
        Args:
            db: Database session
            student_id: Student user ID
            usage_type: Type of usage to check
            user: Optional User object for role validation
            
        Returns:
            Dictionary with limit status information
        """
        # Validate user is a student if user object is provided
        if user and user.role != UserRole.STUDENT:
            return {
                "allowed": False,
                "reason": "Only students have usage limits",
                "current_usage": 0,
                "limit": 0,
                "remaining": 0
            }
        
        # Get usage record for current month
        usage_record = UsageLimitService.get_or_create_usage_record(
            db, student_id, usage_type
        )
        
        is_allowed = not usage_record.is_limit_reached
        
        return {
            "allowed": is_allowed,
            "reason": "Usage limit reached for this month" if not is_allowed else "Within usage limit",
            "current_usage": usage_record.usage_count,
            "limit": usage_record.usage_limit,
            "remaining": usage_record.remaining_usage,
            "month": usage_record.usage_month,
            "reset_date": UsageLimitService.calculate_reset_date(usage_record.usage_month),
            "usage_type": usage_type.value,
            "usage_type_display": UsageLimitService.get_usage_type_display_name(usage_type)
        }

    @staticmethod
    def create_usage_limit_error_response(usage_check: Dict[str, Any]) -> Dict[str, Any]:
        """Create a consistent error response for usage limit exceeded"""
        usage_type_display = usage_check.get("usage_type_display", "usage")

        return {
            "error": f"Monthly {usage_type_display} limit reached",
            "usage_type": usage_check["usage_type"],
            "current_usage": usage_check["current_usage"],
            "limit": usage_check["limit"],
            "remaining": usage_check["remaining"],
            "month": usage_check["month"],
            "reset_date": usage_check["reset_date"],
            "message": f"You have used {usage_check['current_usage']} out of {usage_check['limit']} {usage_type_display}s this month. Your limit will reset on {usage_check['reset_date']}."
        }

    @staticmethod
    def increment_usage(
        db: Session,
        student_id: int,
        usage_type: UsageType,
        increment: int = 1
    ) -> StudentUsageLimit:
        """
        Increment usage count for a student
        
        Args:
            db: Database session
            student_id: Student user ID
            usage_type: Type of usage to increment
            increment: Amount to increment by (default: 1)
            
        Returns:
            Updated StudentUsageLimit record
        """
        usage_record = UsageLimitService.get_or_create_usage_record(
            db, student_id, usage_type
        )
        
        usage_record.usage_count += increment
        db.commit()
        db.refresh(usage_record)
        
        logger.info(f"Incremented usage for student {student_id}, type {usage_type}: {usage_record.usage_count}/{usage_record.usage_limit}")
        
        return usage_record
    
    @staticmethod
    def get_all_usage_stats(
        db: Session,
        student_id: int,
        month: Optional[str] = None
    ) -> Dict[str, Dict[str, Any]]:
        """
        Get usage statistics for all usage types for a student
        
        Args:
            db: Database session
            student_id: Student user ID
            month: Month in YYYY-MM format (defaults to current month)
            
        Returns:
            Dictionary with usage stats for each usage type
        """
        if month is None:
            month = UsageLimitService.get_current_month()
        
        stats = {}
        
        for usage_type in UsageType:
            usage_record = UsageLimitService.get_or_create_usage_record(
                db, student_id, usage_type, month
            )
            
            stats[usage_type.value] = {
                "current_usage": usage_record.usage_count,
                "limit": usage_record.usage_limit,
                "remaining": usage_record.remaining_usage,
                "is_limit_reached": usage_record.is_limit_reached,
                "month": usage_record.usage_month
            }
        
        return stats
    
    @staticmethod
    def reset_monthly_usage(
        db: Session,
        student_id: Optional[int] = None,
        usage_type: Optional[UsageType] = None
    ) -> int:
        """
        Reset usage counts (typically called at the start of a new month)
        
        Args:
            db: Database session
            student_id: Optional specific student ID (if None, resets for all students)
            usage_type: Optional specific usage type (if None, resets all types)
            
        Returns:
            Number of records reset
        """
        query = db.query(StudentUsageLimit)
        
        if student_id:
            query = query.filter(StudentUsageLimit.student_id == student_id)
        
        if usage_type:
            query = query.filter(StudentUsageLimit.usage_type == usage_type)
        
        # Only reset records from previous months
        current_month = UsageLimitService.get_current_month()
        query = query.filter(StudentUsageLimit.usage_month != current_month)
        
        records = query.all()
        count = len(records)
        
        for record in records:
            record.usage_count = 0
        
        db.commit()
        
        logger.info(f"Reset usage for {count} records")
        return count


# Create a singleton instance
usage_limit_service = UsageLimitService()
