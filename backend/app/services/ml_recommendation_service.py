import logging
# Heavy imports - lazy loaded for faster startup
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Optional, Tuple, Any
from sqlalchemy.orm import Session
from sqlalchemy import func, desc, and_, or_, Float, Integer
import joblib
import os
from pathlib import Path

# ML imports - lazy loaded for faster startup
# These will be imported only when ML functionality is actually used

from app.models.ml_recommendations import (
    UserBehaviorEvent, UserFeatureVector, ContentFeatureVector,
    MLRecommendation, MLModelMetadata, UserLearningSession
)
from app.models.question import Question
from app.models.student_progress import StudentQuestionAttempt
# FlashCard model removed - using Gemini Files API instead
from app.models.course import Course
from app.models.user import User, user_course
from app.models.department import Department

logger = logging.getLogger(__name__)


def _lazy_import_sklearn():
    """Lazy import sklearn modules to improve startup time."""
    try:
        from sklearn.feature_extraction.text import TfidfVectorizer
        from sklearn.metrics.pairwise import cosine_similarity
        from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
        from sklearn.model_selection import train_test_split, cross_val_score
        from sklearn.preprocessing import StandardScaler, LabelEncoder
        from sklearn.decomposition import PCA
        from sklearn.cluster import KMeans
        from sklearn.neighbors import NearestNeighbors
        from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score

        return {
            'TfidfVectorizer': TfidfVectorizer,
            'cosine_similarity': cosine_similarity,
            'RandomForestRegressor': RandomForestRegressor,
            'GradientBoostingRegressor': GradientBoostingRegressor,
            'train_test_split': train_test_split,
            'cross_val_score': cross_val_score,
            'StandardScaler': StandardScaler,
            'LabelEncoder': LabelEncoder,
            'PCA': PCA,
            'KMeans': KMeans,
            'NearestNeighbors': NearestNeighbors,
            'mean_squared_error': mean_squared_error,
            'mean_absolute_error': mean_absolute_error,
            'r2_score': r2_score
        }
    except ImportError as e:
        logger.warning(f"scikit-learn not available: {e}")
        return None


class MLRecommendationService:
    """Machine Learning-based recommendation service"""
    
    def __init__(self):
        self.logger = logger
        self.models_dir = Path("ml_models")
        self.models_dir.mkdir(exist_ok=True)

        # Initialize models
        self.user_similarity_model = None
        self.content_recommendation_model = None
        self.performance_prediction_model = None
        self.feature_scaler = None  # Will be initialized when needed
        self._sklearn_modules = None  # Lazy loaded sklearn modules
        
        # Model configurations
        self.feature_dim = 50  # Dimensionality of feature vectors
        self.min_interactions = 5  # Minimum interactions needed for recommendations

    def _get_sklearn_modules(self):
        """Get sklearn modules, loading them lazily if needed."""
        if self._sklearn_modules is None:
            self._sklearn_modules = _lazy_import_sklearn()
            if self._sklearn_modules is None:
                raise ImportError("scikit-learn is required for ML functionality but not available")
        return self._sklearn_modules

    def _ensure_scaler_initialized(self):
        """Ensure the feature scaler is initialized."""
        if self.feature_scaler is None:
            sklearn = self._get_sklearn_modules()
            self.feature_scaler = sklearn['StandardScaler']()

    def track_user_behavior(
        self, 
        db: Session, 
        user_id: int,
        event_type: str,
        content_type: str,
        content_id: int,
        **kwargs
    ) -> UserBehaviorEvent:
        """Track user behavior event for ML training"""
        
        # Get current time info
        now = datetime.now(timezone.utc)
        
        event = UserBehaviorEvent(
            user_id=user_id,
            event_type=event_type,
            content_type=content_type,
            content_id=content_id,
            course_id=kwargs.get('course_id'),
            topic=kwargs.get('topic'),
            difficulty=kwargs.get('difficulty'),
            is_correct=kwargs.get('is_correct'),
            response_time_seconds=kwargs.get('response_time_seconds'),
            confidence_level=kwargs.get('confidence_level'),
            session_id=kwargs.get('session_id'),
            device_type=kwargs.get('device_type'),
            time_of_day=now.hour,
            day_of_week=now.weekday(),
            event_metadata=kwargs.get('event_metadata', {})
        )
        
        db.add(event)
        db.commit()
        db.refresh(event)
        
        # Update user feature vector asynchronously
        self._update_user_features(db, user_id)
        
        return event
    
    def _update_user_features(self, db: Session, user_id: int):
        """Update user feature vector based on recent behavior"""
        try:
            # Get user's behavior events from last 30 days
            cutoff_date = datetime.now(timezone.utc) - timedelta(days=30)
            
            events = db.query(UserBehaviorEvent).filter(
                UserBehaviorEvent.user_id == user_id,
                UserBehaviorEvent.created_at >= cutoff_date
            ).all()
            
            if len(events) < self.min_interactions:
                return
            
            # Calculate features
            features = self._calculate_user_features(events)
            
            # Get or create user feature vector
            user_features = db.query(UserFeatureVector).filter(
                UserFeatureVector.user_id == user_id
            ).first()
            
            if not user_features:
                user_features = UserFeatureVector(user_id=user_id)
                db.add(user_features)
            
            # Update features (convert numpy types to Python native types)
            for key, value in features.items():
                if hasattr(user_features, key):
                    # Convert numpy types to Python native types
                    if hasattr(value, 'item'):  # numpy scalar
                        value = value.item()
                    elif isinstance(value, np.ndarray):
                        value = value.tolist()
                    setattr(user_features, key, value)

            user_features.last_updated = datetime.now(timezone.utc)
            user_features.feature_version = (user_features.feature_version or 0) + 1
            
            db.commit()
            
        except Exception as e:
            self.logger.error(f"Error updating user features for user {user_id}: {str(e)}")
    
    def _calculate_user_features(self, events: List[UserBehaviorEvent]) -> Dict[str, Any]:
        """Calculate user features from behavior events"""
        if not events:
            return {}
        
        # Convert to DataFrame for easier analysis
        event_data = []
        for event in events:
            event_data.append({
                'event_type': event.event_type,
                'content_type': event.content_type,
                'is_correct': event.is_correct,
                'response_time': event.response_time_seconds,
                'topic': event.topic,
                'difficulty': event.difficulty,
                'time_of_day': event.time_of_day,
                'day_of_week': event.day_of_week,
                'created_at': event.created_at
            })
        
        df = pd.DataFrame(event_data)
        
        features = {}
        
        # Basic performance features
        mcq_events = df[df['event_type'] == 'mcq_attempt']
        if len(mcq_events) > 0:
            features['accuracy_rate'] = float(mcq_events['is_correct'].mean()) if mcq_events['is_correct'].notna().any() else 0.0
            features['avg_response_time'] = float(mcq_events['response_time'].mean()) if mcq_events['response_time'].notna().any() else 0.0
        
        # Study frequency (sessions per week)
        unique_days = df['created_at'].dt.date.nunique()
        features['study_frequency'] = (unique_days / 4.0) if unique_days > 0 else 0.0  # Approximate weeks
        
        # Session duration (approximate from event timestamps)
        daily_events = df.groupby(df['created_at'].dt.date)
        session_durations = []
        for date, day_events in daily_events:
            if len(day_events) > 1:
                duration = (day_events['created_at'].max() - day_events['created_at'].min()).total_seconds() / 60
                session_durations.append(min(duration, 180))  # Cap at 3 hours
        
        features['session_duration_avg'] = float(np.mean(session_durations)) if session_durations else 0.0
        
        # Preferred difficulty
        if 'difficulty' in df.columns and df['difficulty'].notna().any():
            difficulty_counts = df['difficulty'].value_counts()
            features['preferred_difficulty'] = difficulty_counts.index[0] if len(difficulty_counts) > 0 else None
        
        # Preferred time of day
        if 'time_of_day' in df.columns and df['time_of_day'].notna().any():
            time_counts = df['time_of_day'].value_counts()
            features['preferred_time_of_day'] = int(time_counts.index[0]) if len(time_counts) > 0 else None
        
        # Topic performance
        topic_performance = {}
        if 'topic' in df.columns and df['topic'].notna().any():
            for topic in df['topic'].dropna().unique():
                topic_events = df[df['topic'] == topic]
                if len(topic_events) > 0 and topic_events['is_correct'].notna().any():
                    topic_performance[topic] = float(topic_events['is_correct'].mean())
        
        features['topic_performance'] = topic_performance
        
        # Engagement features
        total_events = len(df)
        help_events = len(df[df['event_type'] == 'help_request'])
        bookmark_events = len(df[df['event_type'] == 'bookmark_add'])
        
        features['help_seeking_rate'] = help_events / total_events if total_events > 0 else 0.0
        features['bookmark_rate'] = bookmark_events / total_events if total_events > 0 else 0.0
        
        # Consistency score (how regularly user studies)
        study_days = df['created_at'].dt.date.nunique()
        total_days = (df['created_at'].max() - df['created_at'].min()).days + 1
        features['consistency_score'] = study_days / total_days if total_days > 0 else 0.0
        
        # Active days per week
        features['active_days_per_week'] = min(study_days / 4.0, 7.0)  # Approximate weeks, cap at 7
        
        # Generate feature vector for similarity calculations
        feature_vector = [
            features.get('accuracy_rate', 0.0),
            features.get('avg_response_time', 0.0) / 100.0,  # Normalize
            features.get('study_frequency', 0.0),
            features.get('session_duration_avg', 0.0) / 60.0,  # Normalize
            features.get('help_seeking_rate', 0.0),
            features.get('bookmark_rate', 0.0),
            features.get('consistency_score', 0.0),
            features.get('active_days_per_week', 0.0) / 7.0,  # Normalize
        ]
        
        # Pad or truncate to fixed size
        while len(feature_vector) < self.feature_dim:
            feature_vector.append(0.0)
        feature_vector = feature_vector[:self.feature_dim]
        
        features['feature_vector'] = feature_vector
        
        return features
    
    def get_user_recommendations(
        self,
        db: Session,
        user_id: int,
        content_type: str = 'question',
        limit: int = 10
    ) -> List[MLRecommendation]:
        """Get ML-generated recommendations for a user"""

        try:
            from datetime import datetime, timedelta

            # Clean up old recommendations (older than 7 days)
            old_cutoff = datetime.utcnow() - timedelta(days=7)
            db.query(MLRecommendation).filter(
                MLRecommendation.user_id == user_id,
                MLRecommendation.created_at < old_cutoff,
                MLRecommendation.is_completed == False,
                MLRecommendation.is_viewed == False
            ).delete()
            db.commit()

            # Get all existing active recommendations for this user and content type
            existing_recs = db.query(MLRecommendation).filter(
                MLRecommendation.user_id == user_id,
                MLRecommendation.content_type == content_type,
                MLRecommendation.is_completed == False
            ).order_by(MLRecommendation.confidence_score.desc()).all()

            # Check if we should refresh recommendations based on user activity
            should_refresh = self._should_refresh_recommendations(db, user_id, existing_recs)

            if should_refresh:
                self.logger.info(f"Refreshing recommendations for user {user_id} due to activity changes")
                # Mark old recommendations as completed to allow new ones
                for rec in existing_recs:
                    rec.is_completed = True
                db.commit()
                existing_recs = []

            # If we have enough recent recommendations and don't need refresh, return them
            if len(existing_recs) >= limit and not should_refresh:
                self.logger.info(f"Returning {len(existing_recs)} existing recommendations for user {user_id}")
                return existing_recs[:limit]

            # Get user feature vector
            user_features = db.query(UserFeatureVector).filter(
                UserFeatureVector.user_id == user_id
            ).first()

            if not user_features:
                # For new users, generate course-based recommendations
                self.logger.info(f"No user features found for user {user_id}, generating course-based recommendations")
                new_recs = self._get_course_based_recommendations(db, user_id, content_type, limit - len(existing_recs))
                return existing_recs + new_recs

            # Generate new recommendations to fill the gap
            needed_recs = limit - len(existing_recs)

            # Get similar users
            similar_users = self._find_similar_users(db, user_id, user_features)

            # Get content-based recommendations
            content_recs = self._get_content_based_recommendations(db, user_id, content_type, needed_recs // 2 + 1)

            # Get collaborative filtering recommendations
            collab_recs = self._get_collaborative_recommendations(db, user_id, similar_users, content_type, needed_recs // 2 + 1)

            # Combine and rank recommendations
            all_new_recs = content_recs + collab_recs

            # Remove duplicates with existing recommendations and rank by confidence
            existing_content = {(rec.content_type, rec.content_id) for rec in existing_recs}
            seen_content = existing_content.copy()
            final_new_recs = []

            for rec in sorted(all_new_recs, key=lambda x: x.confidence_score, reverse=True):
                content_key = (rec.content_type, rec.content_id)
                if content_key not in seen_content:
                    seen_content.add(content_key)
                    final_new_recs.append(rec)

                    if len(final_new_recs) >= needed_recs:
                        break

            # Save new recommendations to database with error handling for duplicates
            saved_recs = []
            for rec in final_new_recs:
                try:
                    db.add(rec)
                    db.flush()  # Flush to catch constraint violations
                    saved_recs.append(rec)
                except Exception as e:
                    db.rollback()
                    self.logger.warning(f"Duplicate recommendation detected for user {user_id}, content {rec.content_id}: {str(e)}")
                    # Continue with other recommendations

            db.commit()

            # Return combined existing and new recommendations with variety
            all_recommendations = existing_recs + saved_recs
            sorted_recs = sorted(all_recommendations, key=lambda x: x.confidence_score, reverse=True)

            # Ensure variety in the final recommendations
            final_recs = self._ensure_recommendation_variety(sorted_recs, limit)

            return final_recs

        except Exception as e:
            self.logger.error(f"Error generating recommendations for user {user_id}: {str(e)}")
            return []

    def _should_refresh_recommendations(
        self,
        db: Session,
        user_id: int,
        existing_recs: List[MLRecommendation]
    ) -> bool:
        """Determine if recommendations should be refreshed based on user activity"""

        try:
            from datetime import datetime, timedelta

            # If no existing recommendations, we should generate new ones
            if not existing_recs:
                return True

            # Check the age of the oldest recommendation
            oldest_rec = min(existing_recs, key=lambda x: x.created_at)
            age_hours = (datetime.utcnow() - oldest_rec.created_at).total_seconds() / 3600

            # Force refresh if recommendations are older than 12 hours
            if age_hours > 12:
                self.logger.info(f"Refreshing recommendations for user {user_id}: oldest recommendation is {age_hours:.1f} hours old")
                return True

            # Check recent user activity to see if preferences might have changed
            recent_cutoff = datetime.utcnow() - timedelta(hours=6)
            recent_activity = db.query(UserBehaviorEvent).filter(
                UserBehaviorEvent.user_id == user_id,
                UserBehaviorEvent.created_at > recent_cutoff
            ).count()

            # If user has been very active recently (>10 interactions in 6 hours), refresh
            if recent_activity > 10:
                self.logger.info(f"Refreshing recommendations for user {user_id}: high recent activity ({recent_activity} interactions)")
                return True

            # Check if user has completed many of the existing recommendations
            completed_count = sum(1 for rec in existing_recs if rec.is_viewed or rec.is_accepted)
            completion_rate = completed_count / len(existing_recs) if existing_recs else 0

            # If >70% of recommendations have been interacted with, refresh
            if completion_rate > 0.7:
                self.logger.info(f"Refreshing recommendations for user {user_id}: high completion rate ({completion_rate:.1%})")
                return True

            # Check for topic diversity - if all recommendations are for the same topic, refresh
            topics = {rec.topic for rec in existing_recs if rec.topic}
            if len(topics) <= 1 and len(existing_recs) > 1:
                self.logger.info(f"Refreshing recommendations for user {user_id}: low topic diversity")
                return True

            return False

        except Exception as e:
            self.logger.error(f"Error checking if recommendations should refresh for user {user_id}: {str(e)}")
            return False

    def _ensure_recommendation_variety(
        self,
        recommendations: List[MLRecommendation],
        limit: int
    ) -> List[MLRecommendation]:
        """Ensure variety in recommendations by topic and difficulty"""

        if len(recommendations) <= limit:
            return recommendations

        # Group recommendations by topic
        topic_groups = {}
        for rec in recommendations:
            topic = rec.topic or 'general'
            if topic not in topic_groups:
                topic_groups[topic] = []
            topic_groups[topic].append(rec)

        # Sort each group by confidence score
        for topic in topic_groups:
            topic_groups[topic].sort(key=lambda x: x.confidence_score, reverse=True)

        # Select recommendations ensuring topic diversity
        selected = []
        topic_keys = list(topic_groups.keys())
        topic_index = 0

        while len(selected) < limit and any(topic_groups.values()):
            current_topic = topic_keys[topic_index % len(topic_keys)]

            if topic_groups[current_topic]:
                selected.append(topic_groups[current_topic].pop(0))

            topic_index += 1

            # Remove empty topic groups
            if not topic_groups[current_topic]:
                topic_keys.remove(current_topic)
                if not topic_keys:
                    break

        return selected

    def refresh_user_recommendations(
        self,
        db: Session,
        user_id: int,
        content_type: str = 'question'
    ) -> List[MLRecommendation]:
        """Force refresh recommendations for a user"""

        try:
            # Mark all existing recommendations as completed
            db.query(MLRecommendation).filter(
                MLRecommendation.user_id == user_id,
                MLRecommendation.content_type == content_type,
                MLRecommendation.is_completed == False
            ).update({'is_completed': True})

            db.commit()

            # Generate new recommendations
            return self.get_user_recommendations(db, user_id, content_type)

        except Exception as e:
            self.logger.error(f"Error refreshing recommendations for user {user_id}: {str(e)}")
            return []

    def cleanup_old_recommendations(self, db: Session, days_old: int = 30) -> int:
        """Clean up old recommendations to keep database size manageable"""

        try:
            from datetime import datetime, timedelta

            cutoff_date = datetime.utcnow() - timedelta(days=days_old)

            # Delete old completed or viewed recommendations
            deleted_count = db.query(MLRecommendation).filter(
                MLRecommendation.created_at < cutoff_date,
                or_(
                    MLRecommendation.is_completed == True,
                    MLRecommendation.is_viewed == True
                )
            ).delete()

            db.commit()

            self.logger.info(f"Cleaned up {deleted_count} old recommendations")
            return deleted_count

        except Exception as e:
            self.logger.error(f"Error cleaning up old recommendations: {str(e)}")
            return 0

    def _find_similar_users(
        self, 
        db: Session, 
        user_id: int, 
        user_features: UserFeatureVector,
        k: int = 10
    ) -> List[int]:
        """Find users similar to the given user using feature vectors"""
        
        try:
            # Get all user feature vectors
            all_users = db.query(UserFeatureVector).filter(
                UserFeatureVector.user_id != user_id
            ).all()
            
            if len(all_users) < 2:
                return []
            
            # Calculate similarities
            user_vector = np.array(user_features.feature_vector)
            similarities = []
            
            for other_user in all_users:
                other_vector = np.array(other_user.feature_vector)
                
                # Calculate cosine similarity
                similarity = np.dot(user_vector, other_vector) / (
                    np.linalg.norm(user_vector) * np.linalg.norm(other_vector) + 1e-8
                )
                
                similarities.append((other_user.user_id, similarity))
            
            # Sort by similarity and return top k
            similarities.sort(key=lambda x: x[1], reverse=True)
            return [user_id for user_id, _ in similarities[:k]]
            
        except Exception as e:
            self.logger.error(f"Error finding similar users: {str(e)}")
            return []
    
    def _get_content_based_recommendations(
        self, 
        db: Session, 
        user_id: int, 
        content_type: str,
        limit: int
    ) -> List[MLRecommendation]:
        """Get content-based recommendations"""
        
        recommendations = []
        
        try:
            # Get user features
            user_features = db.query(UserFeatureVector).filter(
                UserFeatureVector.user_id == user_id
            ).first()

            # Get user's interaction history
            user_events = db.query(UserBehaviorEvent).filter(
                UserBehaviorEvent.user_id == user_id,
                UserBehaviorEvent.content_type == content_type
            ).order_by(desc(UserBehaviorEvent.created_at)).limit(50).all()

            if not user_events:
                return []
            
            # Analyze user preferences
            preferred_topics = {}
            preferred_difficulty = {}
            
            for event in user_events:
                if event.topic:
                    preferred_topics[event.topic] = preferred_topics.get(event.topic, 0) + 1
                if event.difficulty:
                    preferred_difficulty[event.difficulty] = preferred_difficulty.get(event.difficulty, 0) + 1
            
            # Get content that matches user preferences
            if content_type == 'question':
                query = db.query(Question).filter(Question.is_active == True)
                
                # Filter by preferred topics
                if preferred_topics:
                    top_topics = sorted(preferred_topics.items(), key=lambda x: x[1], reverse=True)[:3]
                    topic_names = [topic for topic, _ in top_topics]
                    query = query.filter(Question.topic.in_(topic_names))
                
                # Exclude already attempted questions
                attempted_ids = [event.content_id for event in user_events]
                query = query.filter(~Question.id.in_(attempted_ids))
                
                questions = query.limit(limit * 2).all()
                
                for question in questions[:limit]:
                    confidence = self._calculate_content_confidence(question, preferred_topics, preferred_difficulty)
                    
                    # Calculate predicted performance based on user's topic performance
                    topic_performance = user_features.topic_performance.get(question.topic, 0.5) if user_features.topic_performance else 0.5

                    rec = MLRecommendation(
                        user_id=user_id,
                        content_type='question',
                        content_id=question.id,
                        topic=question.topic,
                        course_id=question.course_id,
                        confidence_score=confidence,
                        predicted_performance=topic_performance,
                        difficulty_match=self._calculate_difficulty_match(question.difficulty, user_features.preferred_difficulty),
                        interest_score=confidence,
                        model_name='content_based',
                        model_version='1.0',
                        explanation=self._generate_personalized_explanation(
                            question, topic_performance, user_features, 'content_based'
                        )
                    )
                    recommendations.append(rec)
            
        except Exception as e:
            self.logger.error(f"Error generating content-based recommendations: {str(e)}")
        
        return recommendations
    
    def _get_collaborative_recommendations(
        self,
        db: Session,
        user_id: int,
        similar_users: List[int],
        content_type: str,
        limit: int
    ) -> List[MLRecommendation]:
        """Get collaborative filtering recommendations"""
        
        recommendations = []

        try:
            if not similar_users:
                return []

            # Get user features for personalized explanations
            user_features = db.query(UserFeatureVector).filter(
                UserFeatureVector.user_id == user_id
            ).first()
            
            # Get content that similar users interacted with positively
            similar_events = db.query(UserBehaviorEvent).filter(
                UserBehaviorEvent.user_id.in_(similar_users),
                UserBehaviorEvent.content_type == content_type,
                UserBehaviorEvent.is_correct == True  # Only successful interactions
            ).all()
            
            # Count content popularity among similar users
            content_scores = {}
            for event in similar_events:
                content_id = event.content_id
                if content_id not in content_scores:
                    content_scores[content_id] = {
                        'count': 0,
                        'avg_time': 0,
                        'topics': set(),
                        'success_count': 0,
                        'total_attempts': 0
                    }

                content_scores[content_id]['count'] += 1
                content_scores[content_id]['total_attempts'] += 1

                if event.is_correct:
                    content_scores[content_id]['success_count'] += 1

                if event.response_time_seconds:
                    content_scores[content_id]['avg_time'] += event.response_time_seconds
                if event.topic:
                    content_scores[content_id]['topics'].add(event.topic)
            
            # Get user's already attempted content
            user_events = db.query(UserBehaviorEvent).filter(
                UserBehaviorEvent.user_id == user_id,
                UserBehaviorEvent.content_type == content_type
            ).all()
            attempted_ids = {event.content_id for event in user_events}
            
            # Rank content by popularity among similar users
            ranked_content = []
            for content_id, scores in content_scores.items():
                if content_id not in attempted_ids and scores['count'] >= 2:  # At least 2 similar users
                    popularity_score = scores['count'] / len(similar_users)
                    ranked_content.append((content_id, popularity_score, scores))
            
            ranked_content.sort(key=lambda x: x[1], reverse=True)
            
            # Create recommendations
            for content_id, popularity_score, scores in ranked_content[:limit]:
                if content_type == 'question':
                    question = db.query(Question).filter(Question.id == content_id).first()
                    if question:
                        # Calculate actual average performance of similar users on this content
                        success_rate = scores['success_count'] / scores['total_attempts'] if scores['total_attempts'] > 0 else 0.7
                        actual_performance = max(success_rate, 0.1)  # Minimum performance to avoid zero

                        # Only create recommendation if we have meaningful data
                        if actual_performance > 0.0 and popularity_score > 0.1:
                            rec = MLRecommendation(
                                user_id=user_id,
                                content_type='question',
                                content_id=question.id,
                                topic=question.topic,
                                course_id=question.course_id,
                                confidence_score=min(popularity_score, 0.9),
                                predicted_performance=actual_performance,
                                difficulty_match=self._calculate_difficulty_match(
                                    question.difficulty,
                                    db.query(UserFeatureVector).filter(
                                        UserFeatureVector.user_id == user_id
                                    ).first().preferred_difficulty if db.query(UserFeatureVector).filter(
                                        UserFeatureVector.user_id == user_id
                                    ).first() else None
                                ),
                                interest_score=popularity_score,
                                model_name='collaborative_filtering',
                                model_version='1.0',
                                explanation=self._generate_personalized_explanation(
                                    question, actual_performance, user_features, 'collaborative', scores['count']
                                )
                            )
                            recommendations.append(rec)
            
        except Exception as e:
            self.logger.error(f"Error generating collaborative recommendations: {str(e)}")
        
        return recommendations

    def _get_course_based_recommendations(
        self,
        db: Session,
        user_id: int,
        content_type: str,
        limit: int
    ) -> List[MLRecommendation]:
        """Get recommendations for new users based on their enrolled courses or department"""

        recommendations = []

        try:
            # Get user with their enrolled courses and department
            user = db.query(User).filter(User.id == user_id).first()
            if not user:
                self.logger.warning(f"User {user_id} not found for course-based recommendations")
                return []

            self.logger.info(f"Generating course-based recommendations for user {user_id} (department_id: {user.department_id}, institution_id: {user.institution_id})")

            # Get user's enrolled courses
            enrolled_courses = user.courses
            self.logger.info(f"User has {len(enrolled_courses)} enrolled courses: {[c.name for c in enrolled_courses]}")

            # If no enrolled courses, try to get courses from user's department
            if not enrolled_courses and user.department_id:
                self.logger.info(f"No enrolled courses, trying department courses for department_id: {user.department_id}")
                department_courses = db.query(Course).filter(
                    Course.department_id == user.department_id,
                    Course.is_active == True
                ).all()
                enrolled_courses = department_courses[:5]  # Limit to 5 courses from department
                self.logger.info(f"Found {len(enrolled_courses)} courses from user's department")

            # If still no courses, get popular courses from user's institution
            if not enrolled_courses and user.institution_id:
                self.logger.info(f"No department courses, trying institution courses for institution_id: {user.institution_id}")
                institution_courses = db.query(Course).filter(
                    Course.school_id == user.institution_id,
                    Course.is_active == True
                ).limit(3).all()
                enrolled_courses = institution_courses
                self.logger.info(f"Found {len(enrolled_courses)} courses from user's institution")

            if not enrolled_courses:
                self.logger.warning(f"No courses found for user {user_id} - user must enroll in courses first")
                return []

            # Get course IDs
            course_ids = [course.id for course in enrolled_courses]

            if content_type == 'question':
                # Get well-performing questions from enrolled courses
                popular_questions = db.query(
                    Question.id,
                    func.count(UserBehaviorEvent.id).label('interaction_count'),
                    func.avg(UserBehaviorEvent.is_correct.cast(Integer).cast(Float)).label('avg_success')
                ).join(
                    UserBehaviorEvent,
                    and_(
                        UserBehaviorEvent.content_id == Question.id,
                        UserBehaviorEvent.content_type == 'question'
                    ),
                    isouter=True
                ).filter(
                    Question.is_active == True,
                    Question.course_id.in_(course_ids)
                ).group_by(Question.id).order_by(
                    desc('avg_success'),
                    desc('interaction_count'),
                    Question.id  # For consistent ordering when scores are equal
                ).limit(limit * 2).all()

                # If no questions with interactions, get recent questions from courses
                if not popular_questions:
                    recent_questions = db.query(Question).filter(
                        Question.is_active == True,
                        Question.course_id.in_(course_ids)
                    ).order_by(desc(Question.created_at)).limit(limit).all()

                    for question in recent_questions:
                        rec = MLRecommendation(
                            user_id=user_id,
                            content_type='question',
                            content_id=question.id,
                            topic=question.topic,
                            course_id=question.course_id,
                            confidence_score=0.6,  # Moderate confidence for course-based
                            predicted_performance=0.7,  # Optimistic for new users
                            difficulty_match=0.8,  # Assume good match within enrolled courses
                            interest_score=0.7,  # High interest for enrolled courses
                            model_name='course_based',
                            model_version='1.0',
                            explanation=self._generate_course_based_explanation(question, 'new_user')
                        )
                        recommendations.append(rec)
                else:
                    # Create recommendations from popular questions
                    for question_data in popular_questions[:limit]:
                        question = db.query(Question).filter(Question.id == question_data.id).first()
                        if question:
                            # Calculate confidence based on success rate and interaction count
                            success_rate = float(question_data.avg_success or 0.7)
                            interaction_count = int(question_data.interaction_count or 0)

                            confidence = min(0.5 + (success_rate * 0.3) + (min(interaction_count, 10) * 0.02), 0.9)

                            rec = MLRecommendation(
                                user_id=user_id,
                                content_type='question',
                                content_id=question.id,
                                topic=question.topic,
                                course_id=question.course_id,
                                confidence_score=confidence,
                                predicted_performance=success_rate,
                                difficulty_match=0.8,
                                interest_score=0.8,  # High interest for enrolled courses
                                model_name='course_based',
                                model_version='1.0',
                                explanation=self._generate_course_based_explanation(question, 'popular', success_rate)
                            )
                            recommendations.append(rec)

            elif content_type == 'flashcard':
                # Flashcard recommendations disabled - FlashCard model removed
                # TODO: Implement flashcard recommendations using Gemini Files API
                pass

            # Check for existing recommendations to avoid duplicates
            existing_content_ids = {
                (rec.content_type, rec.content_id)
                for rec in db.query(MLRecommendation).filter(
                    MLRecommendation.user_id == user_id,
                    MLRecommendation.content_type == content_type,
                    MLRecommendation.is_completed == False
                ).all()
            }

            # Filter out duplicates
            unique_recommendations = [
                rec for rec in recommendations
                if (rec.content_type, rec.content_id) not in existing_content_ids
            ]

            # Save unique recommendations to database with error handling
            saved_recs = []
            for rec in unique_recommendations:
                try:
                    db.add(rec)
                    db.flush()  # Flush to catch constraint violations
                    saved_recs.append(rec)
                except Exception as e:
                    db.rollback()
                    self.logger.warning(f"Duplicate course-based recommendation detected for user {user_id}, content {rec.content_id}: {str(e)}")

            db.commit()

            return saved_recs

        except Exception as e:
            self.logger.error(f"Error generating course-based recommendations for user {user_id}: {str(e)}")
            return []

    def _calculate_content_confidence(
        self,
        content: Any,
        preferred_topics: Dict[str, int],
        preferred_difficulty: Dict[str, int]
    ) -> float:
        """Calculate confidence score for content recommendation"""

        confidence = 0.0  # Start with no confidence

        # Topic match - only add confidence if there's a real match
        if hasattr(content, 'topic') and content.topic in preferred_topics and preferred_topics:
            topic_weight = preferred_topics[content.topic] / sum(preferred_topics.values())
            confidence += topic_weight * 0.6  # Higher weight for topic match

        # Difficulty match - only add confidence if there's a real match
        if hasattr(content, 'difficulty') and str(content.difficulty) in preferred_difficulty and preferred_difficulty:
            difficulty_weight = preferred_difficulty[str(content.difficulty)] / sum(preferred_difficulty.values())
            confidence += difficulty_weight * 0.4  # Weight for difficulty match

        return min(confidence, 1.0)

    def _calculate_difficulty_match(self, content_difficulty: str, user_preferred_difficulty: str) -> float:
        """Calculate how well content difficulty matches user preference"""
        if not content_difficulty or not user_preferred_difficulty:
            return 0.0  # No match if data is missing

        difficulty_map = {'easy': 1, 'medium': 2, 'hard': 3}
        content_level = difficulty_map.get(content_difficulty.lower())
        user_level = difficulty_map.get(user_preferred_difficulty.lower())

        if content_level is None or user_level is None:
            return 0.0  # No match if difficulty levels are invalid

        # Perfect match = 1.0, one level off = 0.7, two levels off = 0.4
        diff = abs(content_level - user_level)
        if diff == 0:
            return 1.0
        elif diff == 1:
            return 0.7
        else:
            return 0.4

    def _generate_personalized_explanation(
        self,
        question: Any,
        performance: float,
        user_features: UserFeatureVector,
        model_type: str,
        similar_count: int = None
    ) -> str:
        """Generate personalized, engaging explanations for recommendations"""

        import random

        topic = question.topic or "this topic"
        performance_pct = int(performance * 100)

        # Get user's study patterns
        study_frequency = user_features.study_frequency if user_features else 0
        consistency = user_features.consistency_score if user_features else 0
        preferred_difficulty = user_features.preferred_difficulty if user_features else None

        # Get topic performance context
        topic_performance = user_features.topic_performance if user_features else {}
        user_topic_score = topic_performance.get(topic, 0) if topic_performance else 0

        if model_type == 'content_based':
            if performance >= 0.8:
                explanations = [
                    f"You're excelling in {topic}! This question will help you master advanced concepts.",
                    f"Your {performance_pct}% success rate in {topic} shows you're ready for this challenge.",
                    f"Perfect for building on your strong {topic} foundation - you've got this!",
                    f"Your consistent performance in {topic} suggests you'll find this engaging."
                ]
            elif performance >= 0.6:
                explanations = [
                    f"You're making solid progress in {topic} - this will help you level up!",
                    f"Your {performance_pct}% success rate shows you're ready for the next step in {topic}.",
                    f"This {topic} question matches your current skill level perfectly.",
                    f"Great opportunity to strengthen your {topic} understanding."
                ]
            elif performance >= 0.4:
                explanations = [
                    f"Time to turn {topic} into your strength - this question will help!",
                    f"Perfect practice opportunity to improve your {topic} skills.",
                    f"This {topic} question targets areas where you can grow the most.",
                    f"Let's boost your confidence in {topic} with targeted practice."
                ]
            else:
                explanations = [
                    f"Let's build a solid foundation in {topic} - starting here!",
                    f"This {topic} question will help clarify key concepts for you.",
                    f"Perfect starting point to strengthen your {topic} understanding.",
                    f"Designed to help you break through in {topic} - you've got this!"
                ]

        elif model_type == 'collaborative':
            if similar_count and similar_count >= 5:
                explanations = [
                    f"Students with similar learning patterns found this {topic} question really helpful!",
                    f"Learners like you typically see great improvement after practicing this {topic} concept.",
                    f"This {topic} question is highly recommended by students with your learning style.",
                    f"Students who struggled with similar topics found this question breakthrough-worthy!"
                ]
            elif similar_count and similar_count >= 3:
                explanations = [
                    f"A few students with your learning pattern mastered {topic} using this question.",
                    f"This {topic} question helped similar learners make significant progress.",
                    f"Students with comparable performance found this {topic} question very effective.",
                    f"Recommended by learners who had similar challenges in {topic}."
                ]
            else:
                explanations = [
                    f"This {topic} question aligns perfectly with your learning journey.",
                    f"Tailored for students at your current {topic} level.",
                    f"This {topic} practice matches your unique learning needs.",
                    f"Specifically chosen based on your learning patterns in {topic}."
                ]

        # Add study pattern context
        if user_features and consistency > 0.8:
            pattern_boost = [
                " Your consistent study habits make this perfect timing!",
                " Your dedication to regular practice will pay off here.",
                " Given your excellent study consistency, this is ideal for you."
            ]
            return random.choice(explanations) + random.choice(pattern_boost)
        elif user_features and study_frequency > 5:
            pattern_boost = [
                " Your active learning approach makes this a great fit!",
                " Perfect for someone who studies as regularly as you do.",
                " Your frequent practice sessions have prepared you for this."
            ]
            return random.choice(explanations) + random.choice(pattern_boost)

        return random.choice(explanations)

    def _generate_course_based_explanation(
        self,
        question: Any,
        recommendation_type: str,
        success_rate: float = None
    ) -> str:
        """Generate explanations for course-based recommendations"""

        import random

        topic = question.topic or "this topic"
        course_name = question.course.name if question.course else "your course"

        if recommendation_type == 'new_user':
            explanations = [
                f"Great starting point for {topic} in {course_name}!",
                f"This {topic} question is perfect for getting familiar with {course_name} concepts.",
                f"Recommended as an excellent introduction to {topic} from {course_name}.",
                f"Start building your {topic} foundation with this {course_name} question.",
                f"This {topic} question will help you get comfortable with {course_name} material."
            ]
        elif recommendation_type == 'popular' and success_rate:
            success_pct = int(success_rate * 100)
            explanations = [
                f"This {topic} question has a {success_pct}% success rate - students love it!",
                f"Highly effective {topic} practice - {success_pct}% of students found it helpful.",
                f"Popular choice in {course_name}: {success_pct}% success rate for {topic}.",
                f"Students consistently perform well on this {topic} question ({success_pct}% success).",
                f"This {topic} question is a student favorite with {success_pct}% positive outcomes."
            ]
        else:
            explanations = [
                f"Recommended {topic} practice from {course_name}.",
                f"This {topic} question aligns with your {course_name} curriculum.",
                f"Perfect {topic} practice for {course_name} students."
            ]

        return random.choice(explanations)

    # def _generate_flashcard_explanation(self, flashcard: Any) -> str:
    #     """Generate explanations for flashcard recommendations - DISABLED"""
    #     # Flashcard model removed - using Gemini Files API instead
    #     pass


# Create service instance
ml_recommendation_service = MLRecommendationService()
