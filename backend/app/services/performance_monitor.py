import time
import logging
from typing import Dict, Any, Optional
from dataclasses import dataclass, asdict
from datetime import datetime, timezone
import asyncio
from app.core.redis_cache import redis_cache

logger = logging.getLogger(__name__)


@dataclass
class ProcessingMetrics:
    """Performance metrics for PDF processing operations."""
    note_id: int
    student_id: int
    file_size: int
    text_length: int
    chunk_count: int
    embedding_count: int
    total_processing_time: float
    text_extraction_time: float
    chunking_time: float
    embedding_time: float
    extraction_method: str
    timestamp: str
    success: bool
    error_message: Optional[str] = None


class PerformanceMonitor:
    """
    Lightning-fast performance monitoring for PDF processing.
    Tracks metrics and identifies bottlenecks in real-time.
    """
    
    def __init__(self):
        self.metrics_cache_ttl = 86400 * 7  # 7 days
        
    async def record_processing_metrics(
        self,
        note_id: int,
        student_id: int,
        file_size: int,
        processing_result: Dict[str, Any],
        success: bool = True,
        error_message: Optional[str] = None
    ) -> None:
        """
        Record comprehensive processing metrics.
        
        Args:
            note_id: Note ID
            student_id: Student ID
            file_size: PDF file size in bytes
            processing_result: Result from lightning PDF service
            success: Whether processing succeeded
            error_message: Error message if failed
        """
        try:
            # Extract timing information
            extraction_metadata = processing_result.get('extraction_metadata', {})
            
            metrics = ProcessingMetrics(
                note_id=note_id,
                student_id=student_id,
                file_size=file_size,
                text_length=processing_result.get('text_length', 0),
                chunk_count=processing_result.get('chunk_count', 0),
                embedding_count=processing_result.get('embedding_count', 0),
                total_processing_time=processing_result.get('processing_time', 0),
                text_extraction_time=extraction_metadata.get('extraction_time', 0),
                chunking_time=0,  # Will be calculated separately if needed
                embedding_time=0,  # Will be calculated separately if needed
                extraction_method=extraction_metadata.get('extraction_method', 'unknown'),
                timestamp=datetime.now(timezone.utc).isoformat(),
                success=success,
                error_message=error_message
            )
            
            # Store metrics in cache for analysis
            cache_key = f"metrics:note:{note_id}:{int(time.time())}"
            await redis_cache.set(cache_key, asdict(metrics), ttl=self.metrics_cache_ttl)
            
            # Store aggregated metrics
            await self._update_aggregated_metrics(metrics)
            
            # Log performance summary
            if success:
                logger.info(f"⚡ Performance: Note {note_id} | "
                           f"{metrics.total_processing_time:.2f}s total | "
                           f"{metrics.text_length} chars | "
                           f"{metrics.chunk_count} chunks | "
                           f"{metrics.embedding_count} embeddings | "
                           f"Method: {metrics.extraction_method}")
            else:
                logger.error(f"❌ Failed: Note {note_id} | Error: {error_message}")
                
        except Exception as e:
            logger.error(f"Error recording metrics: {str(e)}")
    
    async def _update_aggregated_metrics(self, metrics: ProcessingMetrics) -> None:
        """Update aggregated performance metrics."""
        try:
            # Daily aggregation key
            date_key = datetime.now(timezone.utc).strftime("%Y-%m-%d")
            agg_key = f"metrics:daily:{date_key}"
            
            # Get existing aggregated data
            existing = await redis_cache.get(agg_key) or {
                'total_notes': 0,
                'total_processing_time': 0,
                'total_text_length': 0,
                'total_chunks': 0,
                'total_embeddings': 0,
                'successful_notes': 0,
                'failed_notes': 0,
                'extraction_methods': {},
                'avg_processing_time': 0,
                'avg_text_length': 0
            }
            
            # Update aggregated metrics
            existing['total_notes'] += 1
            existing['total_processing_time'] += metrics.total_processing_time
            existing['total_text_length'] += metrics.text_length
            existing['total_chunks'] += metrics.chunk_count
            existing['total_embeddings'] += metrics.embedding_count
            
            if metrics.success:
                existing['successful_notes'] += 1
            else:
                existing['failed_notes'] += 1
            
            # Track extraction methods
            method = metrics.extraction_method
            existing['extraction_methods'][method] = existing['extraction_methods'].get(method, 0) + 1
            
            # Calculate averages
            if existing['total_notes'] > 0:
                existing['avg_processing_time'] = existing['total_processing_time'] / existing['total_notes']
                existing['avg_text_length'] = existing['total_text_length'] / existing['total_notes']
            
            # Store updated aggregated metrics
            await redis_cache.set(agg_key, existing, ttl=self.metrics_cache_ttl)
            
        except Exception as e:
            logger.error(f"Error updating aggregated metrics: {str(e)}")
    
    async def get_performance_summary(self, days: int = 7) -> Dict[str, Any]:
        """
        Get performance summary for the last N days.
        
        Args:
            days: Number of days to analyze
            
        Returns:
            Performance summary
        """
        try:
            summary = {
                'total_notes': 0,
                'total_processing_time': 0,
                'avg_processing_time': 0,
                'avg_text_length': 0,
                'success_rate': 0,
                'extraction_methods': {},
                'daily_breakdown': []
            }
            
            # Collect data for each day
            for i in range(days):
                date = datetime.now(timezone.utc).replace(hour=0, minute=0, second=0, microsecond=0)
                date = date.replace(days=-i) if i > 0 else date
                date_key = date.strftime("%Y-%m-%d")
                agg_key = f"metrics:daily:{date_key}"
                
                daily_data = await redis_cache.get(agg_key)
                if daily_data:
                    summary['daily_breakdown'].append({
                        'date': date_key,
                        **daily_data
                    })
                    
                    # Aggregate totals
                    summary['total_notes'] += daily_data.get('total_notes', 0)
                    summary['total_processing_time'] += daily_data.get('total_processing_time', 0)
                    
                    # Merge extraction methods
                    for method, count in daily_data.get('extraction_methods', {}).items():
                        summary['extraction_methods'][method] = summary['extraction_methods'].get(method, 0) + count
            
            # Calculate overall averages
            if summary['total_notes'] > 0:
                total_successful = sum(day.get('successful_notes', 0) for day in summary['daily_breakdown'])
                summary['avg_processing_time'] = summary['total_processing_time'] / summary['total_notes']
                summary['success_rate'] = (total_successful / summary['total_notes']) * 100
                
                total_text = sum(day.get('total_text_length', 0) for day in summary['daily_breakdown'])
                summary['avg_text_length'] = total_text / summary['total_notes']
            
            return summary
            
        except Exception as e:
            logger.error(f"Error getting performance summary: {str(e)}")
            return {}
    
    async def identify_bottlenecks(self) -> Dict[str, Any]:
        """
        Identify performance bottlenecks and optimization opportunities.
        
        Returns:
            Bottleneck analysis
        """
        try:
            summary = await self.get_performance_summary(days=7)
            
            bottlenecks = {
                'slow_processing': [],
                'extraction_issues': [],
                'recommendations': []
            }
            
            # Analyze processing times
            avg_time = summary.get('avg_processing_time', 0)
            if avg_time > 30:  # More than 30 seconds average
                bottlenecks['slow_processing'].append(f"Average processing time is {avg_time:.2f}s (target: <10s)")
                bottlenecks['recommendations'].append("Consider optimizing text extraction or embedding generation")
            
            # Analyze extraction methods
            methods = summary.get('extraction_methods', {})
            if methods.get('ai', 0) > methods.get('pymupdf', 0):
                bottlenecks['extraction_issues'].append("High AI extraction usage indicates many image-based PDFs")
                bottlenecks['recommendations'].append("Consider preprocessing PDFs or optimizing AI extraction")
            
            # Analyze success rate
            success_rate = summary.get('success_rate', 100)
            if success_rate < 95:
                bottlenecks['extraction_issues'].append(f"Success rate is {success_rate:.1f}% (target: >95%)")
                bottlenecks['recommendations'].append("Investigate failed processing cases")
            
            return bottlenecks
            
        except Exception as e:
            logger.error(f"Error identifying bottlenecks: {str(e)}")
            return {}


# Create singleton instance
performance_monitor = PerformanceMonitor()
