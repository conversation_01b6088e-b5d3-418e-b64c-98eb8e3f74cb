import logging
from typing import Dict, Any, Optional
from io import BytesIO

import cloudinary
import cloudinary.uploader
import cloudinary.utils
from fastapi import UploadFile, HTTPException

from app.core.config import settings

logger = logging.getLogger(__name__)


class CloudinaryService:
    """Service for handling image uploads to Cloudinary."""

    def __init__(self):
        """Initialize Cloudinary configuration."""
        self.enabled = False

        # Check if Cloudinary is configured
        if not settings.CLOUDINARY_URL:
            logger.warning("Cloudinary not configured - CLOUDINARY_URL is missing. Image upload features will be disabled.")
            return

        try:
            cloudinary.config(
                cloud_name=self._extract_cloud_name(settings.CLOUDINARY_URL),
                api_key=settings.CLOUDINARY_API_KEY,
                api_secret=settings.CLOUDINARY_API_SECRET,
                secure=True
            )
            self.enabled = True
            logger.info("Cloudinary service initialized successfully")
        except Exception as e:
            logger.error(f"Failed to initialize Cloudinary: {str(e)}. Image upload features will be disabled.")

    def _extract_cloud_name(self, cloudinary_url: str) -> str:
        """Extract cloud name from Cloudinary URL."""
        # Format: cloudinary://api_key:api_secret@cloud_name
        if not cloudinary_url:
            raise ValueError("Cloudinary URL is required")
        try:
            return cloudinary_url.split('@')[1]
        except IndexError:
            raise ValueError("Invalid Cloudinary URL format")
    
    async def upload_image(
        self,
        file: UploadFile,
        folder: Optional[str] = None,
        public_id: Optional[str] = None,
        transformation: Optional[Dict[str, Any]] = None
    ) -> Dict[str, Any]:
        """
        Upload an image file to Cloudinary.

        Args:
            file: The uploaded image file
            folder: Optional folder to organize uploads
            public_id: Optional custom public ID
            transformation: Optional transformation parameters

        Returns:
            Upload result from Cloudinary
        """
        if not self.enabled:
            raise HTTPException(
                status_code=503,
                detail="Image upload service is not available. Cloudinary is not configured."
            )

        try:
            # Read file content
            file_content = await file.read()
            
            # Prepare upload options
            upload_options = {
                "resource_type": "image",
                "quality": "auto:good"
            }
            
            if folder:
                upload_options["folder"] = folder
                
            if public_id:
                upload_options["public_id"] = public_id
                
            if transformation:
                upload_options["transformation"] = transformation
            
            # Upload to Cloudinary
            result = cloudinary.uploader.upload(
                BytesIO(file_content),
                **upload_options
            )
            
            return result
            
        except Exception as e:
            logger.error(f"Error uploading image to Cloudinary: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail=f"Failed to upload image: {str(e)}"
            )
    
    async def upload_pdf(
        self,
        file: UploadFile,
        folder: Optional[str] = None,
        public_id: Optional[str] = None
    ) -> Dict[str, Any]:
        """
        Upload a PDF file to Cloudinary.

        Args:
            file: The uploaded PDF file
            folder: Optional folder to organize uploads
            public_id: Optional custom public ID

        Returns:
            Upload result from Cloudinary
        """
        if not self.enabled:
            raise HTTPException(
                status_code=503,
                detail="PDF upload service is not available. Cloudinary is not configured."
            )

        try:
            # Read file content
            file_content = await file.read()
            
            # Prepare upload options
            upload_options = {
                "resource_type": "raw",
                "format": "pdf"
            }
            
            if folder:
                upload_options["folder"] = folder
                
            if public_id:
                # Validate public_id format
                import re
                if not re.match(r'^[a-zA-Z0-9\-_./]+$', public_id):
                    raise ValueError(f"Invalid public_id format: {public_id}")
                upload_options["public_id"] = public_id
                logger.info(f"Uploading to Cloudinary with public_id: {public_id}")

            # Upload to Cloudinary
            result = cloudinary.uploader.upload(
                BytesIO(file_content),
                **upload_options
            )

            return result

        except Exception as e:
            logger.error(f"Error uploading PDF to Cloudinary: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail=f"Failed to upload PDF: {str(e)}"
            )

    async def download_pdf(self, public_id: str) -> bytes:
        """
        Download a PDF file from Cloudinary.

        Args:
            public_id: The public ID of the PDF to download

        Returns:
            PDF file content as bytes
        """
        try:
            # Get the secure URL for the PDF (don't specify format to avoid double extension)
            url = cloudinary.utils.cloudinary_url(
                public_id,
                resource_type="raw",
                secure=True
            )[0]

            # Download the file
            import httpx
            async with httpx.AsyncClient() as client:
                response = await client.get(url)
                response.raise_for_status()
                return response.content

        except Exception as e:
            logger.error(f"Error downloading PDF from Cloudinary: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail=f"Failed to download PDF: {str(e)}"
            )
    
    def get_optimized_transformations(self) -> Dict[str, Dict[str, Any]]:
        """
        Get optimized transformation presets for different use cases.
        
        Returns:
            Dictionary of transformation presets
        """
        return {
            "question_display": {
                "width": 800,
                "height": 600,
                "crop": "limit",
                "quality": "auto:good"
            },
            "flashcard_display": {
                "width": 600,
                "height": 400,
                "crop": "limit",
                "quality": "auto:good"
            },
            "profile_picture": {
                "width": 400,
                "height": 400,
                "crop": "fill",
                "gravity": "face",
                "quality": "auto:good"
            },
            "thumbnail": {
                "width": 200,
                "height": 150,
                "crop": "fill",
                "quality": "auto:low"
            }
        }
    
    async def delete_image(self, public_id: str) -> Dict[str, Any]:
        """
        Delete an image from Cloudinary.

        Args:
            public_id: The public ID of the image to delete

        Returns:
            Deletion result from Cloudinary
        """
        if not self.enabled:
            raise HTTPException(
                status_code=503,
                detail="Image deletion service is not available. Cloudinary is not configured."
            )

        try:
            result = cloudinary.uploader.destroy(public_id)
            return result
            
        except Exception as e:
            logger.error(f"Error deleting image from Cloudinary: {str(e)}")
            raise HTTPException(
                status_code=500,
                detail=f"Failed to delete image: {str(e)}"
            )


# Create singleton instance
cloudinary_service = CloudinaryService()
