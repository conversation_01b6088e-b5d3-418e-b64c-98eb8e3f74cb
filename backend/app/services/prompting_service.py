import logging
import asyncio
import json
from typing import Dict, List, Optional, Any, Union
from pydantic import BaseModel, Field
import openai

from app.core.config import settings
from app.services.gemini_service import gemini_service
from app.services.chunking_service import chunking_service

logger = logging.getLogger(__name__)


class MCQQuestion(BaseModel):
    """Schema for MCQ questions."""
    question: str = Field(description="The question text")
    options: List[str] = Field(description="List of 4 answer options")
    correct_answer: int = Field(description="Index of correct answer (0-3)")
    explanation: str = Field(description="Explanation of the correct answer")
    topic: str = Field(description="Topic or subject area")
    difficulty: str = Field(description="Difficulty level: easy, medium, or hard")


class FlashCard(BaseModel):
    """Schema for flashcards."""
    front: str = Field(description="Front side of the flashcard (question/prompt)")
    back: str = Field(description="Back side of the flashcard (answer/explanation)")
    topic: str = Field(description="Topic or subject area")


class QuestionBatch(BaseModel):
    """Schema for a batch of questions."""
    mcqs: List[MCQQuestion] = Field(description="List of MCQ questions")
    flashcards: List[FlashCard] = Field(description="List of flashcards")


class PromptingService:
    """Enhanced prompting service with map-reduce and function calling."""
    
    def __init__(self):
        self.openai_client = openai.OpenAI(api_key=settings.OPENAI_API_KEY)
        self.summary_token_limit = 250
        self.batch_size = 3  # Number of chunks to process in one LLM call
    
    async def summarize_chunk(
        self, 
        chunk_content: str, 
        max_tokens: int = None
    ) -> str:
        """
        Create a concise summary of chunk content using map-reduce approach.
        
        Args:
            chunk_content: Content to summarize
            max_tokens: Maximum tokens for summary
            
        Returns:
            Summarized content
        """
        max_tokens = max_tokens or self.summary_token_limit
        
        try:
            # Check if summarization is needed
            current_tokens = chunking_service.count_tokens(chunk_content)
            if current_tokens <= max_tokens:
                return chunk_content
            
            # Use OpenAI for summarization with function calling
            messages = [
                {
                    "role": "system",
                    "content": f"""You are an expert at creating concise summaries for educational content. 
                    Create a summary that preserves key concepts, facts, and relationships while staying under {max_tokens} tokens.
                    Focus on information that would be useful for generating educational questions."""
                },
                {
                    "role": "user",
                    "content": f"Summarize this educational content:\n\n{chunk_content}"
                }
            ]
            
            response = await asyncio.to_thread(
                self.openai_client.chat.completions.create,
                model="gpt-4-turbo-preview",
                messages=messages,
                max_tokens=max_tokens,
                temperature=0.3
            )
            
            summary = response.choices[0].message.content.strip()
            return summary if summary else chunk_content[:1000]  # Fallback
            
        except Exception as e:
            logger.error(f"Error summarizing chunk: {str(e)}")
            # Fallback to simple truncation
            return chunking_service.create_chunk_summary(chunk_content, max_tokens)
    
    async def generate_questions_from_chunk(
        self,
        chunk_content: str,
        mcq_count: int,
        flashcard_count: int,
        course_name: Optional[str] = None,
        use_summary: bool = True
    ) -> Dict[str, Any]:
        """
        Generate questions from a single chunk using function calling.
        
        Args:
            chunk_content: Content to generate questions from
            mcq_count: Number of MCQs to generate
            flashcard_count: Number of flashcards to generate
            course_name: Course context
            use_summary: Whether to summarize large chunks first
            
        Returns:
            Dictionary with generated questions and metadata
        """
        try:
            # Summarize if content is too long
            content_to_use = chunk_content
            if use_summary and chunking_service.count_tokens(chunk_content) > 800:
                content_to_use = await self.summarize_chunk(chunk_content)
                logger.info("Used summarized content for question generation")
            
            # Prepare course context
            course_context = f"Course: {course_name}\n" if course_name else ""
            
            # Create system prompt
            system_prompt = f"""You are an expert educational content creator. Generate exactly {mcq_count} multiple-choice questions and {flashcard_count} flashcards from the provided content.

{course_context}
Requirements:
- MCQs must have exactly 4 options with one correct answer
- Questions should test understanding, not just memorization
- Flashcards should reinforce key concepts
- Vary difficulty levels appropriately
- Ensure all content is educationally valuable

Return your response using the provided function schema."""

            # Define function schema
            function_schema = {
                "name": "generate_questions",
                "description": "Generate MCQs and flashcards from educational content",
                "parameters": QuestionBatch.model_json_schema()
            }
            
            messages = [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": f"Generate questions from this content:\n\n{content_to_use}"}
            ]
            
            # Call OpenAI with function calling
            response = await asyncio.to_thread(
                self.openai_client.chat.completions.create,
                model="gpt-4-turbo-preview",
                messages=messages,
                functions=[function_schema],
                function_call={"name": "generate_questions"},
                temperature=0.7
            )
            
            # Parse function call response
            function_call = response.choices[0].message.function_call
            if function_call and function_call.name == "generate_questions":
                result_data = json.loads(function_call.arguments)
                
                # Validate the result
                question_batch = QuestionBatch(**result_data)
                
                # Check if we got the right number of questions
                actual_mcqs = len(question_batch.mcqs)
                actual_flashcards = len(question_batch.flashcards)
                
                if actual_mcqs != mcq_count or actual_flashcards != flashcard_count:
                    logger.warning(f"Expected {mcq_count} MCQs and {flashcard_count} flashcards, got {actual_mcqs} MCQs and {actual_flashcards} flashcards")
                
                return {
                    'mcqs': [mcq.dict() for mcq in question_batch.mcqs],
                    'flashcards': [fc.dict() for fc in question_batch.flashcards],
                    'metadata': {
                        'content_summarized': use_summary and content_to_use != chunk_content,
                        'original_tokens': chunking_service.count_tokens(chunk_content),
                        'processed_tokens': chunking_service.count_tokens(content_to_use),
                        'expected_mcqs': mcq_count,
                        'actual_mcqs': actual_mcqs,
                        'expected_flashcards': flashcard_count,
                        'actual_flashcards': actual_flashcards
                    }
                }
            else:
                raise Exception("No valid function call response received")
                
        except Exception as e:
            logger.error(f"Error generating questions from chunk: {str(e)}")
            # Fallback to Gemini
            return await self._fallback_to_gemini(
                chunk_content, mcq_count, flashcard_count, course_name
            )
    
    async def _fallback_to_gemini(
        self,
        content: str,
        mcq_count: int,
        flashcard_count: int,
        course_name: Optional[str] = None
    ) -> Dict[str, Any]:
        """Fallback to Gemini for question generation."""
        try:
            course_context = f"Course: {course_name}\n" if course_name else ""
            
            prompt = f"""Generate exactly {mcq_count} multiple-choice questions and {flashcard_count} flashcards from this content.

{course_context}

Content:
{content}

Format your response as JSON with this structure:
{{
    "mcqs": [
        {{
            "question": "Question text",
            "options": ["Option A", "Option B", "Option C", "Option D"],
            "correct_answer": 0,
            "explanation": "Explanation text",
            "topic": "Topic name",
            "difficulty": "easy|medium|hard"
        }}
    ],
    "flashcards": [
        {{
            "front": "Question or prompt",
            "back": "Answer or explanation",
            "topic": "Topic name"
        }}
    ]
}}"""

            response = await gemini_service.generate_content(prompt)
            
            # Try to parse JSON from response
            try:
                # Extract JSON from response (might have extra text)
                json_start = response.find('{')
                json_end = response.rfind('}') + 1
                if json_start >= 0 and json_end > json_start:
                    json_str = response[json_start:json_end]
                    result_data = json.loads(json_str)
                    
                    return {
                        'mcqs': result_data.get('mcqs', []),
                        'flashcards': result_data.get('flashcards', []),
                        'metadata': {
                            'fallback_used': True,
                            'provider': 'gemini'
                        }
                    }
            except json.JSONDecodeError:
                logger.error("Failed to parse JSON from Gemini response")
            
            # If JSON parsing fails, return empty result
            return {
                'mcqs': [],
                'flashcards': [],
                'metadata': {
                    'fallback_used': True,
                    'provider': 'gemini',
                    'error': 'Failed to parse response'
                }
            }
            
        except Exception as e:
            logger.error(f"Error in Gemini fallback: {str(e)}")
            return {
                'mcqs': [],
                'flashcards': [],
                'metadata': {
                    'fallback_used': True,
                    'provider': 'gemini',
                    'error': str(e)
                }
            }
    
    async def generate_questions_batch(
        self,
        chunks: List[Dict[str, Any]],
        course_name: Optional[str] = None
    ) -> List[Dict[str, Any]]:
        """
        Generate questions from multiple chunks in batches for efficiency.
        
        Args:
            chunks: List of chunks with content and quotas
            course_name: Course context
            
        Returns:
            List of results for each chunk
        """
        results = []
        
        # Process chunks in batches
        for i in range(0, len(chunks), self.batch_size):
            batch = chunks[i:i + self.batch_size]
            batch_tasks = []
            
            for chunk in batch:
                mcq_quota = chunk.get('question_quota', 1)
                # For now, generate equal MCQs and flashcards
                # This can be made configurable
                flashcard_quota = max(1, mcq_quota // 2)
                
                task = self.generate_questions_from_chunk(
                    chunk['content'],
                    mcq_quota,
                    flashcard_quota,
                    course_name
                )
                batch_tasks.append(task)
            
            # Execute batch concurrently
            batch_results = await asyncio.gather(*batch_tasks, return_exceptions=True)
            
            for j, result in enumerate(batch_results):
                if isinstance(result, Exception):
                    logger.error(f"Error processing chunk {i + j}: {str(result)}")
                    results.append({
                        'mcqs': [],
                        'flashcards': [],
                        'metadata': {'error': str(result)}
                    })
                else:
                    results.append(result)
        
        return results


# Create singleton instance
prompting_service = PromptingService()
