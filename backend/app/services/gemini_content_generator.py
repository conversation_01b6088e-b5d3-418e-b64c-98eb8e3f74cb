"""
Service for generating MCQs, flashcards, and summaries using Gemini Files API with structured output.
Contains optimized prompts and generation logic.
"""

import logging
import asyncio
import json
import re
import time
from typing import List, Dict, Any, Optional
from datetime import datetime

import google.generativeai as genai
from sqlalchemy.orm import Session
from google.api_core.exceptions import (
    ResourceExhausted,
    ServiceUnavailable,
    DeadlineExceeded,
    GoogleAPIError
)

from app.core.config import settings
from app.services.gemini_files_service import gemini_files_service
from app.schemas.gemini_structured_output import (
    MCQGenerationResponse, FlashcardGenerationResponse, SummaryGenerationResponse,
    NoteExplanationGenerationResponse, DifficultyLevel
)
# Database imports removed - MCQ saving handled in endpoint

logger = logging.getLogger(__name__)


class GeminiContentGenerator:
    """Service for generating educational content using Gemini Files API with structured output"""

    def __init__(self):
        """Initialize the content generator"""
        self.api_key = settings.GEMINI_API_KEY
        if not self.api_key:
            raise ValueError("GEMINI_API_KEY is required")

        genai.configure(api_key=self.api_key)
        self.model_name = "gemini-2.0-flash"  # Use latest model for best performance

        # Retry configuration
        self.max_retries = 3
        self.base_delay = 1.0  # Base delay in seconds
        self.max_delay = 60.0  # Maximum delay in seconds

    async def _retry_with_backoff(self, func, *args, **kwargs):
        """Execute a function with exponential backoff retry logic"""
        last_exception = None

        for attempt in range(self.max_retries + 1):
            try:
                return await func(*args, **kwargs)
            except (ResourceExhausted, ServiceUnavailable, DeadlineExceeded, GoogleAPIError) as e:
                last_exception = e

                if attempt == self.max_retries:
                    # Last attempt failed, raise the exception
                    break

                # Calculate delay with exponential backoff
                delay = min(self.base_delay * (2 ** attempt), self.max_delay)

                logger.warning(f"Gemini API call failed (attempt {attempt + 1}/{self.max_retries + 1}): {str(e)}")
                logger.info(f"Retrying in {delay:.1f} seconds...")

                await asyncio.sleep(delay)
            except Exception as e:
                # For non-retryable exceptions, fail immediately
                logger.error(f"Non-retryable error in Gemini API call: {str(e)}")
                raise e

        # If we get here, all retries failed
        if isinstance(last_exception, ResourceExhausted):
            raise ValueError("Gemini API rate limit exceeded. Please try again in a few minutes.")
        elif isinstance(last_exception, (ServiceUnavailable, DeadlineExceeded)):
            raise ValueError("Gemini API is temporarily unavailable. Please try again later.")
        else:
            raise ValueError(f"Gemini API error after {self.max_retries + 1} attempts: {str(last_exception)}")

    def _get_mcq_prompt(self, question_count: int, course_name: Optional[str] = None) -> str:
        """Generate optimized prompt for MCQ generation"""
        course_context = f" for the course '{course_name}'" if course_name else ""

        return f"""You are an expert educational content creator. Analyze the provided PDF document and generate {question_count} high-quality multiple choice questions{course_context}.

You must return a JSON object with this EXACT structure:

{{
  "questions": [
    {{
      "question_text": "What is quantum mechanics?",
      "options": [
        {{"option_text": "A branch of physics", "is_correct": true}},
        {{"option_text": "A type of chemistry", "is_correct": false}},
        {{"option_text": "A mathematical theory", "is_correct": false}},
        {{"option_text": "A biological process", "is_correct": false}}
      ],
      "correct_answer": "A branch of physics",
      "explanation": "Quantum mechanics is indeed a fundamental branch of physics that describes the behavior of matter and energy at the atomic and subatomic level.",
      "topic": "Quantum Mechanics",
      "difficulty": "easy",
      "bloom_taxonomy_level": "Remember"
    }}
  ],
  "total_count": {question_count},
  "source_topics": ["Physics", "Quantum Mechanics"]
}}

REQUIREMENTS:
1. Create exactly {question_count} questions covering different topics from the document
2. Each question must have exactly 4 options with "option_text" and "is_correct" fields
3. Only one option should have "is_correct": true
4. Use lowercase for difficulty: "easy", "medium", or "hard"
5. Use proper Bloom's taxonomy levels: "Remember", "Understand", "Apply", "Analyze", "Evaluate", "Create"
6. Include clear explanations for why the correct answer is right
7. Focus on key concepts, important facts, and practical applications

Generate questions that would help students effectively study and understand the material."""

    def _get_flashcard_prompt(self, card_count: int, course_name: Optional[str] = None) -> str:
        """Generate optimized prompt for flashcard generation"""
        course_context = f" for the course '{course_name}'" if course_name else ""

        return f"""You are an expert educational content creator. Analyze the provided PDF document and generate {card_count} high-quality flashcards{course_context}.

You must return a JSON object with this EXACT structure:

{{
  "flashcards": [
    {{
      "front_content": "What is quantum mechanics?",
      "back_content": "Quantum mechanics is a fundamental theory in physics that describes the behavior of matter and energy at the atomic and subatomic level.",
      "topic": "Quantum Mechanics",
      "difficulty": "easy",
      "card_type": "Definition"
    }}
  ],
  "total_count": {card_count},
  "source_topics": ["Physics", "Quantum Mechanics"]
}}

REQUIREMENTS:
1. Create exactly {card_count} flashcards covering key concepts from the document
2. Each flashcard should have a clear, concise front_content (question/prompt) and comprehensive back_content (answer/explanation)
3. Use lowercase for difficulty: "easy", "medium", or "hard"
4. Include different card_type values: "Definition", "Concept", "Formula", "Example", "Process", "Comparison"
5. Focus on information students need to memorize and understand
6. Make front_content specific enough to elicit the intended answer
7. Make back_content comprehensive but concise
8. Cover the most important topics from the document

Create flashcards that will help students effectively memorize and understand the key concepts."""

    def _get_summary_prompt(self, course_name: Optional[str] = None) -> str:
        """Generate optimized prompt for summary generation"""
        course_context = f" in the context of the course '{course_name}'" if course_name else ""

        return f"""You are an expert educational content creator. Analyze the provided PDF document and create a comprehensive, well-structured summary{course_context}.

You must return a JSON object with this EXACT structure:

{{
  "summary": {{
    "title": "Physics 343 Test Document",
    "overview": "This document covers fundamental concepts in quantum mechanics including wave-particle duality and the uncertainty principle.",
    "sections": [
      {{
        "heading": "Quantum Mechanics",
        "content": "Detailed explanation of quantum mechanics concepts...",
        "key_points": ["Key concept 1", "Key concept 2"]
      }}
    ],
    "key_concepts": ["Quantum mechanics", "Wave-particle duality", "Uncertainty principle"],
    "main_topics": ["Physics", "Quantum Mechanics"],
    "word_count": 250
  }},
  "source_info": {{
    "document_type": "Educational Material",
    "subject": "Physics"
  }}
}}

REQUIREMENTS:
1. Create a clear, hierarchical summary with logical sections
2. Include a brief overview of the entire document
3. Break down content into meaningful sections with descriptive headings
4. Extract and highlight key concepts and important points
5. Identify main topics covered in the document
6. Maintain academic tone and clarity
7. Focus on the most important information students need to know
8. Organize information in a way that aids understanding and retention

Create a summary that serves as an effective study guide and reference material."""

    def _get_note_explanation_prompt(self, course_name: Optional[str] = None) -> str:
        """Generate optimized prompt for comprehensive note explanation with simplified JSON structure"""
        course_context = f" for the course '{course_name}'" if course_name else ""

        return f"""You are an expert educational tutor. Analyze the provided document and create a comprehensive explanation{course_context}.

Return ONLY valid JSON with this exact structure:

{{
  "explanation": {{
    "title": "Document Title Here",
    "overview": "Brief overview of the document content and main themes.",
    "detailed_explanation": "Comprehensive explanation of all concepts, topics, and important information from the document. Explain each concept clearly and thoroughly.",
    "comprehensive_summary": "Summary that ties together all the key points and provides a complete understanding of the material.",
    "key_concepts": ["concept1", "concept2", "concept3"],
    "main_topics": ["topic1", "topic2", "topic3"],
    "word_count": 500
  }},
  "source_info": {{
    "document_type": "Educational Material",
    "subject": "General"
  }}
}}

IMPORTANT RULES:
1. Use simple text without complex formatting, markdown, or special characters
2. Avoid backslashes, quotes within quotes, and complex punctuation
3. Keep explanations clear and comprehensive but use simple language
4. Replace any problematic characters with simple alternatives
5. Ensure all JSON strings are properly formatted
6. Focus on content quality while maintaining JSON validity
7. Use periods instead of colons in explanations to avoid JSON issues
8. Keep array items simple and avoid complex descriptions

Create a thorough explanation that helps students understand the document content completely."""

    async def _generate_mcqs_chunked(self, file_ref, question_count: int, course_name: Optional[str], model):
        """Generate MCQs using chunked approach to avoid token limits and truncation"""
        # Use smaller chunks for better reliability
        chunk_size = 5 if question_count > 20 else 8
        all_questions = []
        all_topics = set()

        chunks_needed = (question_count + chunk_size - 1) // chunk_size
        logger.info(f"Generating {question_count} questions in {chunks_needed} chunks of {chunk_size}")

        for chunk_idx in range(chunks_needed):
            start_idx = chunk_idx * chunk_size
            end_idx = min(start_idx + chunk_size, question_count)
            chunk_count = end_idx - start_idx

            logger.info(f"Generating chunk {chunk_idx + 1}/{chunks_needed} with {chunk_count} questions")

            # Create focused prompt for this chunk
            course_context = f" for the course '{course_name}'" if course_name else ""
            chunk_prompt = f"""Generate exactly {chunk_count} high-quality multiple choice questions from this document{course_context}.

Focus on different topics and concepts to ensure variety across all chunks.
Make questions clear, concise, and educationally valuable.

Return ONLY valid JSON with this exact structure:
{{
  "questions": [
    {{
      "question_text": "Clear, specific question text",
      "options": [
        {{"option_text": "Option A text", "is_correct": true}},
        {{"option_text": "Option B text", "is_correct": false}},
        {{"option_text": "Option C text", "is_correct": false}},
        {{"option_text": "Option D text", "is_correct": false}}
      ],
      "correct_answer": "Option A text",
      "explanation": "Clear explanation of why this answer is correct",
      "topic": "Specific topic name",
      "difficulty": "easy",
      "bloom_taxonomy_level": "Remember"
    }}
  ],
  "source_topics": ["topic1", "topic2"]
}}

Ensure JSON is complete and valid. Keep explanations concise but informative."""

            try:
                chunk_response = await self._retry_with_backoff(
                    lambda: asyncio.to_thread(
                        model.generate_content,
                        [file_ref, chunk_prompt],
                        generation_config=genai.GenerationConfig(
                            response_mime_type="application/json",
                            temperature=0.2,
                            max_output_tokens=1500  # Conservative limit for reliability
                        )
                    )
                )

                # Parse chunk response with robust error handling
                chunk_data = await self._parse_chunk_response(chunk_response.text.strip(), chunk_idx + 1)

                if chunk_data and 'questions' in chunk_data:
                    chunk_questions = chunk_data.get('questions', [])
                    all_questions.extend(chunk_questions)
                    all_topics.update(chunk_data.get('source_topics', []))
                    logger.info(f"Successfully generated {len(chunk_questions)} questions in chunk {chunk_idx + 1}")
                else:
                    logger.warning(f"Chunk {chunk_idx + 1} produced no valid questions")

                # Small delay between chunks to avoid rate limiting
                if chunk_idx < chunks_needed - 1:
                    await asyncio.sleep(1.0)  # Increased delay

            except Exception as chunk_error:
                logger.error(f"Error in chunk {chunk_idx + 1}: {chunk_error}")
                # Try a simpler approach for this chunk
                try:
                    simple_chunk_data = await self._generate_simple_chunk(file_ref, chunk_count, model, chunk_idx + 1)
                    if simple_chunk_data and 'questions' in simple_chunk_data:
                        chunk_questions = simple_chunk_data.get('questions', [])
                        all_questions.extend(chunk_questions)
                        all_topics.update(simple_chunk_data.get('source_topics', []))
                        logger.info(f"Recovered chunk {chunk_idx + 1} with {len(chunk_questions)} questions using simple method")
                except Exception as simple_error:
                    logger.error(f"Simple fallback also failed for chunk {chunk_idx + 1}: {simple_error}")
                    # Continue with other chunks
                    continue

        # Combine all chunks into final response
        response_data = {
            "questions": all_questions,
            "total_count": len(all_questions),
            "source_topics": list(all_topics)
        }

        logger.info(f"Chunked generation completed: {len(all_questions)} total questions from {chunks_needed} chunks")

        # If no questions were generated at all, raise an error
        if len(all_questions) == 0:
            raise ValueError("Unable to generate any questions. The document may be too complex or the AI service is experiencing issues. Please try again with a simpler document or fewer questions.")

        return MCQGenerationResponse(**response_data)

    async def _parse_chunk_response(self, response_text: str, chunk_num: int) -> dict:
        """Parse chunk response with robust error handling"""
        try:
            # Try direct parsing first
            return json.loads(response_text)
        except json.JSONDecodeError as e:
            logger.warning(f"Chunk {chunk_num} JSON parsing failed: {e}")
            logger.debug(f"Problematic response text: {response_text[:500]}...")

            # Initialize variables for error handling
            cleaned_text = response_text

            try:
                # Multiple repair strategies
                cleaned_text = response_text.strip()

                # Remove markdown code blocks if present
                cleaned_text = re.sub(r'```json\s*|\s*```', '', cleaned_text)

                # Remove any leading/trailing non-JSON content
                json_match = re.search(r'\{.*\}', cleaned_text, re.DOTALL)
                if json_match:
                    cleaned_text = json_match.group(0)

                # Strategy 1: Fix common JSON issues
                # Fix trailing commas
                cleaned_text = re.sub(r',(\s*[}\]])', r'\1', cleaned_text)

                # Fix unescaped quotes in strings
                cleaned_text = re.sub(r'(?<!\\)"(?=.*")', r'\\"', cleaned_text)

                # Strategy 2: Extract valid questions using regex
                if '"questions"' in cleaned_text:
                    # Find all complete question objects
                    question_pattern = r'\{\s*"question_text":\s*"[^"]*"[^}]*"bloom_taxonomy_level":\s*"[^"]*"\s*\}'
                    questions = re.findall(question_pattern, cleaned_text, re.DOTALL)

                    if questions:
                        # Build valid JSON from extracted questions
                        questions_json = ',\n    '.join(questions)
                        repaired_json = f'{{\n  "questions": [\n    {questions_json}\n  ],\n  "source_topics": []\n}}'
                        cleaned_text = repaired_json
                        logger.info(f"Extracted {len(questions)} questions from malformed JSON in chunk {chunk_num}")

                # Strategy 3: If still malformed, try to truncate at last valid point
                if not cleaned_text.rstrip().endswith('}'):
                    # Find the last complete question
                    last_question_end = cleaned_text.rfind('}')
                    if last_question_end != -1:
                        # Find the questions array start
                        questions_start = cleaned_text.find('"questions": [')
                        if questions_start != -1:
                            # Extract everything up to the last complete question
                            truncated_part = cleaned_text[:last_question_end + 1]

                            # Ensure proper array closing
                            if '"questions": [' in truncated_part and not truncated_part.count('[') == truncated_part.count(']'):
                                truncated_part += '\n  ]'

                            # Add missing fields and close object
                            if not '"source_topics"' in truncated_part:
                                truncated_part += ',\n  "source_topics": []'

                            if not truncated_part.rstrip().endswith('}'):
                                truncated_part += '\n}'

                            cleaned_text = truncated_part

                return json.loads(cleaned_text)

            except Exception as repair_error:
                logger.error(f"All repair strategies failed for chunk {chunk_num}: {repair_error}")
                # cleaned_text might not be defined if error occurred early
                try:
                    logger.debug(f"Final cleaned text: {cleaned_text[:200]}...")
                except NameError:
                    logger.debug("No cleaned text available for debugging")

                # Last resort: try to extract any question text and create minimal structure
                try:
                    question_texts = re.findall(r'"question_text":\s*"([^"]*)"', response_text)
                    if question_texts:
                        # Create minimal questions from extracted text
                        minimal_questions = []
                        for q_text in question_texts[:3]:  # Max 3 questions
                            minimal_q = {
                                "question_text": q_text,
                                "options": [
                                    {"option_text": "A", "is_correct": True},
                                    {"option_text": "B", "is_correct": False},
                                    {"option_text": "C", "is_correct": False},
                                    {"option_text": "D", "is_correct": False}
                                ],
                                "correct_answer": "A",
                                "explanation": "Answer extracted from partial response",
                                "topic": "General",
                                "difficulty": "easy",
                                "bloom_taxonomy_level": "Remember"
                            }
                            minimal_questions.append(minimal_q)

                        logger.info(f"Created {len(minimal_questions)} minimal questions from chunk {chunk_num}")
                        return {"questions": minimal_questions, "source_topics": []}
                except:
                    pass

                return {"questions": [], "source_topics": []}

    async def _generate_simple_chunk(self, file_ref, chunk_count: int, model, chunk_num: int) -> dict:
        """Generate a simple chunk with minimal prompt to avoid JSON issues"""
        try:
            # Ultra-simple prompt to maximize success rate
            simple_prompt = f"""Create {min(chunk_count, 3)} multiple choice questions from this document.

Return only this JSON format:
{{
  "questions": [
    {{
      "question_text": "What is the main topic?",
      "options": [
        {{"option_text": "Option A", "is_correct": true}},
        {{"option_text": "Option B", "is_correct": false}},
        {{"option_text": "Option C", "is_correct": false}},
        {{"option_text": "Option D", "is_correct": false}}
      ],
      "correct_answer": "Option A",
      "explanation": "Explanation here",
      "topic": "Main Topic",
      "difficulty": "easy",
      "bloom_taxonomy_level": "Remember"
    }}
  ]
}}

Make questions simple. Ensure complete JSON."""

            response = await self._retry_with_backoff(
                lambda: asyncio.to_thread(
                    model.generate_content,
                    [file_ref, simple_prompt],
                    generation_config=genai.GenerationConfig(
                        response_mime_type="application/json",
                        temperature=0.05,  # Very low temperature for consistency
                        max_output_tokens=1000  # Reduced further
                    )
                )
            )

            return await self._parse_chunk_response(response.text.strip(), chunk_num)

        except Exception as e:
            logger.error(f"Simple chunk generation failed for chunk {chunk_num}: {e}")
            return {"questions": [], "source_topics": []}

    async def generate_mcqs(
        self,
        gemini_file_id: str,
        question_count: int,
        student_id: int,
        course_name: Optional[str] = None,
        db: Session = None
    ) -> MCQGenerationResponse:
        """
        Generate MCQs from a Gemini file using structured output
        
        Args:
            gemini_file_id: Gemini file ID
            question_count: Number of questions to generate
            student_id: Student ID for validation
            course_name: Course context
            db: Database session for validation
            
        Returns:
            MCQGenerationResponse with generated questions
        """
        try:
            # No database validation - using Gemini Files API directly

            # Get file reference with retry logic
            file_ref = await self._retry_with_backoff(
                lambda: asyncio.to_thread(genai.get_file, gemini_file_id)
            )

            # Generate prompt
            prompt = self._get_mcq_prompt(question_count, course_name)

            # Create model and generate content with JSON mode
            model = genai.GenerativeModel(model_name=self.model_name)

            # For requests >10 questions, use chunked generation immediately for better reliability
            if question_count > 10:
                logger.info(f"Using chunked generation for {question_count} questions to avoid truncation")
                return await self._generate_mcqs_chunked(file_ref, question_count, course_name, model)

            # Calculate appropriate token limit based on question count
            tokens_per_question = 300  # Normal allocation for smaller batches
            max_tokens = min(8000, question_count * tokens_per_question + 500)

            # Generate content with retry logic
            async def _generate_content():
                return await asyncio.to_thread(
                    model.generate_content,
                    [file_ref, prompt],
                    generation_config=genai.GenerationConfig(
                        response_mime_type="application/json",
                        temperature=0.3,
                        max_output_tokens=max_tokens
                    )
                )

            response = await self._retry_with_backoff(_generate_content)
            
            # Parse JSON response with better error handling
            import json
            import re

            try:
                response_text = response.text.strip()
                logger.debug(f"Raw Gemini response: {response_text[:500]}...")

                # Try to parse JSON directly first
                response_data = json.loads(response_text)

            except json.JSONDecodeError as json_error:
                logger.warning(f"JSON parsing failed: {json_error}")
                logger.debug(f"Problematic JSON: {response_text}")

                # Try to clean and fix common JSON issues
                try:
                    # Remove any markdown code blocks if present
                    cleaned_text = re.sub(r'```json\s*|\s*```', '', response_text)

                    # Remove any leading/trailing whitespace
                    cleaned_text = cleaned_text.strip()

                    # Try to extract JSON from the response if it's embedded in other text
                    json_match = re.search(r'\{.*\}', cleaned_text, re.DOTALL)
                    if json_match:
                        cleaned_text = json_match.group(0)

                    # Check if JSON appears to be truncated and try to fix it
                    if not cleaned_text.rstrip().endswith('}'):
                        logger.warning("JSON appears to be truncated, attempting to fix")

                        # More sophisticated truncation repair
                        try:
                            # Strategy 1: Find the last complete question and rebuild from there
                            question_pattern = r'\{\s*"question_text":[^}]*"bloom_taxonomy_level":\s*"[^"]*"\s*\}'
                            matches = list(re.finditer(question_pattern, cleaned_text, re.DOTALL))

                            if matches:
                                last_match = matches[-1]
                                last_complete_end = last_match.end()

                                # Extract everything up to the last complete question
                                truncated_part = cleaned_text[:last_complete_end]

                                # Ensure we close the questions array properly
                                if '"questions"' in truncated_part:
                                    # Remove any incomplete trailing content after the last question
                                    if not truncated_part.rstrip().endswith('}'):
                                        # Find the last complete question closing brace
                                        last_brace = truncated_part.rfind('}')
                                        if last_brace != -1:
                                            truncated_part = truncated_part[:last_brace + 1]

                                    # Close the questions array
                                    if not truncated_part.rstrip().endswith(']'):
                                        truncated_part += '\n  ]'

                                    # Add required fields
                                    question_count_found = len(matches)
                                    truncated_part += f',\n  "total_count": {question_count_found}'

                                    # Extract topics from the questions for source_topics
                                    topic_pattern = r'"topic":\s*"([^"]*)"'
                                    topic_matches = re.findall(topic_pattern, truncated_part)
                                    unique_topics = list(set(topic_matches))
                                    topics_json = json.dumps(unique_topics)
                                    truncated_part += f',\n  "source_topics": {topics_json}'

                                    # Close the main object
                                    truncated_part += '\n}'

                                    cleaned_text = truncated_part
                                    logger.info(f"Successfully repaired truncated JSON with {len(matches)} complete questions")

                            else:
                                # Strategy 2: If no complete questions found, try to salvage what we can
                                logger.warning("No complete questions found, attempting basic repair")

                                # Find the start of questions array
                                questions_start = cleaned_text.find('"questions": [')
                                if questions_start != -1:
                                    # Create minimal valid JSON
                                    cleaned_text = '{\n  "questions": [],\n  "total_count": 0,\n  "source_topics": []\n}'
                                    logger.info("Created minimal valid JSON structure")

                        except Exception as repair_error:
                            logger.warning(f"Advanced JSON repair failed: {repair_error}")
                            # Strategy 3: Last resort - create minimal valid JSON
                            try:
                                cleaned_text = '{\n  "questions": [],\n  "total_count": 0,\n  "source_topics": []\n}'
                                logger.info("Used emergency fallback - empty JSON structure")
                            except Exception:
                                # If all else fails, let the original error propagate
                                pass

                    response_data = json.loads(cleaned_text)
                    logger.info("Successfully parsed JSON after cleaning")

                except json.JSONDecodeError as second_error:
                    logger.error(f"JSON parsing failed even after cleaning: {second_error}")
                    logger.error(f"Full response text: {response_text}")

                    # Try chunked generation as fallback
                    try:
                        if question_count > 5:
                            logger.info(f"Attempting chunked generation as fallback for {question_count} questions")
                            mcq_response = await self._generate_mcqs_chunked(file_ref, question_count, course_name, model)
                            return mcq_response
                        else:
                            # Simple fallback for very small requests
                            fallback_count = max(1, question_count)
                            logger.info(f"Attempting simple fallback generation with {fallback_count} questions")

                            simple_prompt = f"""Generate exactly {fallback_count} multiple choice questions from this document.
Return ONLY valid JSON with this exact structure:
{{"questions": [{{"question_text": "...", "options": [{{"option_text": "...", "is_correct": true}}, {{"option_text": "...", "is_correct": false}}, {{"option_text": "...", "is_correct": false}}, {{"option_text": "...", "is_correct": false}}], "correct_answer": "...", "explanation": "...", "topic": "...", "difficulty": "easy", "bloom_taxonomy_level": "Remember"}}], "source_topics": []}}

Keep questions short and simple. Ensure JSON is complete and valid."""

                            fallback_response = await self._retry_with_backoff(
                                lambda: asyncio.to_thread(
                                    model.generate_content,
                                    [file_ref, simple_prompt],
                                    generation_config=genai.GenerationConfig(
                                        response_mime_type="application/json",
                                        temperature=0.1,
                                        max_output_tokens=1000
                                    )
                                )
                            )

                            response_data = json.loads(fallback_response.text.strip())
                            logger.info(f"Successfully generated {len(response_data.get('questions', []))} MCQs using simple fallback method")

                    except Exception as fallback_error:
                        logger.error(f"Fallback generation also failed: {fallback_error}")
                        # Provide user-friendly error message
                        if "timeout" in str(fallback_error).lower() or "503" in str(fallback_error):
                            raise ValueError("The AI service is temporarily busy. Please try again in a few minutes.")
                        elif "429" in str(fallback_error) or "quota" in str(fallback_error).lower():
                            raise ValueError("Too many requests. Please wait a moment before trying again.")
                        else:
                            raise ValueError("Unable to generate MCQs at this time. Please try with fewer questions or try again later.")

                except Exception as clean_error:
                    logger.error(f"Error during JSON cleaning: {clean_error}")
                    if "timeout" in str(clean_error).lower():
                        raise ValueError("Request timed out. Please try again with fewer questions.")
                    else:
                        raise ValueError("Unable to process the generated content. Please try again with fewer questions.")

            # Ensure we have the expected structure and add missing fields
            if "questions" not in response_data:
                raise ValueError("Invalid response format: missing 'questions' field")

            # Add total_count if missing
            if "total_count" not in response_data:
                response_data["total_count"] = len(response_data["questions"])

            # Add source_topics if missing
            if "source_topics" not in response_data:
                response_data["source_topics"] = []

            # Validate and fix question structure
            for i, question in enumerate(response_data["questions"]):
                # Ensure all required fields are present
                required_fields = ["question_text", "options", "correct_answer", "explanation", "topic", "difficulty", "bloom_taxonomy_level"]
                for field in required_fields:
                    if field not in question:
                        logger.warning(f"Question {i} missing field: {field}")
                        if field == "options":
                            question[field] = []
                        else:
                            question[field] = ""

                # Ensure difficulty is lowercase
                if "difficulty" in question and question["difficulty"]:
                    question["difficulty"] = question["difficulty"].lower()

            mcq_response = MCQGenerationResponse(**response_data)

            logger.info(f"Generated {len(mcq_response.questions)} MCQs from file {gemini_file_id}")

            return mcq_response

        except ValueError as e:
            # ValueError exceptions already have user-friendly messages
            logger.error(f"MCQ generation failed: {str(e)}")
            raise
        except Exception as e:
            logger.error(f"Unexpected error generating MCQs: {str(e)}")
            # Convert technical errors to user-friendly messages
            error_str = str(e).lower()
            if "timeout" in error_str or "503" in error_str:
                raise ValueError("The AI service is temporarily unavailable. Please try again in a few minutes.")
            elif "429" in error_str or "quota" in error_str or "rate limit" in error_str:
                raise ValueError("Too many requests. Please wait a moment before trying again.")
            elif "network" in error_str or "connection" in error_str:
                raise ValueError("Network connection issue. Please check your internet connection and try again.")
            elif "file not found" in error_str or "404" in error_str:
                raise ValueError("The uploaded file could not be found. Please upload the file again.")
            else:
                raise ValueError("Unable to generate MCQs at this time. Please try again later.")

    async def _generate_flashcards_chunked(self, file_ref, card_count: int, course_name: Optional[str], model):
        """Generate flashcards using chunked approach to avoid token limits and truncation"""
        # Use smaller chunks for better reliability
        chunk_size = 5 if card_count > 20 else 8
        all_flashcards = []
        all_topics = set()

        chunks_needed = (card_count + chunk_size - 1) // chunk_size
        logger.info(f"Generating {card_count} flashcards in {chunks_needed} chunks of {chunk_size}")

        for chunk_idx in range(chunks_needed):
            start_idx = chunk_idx * chunk_size
            end_idx = min(start_idx + chunk_size, card_count)
            chunk_count = end_idx - start_idx

            logger.info(f"Generating flashcard chunk {chunk_idx + 1}/{chunks_needed} with {chunk_count} flashcards")

            # Create focused prompt for this chunk
            course_context = f" for the course '{course_name}'" if course_name else ""
            chunk_prompt = f"""Generate exactly {chunk_count} high-quality flashcards from this document{course_context}.

Focus on different topics and concepts to ensure variety across all chunks.
Make flashcards clear, concise, and educationally valuable.

Return ONLY valid JSON with this exact structure:
{{
  "flashcards": [
    {{
      "front_content": "Question or concept on front",
      "back_content": "Answer or explanation on back",
      "topic": "Specific topic name",
      "difficulty": "easy",
      "card_type": "concept"
    }}
  ],
  "source_topics": ["topic1", "topic2"]
}}

Ensure JSON is complete and valid. Keep content concise but informative."""

            try:
                chunk_response = await self._retry_with_backoff(
                    lambda: asyncio.to_thread(
                        model.generate_content,
                        [file_ref, chunk_prompt],
                        generation_config=genai.GenerationConfig(
                            response_mime_type="application/json",
                            temperature=0.2,
                            max_output_tokens=1500  # Conservative limit for reliability
                        )
                    )
                )

                # Parse chunk response with robust error handling
                chunk_data = await self._parse_flashcard_chunk_response(chunk_response.text.strip(), chunk_idx + 1)

                if chunk_data and 'flashcards' in chunk_data:
                    chunk_flashcards = chunk_data.get('flashcards', [])
                    all_flashcards.extend(chunk_flashcards)
                    all_topics.update(chunk_data.get('source_topics', []))
                    logger.info(f"Successfully generated {len(chunk_flashcards)} flashcards in chunk {chunk_idx + 1}")
                else:
                    logger.warning(f"Flashcard chunk {chunk_idx + 1} produced no valid flashcards")

                # Small delay between chunks to avoid rate limiting
                if chunk_idx < chunks_needed - 1:
                    await asyncio.sleep(1.0)

            except Exception as chunk_error:
                logger.error(f"Error in flashcard chunk {chunk_idx + 1}: {chunk_error}")
                # Try a simpler approach for this chunk
                try:
                    simple_chunk_data = await self._generate_simple_flashcard_chunk(file_ref, chunk_count, model, chunk_idx + 1)
                    if simple_chunk_data and 'flashcards' in simple_chunk_data:
                        chunk_flashcards = simple_chunk_data.get('flashcards', [])
                        all_flashcards.extend(chunk_flashcards)
                        all_topics.update(simple_chunk_data.get('source_topics', []))
                        logger.info(f"Recovered flashcard chunk {chunk_idx + 1} with {len(chunk_flashcards)} flashcards using simple method")
                except Exception as simple_error:
                    logger.error(f"Simple fallback also failed for flashcard chunk {chunk_idx + 1}: {simple_error}")
                    # Continue with other chunks
                    continue

        # Combine all chunks into final response
        response_data = {
            "flashcards": all_flashcards,
            "total_count": len(all_flashcards),
            "source_topics": list(all_topics)
        }

        logger.info(f"Chunked flashcard generation completed: {len(all_flashcards)} total flashcards from {chunks_needed} chunks")

        # If no flashcards were generated at all, raise an error
        if len(all_flashcards) == 0:
            raise ValueError("Unable to generate any flashcards. The document may be too complex or the AI service is experiencing issues. Please try again with a simpler document or fewer flashcards.")

        return FlashcardGenerationResponse(**response_data)

    async def _parse_flashcard_chunk_response(self, response_text: str, chunk_num: int) -> dict:
        """Parse flashcard chunk response with robust error handling"""
        try:
            # Try direct parsing first
            return json.loads(response_text)
        except json.JSONDecodeError as e:
            logger.warning(f"Flashcard chunk {chunk_num} JSON parsing failed: {e}")
            logger.debug(f"Problematic response text: {response_text[:500]}...")

            # Initialize variables for error handling
            cleaned_text = response_text

            try:
                # Multiple repair strategies
                cleaned_text = response_text.strip()

                # Remove markdown code blocks if present
                cleaned_text = re.sub(r'```json\s*|\s*```', '', cleaned_text)

                # Remove any leading/trailing non-JSON content
                json_match = re.search(r'\{.*\}', cleaned_text, re.DOTALL)
                if json_match:
                    cleaned_text = json_match.group(0)

                # Strategy 1: Fix common JSON issues
                # Fix trailing commas
                cleaned_text = re.sub(r',(\s*[}\]])', r'\1', cleaned_text)

                # Strategy 2: Extract valid flashcards using regex
                if '"flashcards"' in cleaned_text:
                    # Find all complete flashcard objects
                    flashcard_pattern = r'\{\s*"front_content":\s*"[^"]*"[^}]*"card_type":\s*"[^"]*"\s*\}'
                    flashcards = re.findall(flashcard_pattern, cleaned_text, re.DOTALL)

                    if flashcards:
                        # Build valid JSON from extracted flashcards
                        flashcards_json = ',\n    '.join(flashcards)
                        repaired_json = f'{{\n  "flashcards": [\n    {flashcards_json}\n  ],\n  "source_topics": []\n}}'
                        cleaned_text = repaired_json
                        logger.info(f"Extracted {len(flashcards)} flashcards from malformed JSON in chunk {chunk_num}")

                # Strategy 3: If still malformed, try to truncate at last valid point
                if not cleaned_text.rstrip().endswith('}'):
                    # Find the last complete flashcard
                    last_flashcard_end = cleaned_text.rfind('}')
                    if last_flashcard_end != -1:
                        # Find the flashcards array start
                        flashcards_start = cleaned_text.find('"flashcards": [')
                        if flashcards_start != -1:
                            # Extract everything up to the last complete flashcard
                            truncated_part = cleaned_text[:last_flashcard_end + 1]

                            # Ensure proper array closing
                            if '"flashcards": [' in truncated_part and not truncated_part.count('[') == truncated_part.count(']'):
                                truncated_part += '\n  ]'

                            # Add missing fields and close object
                            if not '"source_topics"' in truncated_part:
                                truncated_part += ',\n  "source_topics": []'

                            if not truncated_part.rstrip().endswith('}'):
                                truncated_part += '\n}'

                            cleaned_text = truncated_part

                return json.loads(cleaned_text)

            except Exception as repair_error:
                logger.error(f"All repair strategies failed for flashcard chunk {chunk_num}: {repair_error}")
                # cleaned_text might not be defined if error occurred early
                try:
                    logger.debug(f"Final cleaned text: {cleaned_text[:200]}...")
                except NameError:
                    logger.debug("No cleaned text available for debugging")

                # Last resort: try to extract any flashcard content and create minimal structure
                try:
                    front_contents = re.findall(r'"front_content":\s*"([^"]*)"', response_text)
                    back_contents = re.findall(r'"back_content":\s*"([^"]*)"', response_text)
                    if front_contents and back_contents:
                        # Create minimal flashcards from extracted content
                        minimal_flashcards = []
                        for front, back in zip(front_contents[:3], back_contents[:3]):  # Max 3 flashcards
                            minimal_fc = {
                                "front_content": front,
                                "back_content": back,
                                "topic": "General",
                                "difficulty": "easy",
                                "card_type": "concept"
                            }
                            minimal_flashcards.append(minimal_fc)

                        logger.info(f"Created {len(minimal_flashcards)} minimal flashcards from chunk {chunk_num}")
                        return {"flashcards": minimal_flashcards, "source_topics": []}
                except:
                    pass

                return {"flashcards": [], "source_topics": []}

    async def _generate_simple_flashcard_chunk(self, file_ref, chunk_count: int, model, chunk_num: int) -> dict:
        """Generate a simple flashcard chunk with minimal prompt to avoid JSON issues"""
        try:
            # Ultra-simple prompt to maximize success rate
            simple_prompt = f"""Create {min(chunk_count, 3)} flashcards from this document.

Return only this JSON format:
{{
  "flashcards": [
    {{
      "front_content": "Question or concept",
      "back_content": "Answer or explanation",
      "topic": "Topic Name",
      "difficulty": "easy",
      "card_type": "concept"
    }}
  ]
}}

Make flashcards simple. Ensure complete JSON."""

            response = await self._retry_with_backoff(
                lambda: asyncio.to_thread(
                    model.generate_content,
                    [file_ref, simple_prompt],
                    generation_config=genai.GenerationConfig(
                        response_mime_type="application/json",
                        temperature=0.05,  # Very low temperature for consistency
                        max_output_tokens=1000  # Reduced further
                    )
                )
            )

            return await self._parse_flashcard_chunk_response(response.text.strip(), chunk_num)

        except Exception as e:
            logger.error(f"Simple flashcard chunk generation failed for chunk {chunk_num}: {e}")
            return {"flashcards": [], "source_topics": []}

    async def generate_flashcards(
        self,
        gemini_file_id: str,
        card_count: int,
        student_id: int,
        course_name: Optional[str] = None,
        db: Session = None
    ) -> FlashcardGenerationResponse:
        """
        Generate flashcards from a Gemini file using structured output with chunked approach
        """
        try:
            # Get file reference with retry logic
            file_ref = await self._retry_with_backoff(
                lambda: asyncio.to_thread(genai.get_file, gemini_file_id)
            )

            # For requests >10 flashcards, use chunked generation for better reliability
            if card_count > 10:
                logger.info(f"Using chunked generation for {card_count} flashcards to avoid truncation")
                return await self._generate_flashcards_chunked(file_ref, card_count, course_name, genai.GenerativeModel(model_name=self.model_name))

            # Generate prompt
            prompt = self._get_flashcard_prompt(card_count, course_name)

            # Create model and generate content with JSON mode
            model = genai.GenerativeModel(model_name=self.model_name)

            # Generate content with retry logic
            async def _generate_content():
                return await asyncio.to_thread(
                    model.generate_content,
                    [file_ref, prompt],
                    generation_config=genai.GenerationConfig(
                        response_mime_type="application/json",
                        temperature=0.3,
                        max_output_tokens=3000  # Reduced for better reliability
                    )
                )

            response = await self._retry_with_backoff(_generate_content)
            
            # Parse JSON response with robust error handling
            try:
                response_text = response.text.strip()
                logger.debug(f"Raw Gemini flashcard response: {response_text[:500]}...")
                response_data = json.loads(response_text)
            except json.JSONDecodeError as json_error:
                logger.warning(f"Flashcard JSON parsing failed: {json_error}")
                # Try chunked generation as fallback
                logger.info(f"Attempting chunked generation as fallback for {card_count} flashcards")
                return await self._generate_flashcards_chunked(file_ref, card_count, course_name, model)

            # Ensure we have the expected structure and add missing fields
            if "flashcards" not in response_data:
                raise ValueError("Invalid response format: missing 'flashcards' field")

            # Add total_count if missing
            if "total_count" not in response_data:
                response_data["total_count"] = len(response_data["flashcards"])

            # Add source_topics if missing
            if "source_topics" not in response_data:
                response_data["source_topics"] = []

            # Validate and fix flashcard structure
            for i, flashcard in enumerate(response_data["flashcards"]):
                # Ensure all required fields are present
                required_fields = ["front_content", "back_content", "topic", "difficulty", "card_type"]
                for field in required_fields:
                    if field not in flashcard:
                        logger.warning(f"Flashcard {i} missing field: {field}")
                        flashcard[field] = ""

                # Ensure difficulty is lowercase
                if "difficulty" in flashcard and flashcard["difficulty"]:
                    flashcard["difficulty"] = flashcard["difficulty"].lower()

            flashcard_response = FlashcardGenerationResponse(**response_data)

            logger.info(f"Generated {len(flashcard_response.flashcards)} flashcards from file {gemini_file_id}")

            return flashcard_response

        except ValueError as e:
            # ValueError exceptions already have user-friendly messages
            logger.error(f"Flashcard generation failed: {str(e)}")
            raise
        except Exception as e:
            logger.error(f"Unexpected error generating flashcards: {str(e)}")
            # Convert technical errors to user-friendly messages
            error_str = str(e).lower()
            if "timeout" in error_str or "503" in error_str:
                raise ValueError("The AI service is temporarily unavailable. Please try again in a few minutes.")
            elif "429" in error_str or "quota" in error_str or "rate limit" in error_str:
                raise ValueError("Too many requests. Please wait a moment before trying again.")
            elif "network" in error_str or "connection" in error_str:
                raise ValueError("Network connection issue. Please check your internet connection and try again.")
            elif "file not found" in error_str or "404" in error_str:
                raise ValueError("The uploaded file could not be found. Please upload the file again.")
            else:
                raise ValueError("Unable to generate flashcards at this time. Please try again later.")

    async def generate_summary(
        self,
        gemini_file_id: str,
        student_id: int,
        course_name: Optional[str] = None,
        db: Session = None
    ) -> SummaryGenerationResponse:
        """
        Generate summary from a Gemini file using structured output
        """
        try:
            # No database validation - using Gemini Files API directly

            # Get file reference
            file_ref = await asyncio.to_thread(
                genai.get_file,
                name=gemini_file_id
            )
            
            # Generate prompt
            prompt = self._get_summary_prompt(course_name)
            
            # Create model and generate content with JSON mode
            model = genai.GenerativeModel(model_name=self.model_name)

            response = await asyncio.to_thread(
                model.generate_content,
                [file_ref, prompt],
                generation_config=genai.GenerationConfig(
                    response_mime_type="application/json",
                    temperature=0.3,
                    max_output_tokens=4000
                )
            )
            
            # Parse JSON response
            import json
            response_data = json.loads(response.text)

            # Ensure we have the expected structure and add missing fields
            if "summary" not in response_data:
                raise ValueError("Invalid response format: missing 'summary' field")

            # Add source_info if missing
            if "source_info" not in response_data:
                response_data["source_info"] = {}

            # Validate summary structure
            summary = response_data["summary"]
            required_fields = ["title", "overview", "sections", "key_concepts", "main_topics", "word_count"]
            for field in required_fields:
                if field not in summary:
                    logger.warning(f"Summary missing field: {field}")
                    if field in ["sections", "key_concepts", "main_topics"]:
                        summary[field] = []
                    elif field == "word_count":
                        summary[field] = 0
                    else:
                        summary[field] = ""

            summary_response = SummaryGenerationResponse(**response_data)

            logger.info(f"Generated summary from file {gemini_file_id}")

            return summary_response
            
        except Exception as e:
            logger.error(f"Error generating summary: {str(e)}")
            raise

    async def _parse_note_explanation_response(self, response_text: str) -> dict:
        """Parse note explanation response with robust error handling for escape sequences"""
        logger.info("Attempting to repair malformed note explanation JSON")

        try:
            # Clean the response
            cleaned_text = response_text.strip()

            # Remove markdown code blocks if present
            cleaned_text = re.sub(r'```json\s*|\s*```', '', cleaned_text)

            # Extract JSON from the response if it's embedded
            json_match = re.search(r'\{.*\}', cleaned_text, re.DOTALL)
            if json_match:
                cleaned_text = json_match.group(0)

            # Strategy 1: Fix common escape sequence issues more aggressively
            # Replace problematic escape sequences
            cleaned_text = re.sub(r'\\(?!["\\/bfnrt])', r'\\\\', cleaned_text)  # Fix invalid escapes

            # Fix common problematic patterns
            cleaned_text = re.sub(r'\\n', '\\n', cleaned_text)  # Fix newlines
            cleaned_text = re.sub(r'\\t', '\\t', cleaned_text)  # Fix tabs
            cleaned_text = re.sub(r'\\r', '\\r', cleaned_text)  # Fix carriage returns

            # Fix trailing commas
            cleaned_text = re.sub(r',(\s*[}\]])', r'\1', cleaned_text)

            # Strategy 2: Try to parse the cleaned JSON
            try:
                parsed_data = json.loads(cleaned_text)
                logger.info("Successfully repaired and parsed note explanation JSON")
                return parsed_data
            except json.JSONDecodeError as e:
                logger.warning(f"JSON repair attempt failed: {e}")

                # Strategy 3: Try to extract content using regex and rebuild JSON
                try:
                    # Extract key content fields using regex
                    title_match = re.search(r'"title":\s*"([^"]*)"', cleaned_text)
                    overview_match = re.search(r'"overview":\s*"([^"]*)"', cleaned_text)
                    detailed_match = re.search(r'"detailed_explanation":\s*"([^"]*)"', cleaned_text)
                    summary_match = re.search(r'"comprehensive_summary":\s*"([^"]*)"', cleaned_text)

                    # Extract arrays
                    concepts_match = re.search(r'"key_concepts":\s*\[(.*?)\]', cleaned_text, re.DOTALL)
                    topics_match = re.search(r'"main_topics":\s*\[(.*?)\]', cleaned_text, re.DOTALL)

                    # Extract word count
                    word_count_match = re.search(r'"word_count":\s*(\d+)', cleaned_text)

                    if title_match or overview_match or detailed_match:
                        # Build response from extracted content
                        extracted_data = {
                            "explanation": {
                                "title": title_match.group(1) if title_match else "Document Analysis",
                                "overview": overview_match.group(1) if overview_match else "Document overview extracted from content.",
                                "detailed_explanation": detailed_match.group(1) if detailed_match else "Detailed analysis of the document content.",
                                "comprehensive_summary": summary_match.group(1) if summary_match else "Summary of key document insights.",
                                "key_concepts": self._extract_array_items(concepts_match.group(1)) if concepts_match else ["Document Content"],
                                "main_topics": self._extract_array_items(topics_match.group(1)) if topics_match else ["General Topics"],
                                "word_count": int(word_count_match.group(1)) if word_count_match else 500
                            },
                            "source_info": {
                                "processing_note": "Content extracted using regex parsing due to JSON formatting issues."
                            }
                        }
                        logger.info("Successfully extracted content using regex parsing")
                        return extracted_data

                except Exception as extract_error:
                    logger.error(f"Content extraction failed: {extract_error}")

                # Strategy 4: Try a simpler generation approach
                logger.warning("All parsing strategies failed, attempting simple regeneration")
                return await self._generate_simple_note_explanation(response_text)

        except Exception as repair_error:
            logger.error(f"Failed to repair note explanation JSON: {repair_error}")
            # Return minimal structure as last resort
            return {
                "explanation": {
                    "title": "Processing Error",
                    "overview": "Unable to fully process the document content due to technical issues.",
                    "detailed_explanation": "The document could not be processed due to formatting complexities. Please try uploading a simpler document or contact support.",
                    "comprehensive_summary": "Document processing encountered technical difficulties that prevented full content extraction.",
                    "key_concepts": ["Processing Error"],
                    "main_topics": ["Technical Issue"],
                    "word_count": 0
                },
                "source_info": {"error": "All parsing strategies failed"}
            }

    def _extract_array_items(self, array_content: str) -> list:
        """Extract items from array content string"""
        try:
            # Remove quotes and split by comma
            items = re.findall(r'"([^"]*)"', array_content)
            return items if items else ["Content Item"]
        except:
            return ["Content Item"]

    async def _generate_simple_note_explanation(self, original_response: str) -> dict:
        """Generate a simple note explanation when JSON parsing fails completely"""
        try:
            # Extract any readable text content from the response
            text_content = re.sub(r'[{}"\[\],:]', ' ', original_response)
            text_content = ' '.join(text_content.split())  # Clean whitespace

            # Try to find meaningful content
            if len(text_content) > 100:
                # Use the extracted text as content
                return {
                    "explanation": {
                        "title": "Document Content Analysis",
                        "overview": f"Analysis of the uploaded document content: {text_content[:200]}...",
                        "detailed_explanation": f"The document contains the following information: {text_content[:500]}...",
                        "comprehensive_summary": f"Summary: {text_content[:300]}...",
                        "key_concepts": ["Document Analysis", "Content Review"],
                        "main_topics": ["Document Content"],
                        "word_count": len(text_content.split())
                    },
                    "source_info": {
                        "processing_note": "Content extracted from malformed response."
                    }
                }
            else:
                # Fallback to minimal structure
                return {
                    "explanation": {
                        "title": "Document Processing Issue",
                        "overview": "The document was processed but the content could not be properly formatted.",
                        "detailed_explanation": "There was an issue processing the document content. Please try uploading the document again or contact support if the problem persists.",
                        "comprehensive_summary": "Document processing completed with formatting issues.",
                        "key_concepts": ["Processing Issue"],
                        "main_topics": ["Technical Problem"],
                        "word_count": 0
                    },
                    "source_info": {"error": "Content extraction failed"}
                }
        except Exception as e:
            logger.error(f"Simple note explanation generation failed: {e}")
            return {
                "explanation": {
                    "title": "Error",
                    "overview": "Failed to process document",
                    "detailed_explanation": "Unable to process the document",
                    "comprehensive_summary": "Processing failed",
                    "key_concepts": [],
                    "main_topics": [],
                    "word_count": 0
                },
                "source_info": {"error": str(e)}
            }

    async def _generate_simple_note_explanation_direct(self, file_ref, model, course_name: Optional[str] = None) -> dict:
        """Generate note explanation using ultra-simple approach"""
        try:
            course_context = f" for {course_name}" if course_name else ""

            simple_prompt = f"""Analyze this document{course_context} and create a comprehensive explanation.

Return only this JSON format:
{{
  "explanation": {{
    "title": "Document Analysis",
    "overview": "Overview of the main content and themes",
    "detailed_explanation": "Detailed explanation of all important concepts and information",
    "comprehensive_summary": "Complete summary of the key points",
    "key_concepts": ["concept1", "concept2", "concept3"],
    "main_topics": ["topic1", "topic2"],
    "word_count": 300
  }},
  "source_info": {{
    "document_type": "Educational Material",
    "subject": "General"
  }}
}}

Make the explanation thorough and educational. Use simple text without special formatting."""

            response = await self._retry_with_backoff(
                lambda: asyncio.to_thread(
                    model.generate_content,
                    [file_ref, simple_prompt],
                    generation_config=genai.GenerationConfig(
                        response_mime_type="application/json",
                        temperature=0.1,  # Very low temperature for consistency
                        max_output_tokens=3000
                    )
                )
            )

            # Try to parse the simple response
            try:
                return json.loads(response.text.strip())
            except json.JSONDecodeError:
                # If even the simple approach fails, return None
                logger.warning("Simple direct generation also produced invalid JSON")
                return None

        except Exception as e:
            logger.error(f"Simple direct note explanation generation failed: {e}")
            return None

    async def generate_note_explanation(
        self,
        gemini_file_id: str,
        student_id: int,
        course_name: Optional[str] = None,
        db: Session = None
    ) -> NoteExplanationGenerationResponse:
        """
        Generate comprehensive note explanation from a Gemini file using structured output with robust error handling
        """
        try:
            # Get file reference with retry logic
            file_ref = await self._retry_with_backoff(
                lambda: asyncio.to_thread(genai.get_file, gemini_file_id)
            )

            # Generate prompt
            prompt = self._get_note_explanation_prompt(course_name)

            # Create model and generate content with JSON mode
            model = genai.GenerativeModel(model_name=self.model_name)

            # Generate content with retry logic
            async def _generate_content():
                return await asyncio.to_thread(
                    model.generate_content,
                    [file_ref, prompt],
                    generation_config=genai.GenerationConfig(
                        response_mime_type="application/json",
                        temperature=0.3,
                        max_output_tokens=6000  # Reduced for better reliability
                    )
                )

            response = await self._retry_with_backoff(_generate_content)

            # Parse JSON response with robust error handling
            try:
                response_text = response.text.strip()
                logger.info(f"Raw Gemini note explanation response length: {len(response_text)} characters")
                logger.debug(f"Raw Gemini note explanation response: {response_text[:1000]}...")
                response_data = json.loads(response_text)
                logger.info("Successfully parsed note explanation JSON on first attempt")
            except json.JSONDecodeError as json_error:
                logger.warning(f"Note explanation JSON parsing failed: {json_error}")
                logger.error(f"Full problematic response: {response_text}")

                # Try to fix the JSON by handling escape sequences and other issues
                response_data = await self._parse_note_explanation_response(response_text)

                # If the parsed data is still minimal/default, try a simpler generation approach
                if (response_data.get("explanation", {}).get("title") in ["Document Summary", "Processing Error", "Document Content Analysis", "Document Processing Issue", "Error"] or
                    response_data.get("explanation", {}).get("word_count", 0) < 50):
                    logger.info("Attempting simple regeneration due to minimal content")
                    try:
                        simple_response_data = await self._generate_simple_note_explanation_direct(file_ref, model, course_name)
                        if simple_response_data and simple_response_data.get("explanation", {}).get("word_count", 0) > 50:
                            response_data = simple_response_data
                            logger.info("Successfully generated content using simple approach")
                    except Exception as simple_error:
                        logger.warning(f"Simple regeneration failed: {simple_error}")
                        # Continue with the repaired data

            # Ensure we have the expected structure and add missing fields
            if "explanation" not in response_data:
                raise ValueError("Invalid response format: missing 'explanation' field")

            # Add source_info if missing
            if "source_info" not in response_data:
                response_data["source_info"] = {}

            # Validate explanation structure
            explanation = response_data["explanation"]
            required_fields = ["title", "overview", "detailed_explanation", "comprehensive_summary", "key_concepts", "main_topics", "word_count"]
            for field in required_fields:
                if field not in explanation:
                    logger.warning(f"Explanation missing field: {field}")
                    if field in ["key_concepts", "main_topics"]:
                        explanation[field] = []
                    elif field == "word_count":
                        explanation[field] = 0
                    else:
                        explanation[field] = ""

            explanation_response = NoteExplanationGenerationResponse(**response_data)

            logger.info(f"Generated note explanation from file {gemini_file_id}")

            return explanation_response

        except ValueError as e:
            # ValueError exceptions already have user-friendly messages
            logger.error(f"Note explanation generation failed: {str(e)}")
            raise
        except Exception as e:
            logger.error(f"Unexpected error generating note explanation: {str(e)}")
            # Convert technical errors to user-friendly messages
            error_str = str(e).lower()
            if "timeout" in error_str or "503" in error_str:
                raise ValueError("The AI service is temporarily unavailable. Please try again in a few minutes.")
            elif "429" in error_str or "quota" in error_str or "rate limit" in error_str:
                raise ValueError("Too many requests. Please wait a moment before trying again.")
            elif "network" in error_str or "connection" in error_str:
                raise ValueError("Network connection issue. Please check your internet connection and try again.")
            elif "file not found" in error_str or "404" in error_str:
                raise ValueError("The uploaded file could not be found. Please upload the file again.")
            elif "invalid" in error_str and "escape" in error_str:
                raise ValueError("The document contains formatting that cannot be processed. Please try with a simpler document.")
            else:
                raise ValueError("Unable to generate note explanation at this time. Please try again later.")


# Create singleton instance
gemini_content_generator = GeminiContentGenerator()
