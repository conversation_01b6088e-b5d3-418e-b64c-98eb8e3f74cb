from typing import List, Optional
from sqlalchemy.orm import Session

from app.crud.crud_notification import notification
from app.crud.crud_shared_content import shared_content, share_invitation
from app.models.notification import NotificationType
from app.models.shared_content import ContentType, ShareStatus
from app.services import user_service
from app.services.email_service import send_content_share_invitation, send_share_acceptance_notification


class SharingNotificationService:
    """Service for handling sharing-related notifications"""
    
    @staticmethod
    def notify_content_shared(
        db: Session,
        shared_content_id: int,
        invited_user_ids: List[int],
        sharer_id: int
    ) -> None:
        """Send notifications when content is shared"""
        shared_content_obj = shared_content.get(db, id=shared_content_id)
        sharer = user_service.get(db, id=sharer_id)
        
        if not shared_content_obj or not sharer:
            return
        
        for user_id in invited_user_ids:
            notification.create_notification(
                db,
                user_id=user_id,
                notification_type=NotificationType.CONTENT_SHARED,
                title="New content shared with you",
                message=f"{sharer.full_name or sharer.email} shared {shared_content_obj.content_title} with you",
                related_id=shared_content_id,
                related_type="shared_content"
            )
    
    @staticmethod
    def notify_share_accepted(
        db: Session,
        shared_content_id: int,
        accepter_id: int,
        response_message: Optional[str] = None
    ) -> None:
        """Send notification when a share is accepted"""
        shared_content_obj = shared_content.get(db, id=shared_content_id)
        accepter = user_service.get(db, id=accepter_id)
        
        if not shared_content_obj or not accepter:
            return
        
        message = f"{accepter.full_name or accepter.email} accepted your shared content: {shared_content_obj.content_title}"
        if response_message:
            message += f" - Message: {response_message}"
        
        notification.create_notification(
            db,
            user_id=shared_content_obj.shared_by_id,
            notification_type=NotificationType.CONTENT_SHARE_ACCEPTED,
            title="Content share accepted",
            message=message,
            related_id=shared_content_id,
            related_type="shared_content"
        )
    
    @staticmethod
    def notify_share_rejected(
        db: Session,
        shared_content_id: int,
        rejecter_id: int,
        response_message: Optional[str] = None
    ) -> None:
        """Send notification when a share is rejected"""
        shared_content_obj = shared_content.get(db, id=shared_content_id)
        rejecter = user_service.get(db, id=rejecter_id)
        
        if not shared_content_obj or not rejecter:
            return
        
        message = f"{rejecter.full_name or rejecter.email} declined your shared content: {shared_content_obj.content_title}"
        if response_message:
            message += f" - Message: {response_message}"
        
        notification.create_notification(
            db,
            user_id=shared_content_obj.shared_by_id,
            notification_type=NotificationType.CONTENT_SHARE_REJECTED,
            title="Content share declined",
            message=message,
            related_id=shared_content_id,
            related_type="shared_content"
        )
    
    @staticmethod
    def get_sharing_notifications(
        db: Session,
        user_id: int,
        skip: int = 0,
        limit: int = 50
    ) -> List:
        """Get sharing-related notifications for a user"""
        sharing_notification_types = [
            NotificationType.CONTENT_SHARED,
            NotificationType.CONTENT_SHARE_ACCEPTED,
            NotificationType.CONTENT_SHARE_REJECTED
        ]
        
        return notification.get_user_notifications_by_types(
            db,
            user_id=user_id,
            notification_types=sharing_notification_types,
            skip=skip,
            limit=limit
        )
    
    @staticmethod
    def mark_sharing_notifications_read(
        db: Session,
        user_id: int,
        shared_content_id: int
    ) -> int:
        """Mark all notifications related to a specific shared content as read"""
        return notification.mark_notifications_read_by_related_id(
            db,
            user_id=user_id,
            related_id=shared_content_id,
            related_type="shared_content"
        )
    
    @staticmethod
    def send_share_reminder_notifications(
        db: Session,
        days_old: int = 3
    ) -> int:
        """Send reminder notifications for pending share invitations"""
        from datetime import datetime, timedelta
        
        cutoff_date = datetime.utcnow() - timedelta(days=days_old)
        
        # Get pending invitations older than cutoff_date
        pending_invitations = share_invitation.get_pending_invitations_older_than(
            db, cutoff_date=cutoff_date
        )
        
        reminders_sent = 0
        
        for invitation in pending_invitations:
            # Check if user exists
            invited_user = user_service.get_by_email(db, email=invitation.invited_email)
            
            if invited_user:
                # Send in-app notification
                shared_content_obj = shared_content.get(db, id=invitation.shared_content_id)
                sharer = user_service.get(db, id=shared_content_obj.shared_by_id)
                
                if shared_content_obj and sharer:
                    notification.create_notification(
                        db,
                        user_id=invited_user.id,
                        notification_type=NotificationType.REMINDER,
                        title="Reminder: Content shared with you",
                        message=f"Don't forget to check the content {sharer.full_name or sharer.email} shared with you: {shared_content_obj.content_title}",
                        related_id=shared_content_obj.id,
                        related_type="shared_content"
                    )
                    
                    reminders_sent += 1
        
        return reminders_sent
    
    @staticmethod
    def cleanup_expired_invitations(db: Session) -> int:
        """Clean up expired share invitations and send notifications"""
        from datetime import datetime
        
        expired_invitations = share_invitation.get_expired_invitations(db)
        cleaned_count = 0
        
        for invitation in expired_invitations:
            # Update status to expired
            share_invitation.update_invitation_status(
                db,
                invitation_id=invitation.id,
                status=ShareStatus.EXPIRED
            )
            
            # Notify sharer about expiration
            shared_content_obj = shared_content.get(db, id=invitation.shared_content_id)
            if shared_content_obj:
                notification.create_notification(
                    db,
                    user_id=shared_content_obj.shared_by_id,
                    notification_type=NotificationType.SYSTEM,
                    title="Share invitation expired",
                    message=f"Your share invitation for '{shared_content_obj.content_title}' to {invitation.invited_email} has expired",
                    related_id=shared_content_obj.id,
                    related_type="shared_content"
                )
            
            cleaned_count += 1
        
        return cleaned_count


# Create service instance
sharing_notification_service = SharingNotificationService()
