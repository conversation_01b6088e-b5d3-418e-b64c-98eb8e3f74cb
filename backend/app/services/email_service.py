import smtplib
import ssl
import logging
import threading
from email.mime.text import MIMEText
from email.mime.multipart import MIME<PERSON><PERSON><PERSON>art
from typing import Optional
from pathlib import Path

from app.core.config import settings

logger = logging.getLogger(__name__)


def _save_email_to_file(email_to: str, subject: str, html_content: str, text_content: Optional[str] = None) -> bool:
    """
    Save email to file for development testing
    """
    try:
        import os
        from datetime import datetime

        # Create emails directory if it doesn't exist
        emails_dir = "development_emails"
        os.makedirs(emails_dir, exist_ok=True)

        # Create filename with timestamp
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        safe_email = email_to.replace("@", "_at_").replace(".", "_")
        filename = f"{timestamp}_{safe_email}.html"
        filepath = os.path.join(emails_dir, filename)

        # Create complete HTML email
        complete_html = f"""
<!DOCTYPE html>
<html>
<head>
    <meta charset="utf-8">
    <title>{subject}</title>
    <style>
        .email-header {{
            background-color: #f0f0f0;
            padding: 20px;
            border-bottom: 2px solid #1976d2;
            margin-bottom: 20px;
        }}
        .email-info {{
            font-family: monospace;
            background-color: #f8f9fa;
            padding: 10px;
            border-left: 4px solid #1976d2;
            margin-bottom: 20px;
        }}
    </style>
</head>
<body>
    <div class="email-header">
        <h1>📧 Development Email Preview</h1>
        <p><strong>This email would be sent in production</strong></p>
    </div>

    <div class="email-info">
        <p><strong>To:</strong> {email_to}</p>
        <p><strong>Subject:</strong> {subject}</p>
        <p><strong>Timestamp:</strong> {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}</p>
    </div>

    <div class="email-content">
        {html_content}
    </div>

    {f'<div class="text-content"><h3>Text Version:</h3><pre>{text_content}</pre></div>' if text_content else ''}
</body>
</html>
        """

        # Save to file
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(complete_html)

        logger.info(f"📧 Development email saved to: {filepath}")
        logger.info(f"📧 Email details - To: {email_to}, Subject: {subject}")

        # Also log the verification link for easy access
        if "verify-email" in html_content:
            import re
            link_match = re.search(r'href="([^"]*verify-email[^"]*)"', html_content)
            if link_match:
                verification_link = link_match.group(1)
                logger.info(f"🔗 VERIFICATION LINK: {verification_link}")
                separator = "=" * 80
                print(f"\n{separator}")
                print(f"📧 EMAIL VERIFICATION LINK FOR {email_to}:")
                print(f"🔗 {verification_link}")
                print(f"📁 Full email saved to: {filepath}")
                print(f"{separator}\n")

        return True

    except Exception as e:
        logger.error(f"Failed to save development email: {str(e)}")
        return False


def _send_email_sync(
    email_to: str,
    subject: str,
    html_content: str,
    text_content: Optional[str] = None,
) -> bool:
    """
    Internal synchronous email sending function
    """
    try:
        # Check if SMTP is configured
        if not settings.SMTP_USER or not settings.SMTP_PASSWORD:
            logger.error("SMTP credentials not configured. Please set SMTP_USER and SMTP_PASSWORD in .env file")
            logger.info("Falling back to development email file saving...")
            return _save_email_to_file(email_to, subject, html_content, text_content)

        logger.info(f"Attempting to send email to {email_to} via SMTP...")
        logger.debug(f"SMTP Config - Host: {settings.SMTP_HOST}, Port: {settings.SMTP_PORT}, User: {settings.SMTP_USER}")

        # Create message
        msg = MIMEMultipart("alternative")
        msg["Subject"] = subject
        msg["From"] = f"{settings.EMAILS_FROM_NAME} <{settings.EMAILS_FROM_EMAIL or settings.SMTP_USER}>"
        msg["To"] = email_to

        # Add text content
        if text_content:
            text_part = MIMEText(text_content, "plain")
            msg.attach(text_part)

        # Add HTML content
        html_part = MIMEText(html_content, "html")
        msg.attach(html_part)

        # Create SSL context for secure connection
        context = ssl.create_default_context()

        # Try SMTP with STARTTLS first (port 587)
        try:
            logger.debug("Attempting SMTP with STARTTLS...")
            with smtplib.SMTP(settings.SMTP_HOST, settings.SMTP_PORT) as server:
                # Disable debug output to reduce noise (was causing performance issues)
                # Only enable in development if explicitly needed
                # if settings.DEBUG:
                #     server.set_debuglevel(1)

                logger.debug("Starting TLS with SSL context...")
                server.starttls(context=context)

                logger.debug("Logging in to SMTP server...")
                server.login(settings.SMTP_USER, settings.SMTP_PASSWORD)

                logger.debug("Sending message...")
                server.send_message(msg)

        except Exception as starttls_error:
            logger.warning(f"STARTTLS failed: {starttls_error}, trying SMTP_SSL...")

            # Fallback to SMTP_SSL (port 465)
            with smtplib.SMTP_SSL(settings.SMTP_HOST, 465, context=context) as server:
                # Disable debug output to reduce noise
                # if settings.DEBUG:
                #     server.set_debuglevel(1)

                logger.debug("Logging in to SMTP_SSL server...")
                server.login(settings.SMTP_USER, settings.SMTP_PASSWORD)

                logger.debug("Sending message via SMTP_SSL...")
                server.send_message(msg)

        logger.info(f"✅ Email sent successfully to {email_to}")
        return True

    except smtplib.SMTPAuthenticationError as e:
        logger.error(f"❌ SMTP Authentication failed for {email_to}: {str(e)}")
        logger.error("Check your Gmail app password. Regular Gmail password won't work.")
        logger.info("Falling back to development email file saving...")
        return _save_email_to_file(email_to, subject, html_content, text_content)

    except smtplib.SMTPRecipientsRefused as e:
        logger.error(f"❌ SMTP Recipients refused for {email_to}: {str(e)}")
        logger.info("Falling back to development email file saving...")
        return _save_email_to_file(email_to, subject, html_content, text_content)

    except smtplib.SMTPServerDisconnected as e:
        logger.error(f"❌ SMTP Server disconnected for {email_to}: {str(e)}")
        logger.info("Falling back to development email file saving...")
        return _save_email_to_file(email_to, subject, html_content, text_content)

    except Exception as e:
        logger.error(f"❌ Failed to send email to {email_to}: {str(e)}")
        logger.error(f"Error type: {type(e).__name__}")
        logger.info("Falling back to development email file saving...")
        return _save_email_to_file(email_to, subject, html_content, text_content)


def _send_email_background(
    email_to: str,
    subject: str,
    html_content: str,
    text_content: Optional[str] = None,
) -> None:
    """
    Send email in background thread
    """
    try:
        logger.info(f"📧 Starting background email send to {email_to}")
        success = _send_email_sync(email_to, subject, html_content, text_content)
        if success:
            logger.info(f"📧 Background email sent successfully to {email_to}")
        else:
            logger.error(f"📧 Background email failed for {email_to}")
    except Exception as e:
        logger.error(f"📧 Background email thread error for {email_to}: {str(e)}")


def send_email(
    email_to: str,
    subject: str,
    html_content: str,
    text_content: Optional[str] = None,
    background: bool = True,
) -> bool:
    """
    Send an email using SMTP with enhanced error handling and fallback.

    Args:
        email_to: Recipient email address
        subject: Email subject
        html_content: HTML content of the email
        text_content: Plain text content (optional)
        background: If True, send email in background thread (default: True)

    Returns:
        bool: True if email was queued/sent successfully, False otherwise
    """
    try:
        # For development mode or when SMTP is not configured, save to file immediately
        if not settings.SMTP_USER or not settings.SMTP_PASSWORD:
            logger.info("SMTP not configured, saving email to file...")
            return _save_email_to_file(email_to, subject, html_content, text_content)

        if background:
            # Send email in background thread for better performance
            logger.info(f"📧 Queuing email to {email_to} for background sending...")

            # Create and start background thread
            email_thread = threading.Thread(
                target=_send_email_background,
                args=(email_to, subject, html_content, text_content),
                daemon=True,  # Thread will not prevent program exit
                name=f"EmailSender-{email_to}"
            )
            email_thread.start()

            logger.info(f"📧 Email queued successfully for {email_to}")
            return True
        else:
            # Send email synchronously (for testing or when immediate result is needed)
            logger.info(f"📧 Sending email synchronously to {email_to}...")
            return _send_email_sync(email_to, subject, html_content, text_content)

    except Exception as e:
        logger.error(f"❌ Failed to queue/send email to {email_to}: {str(e)}")
        logger.error(f"Error type: {type(e).__name__}")
        # Fallback to file saving
        return _save_email_to_file(email_to, subject, html_content, text_content)


def send_email_sync(
    email_to: str,
    subject: str,
    html_content: str,
    text_content: Optional[str] = None,
) -> bool:
    """
    Send email synchronously (for testing or when immediate result is needed)
    """
    return send_email(email_to, subject, html_content, text_content, background=False)


def send_verification_email(email_to: str, verification_token: str, user_name: str) -> bool:
    """
    Send email verification email
    """
    verification_url = f"{settings.FRONTEND_URL}/verify-email?token={verification_token}"
    
    subject = "Verify your CampusPQ account"
    
    html_content = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Verify Your Email - CampusPQ</title>
        <style>
            body {{
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                line-height: 1.6;
                color: #333;
                max-width: 600px;
                margin: 0 auto;
                padding: 20px;
                background-color: #f5f5f5;
            }}
            .container {{
                background-color: white;
                padding: 40px;
                border-radius: 10px;
                box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            }}
            .header {{
                text-align: center;
                margin-bottom: 30px;
            }}
            .logo {{
                font-size: 28px;
                font-weight: bold;
                color: #1976d2;
                margin-bottom: 10px;
            }}
            .title {{
                font-size: 24px;
                color: #333;
                margin-bottom: 20px;
            }}
            .content {{
                font-size: 16px;
                margin-bottom: 30px;
            }}
            .button {{
                display: inline-block;
                background-color: #1976d2;
                color: white;
                padding: 15px 30px;
                text-decoration: none;
                border-radius: 5px;
                font-weight: bold;
                margin: 20px 0;
            }}
            .button:hover {{
                background-color: #1565c0;
            }}
            .footer {{
                margin-top: 30px;
                padding-top: 20px;
                border-top: 1px solid #eee;
                font-size: 14px;
                color: #666;
                text-align: center;
            }}
            .warning {{
                background-color: #fff3cd;
                border: 1px solid #ffeaa7;
                color: #856404;
                padding: 15px;
                border-radius: 5px;
                margin: 20px 0;
            }}
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <div class="logo">🎓 CampusPQ</div>
                <h1 class="title">Verify Your Email Address</h1>
            </div>
            
            <div class="content">
                <p>Hello {user_name},</p>
                
                <p>Thank you for registering with CampusPQ! To complete your account setup and start your learning journey, please verify your email address by clicking the button below:</p>
                
                <div style="text-align: center;">
                    <a href="{verification_url}" class="button">Verify Email Address</a>
                </div>
                
                <p>If the button doesn't work, you can copy and paste this link into your browser:</p>
                <p style="word-break: break-all; background-color: #f8f9fa; padding: 10px; border-radius: 5px;">
                    {verification_url}
                </p>
                
                <div class="warning">
                    <strong>Important:</strong> This verification link will expire in 24 hours. If you don't verify your email within this time, you'll need to request a new verification email.
                </div>
                
                <p>Once verified, you'll be able to:</p>
                <ul>
                    <li>Access your personalized dashboard</li>
                    <li>Take practice exams and quizzes</li>
                    <li>Track your learning progress</li>
                    <li>Connect with tutors and peers</li>
                </ul>

                <div style="background-color: #e8f5e8; border-left: 4px solid #4caf50; padding: 15px; margin: 20px 0; border-radius: 5px;">
                    <h3 style="color: #2e7d32; margin-top: 0;">🎉 Join Our Community!</h3>
                    <p style="margin-bottom: 10px;">Connect with fellow students, share study tips, and get instant help from our community:</p>
                    <div style="text-align: center;">
                        <a href="https://chat.whatsapp.com/JzGYp4sfwWdJlB7pFzlTnd"
                           style="display: inline-block; background-color: #25d366; color: white; padding: 12px 24px; text-decoration: none; border-radius: 25px; font-weight: bold; margin: 10px 0;"
                           target="_blank" rel="noopener noreferrer">
                            💬 Join WhatsApp Community
                        </a>
                    </div>
                    <p style="font-size: 14px; color: #666; margin-bottom: 0;">Get study tips, ask questions, and connect with other students!</p>
                </div>

                <p>If you didn't create an account with CampusPQ, please ignore this email.</p>
            </div>
            
            <div class="footer">
                <p>Best regards,<br>The CampusPQ Team</p>
                <p>Need help? Contact <NAME_EMAIL></p>
            </div>
        </div>
    </body>
    </html>
    """
    
    text_content = f"""
    Hello {user_name},

    Thank you for registering with CampusPQ! To complete your account setup, please verify your email address by visiting this link:

    {verification_url}

    This verification link will expire in 24 hours.

    🎉 JOIN OUR COMMUNITY!
    Connect with fellow students, share study tips, and get instant help from our WhatsApp community:
    https://chat.whatsapp.com/JzGYp4sfwWdJlB7pFzlTnd

    Get study tips, ask questions, and connect with other students!

    If you didn't create an account with CampusPQ, please ignore this email.

    Best regards,
    The CampusPQ Team
    """
    
    return send_email(email_to, subject, html_content, text_content)


def send_welcome_email(email_to: str, user_name: str) -> bool:
    """
    Send welcome email after successful email verification
    """
    subject = "Welcome to CampusPQ - Let's start your learning journey!"

    html_content = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Welcome to CampusPQ</title>
        <style>
            body {{
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                line-height: 1.6;
                color: #333;
                max-width: 600px;
                margin: 0 auto;
                padding: 20px;
                background-color: #f8f9fa;
            }}
            .container {{
                background-color: white;
                border-radius: 10px;
                padding: 30px;
                box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            }}
            .header {{
                text-align: center;
                margin-bottom: 30px;
                padding-bottom: 20px;
                border-bottom: 2px solid #e9ecef;
            }}
            .logo {{
                font-size: 28px;
                font-weight: bold;
                color: #007bff;
                margin-bottom: 10px;
            }}
            .content {{
                margin-bottom: 30px;
            }}
            .welcome-message {{
                background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                color: white;
                padding: 20px;
                border-radius: 10px;
                text-align: center;
                margin: 20px 0;
            }}
            .feature-grid {{
                display: grid;
                grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
                gap: 20px;
                margin: 20px 0;
            }}
            .feature-card {{
                background-color: #f8f9fa;
                padding: 20px;
                border-radius: 8px;
                border-left: 4px solid #007bff;
            }}
            .feature-card h3 {{
                color: #007bff;
                margin-top: 0;
                margin-bottom: 10px;
            }}
            .community-section {{
                background-color: #e8f5e8;
                border-left: 4px solid #4caf50;
                padding: 20px;
                margin: 25px 0;
                border-radius: 8px;
                text-align: center;
            }}
            .community-button {{
                display: inline-block;
                background-color: #25d366;
                color: white;
                padding: 15px 30px;
                text-decoration: none;
                border-radius: 25px;
                font-weight: bold;
                margin: 15px 0;
                transition: background-color 0.3s;
            }}
            .community-button:hover {{
                background-color: #128c7e;
            }}
            .cta-section {{
                background-color: #007bff;
                color: white;
                padding: 25px;
                border-radius: 10px;
                text-align: center;
                margin: 25px 0;
            }}
            .cta-button {{
                display: inline-block;
                background-color: white;
                color: #007bff;
                padding: 12px 25px;
                text-decoration: none;
                border-radius: 25px;
                font-weight: bold;
                margin: 10px;
            }}
            .footer {{
                text-align: center;
                margin-top: 30px;
                padding-top: 20px;
                border-top: 1px solid #e9ecef;
                color: #6c757d;
                font-size: 14px;
            }}
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <div class="logo">🎓 CampusPQ</div>
                <p>Your AI-Powered Learning Platform</p>
            </div>

            <div class="welcome-message">
                <h2 style="margin: 0;">🎉 Welcome to CampusPQ, {user_name}!</h2>
                <p style="margin: 10px 0 0 0;">Your learning journey starts now!</p>
            </div>

            <div class="content">
                <p>Congratulations! Your email has been verified and your CampusPQ account is now active. You're all set to explore our AI-powered learning platform.</p>

                <h3>🚀 What you can do now:</h3>
                <div class="feature-grid">
                    <div class="feature-card">
                        <h3>📚 Practice MCQs</h3>
                        <p>Access thousands of practice questions across various subjects and difficulty levels.</p>
                    </div>
                    <div class="feature-card">
                        <h3>🧠 AI Study Tools</h3>
                        <p>Upload PDFs and generate custom MCQs, flashcards, and summaries using AI.</p>
                    </div>
                    <div class="feature-card">
                        <h3>👨‍🏫 Find Tutors</h3>
                        <p>Connect with qualified tutors for personalized learning sessions.</p>
                    </div>
                    <div class="feature-card">
                        <h3>📊 Track Progress</h3>
                        <p>Monitor your learning progress with detailed analytics and achievements.</p>
                    </div>
                </div>
            </div>

            <div class="community-section">
                <h3 style="color: #2e7d32; margin-top: 0;">💬 Join Our Student Community!</h3>
                <p>Connect with thousands of students, share study tips, get instant help, and stay motivated together!</p>
                <a href="https://chat.whatsapp.com/JzGYp4sfwWdJlB7pFzlTnd"
                   class="community-button"
                   target="_blank" rel="noopener noreferrer">
                    Join WhatsApp Community
                </a>
                <p style="font-size: 14px; color: #666; margin-bottom: 0;">
                    ✨ Get study tips • 🤝 Ask questions • 📚 Share resources • 🎯 Stay motivated
                </p>
            </div>

            <div class="cta-section">
                <h3 style="margin-top: 0;">Ready to start learning?</h3>
                <p>Jump into your dashboard and begin your personalized learning experience!</p>
                <a href="{settings.FRONTEND_URL}/dashboard" class="cta-button">Go to Dashboard</a>
                <a href="{settings.FRONTEND_URL}/tools" class="cta-button">Explore AI Tools</a>
            </div>

            <div class="footer">
                <p>Best regards,<br><strong>The CampusPQ Team</strong></p>
                <p>Need help? Contact us at <a href="mailto:<EMAIL>"><EMAIL></a></p>
                <p>Follow us for updates and study tips!</p>
            </div>
        </div>
    </body>
    </html>
    """

    text_content = f"""
    Welcome to CampusPQ, {user_name}!

    🎉 Congratulations! Your email has been verified and your CampusPQ account is now active.

    What you can do now:
    📚 Practice MCQs - Access thousands of practice questions
    🧠 AI Study Tools - Upload PDFs and generate custom content
    👨‍🏫 Find Tutors - Connect with qualified tutors
    📊 Track Progress - Monitor your learning with analytics

    💬 JOIN OUR STUDENT COMMUNITY!
    Connect with thousands of students, share study tips, and get instant help:
    https://chat.whatsapp.com/JzGYp4sfwWdJlB7pFzlTnd

    Ready to start learning?
    Dashboard: {settings.FRONTEND_URL}/dashboard
    AI Tools: {settings.FRONTEND_URL}/tools

    Best regards,
    The CampusPQ Team

    Need help? Contact <NAME_EMAIL>
    """

    return send_email(email_to, subject, html_content, text_content)


def send_password_reset_email(email_to: str, reset_token: str, user_name: str) -> bool:
    """
    Send password reset email
    """
    reset_url = f"{settings.FRONTEND_URL}/reset-password?token={reset_token}"

    subject = "Reset your CampusPQ password"

    html_content = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Reset Your Password - CampusPQ</title>
        <style>
            body {{
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                line-height: 1.6;
                color: #333;
                max-width: 600px;
                margin: 0 auto;
                padding: 20px;
                background-color: #f4f4f4;
            }}
            .container {{
                background-color: white;
                padding: 40px;
                border-radius: 10px;
                box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
            }}
            .header {{
                text-align: center;
                margin-bottom: 30px;
                padding-bottom: 20px;
                border-bottom: 2px solid #e0e0e0;
            }}
            .logo {{
                font-size: 28px;
                font-weight: bold;
                color: #2563eb;
                margin-bottom: 10px;
            }}
            .content {{
                margin-bottom: 30px;
            }}
            .reset-button {{
                display: inline-block;
                background-color: #dc2626;
                color: white;
                padding: 15px 30px;
                text-decoration: none;
                border-radius: 5px;
                font-weight: bold;
                margin: 20px 0;
                text-align: center;
            }}
            .reset-button:hover {{
                background-color: #b91c1c;
            }}
            .warning {{
                background-color: #fef3c7;
                border: 1px solid #f59e0b;
                border-radius: 5px;
                padding: 15px;
                margin: 20px 0;
            }}
            .footer {{
                text-align: center;
                margin-top: 30px;
                padding-top: 20px;
                border-top: 1px solid #e0e0e0;
                color: #666;
                font-size: 14px;
            }}
            .security-note {{
                background-color: #f3f4f6;
                border-left: 4px solid #6b7280;
                padding: 15px;
                margin: 20px 0;
                font-size: 14px;
            }}
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <div class="logo">CampusPQ</div>
                <h1>Password Reset Request</h1>
            </div>

            <div class="content">
                <p>Hello {user_name},</p>

                <p>We received a request to reset your password for your CampusPQ account. If you made this request, click the button below to reset your password:</p>

                <div style="text-align: center;">
                    <a href="{reset_url}" class="reset-button">Reset My Password</a>
                </div>

                <div class="warning">
                    <strong>⚠️ Important:</strong> This password reset link will expire in 1 hour for security reasons.
                </div>

                <div class="security-note">
                    <strong>Security Notice:</strong>
                    <ul>
                        <li>If you didn't request this password reset, please ignore this email</li>
                        <li>Your password will remain unchanged if you don't click the reset link</li>
                        <li>Never share this reset link with anyone</li>
                        <li>If you're concerned about your account security, contact our support team</li>
                    </ul>
                </div>

                <p>If the button above doesn't work, you can copy and paste this link into your browser:</p>
                <p style="word-break: break-all; background-color: #f3f4f6; padding: 10px; border-radius: 5px; font-family: monospace;">
                    {reset_url}
                </p>
            </div>

            <div class="footer">
                <p>Best regards,<br>The CampusPQ Team</p>
                <p>Need help? Contact <NAME_EMAIL></p>
            </div>
        </div>
    </body>
    </html>
    """

    text_content = f"""
    Hello {user_name},

    We received a request to reset your password for your CampusPQ account.

    To reset your password, please visit this link:
    {reset_url}

    This password reset link will expire in 1 hour for security reasons.

    If you didn't request this password reset, please ignore this email. Your password will remain unchanged.

    For security reasons, never share this reset link with anyone.

    Best regards,
    The CampusPQ Team
    """

    return send_email(email_to, subject, html_content, text_content)


def send_booking_request_notification(
    tutor_email: str,
    tutor_name: str,
    student_name: str,
    student_email: str,
    message: str,
    request_id: int
) -> bool:
    """
    Send booking request notification email to tutor
    """
    subject = f"New Tutoring Session Request from {student_name}"

    html_content = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>New Booking Request - CampusPQ</title>
        <style>
            body {{
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                line-height: 1.6;
                color: #333;
                max-width: 600px;
                margin: 0 auto;
                padding: 20px;
                background-color: #f5f5f5;
            }}
            .container {{
                background-color: white;
                padding: 30px;
                border-radius: 10px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            }}
            .header {{
                text-align: center;
                margin-bottom: 30px;
                padding-bottom: 20px;
                border-bottom: 2px solid #4CAF50;
            }}
            .logo {{
                font-size: 28px;
                font-weight: bold;
                color: #4CAF50;
                margin-bottom: 10px;
            }}
            .content {{
                margin-bottom: 30px;
            }}
            .highlight {{
                background-color: #f8f9fa;
                padding: 20px;
                border-radius: 8px;
                border-left: 4px solid #4CAF50;
                margin: 20px 0;
            }}
            .button {{
                display: inline-block;
                background-color: #4CAF50;
                color: white;
                padding: 12px 30px;
                text-decoration: none;
                border-radius: 5px;
                font-weight: bold;
                margin: 10px 5px;
            }}
            .button.secondary {{
                background-color: #6c757d;
            }}
            .footer {{
                text-align: center;
                margin-top: 30px;
                padding-top: 20px;
                border-top: 1px solid #eee;
                color: #666;
                font-size: 14px;
            }}
            .student-info {{
                background-color: #e3f2fd;
                padding: 15px;
                border-radius: 8px;
                margin: 15px 0;
            }}
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <div class="logo">CampusPQ</div>
                <h1>New Tutoring Session Request</h1>
            </div>

            <div class="content">
                <p>Hello {tutor_name},</p>

                <p>You have received a new tutoring session request! A student is interested in your tutoring services.</p>

                <div class="student-info">
                    <h3>📚 Request Details</h3>
                    <p><strong>Student:</strong> {student_name}</p>
                </div>

                <div class="highlight">
                    <h4>💬 Student's Message:</h4>
                    <p>"{message}"</p>
                </div>

                <p>You can respond to this request by logging into your CampusPQ tutor dashboard:</p>

                <div style="text-align: center; margin: 30px 0;">
                    <a href="{settings.FRONTEND_URL}/tutor/booking-requests" class="button">
                        View & Respond to Request
                    </a>
                </div>

                <p><strong>What happens next?</strong></p>
                <ul>
                    <li>✅ <strong>Accept:</strong> A chat room will be created for you and the student to coordinate the session</li>
                    <li>❌ <strong>Decline:</strong> The student will be notified and can look for other tutors</li>
                </ul>

                <p>Remember to respond promptly to maintain a good reputation on the platform!</p>
            </div>

            <div class="footer">
                <p>Best regards,<br>The CampusPQ Team</p>
                <p>Need help? Contact <NAME_EMAIL></p>
                <p>This is an automated notification. Please do not reply to this email.</p>
            </div>
        </div>
    </body>
    </html>
    """

    text_content = f"""
    Hello {tutor_name},

    You have received a new tutoring session request on CampusPQ!

    Request Details:
    - Student: {student_name}

    Student's Message:
    "{message}"

    To respond to this request, please visit your tutor dashboard:
    {settings.FRONTEND_URL}/tutor/booking-requests

    What happens next?
    - Accept: A chat room will be created for you and the student
    - Decline: The student will be notified

    Remember to respond promptly to maintain a good reputation!

    Best regards,
    The CampusPQ Team
    """

    return send_email(tutor_email, subject, html_content, text_content)


def send_booking_response_notification(
    student_email: str,
    student_name: str,
    tutor_name: str,
    tutor_email: str,
    response_status: str,
    tutor_message: str,
    request_id: int
) -> bool:
    """
    Send booking response notification email to student
    """
    is_accepted = response_status.lower() == "accepted"
    status_text = "Accepted" if is_accepted else "Declined"
    status_emoji = "✅" if is_accepted else "❌"

    subject = f"Tutoring Request {status_text} - {tutor_name}"

    html_content = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Tutoring Request {status_text} - CampusPQ</title>
        <style>
            body {{
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                line-height: 1.6;
                color: #333;
                max-width: 600px;
                margin: 0 auto;
                padding: 20px;
                background-color: #f5f5f5;
            }}
            .container {{
                background-color: white;
                padding: 30px;
                border-radius: 10px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            }}
            .header {{
                text-align: center;
                margin-bottom: 30px;
                padding-bottom: 20px;
                border-bottom: 2px solid {'#4CAF50' if is_accepted else '#f44336'};
            }}
            .logo {{
                font-size: 28px;
                font-weight: bold;
                color: #4CAF50;
                margin-bottom: 10px;
            }}
            .status {{
                font-size: 24px;
                font-weight: bold;
                color: {'#4CAF50' if is_accepted else '#f44336'};
                margin: 20px 0;
            }}
            .content {{
                margin-bottom: 30px;
            }}
            .highlight {{
                background-color: {'#e8f5e8' if is_accepted else '#ffeaea'};
                padding: 20px;
                border-radius: 8px;
                border-left: 4px solid {'#4CAF50' if is_accepted else '#f44336'};
                margin: 20px 0;
            }}
            .button {{
                display: inline-block;
                background-color: #4CAF50;
                color: white;
                padding: 12px 30px;
                text-decoration: none;
                border-radius: 5px;
                font-weight: bold;
                margin: 10px 5px;
            }}
            .footer {{
                text-align: center;
                margin-top: 30px;
                padding-top: 20px;
                border-top: 1px solid #eee;
                color: #666;
                font-size: 14px;
            }}
            .tutor-info {{
                background-color: #f8f9fa;
                padding: 15px;
                border-radius: 8px;
                margin: 15px 0;
            }}
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <div class="logo">CampusPQ</div>
                <h1>{status_emoji} Tutoring Request {status_text}</h1>
            </div>

            <div class="content">
                <p>Hello {student_name},</p>

                <div class="status">
                    Your tutoring request has been {status_text.lower()}!
                </div>

                <div class="tutor-info">
                    <h3>📚 Request Details</h3>
                    <p><strong>Tutor:</strong> {tutor_name}</p>
                    <p><strong>Status:</strong> {status_emoji} {status_text}</p>
                </div>

                {f'''
                <div class="highlight">
                    <h4>💬 Tutor's Response:</h4>
                    <p>"{tutor_message}"</p>
                </div>
                ''' if tutor_message else ''}

                {f'''
                <p><strong>🎉 Great news!</strong> Your tutor has accepted your request. You can now start chatting with them to coordinate your tutoring session.</p>

                <div style="text-align: center; margin: 30px 0;">
                    <a href="{settings.FRONTEND_URL}/student/chat" class="button">
                        Start Chatting with Your Tutor
                    </a>
                </div>

                <p><strong>Next Steps:</strong></p>
                <ul>
                    <li>💬 Use the chat to discuss session details (time, location, topics)</li>
                    <li>📅 Schedule your tutoring session</li>
                    <li>📚 Prepare any materials or questions you want to cover</li>
                </ul>
                ''' if is_accepted else f'''
                <p>Unfortunately, your tutor was not able to accept your request at this time. Don't worry - there are many other qualified tutors available!</p>

                <div style="text-align: center; margin: 30px 0;">
                    <a href="{settings.FRONTEND_URL}/student/find-tutors" class="button">
                        Find Other Tutors
                    </a>
                </div>

                <p><strong>What you can do:</strong></p>
                <ul>
                    <li>🔍 Browse other tutors in your subject area</li>
                    <li>📝 Send requests to multiple tutors to increase your chances</li>
                    <li>⏰ Try requesting at different times</li>
                </ul>
                '''}
            </div>

            <div class="footer">
                <p>Best regards,<br>The CampusPQ Team</p>
                <p>Need help? Contact <NAME_EMAIL></p>
                <p>This is an automated notification. Please do not reply to this email.</p>
            </div>
        </div>
    </body>
    </html>
    """

    text_content = f"""
    Hello {student_name},

    Your tutoring request has been {status_text.lower()}!

    Request Details:
    - Tutor: {tutor_name}
    - Status: {status_text}

    {f"Tutor's Response: {tutor_message!r}" if tutor_message else ''}

    {f'''Great news! Your tutor has accepted your request. You can now start chatting with them to coordinate your tutoring session.

    Visit your dashboard to start chatting: {settings.FRONTEND_URL}/student/chat

    Next Steps:
    - Use the chat to discuss session details
    - Schedule your tutoring session
    - Prepare materials or questions''' if is_accepted else f'''Unfortunately, your tutor was not able to accept your request at this time. Don't worry - there are many other qualified tutors available!

    Find other tutors: {settings.FRONTEND_URL}/student/find-tutors

    What you can do:
    - Browse other tutors in your subject area
    - Send requests to multiple tutors
    - Try requesting at different times'''}

    Best regards,
    The CampusPQ Team
    """

    return send_email(student_email, subject, html_content, text_content)


def send_content_share_invitation(
    invited_email: str,
    sharer_name: str,
    sharer_email: str,
    content_title: str,
    content_type: str,
    course_name: str,
    share_message: str,
    invitation_token: str,
    is_existing_user: bool = True
) -> bool:
    """
    Send content share invitation email
    """
    content_type_display = {
        "flashcard": "Flashcards",
        "mcq": "MCQ Questions",
        "summary": "Summary"
    }.get(content_type, content_type.title())

    if is_existing_user:
        subject = f"{sharer_name} shared {content_type_display} with you - CampusPQ"
        action_text = "View Shared Content"
        action_url = f"{settings.FRONTEND_URL}/share/accept?token={invitation_token}"
    else:
        subject = f"Join CampusPQ to access shared {content_type_display} from {sharer_name}"
        action_text = "Join CampusPQ & View Content"
        action_url = f"{settings.FRONTEND_URL}/register?invitation_token={invitation_token}"

    # Prepare conditional HTML content
    course_html = f'<div style="margin-top: 10px;"><strong>Course:</strong> {course_name}</div>' if course_name else ''
    message_html = f'<div class="message"><strong>Message from {sharer_name}:</strong><br>"{share_message}"</div>' if share_message else ''
    account_html = '<p>Since you already have a CampusPQ account, you can access this content right away!</p>' if is_existing_user else '<p>To access this content, you\'ll need to create a free CampusPQ account. It only takes a minute!</p>'
    benefits_html = '' if is_existing_user else '''
            <div class="benefits">
                <h3>What you'll get with CampusPQ:</h3>
                <div class="benefit-item">Access to shared study materials</div>
                <div class="benefit-item">Create your own flashcards and summaries</div>
                <div class="benefit-item">Practice with AI-generated questions</div>
                <div class="benefit-item">Connect with tutors and study groups</div>
                <div class="benefit-item">Track your learning progress</div>
            </div>
            '''

    html_content = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Content Shared - CampusPQ</title>
        <style>
            body {{
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                line-height: 1.6;
                color: #333;
                max-width: 600px;
                margin: 0 auto;
                padding: 20px;
                background-color: #f5f5f5;
            }}
            .container {{
                background-color: white;
                padding: 30px;
                border-radius: 10px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            }}
            .header {{
                text-align: center;
                margin-bottom: 30px;
                padding-bottom: 20px;
                border-bottom: 2px solid #4CAF50;
            }}
            .logo {{
                font-size: 28px;
                font-weight: bold;
                color: #4CAF50;
                margin-bottom: 10px;
            }}
            .share-icon {{
                font-size: 48px;
                margin: 20px 0;
            }}
            .content-info {{
                background-color: #f8f9fa;
                padding: 20px;
                border-radius: 8px;
                margin: 20px 0;
                border-left: 4px solid #4CAF50;
            }}
            .content-title {{
                font-size: 18px;
                font-weight: bold;
                color: #2c3e50;
                margin-bottom: 5px;
            }}
            .content-type {{
                color: #7f8c8d;
                font-size: 14px;
                text-transform: uppercase;
                letter-spacing: 1px;
            }}
            .message {{
                background-color: #e8f5e8;
                padding: 15px;
                border-radius: 8px;
                margin: 20px 0;
                font-style: italic;
            }}
            .cta-button {{
                display: inline-block;
                background-color: #4CAF50;
                color: white;
                padding: 15px 30px;
                text-decoration: none;
                border-radius: 5px;
                font-weight: bold;
                margin: 20px 0;
                text-align: center;
            }}
            .cta-button:hover {{
                background-color: #45a049;
            }}
            .footer {{
                margin-top: 30px;
                padding-top: 20px;
                border-top: 1px solid #eee;
                text-align: center;
                color: #666;
                font-size: 14px;
            }}
            .benefits {{
                margin: 20px 0;
            }}
            .benefit-item {{
                margin: 10px 0;
                padding-left: 20px;
                position: relative;
            }}
            .benefit-item:before {{
                content: "✓";
                position: absolute;
                left: 0;
                color: #4CAF50;
                font-weight: bold;
            }}
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <div class="logo">CampusPQ</div>
                <div class="share-icon">📚</div>
                <h1>Content Shared With You!</h1>
            </div>

            <p>Hi there!</p>

            <p><strong>{sharer_name}</strong> ({sharer_email}) has shared some study content with you on CampusPQ.</p>

            <div class="content-info">
                <div class="content-type">{content_type_display}</div>
                <div class="content-title">{content_title}</div>
                {course_html}
            </div>

            {message_html}

            {account_html}

            {benefits_html}

            <div style="text-align: center;">
                <a href="{action_url}" class="cta-button">{action_text}</a>
            </div>

            <div class="footer">
                <p>This invitation will expire in 7 days.</p>
                <p>If you have any questions, feel free to contact <NAME_EMAIL></p>
                <p>© 2024 CampusPQ. All rights reserved.</p>
            </div>
        </div>
    </body>
    </html>
    """

    # Prepare conditional content
    course_line = f'Course: {course_name}' if course_name else ''
    message_line = f'Message from {sharer_name}: "{share_message}"' if share_message else ''
    account_info = 'Since you already have a CampusPQ account, you can access this content right away!' if is_existing_user else 'To access this content, you need to create a free CampusPQ account.'

    text_content = f"""
    Content Shared With You - CampusPQ

    Hi there!

    {sharer_name} ({sharer_email}) has shared some study content with you on CampusPQ.

    Content: {content_title}
    Type: {content_type_display}
    {course_line}

    {message_line}

    {account_info}

    Access the content here: {action_url}

    This invitation will expire in 7 days.

    Best regards,
    The CampusPQ Team
    """

    return send_email(invited_email, subject, html_content, text_content)


def send_share_acceptance_notification(
    sharer_email: str,
    sharer_name: str,
    accepter_name: str,
    accepter_email: str,
    content_title: str,
    content_type: str,
    response_message: str = ""
) -> bool:
    """
    Send notification to content sharer when someone accepts their share
    """
    content_type_display = {
        "flashcard": "Flashcards",
        "mcq": "MCQ Questions",
        "summary": "Summary"
    }.get(content_type, content_type.title())

    subject = f"{accepter_name} accepted your shared {content_type_display} - CampusPQ"

    html_content = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="utf-8">
        <meta name="viewport" content="width=device-width, initial-scale=1.0">
        <title>Share Accepted - CampusPQ</title>
        <style>
            body {{
                font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
                line-height: 1.6;
                color: #333;
                max-width: 600px;
                margin: 0 auto;
                padding: 20px;
                background-color: #f5f5f5;
            }}
            .container {{
                background-color: white;
                padding: 30px;
                border-radius: 10px;
                box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            }}
            .header {{
                text-align: center;
                margin-bottom: 30px;
                padding-bottom: 20px;
                border-bottom: 2px solid #4CAF50;
            }}
            .logo {{
                font-size: 28px;
                font-weight: bold;
                color: #4CAF50;
                margin-bottom: 10px;
            }}
            .success-icon {{
                font-size: 48px;
                margin: 20px 0;
                color: #4CAF50;
            }}
            .content-info {{
                background-color: #f8f9fa;
                padding: 20px;
                border-radius: 8px;
                margin: 20px 0;
                border-left: 4px solid #4CAF50;
            }}
            .message {{
                background-color: #e8f5e8;
                padding: 15px;
                border-radius: 8px;
                margin: 20px 0;
                font-style: italic;
            }}
            .footer {{
                margin-top: 30px;
                padding-top: 20px;
                border-top: 1px solid #eee;
                text-align: center;
                color: #666;
                font-size: 14px;
            }}
        </style>
    </head>
    <body>
        <div class="container">
            <div class="header">
                <div class="logo">CampusPQ</div>
                <div class="success-icon">✅</div>
                <h1>Your Content Was Accepted!</h1>
            </div>

            <p>Hi {sharer_name}!</p>

            <p>Great news! <strong>{accepter_name}</strong> ({accepter_email}) has accepted your shared content.</p>

            <div class="content-info">
                <div><strong>Content:</strong> {content_title}</div>
                <div><strong>Type:</strong> {content_type_display}</div>
            </div>

            {f'<div class="message"><strong>Message from {accepter_name}:</strong><br>"{response_message}"</div>' if response_message else ''}

            <p>Your shared content is now helping another student learn! This is what makes the CampusPQ community so special - students helping students succeed.</p>

            <p>Keep sharing your knowledge and helping others on their learning journey!</p>

            <div class="footer">
                <p>Thank you for being part of the CampusPQ community!</p>
                <p>© 2024 CampusPQ. All rights reserved.</p>
            </div>
        </div>
    </body>
    </html>
    """

    text_content = f"""
    Your Content Was Accepted! - CampusPQ

    Hi {sharer_name}!

    Great news! {accepter_name} ({accepter_email}) has accepted your shared content.

    Content: {content_title}
    Type: {content_type_display}

    {f'Message from {accepter_name}: "{response_message}"' if response_message else ''}

    Your shared content is now helping another student learn! Keep sharing your knowledge and helping others.

    Best regards,
    The CampusPQ Team
    """

    return send_email(sharer_email, subject, html_content, text_content)
