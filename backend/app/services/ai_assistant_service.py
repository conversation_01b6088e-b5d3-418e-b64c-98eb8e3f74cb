import logging
import time
import async<PERSON>
from datetime import timezone
from typing import Dict, List, Optional, AsyncGenerator, Any

import openai
from openai.types.chat import ChatCompletionChunk
from agents import <PERSON>, <PERSON>, WebSearchTool

from app import schemas
from app.schemas.ai_assistant import AIAssistantStreamEvent
from app.core.config import settings
from app.services.question_service import get as get_question_by_id
from app.services.course_service import get as get_course_by_id

from app.models.ai_analytics import InteractionType
from app.services.ai_analytics_service import create_interaction

logger = logging.getLogger(__name__)


class AIAssistantService:
    """Service for AI assistants in the CampusPQ application."""

    def __init__(self):
        """Initialize the AI assistant service."""
        self.client = openai.OpenAI(api_key=settings.OPENAI_API_KEY)
        self.max_retries = 2  # Reduced number of retries for faster response
        self.model = "gpt-3.5-turbo"  # Fast chat model that works with chat completions API

        # Initialize the general assistant
        self.general_assistant = Agent(
            name="CampusPQ Assistant",
            instructions="""
            You are CampusPQ Assistant, an AI assistant for the CampusPQ learning platform.
            Your role is to help students with questions about:
            1. The CampusPQ platform and how to use it
            2. Academic subjects and course content
            3. Study tips and learning strategies

            Be helpful, accurate, and concise in your responses. If you don't know the answer,
            say so and suggest where the student might find the information.

            When answering questions about course content, use the provided context from the
            vector store to give accurate and relevant information. Always cite your sources
            when using information from the vector store.
            """,
            tools=[WebSearchTool()],
        )

        # Initialize the practice mode assistant
        self.practice_assistant = Agent(
            name="CampusPQ Practice Helper",
            instructions="""
            You are CampusPQ Practice Helper, an AI assistant that helps students during practice mode.
            Your role is to:
            1. Provide detailed explanations for questions
            2. Explain concepts related to the question
            3. Offer additional context and learning resources

            When explaining answers, be thorough but clear, breaking down complex concepts
            into understandable parts. Use examples where appropriate.

            Use the provided context from the vector store to enhance your explanations with
            relevant course materials. Always cite your sources when using information from
            the vector store.
            """,
            tools=[WebSearchTool()],
        )

    async def _get_relevant_course_materials(self, query: str, course_id: Optional[int] = None, limit: int = 2) -> List[Dict]:
        """
        Retrieve relevant course materials from the vector store.

        Args:
            query: The query to search for
            course_id: Optional course ID to filter results
            limit: Maximum number of results to return (reduced for faster response)

        Returns:
            List of relevant course materials
        """
        try:
            # Create filter condition if course_id is provided
            filter_condition = [{"key": "course_id", "match": {"value": str(course_id)}}] if course_id else None

            # Vector store removed - return empty results
            logger.info("Vector store functionality removed - using Gemini Files API instead")
            return []
        except Exception as e:
            logger.error(f"Error retrieving course materials: {str(e)}")
            return []

    async def ask_general_assistant(self, query: str, user_id: Optional[int] = None, db = None) -> schemas.AIAssistantResponse:
        """
        Ask the general assistant a question.

        Args:
            query: The question to ask
            user_id: Optional user ID for personalized responses
            db: Optional database session for retrieving user courses

        Returns:
            AIAssistantResponse with the assistant's response
        """
        retry_count = 0
        sources = []
        start_time = time.time()
        success = True
        response_content = ""
        course_id = None

        while retry_count < self.max_retries:
            try:
                # Get relevant course materials from vector store
                course_materials = await self._get_relevant_course_materials(query)

                # Extract course IDs from the user if available
                user_course_ids = []
                if user_id and db:
                    from app.services.user_service import get_user_courses
                    user_courses = get_user_courses(db, user_id)
                    user_course_ids = [course.id for course in user_courses]

                # Get additional course-specific materials if we have user courses
                if user_course_ids:
                    for course_id in user_course_ids[:3]:  # Limit to 3 courses
                        course_specific_materials = await self._get_relevant_course_materials(
                            query, course_id=course_id, limit=1
                        )
                        course_materials.extend(course_specific_materials)

                # Prepare context from course materials
                context = ""
                if course_materials:
                    context = "Here is some relevant information from our course materials:\n\n"
                    for i, material in enumerate(course_materials):
                        context += f"Source {i+1}: {material['text']}\n\n"
                        sources.append(f"Course material: {material.get('metadata', {}).get('title', 'Unknown')}")
                        # Get course_id from the first material if available
                        if i == 0 and not course_id and 'metadata' in material and 'course_id' in material['metadata']:
                            course_id = int(material['metadata']['course_id'])

                # Prepare the full prompt with context
                full_prompt = f"{query}\n\n{context if context else ''}"

                # Try to use the OpenAI API
                try:
                    result = await Runner.run(self.general_assistant, full_prompt)
                    response_content = result.final_output
                    response = schemas.AIAssistantResponse(
                        content=response_content,
                        sources=sources if sources else None
                    )

                    # Record successful interaction
                    success = True
                    break
                except Exception as e:
                    logger.error(f"Error using OpenAI API (attempt {retry_count + 1}): {str(e)}")
                    retry_count += 1

                    # If we've exhausted all retries, return a fallback response
                    if retry_count >= self.max_retries:
                        success = False
                        # For testing purposes, return a mock response for common queries
                        if "what is campuspq" in query.lower():
                            response_content = "CampusPQ is an AI-driven learning ecosystem that brings together students, tutors, and administrators. It offers features like customizable practice and testing, rich content support, performance insights, session management, and more."
                        elif "how do i" in query.lower():
                            response_content = "To get started with CampusPQ, you can:\n\n1. Create an account and complete your profile\n2. Browse available courses\n3. Practice with multiple-choice questions\n4. Book tutoring sessions\n5. Track your progress on the dashboard"
                        else:
                            response_content = "I'm a CampusPQ Assistant designed to help you with questions about the platform and your courses. How can I assist you today?"

                        response = schemas.AIAssistantResponse(
                            content=response_content,
                            sources=None
                        )
                        break

                    # Wait before retrying (exponential backoff)
                    import asyncio
                    await asyncio.sleep(2 ** retry_count)
            except Exception as e:
                logger.error(f"Error asking general assistant: {str(e)}")
                success = False
                response_content = "I'm sorry, I encountered an error while processing your question. Please try again later."
                response = schemas.AIAssistantResponse(
                    content=response_content,
                    sources=None
                )
                break

        # Calculate response time
        response_time_ms = int((time.time() - start_time) * 1000)

        # Record analytics if database is available
        if db:
            try:
                create_interaction(
                    db,
                    user_id=user_id,
                    query=query,
                    response=response_content,
                    interaction_type=InteractionType.GENERAL,
                    metadata={
                        "sources": sources if sources else None,
                        "retry_count": retry_count
                    },  # Will be mapped to interaction_metadata
                    course_id=course_id,
                    response_time_ms=response_time_ms,
                    success=success
                )
            except Exception as e:
                logger.error(f"Error recording analytics: {str(e)}")

        return response

    async def get_practice_help(self, question_id: int, db) -> schemas.AIAssistantResponse:
        """
        Get help for a specific question in practice mode.

        Args:
            question_id: ID of the question
            db: Database session

        Returns:
            AIAssistantResponse with the explanation and help
        """
        retry_count = 0
        sources = []
        start_time = time.time()
        success = True
        response_content = ""

        while retry_count < self.max_retries:
            try:
                # Get the question from the database
                question = get_question_by_id(db, id=question_id)
                if not question:
                    response_content = "Question not found. Please try with a different question."
                    success = False
                    response = schemas.AIAssistantResponse(
                        content=response_content,
                        sources=None
                    )
                    break

                # Get the course for additional context (handle both regular and generated questions)
                course = None
                course_name = "Unknown Course"

                if question.course_id:
                    # Regular question with course_id
                    course = get_course_by_id(db, id=question.course_id)
                    if course:
                        course_name = course.name
                elif question.course_name:
                    # Generated question with course_name (from PDF upload)
                    course_name = question.course_name

                # Get relevant course materials from vector store
                course_materials = await self._get_relevant_course_materials(
                    query=question.content,
                    course_id=question.course_id,
                    limit=3
                )

                # Prepare context from course materials
                context = ""
                if course_materials:
                    context = "Here is some relevant information from course materials:\n\n"
                    for i, material in enumerate(course_materials):
                        context += f"Source {i+1}: {material['text']}\n\n"
                        sources.append(f"Course material: {material.get('metadata', {}).get('title', 'Unknown')}")

                # Create a prompt with the question details and context
                prompt = f"""
                I need help with this question from the course "{course_name}":

                Question: {question.content}

                {f"Options: {question.options}" if question.options else ""}

                The correct answer is: {question.answer}

                {context if context else ""}

                Please explain why this is the correct answer and provide additional context to help me understand the concept better.
                """

                try:
                    result = await Runner.run(self.practice_assistant, prompt)
                    response_content = result.final_output
                    response = schemas.AIAssistantResponse(
                        content=response_content,
                        sources=sources if sources else None
                    )
                    success = True
                    break
                except Exception as e:
                    logger.error(f"Error using OpenAI API for practice help (attempt {retry_count + 1}): {str(e)}")
                    retry_count += 1

                    # If we've exhausted all retries, return a fallback response
                    if retry_count >= self.max_retries:
                        success = False
                        # Return a detailed fallback response
                        response_content = f"""
# Explanation for Question: "{question.content}"

## The Correct Answer: {question.answer}

{question.explanation or "This question tests your understanding of key concepts in this subject area."}

## Additional Context

This question is from the course "{course.name}" and covers important concepts that you'll need to understand for your exams. Here are some additional tips:

1. Make sure you understand the underlying principles
2. Practice similar questions to reinforce your knowledge
3. Review related topics in your course materials
4. Consider creating flashcards for key terms and concepts

Would you like more specific help with this question or topic?
                        """
                        response = schemas.AIAssistantResponse(
                            content=response_content,
                            sources=sources if sources else None
                        )
                        break

                    # Wait before retrying (exponential backoff)
                    import asyncio
                    await asyncio.sleep(2 ** retry_count)
            except Exception as e:
                logger.error(f"Error getting practice help: {str(e)}")
                success = False
                response_content = "I'm sorry, I encountered an error while providing help for this question. Please try again later."
                response = schemas.AIAssistantResponse(
                    content=response_content,
                    sources=None
                )
                break

        # Calculate response time
        response_time_ms = int((time.time() - start_time) * 1000)

        # Record analytics
        try:
            create_interaction(
                db,
                query=f"Practice help for question ID: {question_id}",
                response=response_content,
                interaction_type=InteractionType.PRACTICE,
                metadata={
                    "sources": sources if sources else None,
                    "retry_count": retry_count
                },  # Will be mapped to interaction_metadata
                course_id=question.course_id if 'question' in locals() and question else None,
                question_id=question_id,
                response_time_ms=response_time_ms,
                success=success
            )
        except Exception as e:
            logger.error(f"Error recording analytics for practice help: {str(e)}")

        return response

    async def stream_general_assistant(self, query: str, user_id: Optional[int] = None, db = None) -> AsyncGenerator[AIAssistantStreamEvent, None]:
        """
        Stream responses from the general assistant.

        Args:
            query: The question to ask
            user_id: Optional user ID for personalized responses
            db: Optional database session for retrieving user courses

        Yields:
            AIAssistantStreamEvent with streaming response chunks
        """
        sources = []
        start_time = time.time()
        success = True
        response_content = ""
        course_id = None

        try:
            # Yield start event
            yield AIAssistantStreamEvent(type="start")

            # Start a task to get relevant course materials in the background
            # This allows us to start generating a response immediately
            import asyncio
            materials_task = asyncio.create_task(self._get_relevant_course_materials(query))

            # We'll only wait for a very short time to get materials
            try:
                course_materials = await asyncio.wait_for(materials_task, timeout=0.8)
            except asyncio.TimeoutError:
                logger.info("Vector store search timed out, continuing without materials")
                course_materials = []

            # Skip user-specific course materials to improve response time

            # Prepare context from course materials
            context = ""
            if course_materials:
                context = "Here is some relevant information from our course materials:\\n\\n"
                for i, material in enumerate(course_materials):
                    context += f"Source {i+1}: {material['text']}\\n\\n"
                    sources.append(f"Course material: {material.get('metadata', {}).get('title', 'Unknown')}")
                    # Get course_id from the first material if available
                    if i == 0 and not course_id and 'metadata' in material and 'course_id' in material['metadata']:
                        course_id = int(material['metadata']['course_id'])

            # Shorter system message for faster processing with math formatting
            system_message = """
            You are CampusPQ Assistant. Be helpful, accurate, and concise.
            Use any provided context in your response. Keep answers brief but informative.

            When explaining mathematical concepts, use LaTeX formatting:
            - For inline math, use single dollar signs: $E = mc^2$
            - For block math, use double dollar signs: $$\\int_{a}^{b} f(x) dx = F(b) - F(a)$$
            - Ensure proper escaping of backslashes in LaTeX expressions
            """

            # Prepare the full prompt with context
            user_message = f"{query}\\n\\n{context if context else ''}"

            # Create the streaming completion with optimized parameters for speed
            stream = self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": system_message},
                    {"role": "user", "content": user_message}
                ],
                stream=True,
                max_tokens=300,  # Further limit response length for faster completion
                temperature=0.7,  # Slightly higher temperature for faster responses
                top_p=0.9,  # Slightly lower top_p for faster sampling
                presence_penalty=0.0,  # No penalty for repeated content
                frequency_penalty=0.0  # No penalty for frequent tokens
            )

            # Stream the response
            for chunk in stream:
                if chunk.choices and chunk.choices[0].delta and chunk.choices[0].delta.content:
                    content = chunk.choices[0].delta.content
                    response_content += content
                    yield AIAssistantStreamEvent(type="token", content=content)

            # Yield end event with sources
            yield AIAssistantStreamEvent(type="end", content=response_content, sources=sources if sources else None)

        except Exception as e:
            logger.error(f"Error streaming from general assistant: {str(e)}")
            success = False
            error_message = f"Error: {str(e)}"
            yield AIAssistantStreamEvent(type="error", error=error_message)
        finally:
            # Calculate response time
            response_time_ms = int((time.time() - start_time) * 1000)

            # Record analytics if database is available
            if db:
                try:
                    create_interaction(
                        db,
                        user_id=user_id,
                        query=query,
                        response=response_content,
                        interaction_type=InteractionType.GENERAL,
                        metadata={
                            "sources": sources if sources else None,
                            "streaming": True
                        },
                        course_id=course_id,
                        response_time_ms=response_time_ms,
                        success=success
                    )
                except Exception as e:
                    logger.error(f"Error recording streaming analytics: {str(e)}")

    async def stream_practice_help(self, question_id: int, db) -> AsyncGenerator[AIAssistantStreamEvent, None]:
        """
        Stream help for a specific question in practice mode.

        Args:
            question_id: ID of the question
            db: Database session

        Yields:
            AIAssistantStreamEvent with streaming response chunks
        """
        sources = []
        start_time = time.time()
        success = True
        response_content = ""

        try:
            # Yield start event
            yield AIAssistantStreamEvent(type="start")

            # Get the question from the database
            question = get_question_by_id(db, id=question_id)
            if not question:
                error_message = "Question not found. Please try with a different question."
                yield AIAssistantStreamEvent(type="error", error=error_message)
                return

            # Get the course for additional context (handle both regular and generated questions)
            course = None
            course_name = "Unknown Course"

            if question.course_id:
                # Regular question with course_id
                course = get_course_by_id(db, id=question.course_id)
                if course:
                    course_name = course.name
            elif question.course_name:
                # Generated question with course_name (from PDF upload)
                course_name = question.course_name

            # Get relevant course materials with a very short timeout
            import asyncio
            try:
                materials_task = asyncio.create_task(self._get_relevant_course_materials(
                    query=question.content,
                    course_id=question.course_id,
                    limit=1  # Minimal limit for fastest response
                ))
                course_materials = await asyncio.wait_for(materials_task, timeout=0.5)
            except asyncio.TimeoutError:
                logger.info("Vector store search timed out for practice help, continuing without materials")
                course_materials = []

            # Prepare context from course materials
            context = ""
            if course_materials:
                context = "Here is some relevant information from course materials:\\n\\n"
                for i, material in enumerate(course_materials):
                    context += f"Source {i+1}: {material['text']}\\n\\n"
                    sources.append(f"Course material: {material.get('metadata', {}).get('title', 'Unknown')}")

            # Shorter system message for faster processing with math formatting
            system_message = """
            You are CampusPQ Practice Helper. Explain the correct answer clearly and concisely.
            Use any provided context in your explanation. Be brief but informative.

            When explaining mathematical concepts, use LaTeX formatting:
            - For inline math, use single dollar signs: $E = mc^2$
            - For block math, use double dollar signs: $$\\int_{a}^{b} f(x) dx = F(b) - F(a)$$
            - Ensure proper escaping of backslashes in LaTeX expressions

            For chemistry equations, use proper LaTeX notation: $H_2O + CO_2 \\rightarrow H_2CO_3$
            """

            # Create a prompt with the question details and context
            user_message = f"""
            I need help with this question from the course "{course_name}":

            Question: {question.content}

            {f"Options: {question.options}" if question.options else ""}

            The correct answer is: {question.answer}

            {context if context else ""}

            Please explain why this is the correct answer and provide additional context to help me understand the concept better.
            """

            # Create the streaming completion with optimized parameters for speed
            stream = self.client.chat.completions.create(
                model=self.model,
                messages=[
                    {"role": "system", "content": system_message},
                    {"role": "user", "content": user_message}
                ],
                stream=True,
                max_tokens=300,  # Further limit response length for faster completion
                temperature=0.7,  # Slightly higher temperature for faster responses
                top_p=0.9,  # Slightly lower top_p for faster sampling
                presence_penalty=0.0,  # No penalty for repeated content
                frequency_penalty=0.0  # No penalty for frequent tokens
            )

            # Stream the response
            for chunk in stream:
                if chunk.choices and chunk.choices[0].delta and chunk.choices[0].delta.content:
                    content = chunk.choices[0].delta.content
                    response_content += content
                    yield AIAssistantStreamEvent(type="token", content=content)

            # Yield end event with sources
            yield AIAssistantStreamEvent(type="end", content=response_content, sources=sources if sources else None)

        except Exception as e:
            logger.error(f"Error streaming practice help: {str(e)}")
            success = False
            error_message = f"Error: {str(e)}"
            yield AIAssistantStreamEvent(type="error", error=error_message)
        finally:
            # Calculate response time
            response_time_ms = int((time.time() - start_time) * 1000)

            # Record analytics
            try:
                create_interaction(
                    db,
                    query=f"Practice help for question ID: {question_id}",
                    response=response_content,
                    interaction_type=InteractionType.PRACTICE,
                    metadata={
                        "sources": sources if sources else None,
                        "streaming": True
                    },
                    course_id=question.course_id if 'question' in locals() and question else None,
                    question_id=question_id,
                    response_time_ms=response_time_ms,
                    success=success
                )
            except Exception as e:
                logger.error(f"Error recording streaming analytics for practice help: {str(e)}")

    async def stream_document_chat(self, gemini_file_id: str, query: str, user_id: int, db) -> AsyncGenerator[AIAssistantStreamEvent, None]:
        """
        Stream chat responses about a specific document using Gemini Files API.

        Args:
            gemini_file_id: Gemini file ID to chat about
            query: The question to ask about the document
            user_id: User ID for analytics
            db: Database session

        Yields:
            AIAssistantStreamEvent with streaming response chunks
        """
        import google.generativeai as genai
        from app.crud.crud_gemini_file import gemini_file as gemini_file_crud

        start_time = time.time()
        success = True
        response_content = ""

        try:
            # Yield start event
            yield AIAssistantStreamEvent(type="start")

            # Get file metadata from database
            db_file = gemini_file_crud.get_by_gemini_file_id(db, gemini_file_id=gemini_file_id)
            if not db_file:
                error_message = "Document not found. Please try with a different document."
                yield AIAssistantStreamEvent(type="error", error=error_message)
                return

            # Verify file belongs to user
            if db_file.student_id != user_id:
                error_message = "You don't have permission to access this document."
                yield AIAssistantStreamEvent(type="error", error=error_message)
                return

            # Check if file is still available (not expired)
            if db_file.is_deleted:
                error_message = "This document has expired. Please upload it again."
                yield AIAssistantStreamEvent(type="error", error=error_message)
                return

            # Configure Gemini
            genai.configure(api_key=settings.GEMINI_API_KEY)

            # Get the file from Gemini
            try:
                gemini_file = genai.get_file(gemini_file_id)
            except Exception as e:
                logger.error(f"Error getting file from Gemini: {str(e)}")
                error_message = "Document is no longer available. Please upload it again."
                yield AIAssistantStreamEvent(type="error", error=error_message)
                return

            # Create system message for document chat
            system_message = f"""You are an AI assistant helping students understand their uploaded documents.

You have access to a document titled "{db_file.original_filename}" that the student uploaded.

Your role is to:
1. Answer questions about the document content accurately
2. Provide explanations and clarifications about concepts in the document
3. Help the student understand complex topics from their notes
4. Suggest related concepts or topics for further study
5. Be concise but thorough in your responses

Always base your answers on the document content. If the question is not related to the document, politely redirect the conversation back to the document content.

Keep responses focused and educational. Use a friendly, helpful tone."""

            user_message = f"""Based on the uploaded document, please answer this question: {query}

Please provide a clear, helpful response that helps the student understand the content better."""

            # Create Gemini model for streaming
            model = genai.GenerativeModel(
                model_name="gemini-1.5-flash",
                system_instruction=system_message
            )

            # Create the streaming completion
            response = model.generate_content(
                [gemini_file, user_message],
                stream=True
            )

            # Stream the response
            for chunk in response:
                if chunk.text:
                    content = chunk.text
                    response_content += content
                    yield AIAssistantStreamEvent(type="token", content=content)

            # Yield end event
            yield AIAssistantStreamEvent(type="end", content=response_content)

        except Exception as e:
            logger.error(f"Error streaming document chat: {str(e)}")
            success = False
            error_message = f"Error: {str(e)}"
            yield AIAssistantStreamEvent(type="error", error=error_message)
        finally:
            # Calculate response time
            response_time_ms = int((time.time() - start_time) * 1000)

            # Record analytics
            try:
                create_interaction(
                    db,
                    user_id=user_id,
                    query=f"Document chat: {query}",
                    response=response_content,
                    interaction_type=InteractionType.GENERAL,  # We can add a new type later
                    metadata={
                        "gemini_file_id": gemini_file_id,
                        "document_name": db_file.original_filename if 'db_file' in locals() and db_file else None,
                        "streaming": True,
                        "chat_type": "document"
                    },
                    response_time_ms=response_time_ms,
                    success=success
                )
            except Exception as e:
                logger.error(f"Error recording streaming analytics for document chat: {str(e)}")


# Create a singleton instance
ai_assistant_service = AIAssistantService()
