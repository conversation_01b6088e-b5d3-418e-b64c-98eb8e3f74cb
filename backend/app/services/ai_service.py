"""
Unified AI service that can switch between OpenAI and Gemini based on configuration.
Provides a single interface for all AI operations with model switching capability.
"""

import logging
from typing import List, Dict, Any, Optional
from enum import Enum

from app.core.config import settings
# Lazy import AI services to improve startup time

logger = logging.getLogger(__name__)


class AIProvider(Enum):
    """Supported AI providers."""
    OPENAI = "openai"
    GEMINI = "gemini"


class UnifiedAIService:
    """Unified service that can switch between AI providers."""

    def __init__(self):
        """Initialize the unified AI service."""
        # Get provider from environment variable
        provider_name = getattr(settings, 'AI_PROVIDER', 'gemini').lower()

        try:
            self.current_provider = AIProvider(provider_name)
        except ValueError:
            logger.warning(f"Invalid AI provider '{provider_name}', defaulting to Gemini")
            self.current_provider = AIProvider.GEMINI

        logger.info(f"Initialized AI service with provider: {self.current_provider.value}")

    def get_current_service(self):
        """Get the current AI service based on the provider."""
        if self.current_provider == AIProvider.OPENAI:
            # Lazy import OpenAI service
            from app.services.openai_service import openai_service
            return openai_service
        else:
            # Lazy import Gemini service
            from app.services.gemini_service import gemini_service
            return gemini_service

    def extract_text_from_pdf(self, file_path: str) -> str:
        """Extract text from PDF using the current AI provider."""
        try:
            service = self.get_current_service()

            if self.current_provider == AIProvider.OPENAI:
                # Use enhanced OpenAI GPT-4o vision processing
                logger.info("Using OpenAI GPT-4o enhanced vision processing for PDF")
                return service.extract_text_from_pdf(file_path)
            else:
                # Use Gemini's fast mode
                prompt = "Extract all the text content from this document, preserving the structure and formatting as much as possible. Focus on the actual content and ignore page numbers, headers, and footers."
                return service.process_pdf_content_sync_fast(file_path, prompt)

        except Exception as e:
            logger.error(f"Error extracting text with {self.current_provider.value}: {str(e)}")
            raise

    def extract_text_from_pdf_content(self, pdf_content: bytes) -> str:
        """Extract text from PDF content (bytes) using the current AI provider."""
        try:
            service = self.get_current_service()

            if self.current_provider == AIProvider.OPENAI:
                # Use enhanced OpenAI GPT-4o vision processing with content
                logger.info("Using OpenAI GPT-4o enhanced vision processing for PDF content")
                return service.extract_text_from_pdf_content(pdf_content)
            else:
                # Use Gemini's content processing
                prompt = "Extract all the text content from this document, preserving the structure and formatting as much as possible. Focus on the actual content and ignore page numbers, headers, and footers."
                return service.process_pdf_content_bytes_sync(pdf_content, prompt)

        except Exception as e:
            logger.error(f"Error extracting text from PDF content with {self.current_provider.value}: {str(e)}")
            raise

    def process_pdf_complete(self, file_path: str) -> Dict[str, Any]:
        """Process PDF completely with enhanced capabilities when using OpenAI."""
        try:
            service = self.get_current_service()

            if self.current_provider == AIProvider.OPENAI:
                # Use OpenAI's complete processing pipeline
                logger.info("Using OpenAI complete PDF processing pipeline")
                return service.process_pdf_complete(file_path)
            else:
                # Fallback to basic text extraction for Gemini
                text = self.extract_text_from_pdf(file_path)
                return {
                    "filename": file_path.split('/')[-1],
                    "text": text,
                    "processing_method": "gemini_basic",
                    "content_chunks": [{"content": text, "chunk_index": 0, "chunk_type": "full"}],
                    "metadata": {
                        "total_characters": len(text),
                        "vision_processed": False
                    }
                }

        except Exception as e:
            logger.error(f"Error in complete PDF processing with {self.current_provider.value}: {str(e)}")
            raise

    def generate_mcqs(self, content: str, course_name: Optional[str], question_count: int) -> List[Dict]:
        """Generate MCQs using the current AI provider."""
        try:
            service = self.get_current_service()

            if self.current_provider == AIProvider.OPENAI:
                return service.generate_mcqs(content, course_name, question_count)
            else:
                # Use Gemini's multiagent system
                from app.services.langchain_multiagent_service import langchain_multiagent_service
                from app.core.multiagent_config import MultiAgentConfig

                config = MultiAgentConfig(
                    total_questions=question_count,
                    max_questions_per_agent=10,
                    min_questions_per_agent=3,
                    max_agents=8,
                    timeout_per_agent=90,
                    retry_attempts=3
                )

                import asyncio
                return asyncio.run(langchain_multiagent_service.generate_mcqs_multiagent(
                    note_contents=[content],
                    course_name=course_name,
                    question_count=question_count,
                    config=config
                ))

        except Exception as e:
            logger.error(f"Error generating MCQs with {self.current_provider.value}: {str(e)}")
            raise

    def generate_flashcards(self, content: str, course_name: Optional[str], card_count: int) -> List[Dict]:
        """Generate flashcards using the current AI provider."""
        try:
            service = self.get_current_service()

            if self.current_provider == AIProvider.OPENAI:
                return service.generate_flashcards(content, course_name, card_count)
            else:
                # Use Gemini for flashcard generation
                prompt = f"""Generate exactly {card_count} high-quality flashcards based on the following content.

REQUIREMENTS:
1. Create concise, focused questions on the front
2. Provide comprehensive but clear answers on the back
3. Cover key concepts, definitions, and important facts
4. Vary difficulty and question types
5. Return ONLY valid JSON

JSON FORMAT:
{{
  "flashcards": [
    {{
      "front": "Clear, concise question or prompt",
      "back": "Comprehensive but clear answer",
      "difficulty": "easy|medium|hard",
      "topic": "Specific topic from content",
      "card_type": "definition|concept|fact|application"
    }}
  ]
}}

CONTENT:
{content[:6000]}

Generate exactly {card_count} flashcards in valid JSON format."""

                import json
                response_text = service.chat_completion_sync([
                    {"role": "system", "content": "You are an expert educator who creates effective flashcards for learning. Always return valid JSON."},
                    {"role": "user", "content": prompt}
                ])
                result = json.loads(response_text)
                return result.get("flashcards", [])[:card_count]

        except Exception as e:
            logger.error(f"Error generating flashcards with {self.current_provider.value}: {str(e)}")
            raise

    def summarize_notes(self, content: str, course_name: Optional[str]) -> Dict[str, Any]:
        """Generate note summary using the current AI provider."""
        try:
            service = self.get_current_service()

            if self.current_provider == AIProvider.OPENAI:
                return service.summarize_notes(content, course_name)
            else:
                # Use Gemini for summarization
                course_context = f" for the {course_name} course" if course_name else ""

                prompt = f"""Create a comprehensive summary of these notes{course_context}.

REQUIREMENTS:
1. Provide a clear, structured summary
2. Identify key topics and concepts
3. Extract important definitions and facts
4. Highlight main learning objectives
5. Return ONLY valid JSON

JSON FORMAT:
{{
  "summary": {{
    "title": "Main topic or title",
    "overview": "Brief overview paragraph",
    "key_topics": ["topic1", "topic2", "topic3"],
    "main_concepts": [
      {{
        "concept": "Concept name",
        "definition": "Clear definition",
        "importance": "Why this is important"
      }}
    ],
    "learning_objectives": ["objective1", "objective2"],
    "difficulty_level": "beginner|intermediate|advanced",
    "estimated_study_time": "X hours"
  }}
}}

CONTENT:
{content[:8000]}

Generate a comprehensive summary in valid JSON format."""

                import json
                response_text = service.chat_completion_sync([
                    {"role": "system", "content": "You are an expert educator who creates comprehensive, structured summaries of educational content. Always return valid JSON."},
                    {"role": "user", "content": prompt}
                ])
                result = json.loads(response_text)
                return result.get("summary", {})

        except Exception as e:
            logger.error(f"Error generating summary with {self.current_provider.value}: {str(e)}")
            raise

    def get_provider_info(self) -> Dict[str, Any]:
        """Get information about the current AI provider."""
        return {
            "provider": self.current_provider.value,
            "description": {
                "openai": "OpenAI GPT-4o - Enhanced vision processing for PDFs with images, concurrent analysis, and high-quality content understanding",
                "gemini": "Google Gemini 1.5 Flash - Advanced document understanding with multiagent system"
            }.get(self.current_provider.value, "Unknown provider"),
            "capabilities": {
                "text_extraction": True,
                "mcq_generation": True,
                "flashcard_generation": True,
                "note_summarization": True,
                "vision_processing": self.current_provider == AIProvider.OPENAI,
                "enhanced_pdf_processing": self.current_provider == AIProvider.OPENAI,
                "concurrent_processing": self.current_provider == AIProvider.OPENAI,
                "image_understanding": self.current_provider == AIProvider.OPENAI,
                "multiagent_system": self.current_provider == AIProvider.GEMINI
            }
        }


# Create a singleton instance
ai_service = UnifiedAIService()
