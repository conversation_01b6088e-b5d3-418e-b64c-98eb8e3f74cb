from typing import Any, Dict, Optional, Union
from datetime import datetime, timedelta
import secrets

from sqlalchemy.orm import Session

from app import schemas
from app.models.user import User
from app.core.security import get_password_hash, verify_password
from app.core.config import settings
from app.services.email_service import send_verification_email


def get(db: Session, id: int) -> Optional[User]:
    # First get the user with the selected columns
    user_row = db.query(
        User.id,
        User.email,
        User.hashed_password,
        User.full_name,
        User.other_name,
        User.role,
        User.is_active,
        User.gender,
        User.phone_number,
        User.department,
        User.level,
        User.date_of_birth,
        User.state_of_origin,
        User.profile_picture_url,
        User.profile_completed,
        User.created_at,
        User.updated_at,
        User.institution_id
    ).filter(User.id == id).first()

    if not user_row:
        return None

    # Then get the user's enrolled courses
    from app.models.user import user_course
    from app.models.course import Course

    courses = db.query(Course).join(
        user_course,
        (user_course.c.course_id == Course.id) &
        (user_course.c.user_id == id)
    ).all()

    # We'll create a User object directly

    # Create a User object from the dictionary
    from sqlalchemy.orm import aliased
    UserAlias = aliased(User)
    user = db.query(UserAlias).filter(UserAlias.id == id).first()

    # Manually set the courses attribute
    user.courses = courses

    return user


def get_by_email(db: Session, email: str) -> Optional[User]:
    # First get the user with the selected columns
    user_row = db.query(
        User.id,
        User.email,
        User.hashed_password,
        User.full_name,
        User.other_name,
        User.role,
        User.is_active,
        User.gender,
        User.phone_number,
        User.department,
        User.level,
        User.date_of_birth,
        User.state_of_origin,
        User.profile_picture_url,
        User.profile_completed,
        User.created_at,
        User.updated_at,
        User.institution_id
    ).filter(User.email == email).first()

    if not user_row:
        return None

    # Then get the user's enrolled courses
    from app.models.user import user_course
    from app.models.course import Course

    courses = db.query(Course).join(
        user_course,
        (user_course.c.course_id == Course.id) &
        (user_course.c.user_id == user_row.id)
    ).all()

    # Create a User object from the dictionary
    from sqlalchemy.orm import aliased
    UserAlias = aliased(User)
    user = db.query(UserAlias).filter(UserAlias.id == user_row.id).first()

    # Manually set the courses attribute
    user.courses = courses

    return user


# Username field has been removed


def get_multi(
    db: Session, *, skip: int = 0, limit: int = 100
) -> list[User]:
    # Use a more explicit query to avoid loading columns that might cause issues
    return db.query(
        User.id,
        User.email,
        User.hashed_password,
        User.full_name,
        User.other_name,
        User.role,
        User.is_active,
        User.gender,
        User.phone_number,
        User.department,
        User.level,
        User.date_of_birth,
        User.state_of_origin,
        User.profile_picture_url,
        User.profile_completed,
        User.created_at,
        User.updated_at,
        User.institution_id
    ).offset(skip).limit(limit).all()


def create(db: Session, *, obj_in: schemas.UserCreate) -> User:
    # Set terms acceptance timestamp if terms are accepted
    terms_accepted_at = datetime.utcnow() if obj_in.terms_accepted else None

    db_obj = User(
        email=obj_in.email,
        hashed_password=get_password_hash(obj_in.password),
        full_name=obj_in.full_name,
        role=obj_in.role,
        is_active=obj_in.is_active,
        terms_accepted=obj_in.terms_accepted,
        terms_accepted_at=terms_accepted_at,
    )
    db.add(db_obj)
    db.commit()
    db.refresh(db_obj)
    return db_obj


def update(
    db: Session, *, db_obj: User, obj_in: Union[schemas.UserUpdate, Dict[str, Any]]
) -> User:
    if isinstance(obj_in, dict):
        update_data = obj_in
    else:
        update_data = obj_in.model_dump(exclude_unset=True)
    if update_data.get("password"):
        hashed_password = get_password_hash(update_data["password"])
        del update_data["password"]
        update_data["hashed_password"] = hashed_password

    # Handle department field - it's a legacy string field, not a relationship
    # The department relationship is handled via department_id
    for field in update_data:
        if field in update_data:
            # Skip relationship fields that should be handled separately
            if field in ['department']:  # department is a string field, not a relationship
                setattr(db_obj, field, update_data[field])
            else:
                setattr(db_obj, field, update_data[field])

    db.add(db_obj)
    db.commit()
    db.refresh(db_obj)
    return db_obj


def delete(db: Session, *, id: int) -> User:
    obj = db.query(User).get(id)
    db.delete(obj)
    db.commit()
    return obj


def authenticate(db: Session, *, email: str, password: str) -> Optional[User]:
    user = get_by_email(db, email=email)
    if not user:
        return None
    if not verify_password(password, user.hashed_password):
        return None
    return user


def generate_verification_token() -> str:
    """Generate a secure random token for email verification"""
    return secrets.token_urlsafe(32)


def create_with_verification(db: Session, *, obj_in: schemas.UserCreate) -> User:
    """Create user and send verification email"""
    # Generate verification token
    verification_token = generate_verification_token()
    expires_at = datetime.utcnow() + timedelta(hours=settings.EMAIL_VERIFICATION_TOKEN_EXPIRE_HOURS)

    # Set terms acceptance timestamp if terms are accepted
    terms_accepted_at = datetime.utcnow() if obj_in.terms_accepted else None

    # Create user with verification token
    db_obj = User(
        email=obj_in.email,
        hashed_password=get_password_hash(obj_in.password),
        full_name=obj_in.full_name,
        role=obj_in.role,
        is_active=obj_in.is_active,
        email_verified=False,
        email_verification_token=verification_token,
        email_verification_token_expires=expires_at,
        terms_accepted=obj_in.terms_accepted,
        terms_accepted_at=terms_accepted_at,
    )
    db.add(db_obj)
    db.commit()
    db.refresh(db_obj)

    # Send verification email
    send_verification_email(
        email_to=obj_in.email,
        verification_token=verification_token,
        user_name=obj_in.full_name
    )

    return db_obj


def verify_email(db: Session, *, token: str) -> Optional[User]:
    """Verify email using token"""
    user = db.query(User).filter(
        User.email_verification_token == token,
        User.email_verification_token_expires > datetime.utcnow()
    ).first()

    if not user:
        return None

    # Mark email as verified and clear token
    user.email_verified = True
    user.email_verification_token = None
    user.email_verification_token_expires = None

    db.add(user)
    db.commit()
    db.refresh(user)

    # Send welcome email after successful verification
    from app.services.email_service import send_welcome_email
    send_welcome_email(
        email_to=user.email,
        user_name=user.full_name
    )

    return user


def resend_verification_email(db: Session, *, email: str) -> bool:
    """Resend verification email"""
    user = get_by_email(db, email=email)
    if not user:
        return False

    if user.email_verified:
        return False  # Already verified

    # Generate new verification token
    verification_token = generate_verification_token()
    expires_at = datetime.utcnow() + timedelta(hours=settings.EMAIL_VERIFICATION_TOKEN_EXPIRE_HOURS)

    # Update user with new token
    user.email_verification_token = verification_token
    user.email_verification_token_expires = expires_at

    db.add(user)
    db.commit()

    # Send verification email
    return send_verification_email(
        email_to=email,
        verification_token=verification_token,
        user_name=user.full_name
    )


def generate_password_reset_token() -> str:
    """Generate a secure random token for password reset"""
    return secrets.token_urlsafe(32)


def request_password_reset(db: Session, *, email: str) -> bool:
    """Request password reset for user"""
    from app.services.email_service import send_password_reset_email

    user = get_by_email(db, email=email)
    if not user:
        # Don't reveal if user exists or not for security
        return True

    if not user.is_active:
        # Don't send reset email to inactive users
        return True

    # Generate password reset token
    reset_token = generate_password_reset_token()
    expires_at = datetime.utcnow() + timedelta(hours=settings.PASSWORD_RESET_TOKEN_EXPIRE_HOURS)

    # Update user with reset token
    user.password_reset_token = reset_token
    user.password_reset_token_expires = expires_at

    db.add(user)
    db.commit()

    # Send password reset email
    return send_password_reset_email(
        email_to=email,
        reset_token=reset_token,
        user_name=user.full_name
    )


def reset_password(db: Session, *, token: str, new_password: str) -> Optional[User]:
    """Reset password using token"""
    user = db.query(User).filter(
        User.password_reset_token == token,
        User.password_reset_token_expires > datetime.utcnow()
    ).first()

    if not user:
        return None

    # Update password and clear reset token
    user.hashed_password = get_password_hash(new_password)
    user.password_reset_token = None
    user.password_reset_token_expires = None

    db.add(user)
    db.commit()
    db.refresh(user)

    return user
