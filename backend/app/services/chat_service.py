from typing import List, Optional, Dict, Any
from datetime import datetime
import json

from sqlalchemy.orm import Session
from sqlalchemy import and_

from app.crud.crud_chat import chat_room, chat_message, user_online_status
from app.models.chat import ChatRoom, ChatMessage, UserOnlineStatus, MessageDeliveryStatus
from app.models.user import User
from app.schemas.chat import (
    ChatRoomCreate, ChatMessageCreate,
    UserOnlineStatusCreate, ChatMessage as ChatMessageSchema
)


class ChatService:
    """Service for managing chat functionality"""

    def create_chat_room(
        self, db: Session, *, student_id: int, tutor_id: int
    ) -> ChatRoom:
        """Create or get existing chat room between student and tutor"""
        return chat_room.create_for_booking(
            db, student_id=student_id, tutor_id=tutor_id
        )

    def get_chat_room(
        self, db: Session, *, chat_room_id: int, user_id: int
    ) -> Optional[ChatRoom]:
        """Get a chat room if user is a participant"""
        return chat_room.get_for_user_and_room(
            db, user_id=user_id, chat_room_id=chat_room_id
        )

    def get_user_chat_rooms(
        self, db: Session, *, user_id: int, skip: int = 0, limit: int = 100
    ) -> List[Dict[str, Any]]:
        """Get chat rooms for a user with additional details"""
        rooms = chat_room.get_for_user(db, user_id=user_id, skip=skip, limit=limit)
        
        result = []
        for room in rooms:
            # Get the other participant
            other_user_id = room.tutor_id if room.student_id == user_id else room.student_id
            other_user = db.query(User).filter(User.id == other_user_id).first()
            
            # Get last message
            last_message = db.query(ChatMessage).filter(
                ChatMessage.chat_room_id == room.id
            ).order_by(ChatMessage.created_at.desc()).first()
            
            # Get unread count
            unread_count = chat_message.get_unread_count(
                db, chat_room_id=room.id, user_id=user_id
            )
            
            # Get online status
            online_status = user_online_status.get_by_user_id(db, user_id=other_user_id)
            
            room_data = {
                "id": room.id,
                "student_id": room.student_id,
                "tutor_id": room.tutor_id,
                "is_active": room.is_active,
                "created_at": room.created_at,
                "updated_at": room.updated_at,
                "other_user": {
                    "id": other_user.id,
                    "name": other_user.full_name,
                    "email": other_user.email,
                    "profile_picture_url": other_user.profile_picture_url,
                    "is_online": online_status.is_online if online_status else False,
                    "last_seen": online_status.last_seen if online_status else None
                },
                "last_message": {
                    "content": last_message.content if last_message else None,
                    "created_at": last_message.created_at if last_message else None,
                    "sender_id": last_message.sender_id if last_message else None
                } if last_message else None,
                "unread_count": unread_count
            }
            result.append(room_data)
        
        return result

    def get_chat_messages(
        self, db: Session, *, chat_room_id: int, user_id: int, skip: int = 0, limit: int = 100
    ) -> List[Dict[str, Any]]:
        """Get messages for a chat room with sender details"""
        # Verify user has access to this chat room
        room = chat_room.get(db, id=chat_room_id)
        if not room or (room.student_id != user_id and room.tutor_id != user_id):
            raise ValueError("Access denied to this chat room")
        
        messages = chat_message.get_by_chat_room(
            db, chat_room_id=chat_room_id, skip=skip, limit=limit
        )
        
        result = []
        for msg in messages:
            sender = db.query(User).filter(User.id == msg.sender_id).first()
            message_data = {
                "id": msg.id,
                "content": msg.content,
                "message_type": msg.message_type,
                "sender_id": msg.sender_id,
                "chat_room_id": msg.chat_room_id,
                "is_read": msg.is_read,
                "delivery_status": msg.delivery_status,
                "created_at": msg.created_at,
                "updated_at": msg.updated_at,
                "delivered_at": msg.delivered_at,
                "read_at": msg.read_at,
                "sender": {
                    "id": sender.id,
                    "name": sender.full_name,
                    "email": sender.email,
                    "profile_picture_url": sender.profile_picture_url
                } if sender else None
            }
            result.append(message_data)
        
        return result

    def send_message(
        self, db: Session, *, chat_room_id: int, sender_id: int, content: str, message_type: str = "text"
    ) -> ChatMessage:
        """Send a message in a chat room"""
        # Verify user has access to this chat room
        room = chat_room.get(db, id=chat_room_id)
        if not room or (room.student_id != sender_id and room.tutor_id != sender_id):
            raise ValueError(f"Access denied to chat room {chat_room_id} for user {sender_id}")

        message_create = ChatMessageCreate(
            content=content,
            message_type=message_type,
            chat_room_id=chat_room_id
        )

        # Create message with SENT status (default)
        message = chat_message.create_message(
            db, obj_in=message_create, sender_id=sender_id
        )

        return message

    def mark_messages_as_read(
        self, db: Session, *, chat_room_id: int, user_id: int
    ) -> int:
        """Mark all messages in a chat room as read for a user"""
        # Verify user has access to this chat room
        room = chat_room.get(db, id=chat_room_id)
        if not room or (room.student_id != user_id and room.tutor_id != user_id):
            raise ValueError("Access denied to this chat room")
        
        return chat_message.mark_as_read(
            db, chat_room_id=chat_room_id, user_id=user_id
        )

    def update_user_online_status(
        self, db: Session, *, user_id: int, is_online: bool, connection_id: Optional[str] = None
    ) -> UserOnlineStatus:
        """Update user online status"""
        return user_online_status.update_status(
            db, user_id=user_id, is_online=is_online, connection_id=connection_id
        )

    def get_online_status(
        self, db: Session, *, user_ids: List[int]
    ) -> List[UserOnlineStatus]:
        """Get online status for multiple users"""
        return user_online_status.get_online_users(db, user_ids=user_ids)

    def get_chat_room_participants(
        self, db: Session, *, chat_room_id: int
    ) -> Optional[Dict[str, Any]]:
        """Get participants of a chat room"""
        room = chat_room.get(db, id=chat_room_id)
        if not room:
            return None
        
        student = db.query(User).filter(User.id == room.student_id).first()
        tutor = db.query(User).filter(User.id == room.tutor_id).first()
        
        return {
            "chat_room_id": room.id,
            "student": {
                "id": student.id,
                "name": student.full_name,
                "email": student.email
            } if student else None,
            "tutor": {
                "id": tutor.id,
                "name": tutor.full_name,
                "email": tutor.email
            } if tutor else None
        }

    def mark_message_delivered(
        self, db: Session, *, message_id: int, user_id: int
    ) -> bool:
        """Mark a message as delivered to a specific user"""
        message = chat_message.get(db, id=message_id)
        if not message:
            return False

        # Verify user has access to this message
        room = chat_room.get(db, id=message.chat_room_id)
        if not room or (room.student_id != user_id and room.tutor_id != user_id):
            return False

        # Only mark as delivered if it's currently SENT and not sent by the same user
        if message.delivery_status == MessageDeliveryStatus.SENT and message.sender_id != user_id:
            message.delivery_status = MessageDeliveryStatus.DELIVERED
            message.delivered_at = datetime.utcnow()
            db.add(message)
            db.commit()
            return True

        return False

    def mark_message_read(
        self, db: Session, *, message_id: int, user_id: int
    ) -> bool:
        """Mark a message as read by a specific user"""
        message = chat_message.get(db, id=message_id)
        if not message:
            return False

        # Verify user has access to this message
        room = chat_room.get(db, id=message.chat_room_id)
        if not room or (room.student_id != user_id and room.tutor_id != user_id):
            return False

        # Only mark as read if not sent by the same user
        if message.sender_id != user_id:
            message.delivery_status = MessageDeliveryStatus.READ
            message.read_at = datetime.utcnow()
            message.is_read = True
            db.add(message)
            db.commit()
            return True

        return False

    def get_undelivered_messages_for_user(
        self, db: Session, *, user_id: int
    ) -> List[ChatMessage]:
        """Get all undelivered messages for a user across all their chat rooms"""
        # Get all chat rooms for the user
        user_rooms = chat_room.get_for_user(db, user_id=user_id)
        room_ids = [room.id for room in user_rooms]

        if not room_ids:
            return []

        # Get undelivered messages from these rooms where user is not the sender
        undelivered_messages = db.query(ChatMessage).filter(
            and_(
                ChatMessage.chat_room_id.in_(room_ids),
                ChatMessage.sender_id != user_id,
                ChatMessage.delivery_status == MessageDeliveryStatus.SENT
            )
        ).order_by(ChatMessage.created_at.asc()).all()

        return undelivered_messages

    def get_undelivered_messages_for_chat_room(
        self, db: Session, *, chat_room_id: int, user_id: int
    ) -> List[ChatMessage]:
        """Get undelivered messages for a specific chat room and user"""
        # Verify user has access to this chat room
        room = chat_room.get(db, id=chat_room_id)
        if not room or (room.student_id != user_id and room.tutor_id != user_id):
            return []

        # Get undelivered messages where user is not the sender
        undelivered_messages = db.query(ChatMessage).filter(
            and_(
                ChatMessage.chat_room_id == chat_room_id,
                ChatMessage.sender_id != user_id,
                ChatMessage.delivery_status == MessageDeliveryStatus.SENT
            )
        ).order_by(ChatMessage.created_at.asc()).all()

        return undelivered_messages

    def mark_multiple_messages_delivered(
        self, db: Session, *, message_ids: List[int], user_id: int
    ) -> int:
        """Mark multiple messages as delivered for a user"""
        if not message_ids:
            return 0

        # Get messages that can be marked as delivered
        messages = db.query(ChatMessage).filter(
            and_(
                ChatMessage.id.in_(message_ids),
                ChatMessage.sender_id != user_id,
                ChatMessage.delivery_status == MessageDeliveryStatus.SENT
            )
        ).all()

        # Verify user has access to all these messages
        accessible_messages = []
        for message in messages:
            room = chat_room.get(db, id=message.chat_room_id)
            if room and (room.student_id == user_id or room.tutor_id == user_id):
                accessible_messages.append(message)

        # Mark as delivered
        delivered_count = 0
        for message in accessible_messages:
            message.delivery_status = MessageDeliveryStatus.DELIVERED
            message.delivered_at = datetime.utcnow()
            db.add(message)
            delivered_count += 1

        if delivered_count > 0:
            db.commit()

        return delivered_count

    def get_chat_participant_profile(
        self, db: Session, *, chat_room_id: int, user_id: int, requesting_user_id: int
    ) -> Optional[Dict[str, Any]]:
        """Get profile details for a chat participant"""
        # First verify that the requesting user is part of this chat room
        room = chat_room.get_for_user_and_room(db, user_id=requesting_user_id, chat_room_id=chat_room_id)
        if not room:
            raise ValueError("Access denied to this chat room")

        # Get the participant user
        participant = db.query(User).filter(User.id == user_id).first()
        if not participant:
            return None

        # Build basic profile info
        profile_data = {
            "id": participant.id,
            "full_name": participant.full_name,
            "email": participant.email,
            "role": participant.role.value,
            "profile_picture_url": participant.profile_picture_url,
            "gender": participant.gender.value if participant.gender else None,
            "phone_number": participant.phone_number,
            "level": participant.level,
            "state_of_origin": participant.state_of_origin,
            "created_at": participant.created_at.isoformat(),
        }

        # Add role-specific information
        if participant.role.value == "tutor":
            # Get tutor profile if exists
            from app.models.tutor import TutorProfile
            tutor_profile = db.query(TutorProfile).filter(TutorProfile.user_id == user_id).first()
            if tutor_profile:
                # Parse languages from JSON string
                languages = []
                if tutor_profile.languages:
                    try:
                        languages = json.loads(tutor_profile.languages) if isinstance(tutor_profile.languages, str) else tutor_profile.languages
                        if not isinstance(languages, list):
                            languages = []
                    except (json.JSONDecodeError, TypeError):
                        languages = []

                profile_data.update({
                    "tutor_profile": {
                        "bio": tutor_profile.bio,
                        "experience_years": tutor_profile.experience_years,
                        "hourly_rate": float(tutor_profile.hourly_rate) if tutor_profile.hourly_rate else None,
                        "is_available": tutor_profile.is_available,
                        "preferred_session_type": tutor_profile.preferred_session_type,
                        "location": tutor_profile.location,
                        "languages": languages,
                        "average_rating": float(tutor_profile.average_rating) if tutor_profile.average_rating else None,
                        "total_reviews": tutor_profile.total_reviews,
                        "total_sessions_completed": tutor_profile.total_sessions_completed,
                    }
                })

        # Add department information if available
        if participant.department_id:
            from app.models.course import Department
            department = db.query(Department).filter(Department.id == participant.department_id).first()
            if department:
                profile_data["department"] = {
                    "id": department.id,
                    "name": department.name
                }

        return profile_data


# Create service instance
chat_service = ChatService()
