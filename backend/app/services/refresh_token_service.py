from datetime import datetime, timezone
from typing import Optional

from sqlalchemy.orm import Session

from app.models.refresh_token import RefreshToken
from app.models.user import User
from app.core.security import generate_refresh_token, create_refresh_token_expires


class RefreshTokenService:
    """Service for managing refresh tokens"""
    
    def create_refresh_token(self, db: Session, user_id: int) -> RefreshToken:
        """Create a new refresh token for a user"""
        # Revoke existing refresh tokens for the user (optional - for single device login)
        # self.revoke_user_tokens(db, user_id)
        
        token = generate_refresh_token()
        expires_at = create_refresh_token_expires()
        
        refresh_token = RefreshToken(
            token=token,
            user_id=user_id,
            expires_at=expires_at
        )
        
        db.add(refresh_token)
        db.commit()
        db.refresh(refresh_token)
        
        return refresh_token
    
    def get_refresh_token(self, db: Session, token: str) -> Optional[RefreshToken]:
        """Get refresh token by token string"""
        return db.query(RefreshToken).filter(
            RefreshToken.token == token,
            RefreshToken.is_revoked == False
        ).first()
    
    def revoke_token(self, db: Session, token: str) -> bool:
        """Revoke a refresh token"""
        refresh_token = self.get_refresh_token(db, token)
        if refresh_token:
            refresh_token.is_revoked = True
            db.commit()
            return True
        return False
    
    def revoke_user_tokens(self, db: Session, user_id: int) -> int:
        """Revoke all refresh tokens for a user"""
        count = db.query(RefreshToken).filter(
            RefreshToken.user_id == user_id,
            RefreshToken.is_revoked == False
        ).update({"is_revoked": True})
        db.commit()
        return count
    
    def cleanup_expired_tokens(self, db: Session) -> int:
        """Remove expired refresh tokens from database"""
        now = datetime.now(timezone.utc)
        count = db.query(RefreshToken).filter(
            RefreshToken.expires_at < now
        ).delete()
        db.commit()
        return count
    
    def is_token_valid(self, db: Session, token: str) -> bool:
        """Check if a refresh token is valid"""
        refresh_token = self.get_refresh_token(db, token)
        return refresh_token is not None and refresh_token.is_valid


# Create a singleton instance
refresh_token_service = RefreshTokenService()
