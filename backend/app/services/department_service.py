from typing import Any, Dict, Optional, Union, List

from sqlalchemy.orm import Session

from app import schemas
from app.models.department import Department


def get(db: Session, id: int) -> Optional[Department]:
    return db.query(Department).filter(Department.id == id).first()


def get_by_name(db: Session, name: str) -> Optional[Department]:
    return db.query(Department).filter(Department.name == name).first()


def get_multi(
    db: Session, *, skip: int = 0, limit: int = 100
) -> list[Department]:
    return db.query(Department).offset(skip).limit(limit).all()


def get_by_school(
    db: Session, *, school_id: int, skip: int = 0, limit: int = 100
) -> list[Department]:
    return (
        db.query(Department)
        .filter(Department.school_id == school_id)
        .offset(skip)
        .limit(limit)
        .all()
    )


def create(db: Session, *, obj_in: schemas.DepartmentCreate) -> Department:
    db_obj = Department(
        name=obj_in.name,
        description=obj_in.description,
        is_active=obj_in.is_active,
        school_id=obj_in.school_id,
    )
    db.add(db_obj)
    db.commit()
    db.refresh(db_obj)
    return db_obj


def update(
    db: Session, *, db_obj: Department, obj_in: Union[schemas.DepartmentUpdate, Dict[str, Any]]
) -> Department:
    if isinstance(obj_in, dict):
        update_data = obj_in
    else:
        update_data = obj_in.model_dump(exclude_unset=True)
    for field in update_data:
        if field in update_data:
            setattr(db_obj, field, update_data[field])
    db.add(db_obj)
    db.commit()
    db.refresh(db_obj)
    return db_obj


def delete(db: Session, *, id: int) -> Department:
    obj = db.query(Department).get(id)
    db.delete(obj)
    db.commit()
    return obj
