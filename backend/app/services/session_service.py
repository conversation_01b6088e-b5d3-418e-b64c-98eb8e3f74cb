from typing import Any, Dict, List, Optional, Union
from datetime import datetime

from sqlalchemy.orm import Session, joinedload

from app import schemas
from app.models.session import Session, SessionStatus


def get(db: Session, id: int) -> Optional[Session]:
    return (
        db.query(Session)
        .options(
            joinedload(Session.tutor),
            joinedload(Session.student),
            joinedload(Session.course)
        )
        .filter(Session.id == id)
        .first()
    )


def get_multi(
    db: Session,
    *,
    skip: int = 0,
    limit: int = 100,
    course_id: Optional[int] = None,
    tutor_id: Optional[int] = None,
    status: Optional[SessionStatus] = None
) -> list[Session]:
    query = (
        db.query(Session)
        .options(
            joinedload(Session.tutor),
            joinedload(Session.student),
            joinedload(Session.course)
        )
    )

    if course_id is not None:
        query = query.filter(Session.course_id == course_id)

    if tutor_id is not None:
        query = query.filter(Session.tutor_id == tutor_id)

    if status is not None:
        query = query.filter(Session.status == status)

    return query.offset(skip).limit(limit).all()


def create(db: Session, *, obj_in: schemas.SessionCreate) -> Session:
    # For now, we'll create a course if course_name is provided but course_id is not
    course_id = obj_in.course_id

    # If course_name is provided but no course_id, try to find or create a course
    if obj_in.course_name and not course_id:
        from app.services import course_service
        from app import schemas as course_schemas

        # Try to find existing course by name
        existing_courses = course_service.get_multi(db)
        matching_course = next((c for c in existing_courses if c.name.lower() == obj_in.course_name.lower()), None)

        if matching_course:
            course_id = matching_course.id
        else:
            # Create a new course
            try:
                new_course = course_service.create(db, obj_in=course_schemas.CourseCreate(
                    name=obj_in.course_name,
                    description=f"Course created for tutorial: {obj_in.title}",
                    code=obj_in.course_name.upper().replace(" ", "")[:10],
                    school_id=1,  # Default school ID - you may need to adjust this
                    is_active=True
                ))
                course_id = new_course.id
            except Exception:
                # If course creation fails, we'll proceed without a course_id
                pass

    db_obj = Session(
        title=obj_in.title,
        description=obj_in.description,
        session_type=obj_in.session_type,
        status=obj_in.status,
        start_time=obj_in.start_time,
        end_time=obj_in.end_time,
        location=obj_in.location,
        video_link=obj_in.video_link,
        max_students=obj_in.max_students,
        is_recurring=obj_in.is_recurring,
        recurrence_pattern=obj_in.recurrence_pattern,
        course_id=course_id,
        tutor_id=obj_in.tutor_id,
    )
    db.add(db_obj)
    db.commit()
    db.refresh(db_obj)
    return db_obj


def update(
    db: Session, *, db_obj: Session, obj_in: Union[schemas.SessionUpdate, Dict[str, Any]]
) -> Session:
    if isinstance(obj_in, dict):
        update_data = obj_in
    else:
        update_data = obj_in.model_dump(exclude_unset=True)
    for field in update_data:
        if field in update_data:
            setattr(db_obj, field, update_data[field])
    db.add(db_obj)
    db.commit()
    db.refresh(db_obj)
    return db_obj


def delete(db: Session, *, id: int) -> Session:
    obj = db.query(Session).get(id)
    db.delete(obj)
    db.commit()
    return obj


def update_expired_sessions(db: Session) -> int:
    """
    Update sessions that have passed their end time to 'completed' status.
    Returns the number of sessions updated.
    """
    current_time = datetime.utcnow()

    # Find sessions that have ended but are still marked as 'scheduled' or 'in_progress'
    expired_sessions = db.query(Session).filter(
        Session.end_time < current_time,
        Session.status.in_([SessionStatus.SCHEDULED, SessionStatus.IN_PROGRESS])
    ).all()

    updated_count = 0
    for session in expired_sessions:
        session.status = SessionStatus.COMPLETED
        updated_count += 1

    if updated_count > 0:
        db.commit()

    return updated_count


def get_by_tutor(
    db: Session, *, tutor_id: int, skip: int = 0, limit: int = 100
) -> List[Session]:
    return (
        db.query(Session)
        .filter(Session.tutor_id == tutor_id)
        .offset(skip)
        .limit(limit)
        .all()
    )


def get_by_student(
    db: Session, *, student_id: int, skip: int = 0, limit: int = 100
) -> List[Session]:
    return (
        db.query(Session)
        .filter(Session.student_id == student_id)
        .offset(skip)
        .limit(limit)
        .all()
    )
