import logging
import numpy as np
import pandas as pd
import joblib
import os
from datetime import datetime, timezone, timedelta
from typing import Dict, List, Optional, Tuple, Any
from pathlib import Path
from sqlalchemy.orm import Session
from sqlalchemy import func, desc, and_, or_

# ML imports with fallback
try:
    from sklearn.ensemble import RandomForestRegressor, GradientBoostingRegressor
    from sklearn.linear_model import LogisticRegression, LinearRegression
    from sklearn.model_selection import train_test_split, cross_val_score, GridSearchCV
    from sklearn.preprocessing import StandardScaler, LabelEncoder
    from sklearn.metrics import mean_squared_error, mean_absolute_error, r2_score, accuracy_score, precision_score, recall_score, f1_score
    from sklearn.neighbors import NearestNeighbors
    from sklearn.decomposition import TruncatedSVD
    from sklearn.metrics.pairwise import cosine_similarity
    SKLEARN_AVAILABLE = True
except ImportError:
    SKLEARN_AVAILABLE = False
    logging.warning("scikit-learn not available. ML training will be limited.")

from app.models.ml_recommendations import (
    UserBehaviorEvent, UserFeatureVector, ContentFeatureVector,
    MLRecommendation, MLModelMetadata, UserLearningSession
)
from app.models.question import Question
from app.models.student_progress import StudentQuestionAttempt
from app.models.user import User

logger = logging.getLogger(__name__)


class MLModelTrainer:
    """Machine Learning model training and management service"""
    
    def __init__(self):
        self.logger = logger
        self.models_dir = Path("ml_models")
        self.models_dir.mkdir(exist_ok=True)
        
        # Model configurations
        self.model_configs = {
            'collaborative_filtering': {
                'algorithm': 'matrix_factorization',
                'hyperparameters': {
                    'n_components': 50,
                    'random_state': 42
                }
            },
            'content_based': {
                'algorithm': 'random_forest',
                'hyperparameters': {
                    'n_estimators': 100,
                    'max_depth': 10,
                    'random_state': 42
                }
            },
            'performance_prediction': {
                'algorithm': 'gradient_boosting',
                'hyperparameters': {
                    'n_estimators': 100,
                    'learning_rate': 0.1,
                    'max_depth': 6,
                    'random_state': 42
                }
            },
            'user_similarity': {
                'algorithm': 'nearest_neighbors',
                'hyperparameters': {
                    'n_neighbors': 10,
                    'metric': 'cosine'
                }
            }
        }
        
        # Trained models storage
        self.trained_models = {}
        self.model_scalers = {}
        
    async def train_collaborative_filtering_model(self, db: Session) -> Optional[MLModelMetadata]:
        """Train collaborative filtering model using matrix factorization"""
        if not SKLEARN_AVAILABLE:
            self.logger.error("scikit-learn not available for training")
            return None
        
        try:
            self.logger.info("Starting collaborative filtering model training")
            
            # Prepare training data
            training_data = await self._prepare_collaborative_data(db)
            if training_data is None or len(training_data) < 100:
                self.logger.warning("Insufficient data for collaborative filtering training")
                return None
            
            # Create user-item interaction matrix
            user_item_matrix, user_mapping, item_mapping = self._create_interaction_matrix(training_data)
            
            # Train matrix factorization model
            config = self.model_configs['collaborative_filtering']
            svd = TruncatedSVD(**config['hyperparameters'])
            
            # Fit the model
            start_time = datetime.now()
            user_factors = svd.fit_transform(user_item_matrix)
            item_factors = svd.components_.T
            training_duration = (datetime.now() - start_time).total_seconds()
            
            # Evaluate model
            train_predictions = np.dot(user_factors, item_factors.T)
            mse = mean_squared_error(user_item_matrix.toarray(), train_predictions)
            r2 = r2_score(user_item_matrix.toarray().flatten(), train_predictions.flatten())
            
            # Save model
            model_name = "collaborative_filtering_v1"
            model_path = self.models_dir / f"{model_name}.joblib"
            
            model_data = {
                'svd_model': svd,
                'user_factors': user_factors,
                'item_factors': item_factors,
                'user_mapping': user_mapping,
                'item_mapping': item_mapping
            }
            
            joblib.dump(model_data, model_path)
            
            # Create metadata record
            metadata = MLModelMetadata(
                model_name=model_name,
                model_type='collaborative_filtering',
                version='1.0',
                accuracy=float(r2),
                training_data_size=len(training_data),
                training_duration_seconds=training_duration,
                hyperparameters=config['hyperparameters'],
                is_active=True,
                is_deployed=True,
                model_file_path=str(model_path)
            )
            
            db.add(metadata)
            db.commit()
            db.refresh(metadata)
            
            # Store in memory
            self.trained_models['collaborative_filtering'] = model_data
            
            self.logger.info(f"Collaborative filtering model trained successfully. R² score: {r2:.4f}")
            return metadata
            
        except Exception as e:
            self.logger.error(f"Error training collaborative filtering model: {str(e)}")
            return None
    
    async def train_content_based_model(self, db: Session) -> Optional[MLModelMetadata]:
        """Train content-based recommendation model"""
        if not SKLEARN_AVAILABLE:
            self.logger.error("scikit-learn not available for training")
            return None
        
        try:
            self.logger.info("Starting content-based model training")
            
            # Prepare training data
            training_data = await self._prepare_content_based_data(db)
            if training_data is None or len(training_data) < 50:
                self.logger.warning("Insufficient data for content-based training")
                return None
            
            # Prepare features and targets
            X, y = self._prepare_content_features_and_targets(training_data)
            
            # Split data
            X_train, X_test, y_train, y_test = train_test_split(
                X, y, test_size=0.2, random_state=42
            )
            
            # Scale features
            scaler = StandardScaler()
            X_train_scaled = scaler.fit_transform(X_train)
            X_test_scaled = scaler.transform(X_test)
            
            # Train model
            config = self.model_configs['content_based']
            model = RandomForestRegressor(**config['hyperparameters'])
            
            start_time = datetime.now()
            model.fit(X_train_scaled, y_train)
            training_duration = (datetime.now() - start_time).total_seconds()
            
            # Evaluate model
            train_predictions = model.predict(X_train_scaled)
            test_predictions = model.predict(X_test_scaled)
            
            train_r2 = r2_score(y_train, train_predictions)
            test_r2 = r2_score(y_test, test_predictions)
            test_mse = mean_squared_error(y_test, test_predictions)
            
            # Save model
            model_name = "content_based_v1"
            model_path = self.models_dir / f"{model_name}.joblib"
            scaler_path = self.models_dir / f"{model_name}_scaler.joblib"
            
            joblib.dump(model, model_path)
            joblib.dump(scaler, scaler_path)
            
            # Create metadata record
            metadata = MLModelMetadata(
                model_name=model_name,
                model_type='content_based',
                version='1.0',
                accuracy=float(test_r2),
                precision=None,  # Not applicable for regression
                recall=None,     # Not applicable for regression
                f1_score=None,   # Not applicable for regression
                training_data_size=len(training_data),
                training_duration_seconds=training_duration,
                hyperparameters=config['hyperparameters'],
                is_active=True,
                is_deployed=True,
                model_file_path=str(model_path),
                feature_scaler_path=str(scaler_path)
            )
            
            db.add(metadata)
            db.commit()
            db.refresh(metadata)
            
            # Store in memory
            self.trained_models['content_based'] = model
            self.model_scalers['content_based'] = scaler
            
            self.logger.info(f"Content-based model trained successfully. Test R² score: {test_r2:.4f}")
            return metadata
            
        except Exception as e:
            self.logger.error(f"Error training content-based model: {str(e)}")
            return None
    
    async def train_performance_prediction_model(self, db: Session) -> Optional[MLModelMetadata]:
        """Train model to predict user performance on content"""
        if not SKLEARN_AVAILABLE:
            self.logger.error("scikit-learn not available for training")
            return None
        
        try:
            self.logger.info("Starting performance prediction model training")
            
            # Prepare training data
            training_data = await self._prepare_performance_data(db)
            if training_data is None or len(training_data) < 100:
                self.logger.warning("Insufficient data for performance prediction training")
                return None
            
            # Prepare features and targets
            X, y = self._prepare_performance_features_and_targets(training_data)
            
            # Split data
            X_train, X_test, y_train, y_test = train_test_split(
                X, y, test_size=0.2, random_state=42, stratify=y
            )
            
            # Scale features
            scaler = StandardScaler()
            X_train_scaled = scaler.fit_transform(X_train)
            X_test_scaled = scaler.transform(X_test)
            
            # Train model
            config = self.model_configs['performance_prediction']
            model = GradientBoostingRegressor(**config['hyperparameters'])
            
            start_time = datetime.now()
            model.fit(X_train_scaled, y_train)
            training_duration = (datetime.now() - start_time).total_seconds()
            
            # Evaluate model
            train_predictions = model.predict(X_train_scaled)
            test_predictions = model.predict(X_test_scaled)
            
            train_r2 = r2_score(y_train, train_predictions)
            test_r2 = r2_score(y_test, test_predictions)
            test_mae = mean_absolute_error(y_test, test_predictions)
            
            # Save model
            model_name = "performance_prediction_v1"
            model_path = self.models_dir / f"{model_name}.joblib"
            scaler_path = self.models_dir / f"{model_name}_scaler.joblib"
            
            joblib.dump(model, model_path)
            joblib.dump(scaler, scaler_path)
            
            # Create metadata record
            metadata = MLModelMetadata(
                model_name=model_name,
                model_type='performance_prediction',
                version='1.0',
                accuracy=float(test_r2),
                training_data_size=len(training_data),
                training_duration_seconds=training_duration,
                hyperparameters=config['hyperparameters'],
                is_active=True,
                is_deployed=True,
                model_file_path=str(model_path),
                feature_scaler_path=str(scaler_path)
            )
            
            db.add(metadata)
            db.commit()
            db.refresh(metadata)
            
            # Store in memory
            self.trained_models['performance_prediction'] = model
            self.model_scalers['performance_prediction'] = scaler
            
            self.logger.info(f"Performance prediction model trained successfully. Test R² score: {test_r2:.4f}")
            return metadata
            
        except Exception as e:
            self.logger.error(f"Error training performance prediction model: {str(e)}")
            return None
    
    async def train_user_similarity_model(self, db: Session) -> Optional[MLModelMetadata]:
        """Train user similarity model for finding similar users"""
        if not SKLEARN_AVAILABLE:
            self.logger.error("scikit-learn not available for training")
            return None
        
        try:
            self.logger.info("Starting user similarity model training")
            
            # Get user feature vectors
            user_features = db.query(UserFeatureVector).all()
            if len(user_features) < 10:
                self.logger.warning("Insufficient users for similarity model training")
                return None
            
            # Prepare feature matrix
            feature_matrix = []
            user_ids = []
            
            for user_feature in user_features:
                if user_feature.feature_vector and len(user_feature.feature_vector) > 0:
                    feature_matrix.append(user_feature.feature_vector)
                    user_ids.append(user_feature.user_id)
            
            if len(feature_matrix) < 10:
                self.logger.warning("Insufficient feature vectors for similarity model")
                return None
            
            feature_matrix = np.array(feature_matrix)
            
            # Train nearest neighbors model
            config = self.model_configs['user_similarity']
            model = NearestNeighbors(**config['hyperparameters'])
            
            start_time = datetime.now()
            model.fit(feature_matrix)
            training_duration = (datetime.now() - start_time).total_seconds()
            
            # Save model
            model_name = "user_similarity_v1"
            model_path = self.models_dir / f"{model_name}.joblib"
            
            model_data = {
                'model': model,
                'user_ids': user_ids,
                'feature_matrix': feature_matrix
            }
            
            joblib.dump(model_data, model_path)
            
            # Create metadata record
            metadata = MLModelMetadata(
                model_name=model_name,
                model_type='user_similarity',
                version='1.0',
                training_data_size=len(user_features),
                training_duration_seconds=training_duration,
                hyperparameters=config['hyperparameters'],
                is_active=True,
                is_deployed=True,
                model_file_path=str(model_path)
            )
            
            db.add(metadata)
            db.commit()
            db.refresh(metadata)
            
            # Store in memory
            self.trained_models['user_similarity'] = model_data
            
            self.logger.info(f"User similarity model trained successfully with {len(user_features)} users")
            return metadata
            
        except Exception as e:
            self.logger.error(f"Error training user similarity model: {str(e)}")
            return None
    
    async def _prepare_collaborative_data(self, db: Session) -> Optional[pd.DataFrame]:
        """Prepare data for collaborative filtering training"""
        try:
            # Get user behavior events for MCQ attempts
            events = db.query(UserBehaviorEvent).filter(
                UserBehaviorEvent.event_type == 'mcq_attempt',
                UserBehaviorEvent.is_correct.isnot(None)
            ).all()
            
            if len(events) < 100:
                return None
            
            # Convert to DataFrame
            data = []
            for event in events:
                data.append({
                    'user_id': event.user_id,
                    'content_id': event.content_id,
                    'rating': 1.0 if event.is_correct else 0.0,
                    'response_time': event.response_time_seconds or 60.0,
                    'timestamp': event.created_at
                })
            
            df = pd.DataFrame(data)
            
            # Filter users and items with minimum interactions
            user_counts = df['user_id'].value_counts()
            item_counts = df['content_id'].value_counts()
            
            valid_users = user_counts[user_counts >= 5].index
            valid_items = item_counts[item_counts >= 3].index
            
            df_filtered = df[
                (df['user_id'].isin(valid_users)) & 
                (df['content_id'].isin(valid_items))
            ]
            
            return df_filtered if len(df_filtered) >= 50 else None
            
        except Exception as e:
            self.logger.error(f"Error preparing collaborative data: {str(e)}")
            return None
    
    def _create_interaction_matrix(self, df: pd.DataFrame) -> Tuple[Any, Dict, Dict]:
        """Create user-item interaction matrix"""
        from scipy.sparse import csr_matrix
        
        # Create mappings
        unique_users = df['user_id'].unique()
        unique_items = df['content_id'].unique()
        
        user_mapping = {user_id: idx for idx, user_id in enumerate(unique_users)}
        item_mapping = {item_id: idx for idx, item_id in enumerate(unique_items)}
        
        # Create matrix
        rows = [user_mapping[user_id] for user_id in df['user_id']]
        cols = [item_mapping[item_id] for item_id in df['content_id']]
        data = df['rating'].values
        
        matrix = csr_matrix(
            (data, (rows, cols)), 
            shape=(len(unique_users), len(unique_items))
        )
        
        return matrix, user_mapping, item_mapping
    
    async def _prepare_content_based_data(self, db: Session) -> Optional[pd.DataFrame]:
        """Prepare data for content-based model training"""
        try:
            # Get user interactions with content features
            query = db.query(
                UserBehaviorEvent.user_id,
                UserBehaviorEvent.content_id,
                UserBehaviorEvent.is_correct,
                UserBehaviorEvent.response_time_seconds,
                UserBehaviorEvent.topic,
                UserBehaviorEvent.difficulty,
                UserFeatureVector.accuracy_rate,
                UserFeatureVector.avg_response_time,
                UserFeatureVector.preferred_difficulty,
                ContentFeatureVector.difficulty_score,
                ContentFeatureVector.success_rate,
                ContentFeatureVector.text_complexity
            ).join(
                UserFeatureVector,
                UserBehaviorEvent.user_id == UserFeatureVector.user_id
            ).join(
                ContentFeatureVector,
                and_(
                    UserBehaviorEvent.content_id == ContentFeatureVector.content_id,
                    UserBehaviorEvent.content_type == ContentFeatureVector.content_type
                )
            ).filter(
                UserBehaviorEvent.event_type == 'mcq_attempt',
                UserBehaviorEvent.is_correct.isnot(None)
            ).all()
            
            if len(query) < 50:
                return None
            
            # Convert to DataFrame
            data = []
            for row in query:
                data.append({
                    'user_id': row.user_id,
                    'content_id': row.content_id,
                    'is_correct': row.is_correct,
                    'response_time': row.response_time_seconds or 60.0,
                    'user_accuracy': row.accuracy_rate or 0.5,
                    'user_avg_time': row.avg_response_time or 60.0,
                    'user_pref_difficulty': row.preferred_difficulty or 'medium',
                    'content_difficulty': row.difficulty_score or 0.5,
                    'content_success_rate': row.success_rate or 0.5,
                    'content_complexity': row.text_complexity or 0.5
                })
            
            return pd.DataFrame(data)
            
        except Exception as e:
            self.logger.error(f"Error preparing content-based data: {str(e)}")
            return None
    
    def _prepare_content_features_and_targets(self, df: pd.DataFrame) -> Tuple[np.ndarray, np.ndarray]:
        """Prepare features and targets for content-based model"""
        # Features: user characteristics + content characteristics
        features = []
        targets = []
        
        for _, row in df.iterrows():
            feature_vector = [
                row['user_accuracy'],
                row['user_avg_time'] / 100.0,  # Normalize
                1.0 if row['user_pref_difficulty'] == 'easy' else 0.0,
                1.0 if row['user_pref_difficulty'] == 'medium' else 0.0,
                1.0 if row['user_pref_difficulty'] == 'hard' else 0.0,
                row['content_difficulty'],
                row['content_success_rate'],
                row['content_complexity']
            ]
            
            features.append(feature_vector)
            targets.append(1.0 if row['is_correct'] else 0.0)
        
        return np.array(features), np.array(targets)
    
    async def _prepare_performance_data(self, db: Session) -> Optional[pd.DataFrame]:
        """Prepare data for performance prediction model"""
        # Similar to content-based but focused on predicting performance
        return await self._prepare_content_based_data(db)
    
    def _prepare_performance_features_and_targets(self, df: pd.DataFrame) -> Tuple[np.ndarray, np.ndarray]:
        """Prepare features and targets for performance prediction"""
        return self._prepare_content_features_and_targets(df)
    
    async def retrain_all_models(self, db: Session) -> Dict[str, Optional[MLModelMetadata]]:
        """Retrain all ML models"""
        results = {}
        
        self.logger.info("Starting retraining of all ML models")
        
        # Train each model type
        results['collaborative_filtering'] = await self.train_collaborative_filtering_model(db)
        results['content_based'] = await self.train_content_based_model(db)
        results['performance_prediction'] = await self.train_performance_prediction_model(db)
        results['user_similarity'] = await self.train_user_similarity_model(db)
        
        # Update model deployment status
        for model_name, metadata in results.items():
            if metadata:
                # Deactivate old models
                db.query(MLModelMetadata).filter(
                    MLModelMetadata.model_type == model_name,
                    MLModelMetadata.id != metadata.id
                ).update({'is_active': False, 'is_deployed': False})
                
                # Update deployment timestamp
                metadata.deployed_at = datetime.now(timezone.utc)
                metadata.last_retrained = datetime.now(timezone.utc)
        
        db.commit()
        
        self.logger.info("Model retraining completed")
        return results
    
    def load_trained_models(self, db: Session):
        """Load trained models from disk"""
        try:
            # Get active models from database
            active_models = db.query(MLModelMetadata).filter(
                MLModelMetadata.is_active == True,
                MLModelMetadata.is_deployed == True
            ).all()
            
            for model_metadata in active_models:
                if model_metadata.model_file_path and os.path.exists(model_metadata.model_file_path):
                    try:
                        model_data = joblib.load(model_metadata.model_file_path)
                        self.trained_models[model_metadata.model_type] = model_data
                        
                        # Load scaler if available
                        if model_metadata.feature_scaler_path and os.path.exists(model_metadata.feature_scaler_path):
                            scaler = joblib.load(model_metadata.feature_scaler_path)
                            self.model_scalers[model_metadata.model_type] = scaler
                        
                        self.logger.info(f"Loaded model: {model_metadata.model_name}")
                    except Exception as e:
                        self.logger.error(f"Error loading model {model_metadata.model_name}: {str(e)}")
            
        except Exception as e:
            self.logger.error(f"Error loading trained models: {str(e)}")


# Create service instance
ml_model_trainer = MLModelTrainer()
