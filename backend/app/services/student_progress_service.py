from datetime import datetime, timezone
from typing import List, Optional, Dict, Any

from sqlalchemy.orm import Session

from app import schemas
from app.models.student_progress import (
    StudentQuestionAttempt, StudentBookmark, StudentExam, StudentExamAttempt
)
from app.services import gamification_service


# Question Attempt functions
def create_question_attempt(
    db: Session, *, obj_in: schemas.StudentQuestionAttemptCreate
) -> StudentQuestionAttempt:
    db_obj = StudentQuestionAttempt(
        student_id=obj_in.student_id,
        question_id=obj_in.question_id,
        is_correct=obj_in.is_correct,
        selected_answer=obj_in.selected_answer,
    )
    db.add(db_obj)
    db.commit()
    db.refresh(db_obj)

    # Update user streak
    gamification_service.update_streak(db, user_id=obj_in.student_id)

    # Award points for practice
    points = gamification_service.POINTS["CORRECT_ANSWER"] if obj_in.is_correct else 0
    if points > 0:
        gamification_service.award_points(
            db=db,
            user_id=obj_in.student_id,
            points=points,
            description="Answered a question correctly",
            transaction_type="practice_correct_answer",
            reference_id=db_obj.id
        )

    # Check for practice achievements
    gamification_service.check_for_practice_achievements(db, user_id=obj_in.student_id)

    return db_obj


def get_student_question_attempts(
    db: Session, *, student_id: int, question_id: Optional[int] = None
) -> List[StudentQuestionAttempt]:
    query = db.query(StudentQuestionAttempt).filter(
        StudentQuestionAttempt.student_id == student_id
    )

    if question_id:
        query = query.filter(StudentQuestionAttempt.question_id == question_id)

    return query.order_by(StudentQuestionAttempt.attempt_time.desc()).all()


def get_student_course_attempts(
    db: Session, *, student_id: int, course_id: int
) -> List[StudentQuestionAttempt]:
    from app.models.question import Question
    return (
        db.query(StudentQuestionAttempt)
        .join(Question, StudentQuestionAttempt.question_id == Question.id)
        .filter(
            StudentQuestionAttempt.student_id == student_id,
            Question.course_id == course_id
        )
        .order_by(StudentQuestionAttempt.attempt_time.desc())
        .all()
    )


# Bookmark functions
def create_bookmark(
    db: Session, *, obj_in: schemas.StudentBookmarkCreate
) -> StudentBookmark:
    db_obj = StudentBookmark(
        student_id=obj_in.student_id,
        question_id=obj_in.question_id,
        notes=obj_in.notes,
    )
    db.add(db_obj)
    db.commit()
    db.refresh(db_obj)
    return db_obj


def get_bookmark(db: Session, *, id: int) -> Optional[StudentBookmark]:
    return db.query(StudentBookmark).filter(StudentBookmark.id == id).first()


def get_student_bookmarks(
    db: Session, *, student_id: int, course_id: Optional[int] = None
) -> List[StudentBookmark]:
    query = db.query(StudentBookmark).filter(
        StudentBookmark.student_id == student_id
    )

    if course_id:
        from app.models.question import Question
        query = query.join(Question, StudentBookmark.question_id == Question.id).filter(
            Question.course_id == course_id
        )

    return query.order_by(StudentBookmark.created_at.desc()).all()


def update_bookmark(
    db: Session, *, db_obj: StudentBookmark, obj_in: schemas.StudentBookmarkUpdate
) -> StudentBookmark:
    if obj_in.notes is not None:
        db_obj.notes = obj_in.notes

    db.add(db_obj)
    db.commit()
    db.refresh(db_obj)
    return db_obj


def delete_bookmark(db: Session, *, id: int) -> StudentBookmark:
    obj = db.query(StudentBookmark).get(id)
    db.delete(obj)
    db.commit()
    return obj


# Exam functions
def create_exam(
    db: Session, *, obj_in: schemas.StudentExamCreate
) -> StudentExam:
    # Create the exam object with all fields from the input
    db_obj = StudentExam(
        student_id=obj_in.student_id,
        course_id=obj_in.course_id,
        total_questions=obj_in.total_questions,
        score=obj_in.score,
        correct_answers=obj_in.correct_answers,
        end_time=obj_in.end_time,
    )

    # Add note-generated content fields if present and if the columns exist
    try:
        if obj_in.is_note_generated:
            # Try to set the fields, but catch any exceptions if the columns don't exist
            try:
                db_obj.is_note_generated = True
            except Exception as e:
                print(f"Warning: Could not set is_note_generated field: {str(e)}")

            try:
                db_obj.note_job_id = obj_in.note_job_id
            except Exception as e:
                print(f"Warning: Could not set note_job_id field: {str(e)}")

            try:
                db_obj.course_name = obj_in.course_name
            except Exception as e:
                print(f"Warning: Could not set course_name field: {str(e)}")
    except Exception as e:
        print(f"Warning: Could not set note-generated fields: {str(e)}")
    db.add(db_obj)
    db.commit()
    db.refresh(db_obj)

    # Update user streak
    gamification_service.update_streak(db, user_id=obj_in.student_id)

    # Award points for exam completion
    base_points = gamification_service.POINTS["EXAM_COMPLETION"]

    # Add bonus points based on score
    score_bonus = 0
    if obj_in.score is not None:
        score_bonus = int(obj_in.score / 10)  # 0-10 bonus points based on score percentage

    # Perfect score bonus
    perfect_bonus = 0
    if obj_in.score == 100.0:
        perfect_bonus = gamification_service.POINTS["PERFECT_SCORE"]

    total_points = base_points + score_bonus + perfect_bonus

    gamification_service.award_points(
        db=db,
        user_id=obj_in.student_id,
        points=total_points,
        description=f"Completed exam with score {obj_in.score}%",
        transaction_type="exam_completion",
        reference_id=db_obj.id
    )

    # Check for exam achievements
    gamification_service.check_for_exam_achievements(db, user_id=obj_in.student_id, exam_id=db_obj.id)

    return db_obj


def get_exam(db: Session, *, id: int) -> Optional[StudentExam]:
    return db.query(StudentExam).filter(StudentExam.id == id).first()


def get_student_exams(
    db: Session, *, student_id: int, course_id: Optional[int] = None
) -> List[StudentExam]:
    query = db.query(StudentExam).filter(
        StudentExam.student_id == student_id
    )

    if course_id:
        query = query.filter(StudentExam.course_id == course_id)

    return query.order_by(StudentExam.start_time.desc()).all()


def update_exam(
    db: Session, *, db_obj: StudentExam, obj_in: schemas.StudentExamUpdate
) -> StudentExam:
    # Store original state to check if exam is being completed
    was_completed = db_obj.end_time is not None

    if obj_in.score is not None:
        db_obj.score = obj_in.score
    if obj_in.correct_answers is not None:
        db_obj.correct_answers = obj_in.correct_answers
    if obj_in.end_time is not None:
        db_obj.end_time = obj_in.end_time
    else:
        db_obj.end_time = datetime.now(timezone.utc)

    db.add(db_obj)
    db.commit()
    db.refresh(db_obj)

    # If the exam is being completed (wasn't completed before but now has an end_time)
    if not was_completed and db_obj.end_time is not None and db_obj.score is not None:
        # Update user streak
        gamification_service.update_streak(db, user_id=db_obj.student_id)

        # Award points for exam completion
        base_points = gamification_service.POINTS["EXAM_COMPLETION"]

        # Add bonus points based on score
        score_bonus = int(db_obj.score / 10)  # 0-10 bonus points based on score percentage

        # Perfect score bonus
        perfect_bonus = 0
        if db_obj.score == 100.0:
            perfect_bonus = gamification_service.POINTS["PERFECT_SCORE"]

        total_points = base_points + score_bonus + perfect_bonus

        gamification_service.award_points(
            db=db,
            user_id=db_obj.student_id,
            points=total_points,
            description=f"Completed exam with score {db_obj.score}%",
            transaction_type="exam_completion",
            reference_id=db_obj.id
        )

        # Check for exam achievements
        gamification_service.check_for_exam_achievements(db, user_id=db_obj.student_id, exam_id=db_obj.id)

    return db_obj


def create_exam_attempt(
    db: Session, *, obj_in: schemas.StudentExamAttemptCreate
) -> StudentExamAttempt:
    print(f"Creating exam attempt in service: {obj_in}")
    try:
        db_obj = StudentExamAttempt(
            exam_id=obj_in.exam_id,
            question_id=obj_in.question_id,
            is_correct=obj_in.is_correct,
            selected_answer=obj_in.selected_answer,
            time_spent_seconds=obj_in.time_spent_seconds,
        )
        print(f"Created StudentExamAttempt object: {db_obj}")
        db.add(db_obj)
        print(f"Added to session")
        db.commit()
        print(f"Committed to database")
        db.refresh(db_obj)
        print(f"Refreshed object, id: {db_obj.id}")
        return db_obj
    except Exception as e:
        print(f"Error creating exam attempt: {str(e)}")
        db.rollback()
        raise


def get_exam_with_attempts(db: Session, *, id: int) -> Optional[schemas.StudentExamWithAttempts]:
    print(f"Service: get_exam_with_attempts for id={id}")

    exam = db.query(StudentExam).filter(StudentExam.id == id).first()
    if not exam:
        print(f"Service: Exam not found with id={id}")
        return None

    print(f"Service: Found exam with id={exam.id}, student_id={exam.student_id}, course_id={exam.course_id}")
    print(f"Service: Exam details: score={exam.score}, correct_answers={exam.correct_answers}, total_questions={exam.total_questions}")

    attempts = db.query(StudentExamAttempt).filter(
        StudentExamAttempt.exam_id == id
    ).all()

    print(f"Service: Found {len(attempts)} attempts for exam id={id}")
    for i, attempt in enumerate(attempts):
        print(f"Service: Attempt {i+1}: question_id={attempt.question_id}, is_correct={attempt.is_correct}")

    result = schemas.StudentExamWithAttempts(
        **{k: v for k, v in exam.__dict__.items() if not k.startswith('_')},
        attempts=attempts
    )

    print(f"Service: Returning exam with {len(result.attempts)} attempts")
    return result


def get_exam_topic_analysis(db: Session, *, exam_id: int) -> Dict[str, Any]:
    """
    Get topic-based analysis for an exam.
    Returns a dictionary with topic statistics including:
    - correct_count: number of correct answers for the topic
    - total_count: total number of questions for the topic
    - score: percentage score for the topic
    - avg_time_spent: average time spent on questions for this topic (in seconds)
    """
    from app.models.question import Question

    # Get the exam with attempts
    exam = get_exam_with_attempts(db, id=exam_id)
    if not exam:
        return {}

    # Get all question IDs from the attempts
    question_ids = [attempt.question_id for attempt in exam.attempts]

    # Get the questions with their topics
    questions = db.query(Question).filter(Question.id.in_(question_ids)).all()

    # Create a mapping of question_id to topic
    question_topic_map = {q.id: q.topic if q.topic else "Uncategorized" for q in questions}

    # Initialize topic analysis
    topic_analysis = {}

    # Process each attempt
    for attempt in exam.attempts:
        question_id = attempt.question_id
        topic = question_topic_map.get(question_id, "Uncategorized")

        # Initialize topic data if not exists
        if topic not in topic_analysis:
            topic_analysis[topic] = {
                "correct_count": 0,
                "total_count": 0,
                "time_spent_total": 0,
                "score": 0.0,
                "avg_time_spent": 0
            }

        # Update topic statistics
        topic_analysis[topic]["total_count"] += 1
        if attempt.is_correct:
            topic_analysis[topic]["correct_count"] += 1

        # Add time spent if available
        if hasattr(attempt, 'time_spent_seconds') and attempt.time_spent_seconds:
            topic_analysis[topic]["time_spent_total"] += attempt.time_spent_seconds

    # Calculate percentages and averages
    for topic, data in topic_analysis.items():
        if data["total_count"] > 0:
            data["score"] = (data["correct_count"] / data["total_count"]) * 100
            if data["time_spent_total"] > 0:
                data["avg_time_spent"] = data["time_spent_total"] / data["total_count"]

    return topic_analysis


def get_course_topic_analysis(db: Session, *, student_id: int, course_id: int) -> Dict[str, Any]:
    """
    Get topic-based analysis for all exams in a course for a student.
    Returns a dictionary with topic statistics across all exams.
    """
    # Get all exams for the student in the course
    exams = get_student_exams(db, student_id=student_id, course_id=course_id)
    if not exams:
        return {}

    # Initialize topic analysis
    topic_analysis = {}

    # Process each exam
    for exam in exams:
        exam_analysis = get_exam_topic_analysis(db, exam_id=exam.id)

        # Merge exam analysis into overall analysis
        for topic, data in exam_analysis.items():
            if topic not in topic_analysis:
                topic_analysis[topic] = {
                    "correct_count": 0,
                    "total_count": 0,
                    "time_spent_total": 0,
                    "exams_count": 0,
                    "score": 0.0,
                    "avg_time_spent": 0
                }

            # Update topic statistics
            topic_analysis[topic]["correct_count"] += data["correct_count"]
            topic_analysis[topic]["total_count"] += data["total_count"]
            topic_analysis[topic]["time_spent_total"] += data.get("time_spent_total", 0)
            topic_analysis[topic]["exams_count"] += 1

    # Calculate percentages and averages
    for topic, data in topic_analysis.items():
        if data["total_count"] > 0:
            data["score"] = (data["correct_count"] / data["total_count"]) * 100
            if data["time_spent_total"] > 0:
                data["avg_time_spent"] = data["time_spent_total"] / data["total_count"]

    return topic_analysis


def get_user_mcq_stats(db: Session, *, user_id: int) -> Dict[str, Any]:
    """
    Get MCQ statistics for a user including total exams, average score, and practice questions.
    """
    from sqlalchemy import func
    from app.models.student_progress import StudentExam, StudentQuestionAttempt

    # Get exam statistics
    exam_stats = db.query(
        func.count(StudentExam.id).label('total_exams'),
        func.avg(StudentExam.score).label('average_score')
    ).filter(
        StudentExam.student_id == user_id,
        StudentExam.score.isnot(None)  # Only completed exams
    ).first()

    # Get practice question statistics
    practice_stats = db.query(
        func.count(StudentQuestionAttempt.id).label('total_practice_questions')
    ).filter(
        StudentQuestionAttempt.student_id == user_id
    ).first()

    return {
        'total_exams': exam_stats.total_exams or 0,
        'average_score': round(exam_stats.average_score or 0, 1),
        'total_practice_questions': practice_stats.total_practice_questions or 0
    }


def get_user_performance_data(db: Session, *, user_id: int) -> Dict[str, Any]:
    """
    Get performance data for a user including correct/incorrect answers and exam scores.
    """
    from sqlalchemy import func
    from app.models.student_progress import StudentExam, StudentQuestionAttempt

    # Get practice question statistics
    practice_correct = db.query(func.count(StudentQuestionAttempt.id)).filter(
        StudentQuestionAttempt.student_id == user_id,
        StudentQuestionAttempt.is_correct == True
    ).scalar() or 0

    practice_incorrect = db.query(func.count(StudentQuestionAttempt.id)).filter(
        StudentQuestionAttempt.student_id == user_id,
        StudentQuestionAttempt.is_correct == False
    ).scalar() or 0

    # Get recent exam scores (last 10 exams)
    recent_exams = db.query(StudentExam).filter(
        StudentExam.student_id == user_id,
        StudentExam.score.isnot(None)
    ).order_by(StudentExam.start_time.desc()).limit(10).all()

    exam_scores = []
    for i, exam in enumerate(recent_exams):
        exam_scores.append({
            'name': f'Exam {len(recent_exams) - i}',
            'score': round(exam.score, 1)
        })

    # Reverse to show chronological order
    exam_scores.reverse()

    return {
        'correctAnswers': practice_correct,
        'incorrectAnswers': practice_incorrect,
        'examScores': exam_scores,
        'difficultyBreakdown': []  # Can be implemented later if needed
    }


def get_comprehensive_performance_analytics(db: Session, *, user_id: int, course_id: Optional[int] = None, days: int = 30) -> Dict[str, Any]:
    """
    Get comprehensive performance analytics for a user with detailed insights.
    """
    from sqlalchemy import func, and_, desc, case
    from app.models.student_progress import StudentExam, StudentQuestionAttempt, StudentExamAttempt
    from app.models.course import Course
    from app.models.question import Question
    from datetime import datetime, timedelta

    # Calculate date range
    end_date = datetime.utcnow()
    start_date = end_date - timedelta(days=days)

    # Base filters
    base_filters = [StudentQuestionAttempt.student_id == user_id]
    exam_filters = [StudentExam.student_id == user_id]

    if course_id:
        # For practice questions, we need to join with Question to get course_id
        base_filters.append(Question.course_id == course_id)
        exam_filters.append(StudentExam.course_id == course_id)

    # Get overall statistics
    practice_query = db.query(
        func.count(StudentQuestionAttempt.id).label('total'),
        func.sum(case((StudentQuestionAttempt.is_correct == True, 1), else_=0)).label('correct'),
        func.sum(case((StudentQuestionAttempt.is_correct == False, 1), else_=0)).label('incorrect')
    ).join(Question, StudentQuestionAttempt.question_id == Question.id).filter(
        and_(*base_filters)
    ).first()

    total_practice = practice_query.total or 0
    correct_practice = practice_query.correct or 0
    incorrect_practice = practice_query.incorrect or 0
    practice_accuracy = (correct_practice / total_practice * 100) if total_practice > 0 else 0

    # Get exam statistics
    exam_stats = db.query(
        func.count(StudentExam.id).label('total_exams'),
        func.avg(StudentExam.score).label('avg_score'),
        func.max(StudentExam.score).label('best_score'),
        func.min(StudentExam.score).label('worst_score')
    ).filter(
        and_(*exam_filters),
        StudentExam.score.isnot(None)
    ).first()

    total_exams = exam_stats.total_exams or 0
    avg_exam_score = round(exam_stats.avg_score or 0, 1)
    best_exam_score = round(exam_stats.best_score or 0, 1)
    worst_exam_score = round(exam_stats.worst_score or 0, 1)

    # Get time-based performance trends (last 30 days)
    daily_performance = db.query(
        func.date(StudentQuestionAttempt.attempt_time).label('date'),
        func.count(StudentQuestionAttempt.id).label('total'),
        func.sum(case((StudentQuestionAttempt.is_correct == True, 1), else_=0)).label('correct')
    ).join(Question, StudentQuestionAttempt.question_id == Question.id).filter(
        and_(*base_filters),
        StudentQuestionAttempt.attempt_time >= start_date
    ).group_by(func.date(StudentQuestionAttempt.attempt_time)).order_by(func.date(StudentQuestionAttempt.attempt_time)).all()

    # Format daily performance data
    performance_trend = []
    for day in daily_performance:
        accuracy = (day.correct / day.total * 100) if day.total > 0 else 0
        performance_trend.append({
            'date': day.date.strftime('%Y-%m-%d'),
            'accuracy': round(accuracy, 1),
            'total_questions': day.total,
            'correct_answers': day.correct
        })

    # Get recent exam history with detailed scores
    recent_exams = db.query(StudentExam).filter(
        and_(*exam_filters),
        StudentExam.score.isnot(None)
    ).order_by(desc(StudentExam.start_time)).limit(20).all()

    exam_history = []
    for exam in recent_exams:
        exam_history.append({
            'id': exam.id,
            'date': exam.start_time.strftime('%Y-%m-%d'),
            'score': round(exam.score, 1),
            'total_questions': exam.total_questions,
            'correct_answers': exam.correct_answers or 0,
            'course_id': exam.course_id
        })

    # Get course-specific performance if no specific course is requested
    course_performance = []
    if not course_id:
        course_stats = db.query(
            Course.id,
            Course.name,
            func.count(StudentQuestionAttempt.id).label('total'),
            func.sum(case((StudentQuestionAttempt.is_correct == True, 1), else_=0)).label('correct'),
            func.avg(StudentExam.score).label('avg_exam_score')
        ).join(Question, Course.id == Question.course_id)\
         .join(StudentQuestionAttempt, Question.id == StudentQuestionAttempt.question_id)\
         .outerjoin(StudentExam, and_(StudentExam.course_id == Course.id, StudentExam.student_id == user_id))\
         .filter(StudentQuestionAttempt.student_id == user_id)\
         .group_by(Course.id, Course.name).all()

        for course in course_stats:
            accuracy = (course.correct / course.total * 100) if course.total > 0 else 0
            course_performance.append({
                'course_id': course.id,
                'course_name': course.name,
                'practice_accuracy': round(accuracy, 1),
                'total_practice': course.total,
                'correct_practice': course.correct,
                'avg_exam_score': round(course.avg_exam_score or 0, 1)
            })

    # Calculate improvement metrics
    improvement_rate = 0
    if len(performance_trend) >= 7:  # Need at least a week of data
        recent_week = performance_trend[-7:]
        earlier_week = performance_trend[-14:-7] if len(performance_trend) >= 14 else performance_trend[:-7]

        if earlier_week:
            recent_avg = sum(day['accuracy'] for day in recent_week) / len(recent_week)
            earlier_avg = sum(day['accuracy'] for day in earlier_week) / len(earlier_week)
            improvement_rate = recent_avg - earlier_avg

    # Calculate consistency score (lower standard deviation = higher consistency)
    consistency_score = 0
    if len(performance_trend) >= 5:
        accuracies = [day['accuracy'] for day in performance_trend]
        mean_accuracy = sum(accuracies) / len(accuracies)
        variance = sum((x - mean_accuracy) ** 2 for x in accuracies) / len(accuracies)
        std_dev = variance ** 0.5
        # Convert to 0-100 scale where 100 is perfect consistency
        consistency_score = max(0, 100 - std_dev)

    # Calculate activity metrics
    active_days = len(performance_trend)
    total_activity = sum(day['total_questions'] for day in performance_trend)
    avg_daily_questions = total_activity / max(active_days, 1)

    return {
        'overview': {
            'total_practice_questions': total_practice,
            'correct_practice': correct_practice,
            'incorrect_practice': incorrect_practice,
            'practice_accuracy': round(practice_accuracy, 1),
            'total_exams': total_exams,
            'avg_exam_score': avg_exam_score,
            'best_exam_score': best_exam_score,
            'worst_exam_score': worst_exam_score
        },
        'performance_trend': performance_trend,
        'exam_history': exam_history,
        'course_performance': course_performance,
        'insights': {
            'improvement_rate': round(improvement_rate, 1),
            'consistency_score': round(consistency_score, 1),
            'active_days': active_days,
            'avg_daily_questions': round(avg_daily_questions, 1),
            'days_analyzed': days
        },
        'challenge_metrics': {
            'streak_potential': calculate_streak_potential(performance_trend),
            'next_milestone': calculate_next_milestone(total_practice, avg_exam_score),
            'improvement_areas': identify_improvement_areas(course_performance, practice_accuracy, avg_exam_score)
        }
    }


def calculate_streak_potential(performance_trend: List[Dict]) -> Dict[str, Any]:
    """Calculate streak potential and current streaks."""
    if not performance_trend:
        return {'current_streak': 0, 'best_streak': 0, 'streak_type': 'none'}

    # Calculate accuracy streak (days with >70% accuracy)
    current_streak = 0
    best_streak = 0
    temp_streak = 0

    for day in reversed(performance_trend):  # Start from most recent
        if day['accuracy'] >= 70:
            temp_streak += 1
            if current_streak == 0:  # Still building current streak
                current_streak = temp_streak
        else:
            if temp_streak > best_streak:
                best_streak = temp_streak
            temp_streak = 0
            if current_streak > 0:  # Current streak broken
                break

    # Update best streak if current is better
    best_streak = max(best_streak, temp_streak)

    streak_type = 'accuracy' if current_streak > 0 else 'none'

    return {
        'current_streak': current_streak,
        'best_streak': best_streak,
        'streak_type': streak_type
    }


def calculate_next_milestone(total_practice: int, avg_exam_score: float) -> Dict[str, Any]:
    """Calculate the next achievement milestone for the student."""
    milestones = []

    # Practice milestones
    practice_milestones = [100, 250, 500, 1000, 2500, 5000]
    for milestone in practice_milestones:
        if total_practice < milestone:
            milestones.append({
                'type': 'practice',
                'target': milestone,
                'current': total_practice,
                'progress': (total_practice / milestone) * 100,
                'description': f'Answer {milestone} practice questions'
            })
            break

    # Exam score milestones
    score_milestones = [70, 80, 85, 90, 95]
    for milestone in score_milestones:
        if avg_exam_score < milestone:
            milestones.append({
                'type': 'exam_score',
                'target': milestone,
                'current': avg_exam_score,
                'progress': (avg_exam_score / milestone) * 100,
                'description': f'Achieve {milestone}% average exam score'
            })
            break

    return milestones[0] if milestones else {
        'type': 'mastery',
        'description': 'You\'ve achieved all major milestones! Keep practicing to maintain your skills.'
    }


def identify_improvement_areas(course_performance: List[Dict], practice_accuracy: float, avg_exam_score: float) -> List[str]:
    """Identify areas where the student can improve."""
    areas = []

    # Check overall practice accuracy
    if practice_accuracy < 70:
        areas.append('Focus on improving practice question accuracy')

    # Check exam performance
    if avg_exam_score < 75:
        areas.append('Work on exam preparation and time management')

    # Check course-specific weaknesses
    if course_performance:
        weak_courses = [course for course in course_performance if course['practice_accuracy'] < 60]
        if weak_courses:
            course_names = [course['course_name'] for course in weak_courses[:2]]  # Limit to 2 courses
            areas.append(f'Strengthen understanding in: {", ".join(course_names)}')

    # Check practice vs exam performance gap
    if practice_accuracy > 0 and avg_exam_score > 0:
        gap = practice_accuracy - avg_exam_score
        if gap > 15:  # Significant gap
            areas.append('Bridge the gap between practice and exam performance')

    return areas[:3]  # Limit to top 3 areas
