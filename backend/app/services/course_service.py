from typing import Any, Dict, List, Optional, Union

from sqlalchemy import case, or_
from sqlalchemy.orm import Session

from app import schemas
from app.models.course import Course
from app.models.user import User


def get(db: Session, id: int) -> Optional[Course]:
    return db.query(Course).filter(Course.id == id).first()


def get_by_code(db: Session, code: str) -> Optional[Course]:
    return db.query(Course).filter(Course.code == code).first()


def get_multi(
    db: Session, *, skip: int = 0, limit: int = 100
) -> list[Course]:
    return db.query(Course).offset(skip).limit(limit).all()


def get_by_school(
    db: Session, *, school_id: int, skip: int = 0, limit: int = 100
) -> list[Course]:
    return (
        db.query(Course)
        .filter(Course.school_id == school_id)
        .offset(skip)
        .limit(limit)
        .all()
    )


def get_by_department(
    db: Session, *, department_id: int, skip: int = 0, limit: int = 100
) -> list[Course]:
    return (
        db.query(Course)
        .filter(Course.department_id == department_id)
        .offset(skip)
        .limit(limit)
        .all()
    )


def create(db: Session, *, obj_in: schemas.CourseCreate) -> Course:
    db_obj = Course(
        name=obj_in.name,
        description=obj_in.description,
        code=obj_in.code,
        is_active=obj_in.is_active,
        school_id=obj_in.school_id,
        department_id=obj_in.department_id,
    )
    db.add(db_obj)
    db.commit()
    db.refresh(db_obj)
    return db_obj


def update(
    db: Session, *, db_obj: Course, obj_in: Union[schemas.CourseUpdate, Dict[str, Any]]
) -> Course:
    if isinstance(obj_in, dict):
        update_data = obj_in
    else:
        update_data = obj_in.model_dump(exclude_unset=True)
    for field in update_data:
        if field in update_data:
            setattr(db_obj, field, update_data[field])
    db.add(db_obj)
    db.commit()
    db.refresh(db_obj)
    return db_obj


def delete(db: Session, *, id: int) -> Course:
    obj = db.query(Course).get(id)
    db.delete(obj)
    db.commit()
    return obj


def get_enrolled_users(db: Session, *, course_id: int) -> List[User]:
    course = get(db, id=course_id)
    if not course:
        return []
    return course.users


def get_enrolled_courses_for_user(db: Session, *, user_id: int) -> List[Course]:
    """
    Get courses that a user has interacted with through:
    1. Enrollment
    2. Taking exams
    3. Practice sessions (question attempts)
    4. Flashcard usage
    5. User behavior events

    Returns an empty list if the user hasn't interacted with any courses.
    """
    from app.models.user import user_course
    from app.models.student_progress import StudentExam, StudentQuestionAttempt
    from app.models.generated_flashcard import GeneratedFlashcard
    from app.models.ml_recommendations import UserBehaviorEvent
    from app.models.question import Question
    from sqlalchemy import union, distinct

    # 1. Enrolled courses
    enrolled_courses = (
        db.query(Course.id)
        .join(user_course, user_course.c.course_id == Course.id)
        .filter(user_course.c.user_id == user_id)
        .filter(Course.is_active == True)
    )

    # 2. Courses from exam sessions
    exam_courses = (
        db.query(Course.id)
        .join(StudentExam, StudentExam.course_id == Course.id)
        .filter(StudentExam.student_id == user_id)
        .filter(Course.is_active == True)
    )

    # 3. Courses from practice sessions (question attempts)
    practice_courses = (
        db.query(Course.id)
        .join(Question, Question.course_id == Course.id)
        .join(StudentQuestionAttempt, StudentQuestionAttempt.question_id == Question.id)
        .filter(StudentQuestionAttempt.student_id == user_id)
        .filter(Course.is_active == True)
    )

    # 4. Courses from user behavior events
    behavior_courses = (
        db.query(Course.id)
        .join(UserBehaviorEvent, UserBehaviorEvent.course_id == Course.id)
        .filter(UserBehaviorEvent.user_id == user_id)
        .filter(Course.is_active == True)
    )

    # Union all course IDs and get distinct courses
    all_course_ids = union(enrolled_courses, exam_courses, practice_courses, behavior_courses)

    # Get the actual course objects
    courses_from_ids = (
        db.query(Course)
        .filter(Course.id.in_(all_course_ids))
        .filter(Course.is_active == True)
    )

    # 5. Also get courses from flashcards and exams that use course_name
    # Get unique course names from flashcards and exams
    flashcard_course_names = (
        db.query(GeneratedFlashcard.course_name)
        .filter(GeneratedFlashcard.student_id == user_id)
        .filter(GeneratedFlashcard.course_name.isnot(None))
        .distinct()
    ).all()

    exam_course_names = (
        db.query(StudentExam.course_name)
        .filter(StudentExam.student_id == user_id)
        .filter(StudentExam.course_name.isnot(None))
        .distinct()
    ).all()

    # Combine course names
    all_course_names = set()
    for (name,) in flashcard_course_names:
        if name:
            all_course_names.add(name)
    for (name,) in exam_course_names:
        if name:
            all_course_names.add(name)

    # Get courses by name
    courses_from_names = []
    if all_course_names:
        courses_from_names = (
            db.query(Course)
            .filter(Course.name.in_(all_course_names))
            .filter(Course.is_active == True)
        ).all()

    # Combine all courses and remove duplicates
    all_courses = list(courses_from_ids.all()) + courses_from_names
    unique_courses = {course.id: course for course in all_courses}.values()

    # Sort by name and return
    return sorted(unique_courses, key=lambda c: c.name)


def get_prioritized_by_department(
    db: Session, *, user_department_id: Optional[int], skip: int = 0, limit: int = 100
) -> list[Course]:
    """
    Get all courses but prioritize those from the user's department.
    Courses from the user's department will appear first in the results.
    """
    if not user_department_id:
        return get_multi(db, skip=skip, limit=limit)

    # Use case statement to prioritize courses from user's department
    # Note: In newer SQLAlchemy versions, whens are passed as individual arguments, not as a list
    priority_case = case(
        (Course.department_id == user_department_id, 1),
        else_=2
    )

    return (
        db.query(Course)
        .order_by(priority_case, Course.name)
        .offset(skip)
        .limit(limit)
        .all()
    )


def get_prioritized_enrolled_courses(
    db: Session, *, user_id: int, skip: int = 0, limit: int = 100
) -> list[Course]:
    """
    Get all courses but prioritize those the user is enrolled in.
    Enrolled courses will appear first in the results.
    """
    from app.models.user import user_course
    from sqlalchemy import exists

    # Create a subquery to check if the user is enrolled in each course
    enrolled_subquery = exists().where(
        (user_course.c.user_id == user_id) &
        (user_course.c.course_id == Course.id)
    )

    # Use case statement to prioritize enrolled courses
    priority_case = case(
        (enrolled_subquery, 1),
        else_=2
    )

    return (
        db.query(Course)
        .order_by(priority_case, Course.name)
        .offset(skip)
        .limit(limit)
        .all()
    )


def search_courses(
    db: Session, *, search_query: str, skip: int = 0, limit: int = 100
) -> list[Course]:
    """
    Search for courses by name or code.

    Args:
        db: Database session
        search_query: Search query string
        skip: Number of records to skip
        limit: Maximum number of records to return

    Returns:
        List of courses matching the search query
    """
    # Create a search pattern with wildcards
    search_pattern = f"%{search_query}%"

    return (
        db.query(Course)
        .filter(
            or_(
                Course.name.ilike(search_pattern),
                Course.code.ilike(search_pattern),
                Course.description.ilike(search_pattern)
            )
        )
        .order_by(Course.name)
        .offset(skip)
        .limit(limit)
        .all()
    )
