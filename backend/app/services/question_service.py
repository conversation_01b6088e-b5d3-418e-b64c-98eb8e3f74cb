from typing import Any, Dict, List, Optional, Union

from sqlalchemy.orm import Session
from sqlalchemy.exc import IntegrityError

from app import schemas
from app.models.question import Question, DifficultyLevel, QuestionType
from app.models.student_progress import StudentQuestionAttempt, StudentBookmark, StudentExamAttempt


def get(db: Session, id: int) -> Optional[Question]:
    return db.query(Question).filter(Question.id == id).first()


def get_multi(
    db: Session,
    *,
    skip: int = 0,
    limit: int = 100,
    course_id: Optional[int] = None,
    difficulty: Optional[DifficultyLevel] = None,
    question_type: Optional[QuestionType] = None,
    topic: Optional[str] = None
) -> list[Question]:
    query = db.query(Question)

    if course_id is not None:
        query = query.filter(Question.course_id == course_id)

    if difficulty is not None:
        query = query.filter(Question.difficulty == difficulty)

    if question_type is not None:
        query = query.filter(Question.question_type == question_type)

    if topic is not None:
        query = query.filter(Question.topic == topic)

    return query.offset(skip).limit(limit).all()


def create(
    db: Session, *, obj_in: schemas.QuestionCreate, created_by_id: int
) -> Question:
    # Create the question object with only the fields that exist in the model
    db_obj_data = {
        'content': obj_in.content,
        'question_type': obj_in.question_type,
        'difficulty': obj_in.difficulty,
        'topic': obj_in.topic,
        'options': obj_in.options,
        'answer': obj_in.answer,
        'explanation': obj_in.explanation,
        'media_url': obj_in.media_url,
        'is_active': obj_in.is_active,
        'course_id': obj_in.course_id,
        'course_name': obj_in.course_name,
        'created_by_id': created_by_id,
    }

    # Only add mcq_job_id if it's provided and the field exists in the model
    # (Currently commented out in the model, so we skip it)
    # if hasattr(obj_in, 'mcq_job_id') and obj_in.mcq_job_id is not None:
    #     db_obj_data['mcq_job_id'] = obj_in.mcq_job_id

    db_obj = Question(**db_obj_data)
    db.add(db_obj)
    db.commit()
    db.refresh(db_obj)
    return db_obj


def update(
    db: Session, *, db_obj: Question, obj_in: Union[schemas.QuestionUpdate, Dict[str, Any]]
) -> Question:
    if isinstance(obj_in, dict):
        update_data = obj_in
    else:
        update_data = obj_in.model_dump(exclude_unset=True)
    for field in update_data:
        if field in update_data:
            setattr(db_obj, field, update_data[field])
    db.add(db_obj)
    db.commit()
    db.refresh(db_obj)
    return db_obj


def get_question_usage_stats(db: Session, *, id: int) -> Dict[str, int]:
    """
    Get statistics about how a question is being used.
    Returns counts of related records that would be deleted.
    """
    stats = {
        "student_attempts": db.query(StudentQuestionAttempt).filter(
            StudentQuestionAttempt.question_id == id
        ).count(),
        "bookmarks": db.query(StudentBookmark).filter(
            StudentBookmark.question_id == id
        ).count(),
        "exam_attempts": db.query(StudentExamAttempt).filter(
            StudentExamAttempt.question_id == id
        ).count(),
    }
    stats["total_references"] = sum(stats.values())
    return stats


def delete(db: Session, *, id: int) -> Question:
    """
    Delete a question and handle foreign key constraints.
    This will cascade delete related records in student progress tables.
    """
    obj = db.query(Question).get(id)
    if not obj:
        return None

    try:
        # Delete related records first to avoid foreign key constraint violations

        # Delete student question attempts
        db.query(StudentQuestionAttempt).filter(
            StudentQuestionAttempt.question_id == id
        ).delete(synchronize_session=False)

        # Delete student bookmarks
        db.query(StudentBookmark).filter(
            StudentBookmark.question_id == id
        ).delete(synchronize_session=False)

        # Delete student exam attempts
        db.query(StudentExamAttempt).filter(
            StudentExamAttempt.question_id == id
        ).delete(synchronize_session=False)

        # Now delete the question itself
        db.delete(obj)
        db.commit()
        return obj

    except IntegrityError as e:
        db.rollback()
        # If there are still foreign key constraints we missed, raise a more user-friendly error
        raise ValueError(f"Cannot delete question {id}: it is still referenced by other records. Please contact an administrator.")
    except Exception as e:
        db.rollback()
        raise e


def get_by_course(
    db: Session, *, course_id: int, skip: int = 0, limit: int = 100
) -> List[Question]:
    return (
        db.query(Question)
        .filter(Question.course_id == course_id)
        .offset(skip)
        .limit(limit)
        .all()
    )


# Temporarily commented out because mcq_job_id field is commented out in the Question model
# def get_by_mcq_job(
#     db: Session, *, mcq_job_id: int, skip: int = 0, limit: int = 100
# ) -> List[Question]:
#     """Get questions generated by a specific MCQ generation job."""
#     return (
#         db.query(Question)
#         .filter(Question.mcq_job_id == mcq_job_id)
#         .offset(skip)
#         .limit(limit)
#         .all()
#     )
