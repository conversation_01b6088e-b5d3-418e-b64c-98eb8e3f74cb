from typing import Any, Dict, Optional, Union

from sqlalchemy.orm import Session

from app import schemas
from app.models.school import School


def get(db: Session, id: int) -> Optional[School]:
    return db.query(School).filter(School.id == id).first()


def get_by_name(db: Session, name: str) -> Optional[School]:
    return db.query(School).filter(School.name == name).first()


def get_multi(
    db: Session, *, skip: int = 0, limit: int = 100
) -> list[School]:
    return db.query(School).offset(skip).limit(limit).all()


def create(db: Session, *, obj_in: schemas.SchoolCreate) -> School:
    db_obj = School(
        name=obj_in.name,
        description=obj_in.description,
        location=obj_in.location,
        is_active=obj_in.is_active,
    )
    db.add(db_obj)
    db.commit()
    db.refresh(db_obj)
    return db_obj


def update(
    db: Session, *, db_obj: School, obj_in: Union[schemas.SchoolUpdate, Dict[str, Any]]
) -> School:
    if isinstance(obj_in, dict):
        update_data = obj_in
    else:
        update_data = obj_in.model_dump(exclude_unset=True)
    for field in update_data:
        if field in update_data:
            setattr(db_obj, field, update_data[field])
    db.add(db_obj)
    db.commit()
    db.refresh(db_obj)
    return db_obj


def delete(db: Session, *, id: int) -> School:
    obj = db.query(School).get(id)
    db.delete(obj)
    db.commit()
    return obj
