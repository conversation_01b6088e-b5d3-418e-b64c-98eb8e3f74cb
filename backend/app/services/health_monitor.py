"""
Health monitoring service to detect and prevent hanging issues
"""
import asyncio
import time
import psutil
import logging
from typing import Dict, Any, List, Optional
from datetime import datetime, timedelta
from sqlalchemy.orm import Session
from sqlalchemy import text

from app.db.session import SessionLocal
from app.core.config import settings

logger = logging.getLogger(__name__)


class HealthMonitor:
    """
    Service to monitor system health and detect potential hanging issues
    """
    
    def __init__(self):
        self.start_time = time.time()
        self.request_count = 0
        self.slow_requests = []
        self.error_count = 0
        self.last_health_check = None
    
    async def comprehensive_health_check(self) -> Dict[str, Any]:
        """
        Perform comprehensive health check of all system components
        
        Returns:
            Dictionary with health status of all components
        """
        health_status = {
            "timestamp": datetime.utcnow().isoformat(),
            "status": "healthy",
            "checks": {},
            "metrics": {}
        }
        
        try:
            # Database health check
            db_health = await self._check_database_health()
            health_status["checks"]["database"] = db_health
            
            # Memory health check
            memory_health = self._check_memory_health()
            health_status["checks"]["memory"] = memory_health
            
            # CPU health check
            cpu_health = self._check_cpu_health()
            health_status["checks"]["cpu"] = cpu_health
            
            # Connection health check
            connection_health = self._check_connection_health()
            health_status["checks"]["connections"] = connection_health
            
            # Application metrics
            app_metrics = self._get_application_metrics()
            health_status["metrics"] = app_metrics
            
            # Determine overall status
            failed_checks = [
                check for check, status in health_status["checks"].items()
                if status.get("status") != "healthy"
            ]
            
            if failed_checks:
                health_status["status"] = "unhealthy"
                health_status["failed_checks"] = failed_checks
            
            self.last_health_check = datetime.utcnow()
            
        except Exception as e:
            logger.error(f"Health check failed: {str(e)}")
            health_status["status"] = "error"
            health_status["error"] = str(e)
        
        return health_status
    
    async def _check_database_health(self) -> Dict[str, Any]:
        """Check database connectivity and performance"""
        try:
            start_time = time.time()
            
            # Test database connection with timeout
            db = SessionLocal()
            try:
                # Simple query to test connectivity
                result = await asyncio.wait_for(
                    asyncio.to_thread(db.execute, text("SELECT 1")),
                    timeout=5.0
                )
                
                # Check connection pool status
                engine = db.get_bind()
                pool = engine.pool

                response_time = time.time() - start_time

                # Get pool stats safely
                pool_stats = {
                    "pool_size": getattr(pool, 'size', lambda: 0)(),
                    "checked_in": getattr(pool, 'checkedin', lambda: 0)(),
                    "checked_out": getattr(pool, 'checkedout', lambda: 0)(),
                    "overflow": getattr(pool, 'overflow', lambda: 0)(),
                }

                # Add invalid count if available
                if hasattr(pool, 'invalid'):
                    pool_stats["invalid"] = pool.invalid()

                return {
                    "status": "healthy" if response_time < 2.0 else "slow",
                    "response_time_ms": round(response_time * 1000, 2),
                    **pool_stats
                }
                
            finally:
                db.close()
                
        except asyncio.TimeoutError:
            return {
                "status": "timeout",
                "error": "Database query timed out after 5 seconds"
            }
        except Exception as e:
            return {
                "status": "error",
                "error": str(e)
            }
    
    def _check_memory_health(self) -> Dict[str, Any]:
        """Check memory usage"""
        try:
            memory = psutil.virtual_memory()
            
            status = "healthy"
            if memory.percent > 90:
                status = "critical"
            elif memory.percent > 80:
                status = "warning"
            
            return {
                "status": status,
                "total_gb": round(memory.total / (1024**3), 2),
                "available_gb": round(memory.available / (1024**3), 2),
                "used_percent": memory.percent,
                "free_gb": round(memory.free / (1024**3), 2)
            }
            
        except Exception as e:
            return {
                "status": "error",
                "error": str(e)
            }
    
    def _check_cpu_health(self) -> Dict[str, Any]:
        """Check CPU usage"""
        try:
            cpu_percent = psutil.cpu_percent(interval=1)
            cpu_count = psutil.cpu_count()
            
            status = "healthy"
            if cpu_percent > 90:
                status = "critical"
            elif cpu_percent > 80:
                status = "warning"
            
            return {
                "status": status,
                "usage_percent": cpu_percent,
                "cpu_count": cpu_count,
                "load_average": psutil.getloadavg() if hasattr(psutil, 'getloadavg') else None
            }
            
        except Exception as e:
            return {
                "status": "error",
                "error": str(e)
            }
    
    def _check_connection_health(self) -> Dict[str, Any]:
        """Check network connections"""
        try:
            connections = psutil.net_connections()
            
            # Count connections by status
            connection_stats = {}
            for conn in connections:
                status = conn.status
                connection_stats[status] = connection_stats.get(status, 0) + 1
            
            # Check for too many connections
            total_connections = len(connections)
            status = "healthy"
            if total_connections > 1000:
                status = "warning"
            elif total_connections > 2000:
                status = "critical"
            
            return {
                "status": status,
                "total_connections": total_connections,
                "by_status": connection_stats
            }
            
        except Exception as e:
            return {
                "status": "error",
                "error": str(e)
            }
    
    def _get_application_metrics(self) -> Dict[str, Any]:
        """Get application-specific metrics"""
        uptime = time.time() - self.start_time
        
        return {
            "uptime_seconds": round(uptime, 2),
            "uptime_hours": round(uptime / 3600, 2),
            "request_count": self.request_count,
            "error_count": self.error_count,
            "slow_requests_count": len(self.slow_requests),
            "requests_per_second": round(self.request_count / uptime, 2) if uptime > 0 else 0,
            "last_health_check": self.last_health_check.isoformat() if self.last_health_check else None
        }
    
    def record_request(self, duration: float, path: str, status_code: int):
        """Record request metrics"""
        self.request_count += 1
        
        if status_code >= 400:
            self.error_count += 1
        
        if duration > 10.0:  # Slow request threshold
            self.slow_requests.append({
                "path": path,
                "duration": duration,
                "timestamp": datetime.utcnow().isoformat(),
                "status_code": status_code
            })
            
            # Keep only last 100 slow requests
            if len(self.slow_requests) > 100:
                self.slow_requests = self.slow_requests[-100:]
    
    async def detect_hanging_issues(self) -> List[Dict[str, Any]]:
        """
        Detect potential hanging issues in the system
        
        Returns:
            List of detected issues
        """
        issues = []
        
        try:
            # Check for database connection pool exhaustion
            db = SessionLocal()
            try:
                engine = db.get_bind()
                pool = engine.pool
                
                if pool.checkedout() >= pool.size() + pool.overflow():
                    issues.append({
                        "type": "database_pool_exhaustion",
                        "severity": "critical",
                        "message": "Database connection pool is exhausted",
                        "details": {
                            "checked_out": pool.checkedout(),
                            "pool_size": pool.size(),
                            "overflow": pool.overflow()
                        }
                    })
            finally:
                db.close()
            
            # Check for memory issues
            memory = psutil.virtual_memory()
            if memory.percent > 95:
                issues.append({
                    "type": "high_memory_usage",
                    "severity": "critical",
                    "message": f"Memory usage is critically high: {memory.percent}%",
                    "details": {
                        "used_percent": memory.percent,
                        "available_gb": round(memory.available / (1024**3), 2)
                    }
                })
            
            # Check for too many slow requests
            recent_slow_requests = [
                req for req in self.slow_requests
                if datetime.fromisoformat(req["timestamp"]) > datetime.utcnow() - timedelta(minutes=5)
            ]
            
            if len(recent_slow_requests) > 10:
                issues.append({
                    "type": "high_slow_request_rate",
                    "severity": "warning",
                    "message": f"High number of slow requests in last 5 minutes: {len(recent_slow_requests)}",
                    "details": {
                        "slow_requests_count": len(recent_slow_requests),
                        "paths": list(set(req["path"] for req in recent_slow_requests))
                    }
                })
                
        except Exception as e:
            logger.error(f"Error detecting hanging issues: {str(e)}")
            issues.append({
                "type": "health_check_error",
                "severity": "error",
                "message": f"Failed to check for hanging issues: {str(e)}"
            })
        
        return issues


# Global health monitor instance
health_monitor = HealthMonitor()
