"""
Service for handling Gemini Files API operations with structured output.
Handles file upload, metadata management, and automatic cleanup.
"""

import logging
import asyncio
from datetime import datetime, timedelta
from typing import Optional, Dict, Any, List, Union
import io

import google.generativeai as genai
from sqlalchemy.orm import Session
from fastapi import UploadFile, HTTPException

from app.core.config import settings
from app.schemas.gemini_structured_output import (
    MCQGenerationResponse, FlashcardGenerationResponse, SummaryGenerationResponse
)
from app.models.gemini_file import GeminiFile
from app.crud.crud_gemini_file import gemini_file as gemini_file_crud
from app.schemas.gemini_file import GeminiFileCreate

logger = logging.getLogger(__name__)


class GeminiFilesService:
    """Service for Gemini Files API operations with structured output"""

    def __init__(self):
        """Initialize the Gemini Files service"""
        self.api_key = settings.GEMINI_API_KEY
        if not self.api_key:
            raise ValueError("GEMINI_API_KEY is required")
        
        # Configure Gemini
        genai.configure(api_key=self.api_key)
        
        # Gemini automatically deletes files after 48 hours (2 days)
        # No need to set custom expiry times

    async def upload_pdf_to_gemini(
        self,
        file: UploadFile,
        student_id: int,
        db: Session,
        course_name: Optional[str] = None,
        file_content: Optional[bytes] = None
    ) -> Dict[str, Any]:
        """
        Upload PDF to Gemini Files API and store metadata

        Args:
            file: Uploaded PDF file
            student_id: ID of the student uploading
            db: Database session
            course_name: Optional course name for the uploaded file
            file_content: Optional pre-read file content (to avoid re-reading)

        Returns:
            Dictionary with upload results and metadata
        """
        try:
            # Import streaming file handler
            from app.utils.streaming_file_handler import streaming_file_handler

            # Read file content safely if not provided
            if file_content is None:
                file_content = await streaming_file_handler.get_file_content_safe(
                    file, max_size=30 * 1024 * 1024
                )

            file_size = len(file_content)

            # Validate file size (30MB max as per user requirements)
            max_size = 30 * 1024 * 1024  # 30MB
            if file_size > max_size:
                raise ValueError(f"File size {file_size} bytes exceeds maximum of {max_size} bytes (30MB)")

            # Upload to Gemini Files API with timeout protection
            # Increased timeout for EC2 environments where network may be slower
            logger.info(f"Starting Gemini upload for file: {file.filename} (size: {file_size} bytes)")
            upload_start_time = datetime.utcnow()

            gemini_file = await asyncio.wait_for(
                asyncio.to_thread(
                    genai.upload_file,
                    path=io.BytesIO(file_content),
                    mime_type=file.content_type or "application/pdf"
                ),
                timeout=180.0  # 3 minute timeout for Gemini upload (increased for EC2)
            )

            upload_duration = (datetime.utcnow() - upload_start_time).total_seconds()
            logger.info(f"Gemini upload completed in {upload_duration:.2f} seconds")

            # Get upload and expiry times
            upload_time = datetime.utcnow()

            # Set auto-delete time to 10 hours as per user requirements
            # Gemini files naturally expire after 48 hours, but we auto-delete after 10 hours
            auto_delete_time = upload_time + timedelta(hours=10)

            logger.info(f"File uploaded to Gemini: {gemini_file.name}")
            logger.info(f"Upload time: {upload_time}")
            logger.info(f"Auto-delete time (10h): {auto_delete_time}")

            # Save metadata to database if db session is provided
            if db:
                gemini_file_data = GeminiFileCreate(
                    gemini_file_id=gemini_file.name,
                    original_filename=file.filename,
                    file_size=file_size,
                    mime_type=file.content_type or "application/pdf",
                    student_id=student_id,
                    course_name=course_name,
                    auto_delete_time=auto_delete_time  # Auto-delete after 10 hours
                )

                db_file = gemini_file_crud.create(db, obj_in=gemini_file_data)
                logger.info(f"Saved file metadata to database with ID: {db_file.id}")

            logger.info(f"Successfully uploaded file {file.filename} to Gemini. File ID: {gemini_file.name}")

            # Clean up file content from memory
            del file_content

            return {
                "gemini_file_id": gemini_file.name,
                "original_filename": file.filename,
                "file_size": file_size,
                "upload_time": upload_time,
                "auto_delete_time": auto_delete_time,
                "course_name": course_name
            }
            
        except asyncio.TimeoutError:
            logger.error(f"Timeout uploading PDF to Gemini: {file.filename} (size: {file_size} bytes)")
            raise HTTPException(
                status_code=504,
                detail="Upload timeout - the file upload took too long to complete. Please try again or use a smaller file."
            )
        except Exception as e:
            logger.error(f"Error uploading file to Gemini: {str(e)}")
            # Check if it's a network-related error
            if "network" in str(e).lower() or "connection" in str(e).lower() or "timeout" in str(e).lower():
                raise HTTPException(
                    status_code=503,
                    detail="Network error during upload. Please check your connection and try again."
                )
            raise

    async def get_file_metadata(self, gemini_file_id: str) -> Optional[Dict[str, Any]]:
        """
        Get metadata for a Gemini file
        
        Args:
            gemini_file_id: Gemini file ID
            
        Returns:
            File metadata or None if not found
        """
        try:
            file_info = await asyncio.to_thread(
                genai.get_file,
                gemini_file_id
            )
            
            return {
                "name": file_info.name,
                "display_name": getattr(file_info, 'display_name', ''),
                "mime_type": file_info.mime_type,
                "size_bytes": getattr(file_info, 'size_bytes', 0),
                "create_time": getattr(file_info, 'create_time', None),
                "update_time": getattr(file_info, 'update_time', None),
                "expiration_time": getattr(file_info, 'expiration_time', None),
                "state": getattr(file_info, 'state', 'UNKNOWN')
            }
            
        except Exception as e:
            logger.error(f"Error getting file metadata from Gemini: {str(e)}")
            return None

    async def delete_file_from_gemini(self, gemini_file_id: str, db: Session) -> bool:
        """
        Delete file from Gemini and update database
        
        Args:
            gemini_file_id: Gemini file ID
            db: Database session
            
        Returns:
            True if successful, False otherwise
        """
        try:
            # Delete from Gemini
            await asyncio.to_thread(
                genai.delete_file,
                gemini_file_id
            )

            # Update database metadata
            gemini_file_crud.mark_as_deleted(db, gemini_file_id=gemini_file_id)
            
            logger.info(f"Successfully deleted file {gemini_file_id} from Gemini")
            return True
            
        except Exception as e:
            logger.error(f"Error deleting file from Gemini: {str(e)}")
            return False

    async def cleanup_expired_files(self, db: Session) -> Dict[str, int]:
        """
        Clean up files that have passed their auto-delete time
        
        Args:
            db: Database session
            
        Returns:
            Dictionary with cleanup statistics
        """
        try:
            current_time = datetime.utcnow()
            
            # Find files that should be auto-deleted
            expired_files = gemini_file_crud.get_expired_files(db, current_time=current_time)

            deleted_count = 0
            failed_count = 0

            for file_metadata in expired_files:
                success = await self.delete_file_from_gemini(
                    file_metadata.gemini_file_id, db
                )
                if success:
                    deleted_count += 1
                else:
                    failed_count += 1

            logger.info(f"Cleanup completed: {deleted_count} deleted, {failed_count} failed")

            return {
                "deleted_count": deleted_count,
                "failed_count": failed_count,
                "total_processed": len(expired_files)
            }
            
        except Exception as e:
            logger.error(f"Error during file cleanup: {str(e)}")
            return {"deleted_count": 0, "failed_count": 0, "total_processed": 0}

    def _get_file_reference(self, gemini_file_id: str):
        """Get file reference for use in generate_content calls"""
        return genai.get_file(gemini_file_id)

    async def delete_gemini_file(self, gemini_file_id: str, student_id: int, db: Session = None) -> dict:
        """
        Delete a file from Gemini Files API.

        Args:
            gemini_file_id: The Gemini file ID to delete
            student_id: ID of the student (for logging)
            db: Database session (unused, kept for compatibility)

        Returns:
            Dictionary with deletion status and details
        """
        try:
            logger.info(f"Deleting Gemini file {gemini_file_id} for student {student_id}")

            # Normalize the file ID - try both with and without 'files/' prefix
            file_id_variants = [gemini_file_id]
            if gemini_file_id.startswith('files/'):
                file_id_variants.append(gemini_file_id[6:])  # Remove 'files/' prefix
            else:
                file_id_variants.append(f'files/{gemini_file_id}')  # Add 'files/' prefix

            # Without database metadata, we can't verify ownership
            # Just proceed with deletion attempt

            # Delete from Gemini Files API
            try:
                genai.delete_file(name=gemini_file_id)
                logger.info(f"Successfully deleted file {gemini_file_id} from Gemini")
                gemini_deleted = True
            except Exception as gemini_error:
                logger.warning(f"Could not delete file from Gemini (may already be deleted): {str(gemini_error)}")
                gemini_deleted = False

            # No database records to clean up - using Gemini Files API only

            logger.info(f"Successfully deleted Gemini file {gemini_file_id} and cleaned up database records")

            return {
                "gemini_file_id": gemini_file_id,
                "gemini_deleted": gemini_deleted,
                "message": "File deleted successfully"
            }

        except Exception as e:
            logger.error(f"Error deleting Gemini file {gemini_file_id}: {str(e)}")
            return {
                "gemini_file_id": gemini_file_id,
                "gemini_deleted": False,
                "message": f"Error deleting file: {str(e)}"
            }


# Create singleton instance
gemini_files_service = GeminiFilesService()
