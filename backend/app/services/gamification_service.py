from datetime import datetime, timedelta, timezone
from typing import List, Dict, Optional, Any, Tuple
from sqlalchemy.orm import Session
from sqlalchemy import func, desc, text

from app.models.user import User
from app.models.gamification import (
    Badge, UserBadge, PointsTransaction, UserLevel, UserStreak,
    BadgeType
)
from app.models.student_progress import StudentExam, StudentQuestionAttempt
from app.schemas.gamification import (
    BadgeCreate, UserBadgeCreate, PointsTransactionCreate,
    UserLevelCreate, UserStreakCreate
)

# Constants for point values - Reverted to lower values
POINTS = {
    "EXAM_COMPLETION": 2,
    "PRACTICE_COMPLETION": 2,
    "CORRECT_ANSWER": 1,
    "PERFECT_SCORE": 5,
    "STREAK_DAY": 1,
    "PROFILE_COMPLETION": 3,
    "COURSE_ENROLLMENT": 2,
    "BADGE_EARNED": 3,
    "FLASHCARD_COMPLETION": 2,
    "NOTE_EXPLANATION_GENERATED": 2,
    "FIRST_LOGIN": 2,
    "WEEKLY_GOAL_ACHIEVED": 5,
    "MONTHLY_GOAL_ACHIEVED": 10,
    "QUESTION_STREAK_5": 3,
    "QUESTION_STREAK_10": 5,
    "QUESTION_STREAK_20": 10,
    "SPEED_BONUS": 1,  # For answering quickly
    "COMEBACK_BONUS": 3,  # For returning after absence
}

# Level definitions - 20 levels with adjusted point requirements for lower point system
LEVELS = [
    {"level_number": 1, "name": "Novice", "min_points": 0, "max_points": 9, "icon_url": "/icons/level1.svg"},
    {"level_number": 2, "name": "Beginner", "min_points": 10, "max_points": 24, "icon_url": "/icons/level2.svg"},
    {"level_number": 3, "name": "Apprentice", "min_points": 25, "max_points": 49, "icon_url": "/icons/level3.svg"},
    {"level_number": 4, "name": "Student", "min_points": 50, "max_points": 99, "icon_url": "/icons/level4.svg"},
    {"level_number": 5, "name": "Scholar", "min_points": 100, "max_points": 174, "icon_url": "/icons/level5.svg"},
    {"level_number": 6, "name": "Researcher", "min_points": 175, "max_points": 274, "icon_url": "/icons/level6.svg"},
    {"level_number": 7, "name": "Specialist", "min_points": 275, "max_points": 399, "icon_url": "/icons/level7.svg"},
    {"level_number": 8, "name": "Expert", "min_points": 400, "max_points": 574, "icon_url": "/icons/level8.svg"},
    {"level_number": 9, "name": "Professional", "min_points": 575, "max_points": 799, "icon_url": "/icons/level9.svg"},
    {"level_number": 10, "name": "Mentor", "min_points": 800, "max_points": 1099, "icon_url": "/icons/level10.svg"},
    {"level_number": 11, "name": "Master", "min_points": 1100, "max_points": 1474, "icon_url": "/icons/level11.svg"},
    {"level_number": 12, "name": "Sage", "min_points": 1475, "max_points": 1974, "icon_url": "/icons/level12.svg"},
    {"level_number": 13, "name": "Virtuoso", "min_points": 1975, "max_points": 2599, "icon_url": "/icons/level13.svg"},
    {"level_number": 14, "name": "Grandmaster", "min_points": 2600, "max_points": 3399, "icon_url": "/icons/level14.svg"},
    {"level_number": 15, "name": "Champion", "min_points": 3400, "max_points": 4399, "icon_url": "/icons/level15.svg"},
    {"level_number": 16, "name": "Elite", "min_points": 4400, "max_points": 5649, "icon_url": "/icons/level16.svg"},
    {"level_number": 17, "name": "Prodigy", "min_points": 5650, "max_points": 7249, "icon_url": "/icons/level17.svg"},
    {"level_number": 18, "name": "Genius", "min_points": 7250, "max_points": 9249, "icon_url": "/icons/level18.svg"},
    {"level_number": 19, "name": "Legend", "min_points": 9250, "max_points": 11749, "icon_url": "/icons/level19.svg"},
    {"level_number": 20, "name": "Immortal", "min_points": 11750, "max_points": 9999999, "icon_url": "/icons/level20.svg"},
]

# Enhanced Badge definitions with SVG designs
DEFAULT_BADGES = [
    # Achievement Badges
    {
        "name": "First Perfect Score",
        "description": "Achieved a perfect score on an exam",
        "badge_type": BadgeType.FIRST_PERFECT_SCORE,
        "icon_url": "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMzIiIGN5PSIzMiIgcj0iMzAiIGZpbGw9IiNGRkQ3MDAiIHN0cm9rZT0iI0ZGQjAwMCIgc3Ryb2tlLXdpZHRoPSI0Ii8+CjxwYXRoIGQ9Ik0yMCAzMkwzMCA0Mkw0NCAyOCIgc3Ryb2tlPSIjRkZGRkZGIiBzdHJva2Utd2lkdGg9IjQiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8L3N2Zz4K",
        "points_value": 5
    },
    {
        "name": "Practice Master",
        "description": "Completed 25 practice sessions",
        "badge_type": BadgeType.PRACTICE_MASTER,
        "icon_url": "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMzIiIGN5PSIzMiIgcj0iMzAiIGZpbGw9IiM0Qzc5RkYiIHN0cm9rZT0iIzJENTVGRiIgc3Ryb2tlLXdpZHRoPSI0Ii8+CjxwYXRoIGQ9Ik0yNCAyNEg0MFYzNkgyNFYyNFoiIGZpbGw9IiNGRkZGRkYiLz4KPHBhdGggZD0iTTI2IDQwSDM4VjQ0SDI2VjQwWiIgZmlsbD0iI0ZGRkZGRiIvPgo8L3N2Zz4K",
        "points_value": 100
    },
    {
        "name": "Course Completion",
        "description": "Completed all questions in a course",
        "badge_type": BadgeType.COURSE_COMPLETION,
        "icon_url": "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMzIiIGN5PSIzMiIgcj0iMzAiIGZpbGw9IiMxMEI5ODEiIHN0cm9rZT0iIzA1OTY2OSIgc3Ryb2tlLXdpZHRoPSI0Ii8+CjxwYXRoIGQ9Ik0yMiAyNkg0MlYzOEgyMlYyNloiIGZpbGw9IiNGRkZGRkYiLz4KPHBhdGggZD0iTTI4IDMwSDM2VjM0SDI4VjMwWiIgZmlsbD0iIzEwQjk4MSIvPgo8L3N2Zz4K",
        "points_value": 200
    },
    {
        "name": "Quick Learner",
        "description": "Answered 50 questions correctly in a row",
        "badge_type": BadgeType.QUICK_LEARNER,
        "icon_url": "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMzIiIGN5PSIzMiIgcj0iMzAiIGZpbGw9IiNGRjZBMDAiIHN0cm9rZT0iI0VBNTUwNiIgc3Ryb2tlLXdpZHRoPSI0Ii8+CjxwYXRoIGQ9Ik0zMiAxNkwzNiAyOEgzMkwyOCAyOEwzMiAxNloiIGZpbGw9IiNGRkZGRkYiLz4KPHBhdGggZD0iTTMyIDM2TDM2IDQ4SDMyTDI4IDQ4TDMyIDM2WiIgZmlsbD0iI0ZGRkZGRiIvPgo8L3N2Zz4K",
        "points_value": 125
    },
    {
        "name": "Consistent Learner",
        "description": "Studied for 10 consecutive days",
        "badge_type": BadgeType.CONSISTENT_LEARNER,
        "icon_url": "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMzIiIGN5PSIzMiIgcj0iMzAiIGZpbGw9IiM4QjVDRjYiIHN0cm9rZT0iIzc0NEQ5RiIgc3Ryb2tlLXdpZHRoPSI0Ii8+CjxjaXJjbGUgY3g9IjMyIiBjeT0iMzIiIHI9IjEyIiBmaWxsPSIjRkZGRkZGIi8+CjxjaXJjbGUgY3g9IjMyIiBjeT0iMzIiIHI9IjYiIGZpbGw9IiM4QjVDRjYiLz4KPC9zdmc+Cg==",
        "points_value": 75
    },

    # Streak Badges
    {
        "name": "Streak Champion",
        "description": "Maintained a 7-day learning streak",
        "badge_type": BadgeType.STREAK,
        "icon_url": "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMzIiIGN5PSIzMiIgcj0iMzAiIGZpbGw9IiNGRjU3MjIiIHN0cm9rZT0iI0VBNDMwQSIgc3Ryb2tlLXdpZHRoPSI0Ii8+CjxwYXRoIGQ9Ik0zMiAxNkwzNiAyNEgzMkwyOCAyNEwzMiAxNloiIGZpbGw9IiNGRkZGRkYiLz4KPHBhdGggZD0iTTMyIDI4TDM2IDM2SDMyTDI4IDM2TDMyIDI4WiIgZmlsbD0iI0ZGRkZGRiIvPgo8cGF0aCBkPSJNMzIgNDBMMzYgNDhIMzJMMjggNDhMMzIgNDBaIiBmaWxsPSIjRkZGRkZGIi8+Cjwvc3ZnPgo=",
        "points_value": 100
    },
    {
        "name": "Streak Warrior",
        "description": "Maintained a 30-day learning streak",
        "badge_type": BadgeType.STREAK_WARRIOR,
        "icon_url": "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMzIiIGN5PSIzMiIgcj0iMzAiIGZpbGw9IiNEQzI2MjYiIHN0cm9rZT0iI0I5MTQxNCIgc3Ryb2tlLXdpZHRoPSI0Ii8+CjxwYXRoIGQ9Ik0yOCAyMEgzNlYyOEgyOFYyMFoiIGZpbGw9IiNGRkZGRkYiLz4KPHBhdGggZD0iTTI0IDMySDQwVjQwSDI0VjMyWiIgZmlsbD0iI0ZGRkZGRiIvPgo8cGF0aCBkPSJNMjggNDRIMzZWNTJIMjhWNDRaIiBmaWxsPSIjRkZGRkZGIi8+Cjwvc3ZnPgo=",
        "points_value": 300
    },
    {
        "name": "Streak Legend",
        "description": "Maintained a 100-day learning streak",
        "badge_type": BadgeType.STREAK_LEGEND,
        "icon_url": "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMzIiIGN5PSIzMiIgcj0iMzAiIGZpbGw9IiM5MzMzRUEiIHN0cm9rZT0iIzc5MjZEOSIgc3Ryb2tlLXdpZHRoPSI0Ii8+CjxwYXRoIGQ9Ik0zMiAxNkwzOCAyNkgzMkwyNiAyNkwzMiAxNloiIGZpbGw9IiNGRkZGRkYiLz4KPHBhdGggZD0iTTMyIDMwTDM4IDQwSDMyTDI2IDQwTDMyIDMwWiIgZmlsbD0iI0ZGRkZGRiIvPgo8cGF0aCBkPSJNMzIgNDRMMzggNTRIMzJMMjYgNTRMMzIgNDRaIiBmaWxsPSIjRkZGRkZGIi8+Cjwvc3ZnPgo=",
        "points_value": 1000
    },
    {
        "name": "Streak Immortal",
        "description": "Maintained a 365-day learning streak",
        "badge_type": BadgeType.STREAK_IMMORTAL,
        "icon_url": "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMzIiIGN5PSIzMiIgcj0iMzAiIGZpbGw9InVybCgjZ3JhZGllbnQpIiBzdHJva2U9IiNGRkQ3MDAiIHN0cm9rZS13aWR0aD0iNCIvPgo8ZGVmcz4KPGxpbmVhckdyYWRpZW50IGlkPSJncmFkaWVudCI+CjxzdG9wIG9mZnNldD0iMCUiIHN0b3AtY29sb3I9IiNGRkQ3MDAiLz4KPHN0b3Agb2Zmc2V0PSIxMDAlIiBzdG9wLWNvbG9yPSIjRkY1NzIyIi8+CjwvbGluZWFyR3JhZGllbnQ+CjwvZGVmcz4KPHBhdGggZD0iTTMyIDEyTDQwIDI4SDMyTDI0IDI4TDMyIDEyWiIgZmlsbD0iI0ZGRkZGRiIvPgo8cGF0aCBkPSJNMzIgMzJMNDAgNDhIMzJMMjQgNDhMMzIgMzJaIiBmaWxsPSIjRkZGRkZGIi8+Cjwvc3ZnPgo=",
        "points_value": 2500
    },

    # Speed Badges
    {
        "name": "Speed Demon",
        "description": "Answer 10 questions in under 30 seconds each",
        "badge_type": BadgeType.SPEED_DEMON,
        "icon_url": "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMzIiIGN5PSIzMiIgcj0iMzAiIGZpbGw9IiNGRkVCM0IiIHN0cm9rZT0iI0Y1OUUwQiIgc3Ryb2tlLXdpZHRoPSI0Ii8+CjxwYXRoIGQ9Ik0yMCAzMkwzMiAyMEw0NCAzMkwzMiA0NEwyMCAzMloiIGZpbGw9IiNGRkZGRkYiLz4KPC9zdmc+Cg==",
        "points_value": 150
    },
    {
        "name": "Lightning Fast",
        "description": "Answer 25 questions in under 15 seconds each",
        "badge_type": BadgeType.LIGHTNING_FAST,
        "icon_url": "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMzIiIGN5PSIzMiIgcj0iMzAiIGZpbGw9IiNGRkVCM0IiIHN0cm9rZT0iI0Y1OUUwQiIgc3Ryb2tlLXdpZHRoPSI0Ii8+CjxwYXRoIGQ9Ik0zMiAxNkwzNiAyOEgzMkwyOCAyOEwzMiAxNloiIGZpbGw9IiNGRkZGRkYiLz4KPHBhdGggZD0iTTMyIDM2TDM2IDQ4SDMyTDI4IDQ4TDMyIDM2WiIgZmlsbD0iI0ZGRkZGRiIvPgo8L3N2Zz4K",
        "points_value": 250
    },
    {
        "name": "Flash Master",
        "description": "Answer 50 questions in under 10 seconds each",
        "badge_type": BadgeType.FLASH_MASTER,
        "icon_url": "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMzIiIGN5PSIzMiIgcj0iMzAiIGZpbGw9InVybCgjZmxhc2gpIiBzdHJva2U9IiNGNTlFMEIiIHN0cm9rZS13aWR0aD0iNCIvPgo8ZGVmcz4KPGxpbmVhckdyYWRpZW50IGlkPSJmbGFzaCI+CjxzdG9wIG9mZnNldD0iMCUiIHN0b3AtY29sb3I9IiNGRkVCM0IiLz4KPHN0b3Agb2Zmc2V0PSIxMDAlIiBzdG9wLWNvbG9yPSIjRkZEQjAwIi8+CjwvbGluZWFyR3JhZGllbnQ+CjwvZGVmcz4KPHBhdGggZD0iTTI4IDE2TDM2IDMySDI4TDM2IDQ4TDI4IDMySDM2TDI4IDE2WiIgZmlsbD0iI0ZGRkZGRiIvPgo8L3N2Zz4K",
        "points_value": 500
    },

    # Accuracy Badges
    {
        "name": "Sharpshooter",
        "description": "Maintain 90% accuracy over 100 questions",
        "badge_type": BadgeType.SHARPSHOOTER,
        "icon_url": "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMzIiIGN5PSIzMiIgcj0iMzAiIGZpbGw9IiNFRjQ0NDQiIHN0cm9rZT0iI0RDMjYyNiIgc3Ryb2tlLXdpZHRoPSI0Ii8+CjxjaXJjbGUgY3g9IjMyIiBjeT0iMzIiIHI9IjE4IiBmaWxsPSIjRkZGRkZGIi8+CjxjaXJjbGUgY3g9IjMyIiBjeT0iMzIiIHI9IjEyIiBmaWxsPSIjRUY0NDQ0Ii8+CjxjaXJjbGUgY3g9IjMyIiBjeT0iMzIiIHI9IjYiIGZpbGw9IiNGRkZGRkYiLz4KPC9zdmc+Cg==",
        "points_value": 200
    },
    {
        "name": "Precision Master",
        "description": "Maintain 95% accuracy over 200 questions",
        "badge_type": BadgeType.PRECISION_MASTER,
        "icon_url": "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMzIiIGN5PSIzMiIgcj0iMzAiIGZpbGw9IiNEQzI2MjYiIHN0cm9rZT0iI0I5MTQxNCIgc3Ryb2tlLXdpZHRoPSI0Ii8+CjxjaXJjbGUgY3g9IjMyIiBjeT0iMzIiIHI9IjIwIiBmaWxsPSIjRkZGRkZGIi8+CjxjaXJjbGUgY3g9IjMyIiBjeT0iMzIiIHI9IjE0IiBmaWxsPSIjREMyNjI2Ii8+CjxjaXJjbGUgY3g9IjMyIiBjeT0iMzIiIHI9IjgiIGZpbGw9IiNGRkZGRkYiLz4KPGNpcmNsZSBjeD0iMzIiIGN5PSIzMiIgcj0iNCIgZmlsbD0iI0RDMjYyNiIvPgo8L3N2Zz4K",
        "points_value": 400
    },
    {
        "name": "Perfectionist",
        "description": "Maintain 98% accuracy over 500 questions",
        "badge_type": BadgeType.PERFECTIONIST,
        "icon_url": "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMzIiIGN5PSIzMiIgcj0iMzAiIGZpbGw9InVybCgjcGVyZmVjdCkiIHN0cm9rZT0iI0I5MTQxNCIgc3Ryb2tlLXdpZHRoPSI0Ii8+CjxkZWZzPgo8cmFkaWFsR3JhZGllbnQgaWQ9InBlcmZlY3QiPgo8c3RvcCBvZmZzZXQ9IjAlIiBzdG9wLWNvbG9yPSIjRkZEQjAwIi8+CjxzdG9wIG9mZnNldD0iMTAwJSIgc3RvcC1jb2xvcj0iI0RDMjYyNiIvPgo8L3JhZGlhbEdyYWRpZW50Pgo8L2RlZnM+CjxjaXJjbGUgY3g9IjMyIiBjeT0iMzIiIHI9IjIyIiBmaWxsPSIjRkZGRkZGIi8+CjxjaXJjbGUgY3g9IjMyIiBjeT0iMzIiIHI9IjE2IiBmaWxsPSJ1cmwoI3BlcmZlY3QpIi8+CjxjaXJjbGUgY3g9IjMyIiBjeT0iMzIiIHI9IjEwIiBmaWxsPSIjRkZGRkZGIi8+CjxjaXJjbGUgY3g9IjMyIiBjeT0iMzIiIHI9IjQiIGZpbGw9IiNGRkQ3MDAiLz4KPC9zdmc+Cg==",
        "points_value": 800
    },

    # Progress Badges
    {
        "name": "Early Bird",
        "description": "Complete 50 sessions before 9 AM",
        "badge_type": BadgeType.EARLY_BIRD,
        "icon_url": "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMzIiIGN5PSIzMiIgcj0iMzAiIGZpbGw9IiNGRkVCM0IiIHN0cm9rZT0iI0Y1OUUwQiIgc3Ryb2tlLXdpZHRoPSI0Ii8+CjxjaXJjbGUgY3g9IjMyIiBjeT0iMzIiIHI9IjE4IiBmaWxsPSIjRkZGRkZGIi8+CjxwYXRoIGQ9Ik0zMiAyMFYzMkw0MCAzNiIgc3Ryb2tlPSIjRjU5RTBCIiBzdHJva2Utd2lkdGg9IjMiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIvPgo8L3N2Zz4K",
        "points_value": 150
    },
    {
        "name": "Night Owl",
        "description": "Complete 50 sessions after 10 PM",
        "badge_type": BadgeType.NIGHT_OWL,
        "icon_url": "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMzIiIGN5PSIzMiIgcj0iMzAiIGZpbGw9IiMzNzMwNjYiIHN0cm9rZT0iIzI1MjA0QSIgc3Ryb2tlLXdpZHRoPSI0Ii8+CjxjaXJjbGUgY3g9IjMyIiBjeT0iMzIiIHI9IjE4IiBmaWxsPSIjRkZGRkZGIi8+CjxwYXRoIGQ9Ik0zMiAyMFYzMkwyNCAzNiIgc3Ryb2tlPSIjMzczMDY2IiBzdHJva2Utd2lkdGg9IjMiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIvPgo8Y2lyY2xlIGN4PSI0NCIgY3k9IjIwIiByPSI0IiBmaWxsPSIjRkZGRkZGIi8+Cjwvc3ZnPgo=",
        "points_value": 150
    },
    {
        "name": "Weekend Warrior",
        "description": "Complete 25 sessions on weekends",
        "badge_type": BadgeType.WEEKEND_WARRIOR,
        "icon_url": "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMzIiIGN5PSIzMiIgcj0iMzAiIGZpbGw9IiMxMEI5ODEiIHN0cm9rZT0iIzA1OTY2OSIgc3Ryb2tlLXdpZHRoPSI0Ii8+CjxwYXRoIGQ9Ik0yMCAyNEg0NFYzNkgyMFYyNFoiIGZpbGw9IiNGRkZGRkYiLz4KPHBhdGggZD0iTTI0IDQwSDQwVjQ0SDI0VjQwWiIgZmlsbD0iI0ZGRkZGRiIvPgo8L3N2Zz4K",
        "points_value": 125
    },
    {
        "name": "Comeback Kid",
        "description": "Return after 30 days of inactivity",
        "badge_type": BadgeType.COMEBACK_KID,
        "icon_url": "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMzIiIGN5PSIzMiIgcj0iMzAiIGZpbGw9IiM2MzY2RjEiIHN0cm9rZT0iIzQ5NEJGRiIgc3Ryb2tlLXdpZHRoPSI0Ii8+CjxwYXRoIGQ9Ik0yNCAzMkMzMiAyNCA0MCAzMiA0MCAzMkMzMiA0MCAyNCAzMiAyNCAzMloiIGZpbGw9IiNGRkZGRkYiLz4KPC9zdmc+Cg==",
        "points_value": 200
    },

    # Special Badges
    {
        "name": "First Login",
        "description": "Welcome to the platform!",
        "badge_type": BadgeType.FIRST_LOGIN,
        "icon_url": "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMzIiIGN5PSIzMiIgcj0iMzAiIGZpbGw9IiM0Qzc5RkYiIHN0cm9rZT0iIzJENTVGRiIgc3Ryb2tlLXdpZHRoPSI0Ii8+CjxwYXRoIGQ9Ik0yNCAzMkwzMCA0Mkw0NCAyOCIgc3Ryb2tlPSIjRkZGRkZGIiBzdHJva2Utd2lkdGg9IjQiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8L3N2Zz4K",
        "points_value": 50
    },
    {
        "name": "Profile Complete",
        "description": "Complete your profile information",
        "badge_type": BadgeType.PROFILE_COMPLETE,
        "icon_url": "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMzIiIGN5PSIzMiIgcj0iMzAiIGZpbGw9IiM4QjVDRjYiIHN0cm9rZT0iIzc0NEQ5RiIgc3Ryb2tlLXdpZHRoPSI0Ii8+CjxjaXJjbGUgY3g9IjMyIiBjeT0iMjYiIHI9IjgiIGZpbGw9IiNGRkZGRkYiLz4KPHBhdGggZD0iTTIwIDQ4QzIwIDQwIDI1IDM2IDMyIDM2UzQ0IDQwIDQ0IDQ4IiBmaWxsPSIjRkZGRkZGIi8+Cjwvc3ZnPgo=",
        "points_value": 100
    },
    {
        "name": "Explorer",
        "description": "Visit all sections of the platform",
        "badge_type": BadgeType.EXPLORER,
        "icon_url": "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMzIiIGN5PSIzMiIgcj0iMzAiIGZpbGw9IiMwNTk2NjkiIHN0cm9rZT0iIzA0N0M1NyIgc3Ryb2tlLXdpZHRoPSI0Ii8+CjxjaXJjbGUgY3g9IjMyIiBjeT0iMzIiIHI9IjE4IiBmaWxsPSIjRkZGRkZGIi8+CjxwYXRoIGQ9Ik0zMiAyMEwzNiAzMkgzMkwyOCAzMkwzMiAyMFoiIGZpbGw9IiMwNTk2NjkiLz4KPC9zdmc+Cg==",
        "points_value": 75
    },

    # Milestone Badges
    {
        "name": "Hundred Club",
        "description": "Earn 100 total points",
        "badge_type": BadgeType.HUNDRED_CLUB,
        "icon_url": "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMzIiIGN5PSIzMiIgcj0iMzAiIGZpbGw9IiNDMEM0Q0MiIHN0cm9rZT0iI0E1QTlCNCIgc3Ryb2tlLXdpZHRoPSI0Ii8+CjxwYXRoIGQ9Ik0yNCAyNEg0MFYzNkgyNFYyNFoiIGZpbGw9IiNGRkZGRkYiLz4KPHRleHQgeD0iMzIiIHk9IjMyIiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMTIiIGZpbGw9IiNDMEM0Q0MiIHRleHQtYW5jaG9yPSJtaWRkbGUiPjEwMDwvdGV4dD4KPC9zdmc+Cg==",
        "points_value": 25
    },
    {
        "name": "Thousand Club",
        "description": "Earn 1,000 total points",
        "badge_type": BadgeType.THOUSAND_CLUB,
        "icon_url": "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMzIiIGN5PSIzMiIgcj0iMzAiIGZpbGw9IiNGRkQ3MDAiIHN0cm9rZT0iI0ZGQjAwMCIgc3Ryb2tlLXdpZHRoPSI0Ii8+CjxwYXRoIGQ9Ik0yMCAyMEg0NFY0MEgyMFYyMFoiIGZpbGw9IiNGRkZGRkYiLz4KPHRleHQgeD0iMzIiIHk9IjMyIiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMTAiIGZpbGw9IiNGRkQ3MDAiIHRleHQtYW5jaG9yPSJtaWRkbGUiPjFLPC90ZXh0Pgo8L3N2Zz4K",
        "points_value": 100
    },
    {
        "name": "Elite Club",
        "description": "Earn 10,000 total points",
        "badge_type": BadgeType.ELITE_CLUB,
        "icon_url": "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMzIiIGN5PSIzMiIgcj0iMzAiIGZpbGw9InVybCgjZWxpdGUpIiBzdHJva2U9IiM5MzMzRUEiIHN0cm9rZS13aWR0aD0iNCIvPgo8ZGVmcz4KPGxpbmVhckdyYWRpZW50IGlkPSJlbGl0ZSI+CjxzdG9wIG9mZnNldD0iMCUiIHN0b3AtY29sb3I9IiM5MzMzRUEiLz4KPHN0b3Agb2Zmc2V0PSIxMDAlIiBzdG9wLWNvbG9yPSIjNzkyNkQ5Ii8+CjwvbGluZWFyR3JhZGllbnQ+CjwvZGVmcz4KPHBhdGggZD0iTTE4IDE4SDQ2VjQ2SDE4VjE4WiIgZmlsbD0iI0ZGRkZGRiIvPgo8dGV4dCB4PSIzMiIgeT0iMzQiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSI5IiBmaWxsPSIjOTMzM0VBIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIj4xMEs8L3RleHQ+Cjwvc3ZnPgo=",
        "points_value": 500
    },
    {
        "name": "Legendary Club",
        "description": "Earn 100,000 total points",
        "badge_type": BadgeType.LEGENDARY_CLUB,
        "icon_url": "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMzIiIGN5PSIzMiIgcj0iMzAiIGZpbGw9InVybCgjbGVnZW5kYXJ5KSIgc3Ryb2tlPSIjRkZEQjAwIiBzdHJva2Utd2lkdGg9IjQiLz4KPGRlZnM+CjxyYWRpYWxHcmFkaWVudCBpZD0ibGVnZW5kYXJ5Ij4KPHN0b3Agb2Zmc2V0PSIwJSIgc3RvcC1jb2xvcj0iI0ZGRDcwMCIvPgo8c3RvcCBvZmZzZXQ9IjUwJSIgc3RvcC1jb2xvcj0iI0ZGNTcyMiIvPgo8c3RvcCBvZmZzZXQ9IjEwMCUiIHN0b3AtY29sb3I9IiM5MzMzRUEiLz4KPC9yYWRpYWxHcmFkaWVudD4KPC9kZWZzPgo8cGF0aCBkPSJNMTYgMTZINDhWNDhIMTZWMTZaIiBmaWxsPSIjRkZGRkZGIi8+Cjx0ZXh0IHg9IjMyIiB5PSIzNiIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjgiIGZpbGw9InVybCgjbGVnZW5kYXJ5KSIgdGV4dC1hbmNob3I9Im1pZGRsZSI+MTAwSzwvdGV4dD4KPC9zdmc+Cg==",
        "points_value": 2000
    },

    # Additional Special Badges
    {
        "name": "Collector",
        "description": "Earn 10 different badges",
        "badge_type": BadgeType.COLLECTOR,
        "icon_url": "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMzIiIGN5PSIzMiIgcj0iMzAiIGZpbGw9IiNGNTlFMEIiIHN0cm9rZT0iI0Q5N0YwNiIgc3Ryb2tlLXdpZHRoPSI0Ii8+CjxyZWN0IHg9IjIwIiB5PSIyMCIgd2lkdGg9IjI0IiBoZWlnaHQ9IjI0IiBmaWxsPSIjRkZGRkZGIi8+CjxyZWN0IHg9IjI0IiB5PSIyNCIgd2lkdGg9IjE2IiBoZWlnaHQ9IjE2IiBmaWxsPSIjRjU5RTBCIi8+Cjwvc3ZnPgo=",
        "points_value": 300
    },
    {
        "name": "Overachiever",
        "description": "Earn 25 different badges",
        "badge_type": BadgeType.OVERACHIEVER,
        "icon_url": "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMzIiIGN5PSIzMiIgcj0iMzAiIGZpbGw9InVybCgjb3ZlcmFjaGlldmVyKSIgc3Ryb2tlPSIjRkZEQjAwIiBzdHJva2Utd2lkdGg9IjQiLz4KPGRlZnM+CjxyYWRpYWxHcmFkaWVudCBpZD0ib3ZlcmFjaGlldmVyIj4KPHN0b3Agb2Zmc2V0PSIwJSIgc3RvcC1jb2xvcj0iI0ZGRDcwMCIvPgo8c3RvcCBvZmZzZXQ9IjEwMCUiIHN0b3AtY29sb3I9IiNGRjU3MjIiLz4KPC9yYWRpYWxHcmFkaWVudD4KPC9kZWZzPgo8cGF0aCBkPSJNMzIgMTJMMzggMjZIMzJMMjYgMjZMMzIgMTJaIiBmaWxsPSIjRkZGRkZGIi8+CjxwYXRoIGQ9Ik0zMiAzOEwzOCA1MkgzMkwyNiA1MkwzMiAzOFoiIGZpbGw9IiNGRkZGRkYiLz4KPGNpcmNsZSBjeD0iMzIiIGN5PSIzMiIgcj0iNiIgZmlsbD0iI0ZGRkZGRiIvPgo8L3N2Zz4K",
        "points_value": 1000
    }
]


def get_level_for_points(points: int) -> Dict[str, Any]:
    """Get the level information for a given point total"""
    for level in LEVELS:
        if level["min_points"] <= points <= level["max_points"]:
            return level
    # Default to highest level if points exceed all defined levels
    return LEVELS[-1]


def initialize_gamification_data(db: Session) -> None:
    """Initialize default badges and levels in the database"""
    try:
        # Create default levels if they don't exist
        existing_levels = db.execute(text("SELECT COUNT(*) FROM userlevel")).scalar() or 0
        if existing_levels == 0:
            for level_data in LEVELS:
                # Insert level using raw SQL
                db.execute(
                    text("""
                        INSERT INTO userlevel
                        (level_number, name, min_points, max_points, icon_url)
                        VALUES (:level_number, :name, :min_points, :max_points, :icon_url)
                    """),
                    {
                        "level_number": level_data["level_number"],
                        "name": level_data["name"],
                        "min_points": level_data["min_points"],
                        "max_points": level_data["max_points"],
                        "icon_url": level_data["icon_url"]
                    }
                )
            db.commit()
            print("Default levels created successfully")

        # Create default badges if they don't exist
        existing_badges = db.execute(text("SELECT COUNT(*) FROM badge")).scalar() or 0
        if existing_badges == 0:
            for badge_data in DEFAULT_BADGES:
                # Insert badge using raw SQL
                db.execute(
                    text("""
                        INSERT INTO badge
                        (name, description, badge_type, icon_url, points_value)
                        VALUES (:name, :description, :badge_type, :icon_url, :points_value)
                    """),
                    {
                        "name": badge_data["name"],
                        "description": badge_data["description"],
                        "badge_type": badge_data["badge_type"],
                        "icon_url": badge_data["icon_url"],
                        "points_value": badge_data["points_value"]
                    }
                )
            db.commit()
            print("Default badges created successfully")
    except Exception as e:
        print(f"Error initializing gamification data: {e}")
        db.rollback()


def award_points(
    db: Session,
    user_id: int,
    points: int,
    description: str,
    transaction_type: str,
    reference_id: Optional[int] = None
) -> PointsTransaction:
    """Award points to a user and create a transaction record"""
    transaction = PointsTransaction(
        user_id=user_id,
        points=points,
        description=description,
        transaction_type=transaction_type,
        reference_id=reference_id
    )
    db.add(transaction)
    db.commit()
    db.refresh(transaction)

    # Check for level-up achievements
    check_for_level_achievements(db, user_id)

    return transaction


def award_badge(
    db: Session,
    user_id: int,
    badge_id: int
) -> Optional[UserBadge]:
    """Award a badge to a user if they don't already have it"""
    # Check if user already has this badge
    existing_badge = db.query(UserBadge).filter(
        UserBadge.user_id == user_id,
        UserBadge.badge_id == badge_id
    ).first()

    if existing_badge:
        return None

    # Get the badge to award points
    badge = db.query(Badge).filter(Badge.id == badge_id).first()
    if not badge:
        return None

    # Create user badge
    user_badge = UserBadge(
        user_id=user_id,
        badge_id=badge_id
    )
    db.add(user_badge)

    # Award points for earning the badge
    award_points(
        db=db,
        user_id=user_id,
        points=badge.points_value,
        description=f"Earned the '{badge.name}' badge",
        transaction_type="badge_earned",
        reference_id=badge_id
    )

    db.commit()
    db.refresh(user_badge)
    return user_badge


def update_streak(db: Session, user_id: int) -> UserStreak:
    """Update a user's learning streak"""
    today = datetime.now(timezone.utc).date()

    # Get or create user streak
    user_streak = db.query(UserStreak).filter(UserStreak.user_id == user_id).first()
    if not user_streak:
        user_streak = UserStreak(
            user_id=user_id,
            current_streak=1,
            longest_streak=1,
            last_activity_date=datetime.now(timezone.utc)
        )
        db.add(user_streak)

        # Award points for first day streak
        award_points(
            db=db,
            user_id=user_id,
            points=POINTS["STREAK_DAY"],
            description="Started a learning streak",
            transaction_type="streak_day"
        )
    else:
        last_activity = user_streak.last_activity_date.date()

        # If last activity was yesterday, increment streak
        if (today - last_activity).days == 1:
            user_streak.current_streak += 1
            user_streak.last_activity_date = datetime.now(timezone.utc)

            # Update longest streak if current is longer
            if user_streak.current_streak > user_streak.longest_streak:
                user_streak.longest_streak = user_streak.current_streak

            # Award points for continuing streak
            award_points(
                db=db,
                user_id=user_id,
                points=POINTS["STREAK_DAY"],
                description=f"Continued learning streak: {user_streak.current_streak} days",
                transaction_type="streak_day"
            )

            # Check for streak badges
            check_for_streak_achievements(db, user_id, user_streak.current_streak)

        # If last activity was today, just update the timestamp
        elif (today - last_activity).days == 0:
            user_streak.last_activity_date = datetime.now(timezone.utc)

        # If more than 1 day has passed, reset streak
        else:
            user_streak.current_streak = 1
            user_streak.last_activity_date = datetime.now(timezone.utc)

            # Award points for new streak
            award_points(
                db=db,
                user_id=user_id,
                points=POINTS["STREAK_DAY"],
                description="Started a new learning streak",
                transaction_type="streak_day"
            )

    db.commit()
    db.refresh(user_streak)
    return user_streak


def check_for_streak_achievements(db: Session, user_id: int, streak_days: int) -> None:
    """Check and award streak-related achievements"""
    if streak_days >= 7:
        # Award 7-day streak badge
        streak_badge = db.query(Badge).filter(Badge.badge_type == BadgeType.STREAK).first()
        if streak_badge:
            award_badge(db, user_id, streak_badge.id)

    if streak_days >= 5:
        # Award consistent learner badge
        consistent_badge = db.query(Badge).filter(Badge.badge_type == BadgeType.CONSISTENT_LEARNER).first()
        if consistent_badge:
            award_badge(db, user_id, consistent_badge.id)


def check_for_level_achievements(db: Session, user_id: int) -> None:
    """Check for level-up achievements"""
    # This could be expanded with level-specific badges
    pass


def check_for_exam_achievements(db: Session, user_id: int, exam_id: int) -> None:
    """Check and award exam-related achievements"""
    exam = db.query(StudentExam).filter(StudentExam.id == exam_id).first()
    if not exam:
        return

    # Check for perfect score
    if exam.score == 100.0:
        perfect_score_badge = db.query(Badge).filter(
            Badge.badge_type == BadgeType.FIRST_PERFECT_SCORE
        ).first()
        if perfect_score_badge:
            award_badge(db, user_id, perfect_score_badge.id)

    # Count total exams for this user
    exam_count = db.query(StudentExam).filter(
        StudentExam.student_id == user_id
    ).count()

    # Could add more achievements based on exam count


def check_for_practice_achievements(db: Session, user_id: int) -> None:
    """Check and award practice-related achievements"""
    # Count practice attempts
    practice_count = db.query(StudentQuestionAttempt).filter(
        StudentQuestionAttempt.student_id == user_id
    ).count()

    if practice_count >= 100:
        practice_master_badge = db.query(Badge).filter(
            Badge.badge_type == BadgeType.PRACTICE_MASTER
        ).first()
        if practice_master_badge:
            award_badge(db, user_id, practice_master_badge.id)

    # Check for recent correct answers (last 20 attempts)
    recent_attempts = db.query(StudentQuestionAttempt).filter(
        StudentQuestionAttempt.student_id == user_id
    ).order_by(
        StudentQuestionAttempt.attempt_time.desc()
    ).limit(20).all()

    # Count consecutive correct answers from the most recent attempts
    consecutive_correct = 0
    for attempt in recent_attempts:
        if attempt.is_correct:
            consecutive_correct += 1
        else:
            break

    if consecutive_correct >= 20:
        quick_learner_badge = db.query(Badge).filter(
            Badge.badge_type == BadgeType.QUICK_LEARNER
        ).first()
        if quick_learner_badge:
            award_badge(db, user_id, quick_learner_badge.id)


def get_user_badges(db: Session, user_id: int) -> List[Dict[str, Any]]:
    """Get all badges earned by a user with badge details"""
    user_badges = db.query(UserBadge, Badge).join(
        Badge, UserBadge.badge_id == Badge.id
    ).filter(
        UserBadge.user_id == user_id
    ).all()

    return [
        {
            "badge_id": badge.id,
            "id": badge.id,
            "name": badge.name,
            "description": badge.description,
            "badge_type": badge.badge_type,
            "icon_url": badge.icon_url,
            "points_value": badge.points_value,
            "earned_at": user_badge.earned_at
        }
        for user_badge, badge in user_badges
    ]


def get_user_points(db: Session, user_id: int) -> int:
    """Get total points for a user"""
    total_points = db.query(func.sum(PointsTransaction.points)).filter(
        PointsTransaction.user_id == user_id
    ).scalar() or 0

    return total_points


def get_leaderboard(
    db: Session,
    limit: int = 10,
    school_id: Optional[int] = None,
    department_id: Optional[int] = None,
    course_id: Optional[int] = None,
    time_period: Optional[str] = None
) -> List[Dict[str, Any]]:
    """
    Get top users by points for the leaderboard with optional filtering

    Args:
        db: Database session
        limit: Number of users to return
        school_id: Filter by school ID
        department_id: Filter by department ID
        course_id: Filter by course ID
        time_period: Filter by time period ('daily', 'weekly', 'all_time')
    """
    try:
        # Build query parameters
        params = {"limit": limit}

        # Use a simpler query approach to avoid circular import issues
        # First, get all students that match the filters
        student_query = """
            SELECT DISTINCT
                u.id,
                u.full_name,
                u.profile_picture_url,
                s.name as school_name,
                d.name as department_name
            FROM
                "user" u
            LEFT JOIN
                school s ON u.institution_id = s.id
            LEFT JOIN
                department d ON u.department_id = d.id
        """

        # Add course join only if course_id is provided
        if course_id is not None:
            student_query += """
            INNER JOIN
                user_course uc ON u.id = uc.user_id
            """
        else:
            student_query += """
            LEFT JOIN
                user_course uc ON u.id = uc.user_id
            """

        # Start building WHERE clause
        # Use the correct enum value for student role
        where_clauses = ["u.role = :role"]
        params["role"] = "STUDENT"

        # Add school filter
        if school_id is not None:
            where_clauses.append("u.institution_id = :school_id")
            params["school_id"] = school_id

        # Add department filter
        if department_id is not None:
            where_clauses.append("u.department_id = :department_id")
            params["department_id"] = department_id

        # Add course filter
        if course_id is not None:
            where_clauses.append("uc.course_id = :course_id")
            params["course_id"] = course_id

        # Combine WHERE clauses
        student_query += " WHERE " + " AND ".join(where_clauses)

        print(f"Student query: {student_query}")
        print(f"Params: {params}")

        # Get the students with error handling
        try:
            students = db.execute(text(student_query), params).fetchall()
            print(f"Found {len(students)} students")
        except Exception as query_error:
            print(f"Error executing student query: {query_error}")
            # Try to rollback and return empty result
            try:
                db.rollback()
            except Exception:
                pass
            return []

        # For each student, get their points, badges, and streak
        result = []
        for student in students:
            try:
                # Get points with time period filter
                points_query = """
                    SELECT COALESCE(SUM(points), 0) as total_points
                    FROM pointstransaction
                    WHERE user_id = :user_id
                """

                points_params = {"user_id": student.id}

                # Add time period filter
                if time_period:
                    if time_period == 'daily':
                        points_query += " AND DATE(created_at) = CURRENT_DATE"
                    elif time_period == 'weekly':
                        # Use proper date calculation for PostgreSQL
                        points_query += " AND created_at >= (CURRENT_DATE - INTERVAL '7 days')"
                    # 'all_time' doesn't need a filter as it includes all points

                print(f"Points query for user {student.id}: {points_query}")
                print(f"Points params: {points_params}")

                points = db.execute(text(points_query), points_params).scalar() or 0
                print(f"User {student.id} points: {points}")

                # Get badge count
                badge_count = db.execute(text("""
                    SELECT COUNT(DISTINCT badge_id) as badge_count
                    FROM userbadge
                    WHERE user_id = :user_id
                """), {"user_id": student.id}).scalar() or 0

                # Get streak
                streak = db.execute(text("""
                    SELECT COALESCE(current_streak, 0) as current_streak
                    FROM userstreak
                    WHERE user_id = :user_id
                """), {"user_id": student.id}).scalar() or 0

                user_level = get_level_for_points(points)
                result.append({
                    "user_id": student.id,
                    "full_name": student.full_name,
                    "profile_picture_url": student.profile_picture_url,
                    "total_points": points,
                    "badge_count": badge_count,
                    "current_streak": streak,
                    "level": user_level,
                    "school_name": student.school_name,
                    "department_name": student.department_name
                })
            except Exception as student_error:
                print(f"Error processing student {student.id}: {student_error}")
                # Continue with next student instead of failing completely
                continue

        # Sort by total points
        result.sort(key=lambda x: x["total_points"], reverse=True)

        # Apply limit
        result = result[:limit]

        # Log the result
        print(f"Leaderboard entries: {len(result)}")
        for entry in result:
            print(f"User: {entry['full_name']}, Points: {entry['total_points']}, Badges: {entry['badge_count']}, Streak: {entry['current_streak']}")

        return result
    except Exception as e:
        print(f"Error in get_leaderboard: {e}")
        # Return empty list in case of error
        return []


def get_user_total_points(db: Session, user_id: int) -> int:
    """Get total points for a user"""
    total = db.execute(
        text("SELECT COALESCE(SUM(points), 0) FROM pointstransaction WHERE user_id = :user_id"),
        {"user_id": user_id}
    ).scalar()
    return total or 0


def check_for_milestone_achievements(db: Session, user_id: int) -> None:
    """Check and award milestone badges based on total points"""
    total_points = get_user_total_points(db, user_id)

    # Check for point milestone badges (updated for lower point system)
    milestones = [
        (50, BadgeType.HUNDRED_CLUB),
        (500, BadgeType.THOUSAND_CLUB),
        (2500, BadgeType.ELITE_CLUB),
        (10000, BadgeType.LEGENDARY_CLUB)
    ]

    for points_threshold, badge_type in milestones:
        if total_points >= points_threshold:
            badge = db.query(Badge).filter(Badge.badge_type == badge_type).first()
            if badge:
                award_badge(db, user_id, badge.id)


def check_for_accuracy_achievements(db: Session, user_id: int) -> None:
    """Check and award accuracy-based badges"""
    # Get recent attempts for accuracy calculation
    recent_attempts = db.query(StudentQuestionAttempt).filter(
        StudentQuestionAttempt.student_id == user_id
    ).order_by(StudentQuestionAttempt.attempt_time.desc()).limit(500).all()

    if len(recent_attempts) >= 100:
        correct_count = sum(1 for attempt in recent_attempts if attempt.is_correct)
        accuracy = correct_count / len(recent_attempts)

        # Award accuracy badges
        if accuracy >= 0.98 and len(recent_attempts) >= 500:
            badge = db.query(Badge).filter(Badge.badge_type == BadgeType.PERFECTIONIST).first()
            if badge:
                award_badge(db, user_id, badge.id)
        elif accuracy >= 0.95 and len(recent_attempts) >= 200:
            badge = db.query(Badge).filter(Badge.badge_type == BadgeType.PRECISION_MASTER).first()
            if badge:
                award_badge(db, user_id, badge.id)
        elif accuracy >= 0.90 and len(recent_attempts) >= 100:
            badge = db.query(Badge).filter(Badge.badge_type == BadgeType.SHARPSHOOTER).first()
            if badge:
                award_badge(db, user_id, badge.id)


def check_for_speed_achievements(db: Session, user_id: int, answer_time: float) -> None:
    """Check and award speed-based badges"""
    # Count fast answers
    fast_answers_10s = db.query(StudentQuestionAttempt).filter(
        StudentQuestionAttempt.student_id == user_id,
        StudentQuestionAttempt.time_taken <= 10,
        StudentQuestionAttempt.is_correct == True
    ).count()

    fast_answers_15s = db.query(StudentQuestionAttempt).filter(
        StudentQuestionAttempt.student_id == user_id,
        StudentQuestionAttempt.time_taken <= 15,
        StudentQuestionAttempt.is_correct == True
    ).count()

    fast_answers_30s = db.query(StudentQuestionAttempt).filter(
        StudentQuestionAttempt.student_id == user_id,
        StudentQuestionAttempt.time_taken <= 30,
        StudentQuestionAttempt.is_correct == True
    ).count()

    # Award speed badges
    if fast_answers_10s >= 50:
        badge = db.query(Badge).filter(Badge.badge_type == BadgeType.FLASH_MASTER).first()
        if badge:
            award_badge(db, user_id, badge.id)
    elif fast_answers_15s >= 25:
        badge = db.query(Badge).filter(Badge.badge_type == BadgeType.LIGHTNING_FAST).first()
        if badge:
            award_badge(db, user_id, badge.id)
    elif fast_answers_30s >= 10:
        badge = db.query(Badge).filter(Badge.badge_type == BadgeType.SPEED_DEMON).first()
        if badge:
            award_badge(db, user_id, badge.id)


def check_for_special_achievements(db: Session, user_id: int) -> None:
    """Check and award special badges"""
    # Check for first login badge
    first_login_badge = db.query(Badge).filter(Badge.badge_type == BadgeType.FIRST_LOGIN).first()
    if first_login_badge:
        award_badge(db, user_id, first_login_badge.id)

    # Check for profile completion badge
    profile_badge = db.query(Badge).filter(Badge.badge_type == BadgeType.PROFILE_COMPLETE).first()
    if profile_badge:
        award_badge(db, user_id, profile_badge.id)

    # Check for collector badge (10 different badges)
    user_badge_count = db.query(UserBadge).filter(UserBadge.user_id == user_id).count()
    if user_badge_count >= 10:
        collector_badge = db.query(Badge).filter(Badge.badge_type == BadgeType.COLLECTOR).first()
        if collector_badge:
            award_badge(db, user_id, collector_badge.id)

    # Check for overachiever badge (25 different badges)
    if user_badge_count >= 25:
        overachiever_badge = db.query(Badge).filter(Badge.badge_type == BadgeType.OVERACHIEVER).first()
        if overachiever_badge:
            award_badge(db, user_id, overachiever_badge.id)


def award_points_and_check_badges(
    db: Session,
    user_id: int,
    action: str,
    points_override: Optional[int] = None,
    **kwargs
) -> int:
    """Comprehensive function to award points and check for badge eligibility"""
    try:
        # Determine points to award
        points_earned = points_override if points_override is not None else POINTS.get(action, 0)

        # Add bonus points for special conditions
        bonus_description = ""
        if action == "CORRECT_ANSWER":
            # Speed bonus for quick answers
            answer_time = kwargs.get('answer_time', 0)
            if answer_time > 0 and answer_time <= 10:
                points_earned += POINTS.get("SPEED_BONUS", 0)
                bonus_description += " (Speed Bonus)"

            # Streak bonus
            streak_count = kwargs.get('streak_count', 0)
            if streak_count >= 20:
                points_earned += POINTS.get("QUESTION_STREAK_20", 0)
                bonus_description += " (20+ Streak)"
            elif streak_count >= 10:
                points_earned += POINTS.get("QUESTION_STREAK_10", 0)
                bonus_description += " (10+ Streak)"
            elif streak_count >= 5:
                points_earned += POINTS.get("QUESTION_STREAK_5", 0)
                bonus_description += " (5+ Streak)"

        # Award points if any earned
        if points_earned > 0:
            award_points(
                db=db,
                user_id=user_id,
                points=points_earned,
                description=f"Points earned for {action.replace('_', ' ').lower()}{bonus_description}",
                transaction_type=action.lower()
            )

        # Check for various badge achievements based on action
        if action == "EXAM_COMPLETION":
            exam_id = kwargs.get('exam_id')
            if exam_id:
                check_for_exam_achievements(db, user_id, exam_id)
        elif action == "PRACTICE_COMPLETION":
            check_for_practice_achievements(db, user_id)
        elif action == "STREAK_DAY":
            streak_days = kwargs.get('streak_days', 1)
            check_for_streak_achievements(db, user_id, streak_days)
        elif action == "FIRST_LOGIN":
            check_for_special_achievements(db, user_id)
        elif action == "PROFILE_COMPLETION":
            check_for_special_achievements(db, user_id)

        # Always check for milestone achievements (point-based badges)
        check_for_milestone_achievements(db, user_id)

        # Check for accuracy-based achievements
        check_for_accuracy_achievements(db, user_id)

        # Check for speed-based achievements
        if action == "CORRECT_ANSWER" and kwargs.get('answer_time'):
            answer_time = kwargs.get('answer_time', 0)
            if isinstance(answer_time, (int, float)) and answer_time > 0:
                check_for_speed_achievements(db, user_id, float(answer_time))

        db.commit()
        return points_earned

    except Exception as e:
        db.rollback()
        print(f"Error awarding points and badges: {e}")
        return 0
