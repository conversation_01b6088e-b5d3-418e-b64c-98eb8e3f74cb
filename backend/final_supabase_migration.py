#!/usr/bin/env python3

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.db.session import SessionLocal
from sqlalchemy import text

def final_supabase_migration():
    """Final migration with corrected badge types"""
    db = SessionLocal()
    
    try:
        print("=== FINAL SUPABASE MIGRATION ===")
        
        # Check current state
        badge_count = db.execute(text("SELECT COUNT(*) FROM badge")).scalar()
        level_count = db.execute(text("SELECT COUNT(*) FROM userlevel")).scalar()
        
        print(f"Current state: {badge_count} badges, {level_count} levels")
        
        # Add key badges with correct enum values (using the ones that exist in DB)
        key_badges = [
            {
                "name": "First Perfect Score",
                "description": "Achieved a perfect score on an exam",
                "badge_type": "first_perfect_score",  # lowercase
                "icon_url": "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMzIiIGN5PSIzMiIgcj0iMzAiIGZpbGw9IiNGRkQ3MDAiIHN0cm9rZT0iI0ZGQjAwMCIgc3Ryb2tlLXdpZHRoPSI0Ii8+CjxwYXRoIGQ9Ik0yMCAzMkwzMCA0Mkw0NCAyOCIgc3Ryb2tlPSIjRkZGRkZGIiBzdHJva2Utd2lkdGg9IjQiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIgc3Ryb2tlLWxpbmVqb2luPSJyb3VuZCIvPgo8L3N2Zz4K",
                "points_value": 5
            },
            {
                "name": "Practice Master",
                "description": "Completed 25 practice sessions",
                "badge_type": "practice_master",  # lowercase
                "icon_url": "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMzIiIGN5PSIzMiIgcj0iMzAiIGZpbGw9IiM0Qzc5RkYiIHN0cm9rZT0iIzJENTVGRiIgc3Ryb2tlLXdpZHRoPSI0Ii8+CjxwYXRoIGQ9Ik0yNCAyNEg0MFYzNkgyNFYyNFoiIGZpbGw9IiNGRkZGRkYiLz4KPHBhdGggZD0iTTI2IDQwSDM4VjQ0SDI2VjQwWiIgZmlsbD0iI0ZGRkZGRiIvPgo8L3N2Zz4K",
                "points_value": 3
            },
            {
                "name": "Course Completion",
                "description": "Completed all questions in a course",
                "badge_type": "course_completion",  # lowercase
                "icon_url": "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMzIiIGN5PSIzMiIgcj0iMzAiIGZpbGw9IiMxMEI5ODEiIHN0cm9rZT0iIzA1OTY2OSIgc3Ryb2tlLXdpZHRoPSI0Ii8+CjxwYXRoIGQ9Ik0yMiAyNkg0MlYzOEgyMlYyNloiIGZpbGw9IiNGRkZGRkYiLz4KPHBhdGggZD0iTTI4IDMwSDM2VjM0SDI4VjMwWiIgZmlsbD0iIzEwQjk4MSIvPgo8L3N2Zz4K",
                "points_value": 5
            },
            {
                "name": "Quick Learner",
                "description": "Answered 50 questions correctly in a row",
                "badge_type": "quick_learner",  # lowercase
                "icon_url": "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMzIiIGN5PSIzMiIgcj0iMzAiIGZpbGw9IiNGRjZBMDAiIHN0cm9rZT0iI0VBNTUwNiIgc3Ryb2tlLXdpZHRoPSI0Ii8+CjxwYXRoIGQ9Ik0zMiAxNkwzNiAyOEgzMkwyOCAyOEwzMiAxNloiIGZpbGw9IiNGRkZGRkYiLz4KPHBhdGggZD0iTTMyIDM2TDM2IDQ4SDMyTDI4IDQ4TDMyIDM2WiIgZmlsbD0iI0ZGRkZGRiIvPgo8L3N2Zz4K",
                "points_value": 4
            },
            {
                "name": "Consistent Learner",
                "description": "Studied for 10 consecutive days",
                "badge_type": "consistent_learner",  # lowercase
                "icon_url": "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMzIiIGN5PSIzMiIgcj0iMzAiIGZpbGw9IiM4QjVDRjYiIHN0cm9rZT0iIzc0NEQ5RiIgc3Ryb2tlLXdpZHRoPSI0Ii8+CjxjaXJjbGUgY3g9IjMyIiBjeT0iMzIiIHI9IjEyIiBmaWxsPSIjRkZGRkZGIi8+CjxjaXJjbGUgY3g9IjMyIiBjeT0iMzIiIHI9IjYiIGZpbGw9IiM4QjVDRjYiLz4KPC9zdmc+Cg==",
                "points_value": 3
            },
            {
                "name": "Streak Champion",
                "description": "Maintained a 7-day learning streak",
                "badge_type": "streak",  # using existing 'streak' type
                "icon_url": "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMzIiIGN5PSIzMiIgcj0iMzAiIGZpbGw9IiNGRjU3MjIiIHN0cm9rZT0iI0VBNDMwQSIgc3Ryb2tlLXdpZHRoPSI0Ii8+CjxwYXRoIGQ9Ik0zMiAxNkwzNiAyNEgzMkwyOCAyNEwzMiAxNloiIGZpbGw9IiNGRkZGRkYiLz4KPHBhdGggZD0iTTMyIDI4TDM2IDM2SDMyTDI4IDM2TDMyIDI4WiIgZmlsbD0iI0ZGRkZGRiIvPgo8cGF0aCBkPSJNMzIgNDBMMzYgNDhIMzJMMjggNDhMMzIgNDBaIiBmaWxsPSIjRkZGRkZGIi8+Cjwvc3ZnPgo=",
                "points_value": 3
            },
            # Using uppercase types that exist
            {
                "name": "Speed Demon",
                "description": "Answer 10 questions in under 30 seconds each",
                "badge_type": "SPEED_DEMON",  # uppercase
                "icon_url": "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMzIiIGN5PSIzMiIgcj0iMzAiIGZpbGw9IiNGRkVCM0IiIHN0cm9rZT0iI0Y1OUUwQiIgc3Ryb2tlLXdpZHRoPSI0Ii8+CjxwYXRoIGQ9Ik0yMCAzMkwzMiAyMEw0NCAzMkwzMiA0NEwyMCAzMloiIGZpbGw9IiNGRkZGRkYiLz4KPC9zdmc+Cg==",
                "points_value": 4
            },
            {
                "name": "Sharpshooter",
                "description": "Maintain 90% accuracy over 100 questions",
                "badge_type": "SHARPSHOOTER",  # uppercase
                "icon_url": "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMzIiIGN5PSIzMiIgcj0iMzAiIGZpbGw9IiNFRjQ0NDQiIHN0cm9rZT0iI0RDMjYyNiIgc3Ryb2tlLXdpZHRoPSI0Ii8+CjxjaXJjbGUgY3g9IjMyIiBjeT0iMzIiIHI9IjE4IiBmaWxsPSIjRkZGRkZGIi8+CjxjaXJjbGUgY3g9IjMyIiBjeT0iMzIiIHI9IjEyIiBmaWxsPSIjRUY0NDQ0Ii8+CjxjaXJjbGUgY3g9IjMyIiBjeT0iMzIiIHI9IjYiIGZpbGw9IiNGRkZGRkYiLz4KPC9zdmc+Cg==",
                "points_value": 5
            },
            {
                "name": "Hundred Club",
                "description": "Earn 50 total points",
                "badge_type": "HUNDRED_CLUB",  # uppercase
                "icon_url": "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMzIiIGN5PSIzMiIgcj0iMzAiIGZpbGw9IiNDMEM0Q0MiIHN0cm9rZT0iI0E1QTlCNCIgc3Ryb2tlLXdpZHRoPSI0Ii8+CjxwYXRoIGQ9Ik0yNCAyNEg0MFYzNkgyNFYyNFoiIGZpbGw9IiNGRkZGRkYiLz4KPHRleHQgeD0iMzIiIHk9IjMyIiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMTIiIGZpbGw9IiNDMEM0Q0MiIHRleHQtYW5jaG9yPSJtaWRkbGUiPjEwMDwvdGV4dD4KPC9zdmc+Cg==",
                "points_value": 1
            },
            {
                "name": "Thousand Club",
                "description": "Earn 500 total points",
                "badge_type": "THOUSAND_CLUB",  # uppercase
                "icon_url": "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMzIiIGN5PSIzMiIgcj0iMzAiIGZpbGw9IiNGRkQ3MDAiIHN0cm9rZT0iI0ZGQjAwMCIgc3Ryb2tlLXdpZHRoPSI0Ii8+CjxwYXRoIGQ9Ik0yMCAyMEg0NFY0MEgyMFYyMFoiIGZpbGw9IiNGRkZGRkYiLz4KPHRleHQgeD0iMzIiIHk9IjMyIiBmb250LWZhbWlseT0iQXJpYWwiIGZvbnQtc2l6ZT0iMTAiIGZpbGw9IiNGRkQ3MDAiIHRleHQtYW5jaG9yPSJtaWRkbGUiPjFLPC90ZXh0Pgo8L3N2Zz4K",
                "points_value": 5
            }
        ]
        
        added_count = 0
        updated_count = 0
        
        for badge_data in key_badges:
            try:
                # Check if badge already exists
                existing = db.execute(
                    text("SELECT id, points_value FROM badge WHERE name = :name"),
                    {"name": badge_data["name"]}
                ).fetchone()
                
                if existing:
                    # Update existing badge
                    db.execute(
                        text("""
                            UPDATE badge 
                            SET points_value = :points_value, 
                                description = :description,
                                icon_url = :icon_url,
                                updated_at = NOW()
                            WHERE name = :name
                        """),
                        {
                            "name": badge_data["name"],
                            "points_value": badge_data["points_value"],
                            "description": badge_data["description"],
                            "icon_url": badge_data["icon_url"]
                        }
                    )
                    updated_count += 1
                    print(f"✅ Updated: {badge_data['name']} → {badge_data['points_value']} pts")
                else:
                    # Create new badge
                    db.execute(
                        text("""
                            INSERT INTO badge (name, description, badge_type, icon_url, points_value, created_at, updated_at)
                            VALUES (:name, :description, :badge_type, :icon_url, :points_value, NOW(), NOW())
                        """),
                        badge_data
                    )
                    added_count += 1
                    print(f"🆕 Added: {badge_data['name']} ({badge_data['points_value']} pts)")
                    
            except Exception as e:
                print(f"❌ Error with badge {badge_data['name']}: {e}")
                continue
        
        db.commit()
        
        # Final verification
        final_badge_count = db.execute(text("SELECT COUNT(*) FROM badge")).scalar()
        final_level_count = db.execute(text("SELECT COUNT(*) FROM userlevel")).scalar()
        
        print(f"\n✅ FINAL MIGRATION COMPLETED!")
        print(f"   Added: {added_count} new badges")
        print(f"   Updated: {updated_count} existing badges")
        print(f"   Total badges: {final_badge_count}")
        print(f"   Total levels: {final_level_count}")
        
        print(f"\n🎯 SUPABASE GAMIFICATION SYSTEM READY!")
        print(f"   📊 Point System: 1-5 pts per action")
        print(f"   🎯 20 Levels: 0-11,750+ pts")
        print(f"   🏆 {final_badge_count} Badges with SVG designs")
        print(f"   🏅 Lower point values for sustainable progression")
        print(f"   🎨 Beautiful SVG icons for all badges and levels")
        
    except Exception as e:
        print(f"❌ Error during final migration: {e}")
        db.rollback()
        import traceback
        traceback.print_exc()
    finally:
        db.close()

if __name__ == "__main__":
    final_supabase_migration()
