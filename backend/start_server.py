#!/usr/bin/env python3
"""
Smart Server Startup Script
Automatically handles database setup and starts the server
"""

import os
import sys
import logging
import argparse

# Load environment variables from .env file
from dotenv import load_dotenv
load_dotenv()

# Add the app directory to Python path
sys.path.insert(0, os.path.dirname(__file__))

from app.core.database_manager import get_database_status
from app.core.startup import initialize_application

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def check_environment():
    """Check if environment is properly configured"""
    required_vars = ['DATABASE_URL']
    missing_vars = []
    
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        logger.error(f"Missing required environment variables: {missing_vars}")
        logger.error("Please check your .env file")
        return False
    
    return True

def start_server(host="0.0.0.0", port=8000, reload=True):
    """Start the FastAPI server"""
    try:
        import uvicorn
        logger.info(f"Starting server on {host}:{port}")
        uvicorn.run(
            "app.main:app", 
            host=host, 
            port=port, 
            reload=reload,
            log_level="info"
        )
    except KeyboardInterrupt:
        logger.info("Server stopped by user")
    except Exception as e:
        logger.error(f"Server startup failed: {e}")
        sys.exit(1)

def main():
    parser = argparse.ArgumentParser(description="Smart Server Startup")
    parser.add_argument("--host", default="0.0.0.0", help="Host to bind to")
    parser.add_argument("--port", type=int, default=8000, help="Port to bind to")
    parser.add_argument("--no-reload", action="store_true", help="Disable auto-reload")
    parser.add_argument("--force-recreate", action="store_true", help="Force recreate database tables")
    parser.add_argument("--check-only", action="store_true", help="Only check database status")
    
    args = parser.parse_args()
    
    # Set environment variable for force recreate
    if args.force_recreate:
        os.environ["DB_FORCE_RECREATE"] = "true"
        logger.warning("Force recreate enabled - all tables will be dropped and recreated!")
    
    # Check environment
    if not check_environment():
        sys.exit(1)
    
    # Check database status
    if args.check_only:
        logger.info("Checking database status...")
        status = get_database_status()
        
        print("\n=== Database Status ===")
        for key, value in status.items():
            if key == "existing_tables" and isinstance(value, list):
                print(f"{key}: {len(value)} tables")
                for table in sorted(value):
                    print(f"  - {table}")
            else:
                print(f"{key}: {value}")
        return
    
    # Initialize application (this will be done again in startup event, but good for early validation)
    logger.info("Pre-startup validation...")
    try:
        success = initialize_application()
        if not success:
            logger.error("Database initialization failed!")
            sys.exit(1)
    except Exception as e:
        logger.error(f"Pre-startup validation failed: {e}")
        sys.exit(1)
    
    # Start server
    start_server(
        host=args.host,
        port=args.port,
        reload=not args.no_reload
    )

if __name__ == "__main__":
    main()
