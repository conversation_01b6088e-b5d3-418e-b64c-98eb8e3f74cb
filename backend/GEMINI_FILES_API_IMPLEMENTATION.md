# Gemini Files API Implementation

## Overview

This implementation replaces the failing PDF processing system with a new approach using Google's Gemini Files API and structured output. The system allows students to upload PDFs directly to Gemini, then generate MCQs, flashcards, and summaries using structured output for reliable, consistent results.

## Key Features

- **Direct PDF Upload to Gemini**: Files are uploaded directly to Gemini Files API (max 30MB)
- **Automatic File Cleanup**: Files are automatically deleted after 1 hour as per user requirements
- **Structured Output**: Uses Pydantic schemas to ensure consistent, validated responses
- **User-Specific Content**: Generated content is tied to individual students, not courses
- **High-Quality Prompts**: Optimized prompts for educational content generation

## Architecture

### Database Models

#### GeminiFileMetadata
Stores metadata for files uploaded to Gemini:
- `gemini_file_id`: Unique Gemini file identifier
- `original_filename`: Original uploaded filename
- `file_size`: File size in bytes
- `upload_time`: When file was uploaded
- `expiry_time`: When file expires (48h from upload)
- `auto_delete_time`: When to auto-delete (10h from upload)
- `student_id`: Owner of the file

#### StudentNote (Updated)
Added `gemini_file_id` field to link notes with Gemini files.

### Services

#### GeminiFilesService
Handles all Gemini Files API operations:
- File upload with metadata storage
- File metadata retrieval
- File deletion
- Automatic cleanup of expired files

#### GeminiContentGenerator
Generates educational content using structured output:
- MCQ generation with detailed schemas
- Flashcard generation with front/back content
- Summary generation with structured sections

### API Endpoints

#### Upload PDF
```
POST /api/v1/student-tools/gemini/upload-pdf
```
- Uploads PDF to Gemini Files API
- Stores metadata in database
- Returns file ID and upload details

#### Generate MCQs
```
POST /api/v1/student-tools/gemini/generate-mcqs
```
- Generates MCQs from uploaded PDF
- Uses structured output for consistent format
- Returns validated MCQ objects

#### Generate Flashcards
```
POST /api/v1/student-tools/gemini/generate-flashcards
```
- Generates flashcards from uploaded PDF
- Uses structured output for front/back content
- Returns validated flashcard objects

#### Generate Summary
```
POST /api/v1/student-tools/gemini/generate-summary
```
- Generates structured summary from uploaded PDF
- Includes sections, key concepts, and main topics
- Returns validated summary object

#### File Management
```
DELETE /api/v1/student-tools/gemini/files/{gemini_file_id}
POST /api/v1/student-tools/gemini/cleanup-expired
```
- Manual file deletion
- Trigger cleanup of expired files

## Structured Output Schemas

### MCQ Schema
```python
class GeneratedMCQ(BaseModel):
    question_text: str
    options: List[MCQOption]  # 4 options with is_correct flag
    correct_answer: str
    explanation: str
    topic: str
    difficulty: DifficultyLevel
    bloom_taxonomy_level: str
```

### Flashcard Schema
```python
class GeneratedFlashcard(BaseModel):
    front_content: str
    back_content: str
    topic: str
    difficulty: DifficultyLevel
    card_type: str
```

### Summary Schema
```python
class GeneratedSummary(BaseModel):
    title: str
    overview: str
    sections: List[SummarySection]
    key_concepts: List[str]
    main_topics: List[str]
    word_count: int
```

## Optimized Prompts

### MCQ Generation Prompt
- Focuses on educational quality and variety
- Includes different Bloom's taxonomy levels
- Ensures plausible distractors
- Covers key concepts from the document

### Flashcard Generation Prompt
- Creates different types of flashcards (definition, concept, formula, etc.)
- Ensures clear, concise front and comprehensive back content
- Focuses on memorization and understanding

### Summary Generation Prompt
- Creates hierarchical, well-structured summaries
- Extracts key concepts and main topics
- Maintains academic tone and clarity
- Organizes information for effective study

## File Management

### Upload Process
1. Validate file type and size (PDF, max 30MB)
2. Upload to Gemini Files API
3. Store metadata in database with expiry times
4. Return file ID for future operations

### Automatic Cleanup
- Files are marked for deletion 10 hours after upload
- Cleanup service runs periodically to delete expired files
- Database metadata is updated when files are deleted

### Security
- Files are tied to specific students
- Access validation ensures students can only access their own files
- Expired files are automatically cleaned up

## Usage Flow

1. **Upload PDF**: Student uploads PDF via `/gemini/upload-pdf`
2. **Get File ID**: System returns Gemini file ID
3. **Generate Content**: Student uses file ID to generate MCQs, flashcards, or summaries
4. **Auto Cleanup**: File is automatically deleted after 1 hour

## Benefits

- **Reliability**: Uses Gemini's robust document understanding
- **Consistency**: Structured output ensures predictable results
- **Performance**: Direct API calls without complex processing pipelines
- **Scalability**: Leverages Gemini's infrastructure
- **Quality**: Optimized prompts for educational content

## Testing

Run the test script to validate the implementation:
```bash
python test_gemini_integration.py
```

## Migration

Database migration has been created and applied:
```bash
alembic upgrade head
```

## Configuration

Ensure `GEMINI_API_KEY` is set in your environment variables.

## Next Steps

1. Test the API endpoints with real PDF files
2. Monitor file cleanup and storage usage
3. Gather user feedback on content quality
4. Consider adding batch processing for multiple files
