absl-py==2.3.0
aiofiles==24.1.0
aiohappyeyeballs==2.6.1
aiohttp==3.12.13
aiosignal==1.3.2
alembic==1.16.1
amqp==5.3.1
annotated-types==0.7.0
anyio==4.9.0
astunparse==1.6.3
attrs==25.3.0
bcrypt==4.3.0
billiard==4.2.1
cachetools==5.5.2
celery==5.5.3
certifi==2025.4.26
chardet==5.2.0
charset-normalizer==3.4.2
click==8.2.1
click-didyoumean==0.3.1
click-plugins==1.1.1
click-repl==0.3.0
cloudinary==1.41.0
cloudpickle==3.1.1
colorama==0.4.6
dataclasses-json==0.6.7
distro==1.9.0
dnspython==2.7.0
ecdsa==0.19.1
email_validator==2.2.0
fastapi==0.115.12
filetype==1.2.0
flatbuffers==25.2.10
frozenlist==1.7.0
gast==0.6.0
google-ai-generativelanguage==0.6.15
google-api-core==2.25.0
google-api-python-client==2.172.0
google-auth==2.40.3
google-auth-httplib2==0.2.0
google-generativeai==0.8.5
google-pasta==0.2.0
googleapis-common-protos==1.70.0
greenlet==3.2.3
griffe==1.7.3
grpcio==1.73.0
grpcio-status==1.71.0
gym==0.26.2
gym-notices==0.0.8
h11==0.16.0
h2==4.2.0
h5py==3.14.0
hiredis==3.2.1
hpack==4.1.0
httpcore==1.0.9
httplib2==0.22.0
httpx==0.28.1
httpx-sse==0.4.0
hyperframe==6.1.0
idna==3.10
iniconfig==2.1.0
jiter==0.10.0
joblib==1.5.1
jsonpatch==1.33
jsonpointer==3.0.0
kombu==5.5.4
# LangChain dependencies removed - using Gemini Files API instead
# More LangChain dependencies removed
libclang==18.1.1
Mako==1.3.10
Markdown==3.8
markdown-it-py==3.0.0
MarkupSafe==3.0.2
marshmallow==3.26.1
mcp==1.9.3
mdurl==0.1.2
ml_dtypes==0.5.1
multidict==6.4.4
mypy_extensions==1.1.0
namex==0.1.0
numpy==2.1.3
openai==1.86.0
openai-agents==0.0.17
opt_einsum==3.4.0
optree==0.16.0
orjson==3.10.18
ormsgpack==1.10.0
packaging==24.2
pandas==2.3.0
passlib==1.7.4
pdf2image==1.17.0
pillow==11.2.1
pluggy==1.6.0
portalocker==2.10.1
prompt_toolkit==3.0.51
propcache==0.3.2
proto-plus==1.26.1
protobuf==5.29.5
psutil==6.1.0
psycopg2-binary==2.9.9
pytesseract==0.3.13
pyasn1==0.6.1
pyasn1_modules==0.4.2
pydantic==2.11.5
pydantic-settings==2.9.1
pydantic_core==2.33.2
Pygments==2.19.1
PyMuPDF==1.26.0
pyparsing==3.2.3
pytest==8.4.0
python-dateutil==2.9.0.post0
python-dotenv==1.1.0
python-jose==3.5.0
python-multipart==0.0.20
pytz==2025.2
PyYAML==6.0.2
# qdrant-client removed - using Gemini Files API instead
redis==6.2.0
regex==2024.11.6
reportlab==4.4.1
requests==2.32.4
requests-toolbelt==1.0.0
rich==14.0.0
rsa==4.9.1
ruamel.yaml==0.18.14
ruamel.yaml.clib==0.2.12
scikit-learn==1.7.0
scipy==1.15.3
setuptools==80.9.0
six==1.17.0
sniffio==1.3.1
SQLAlchemy==2.0.41
sse-starlette==2.3.6
starlette==0.46.2
tenacity==9.1.2

termcolor==3.1.0
threadpoolctl==3.6.0
tiktoken==0.9.0
tqdm==4.67.1
types-requests==2.32.4.20250611
typing-inspect==0.9.0
typing-inspection==0.4.1
typing_extensions==4.14.0
tzdata==2025.2
uritemplate==4.2.0
urllib3==2.4.0
uvicorn==0.34.3
vine==5.1.0
wcwidth==0.2.13
websockets==15.0.1
Werkzeug==3.1.3
wheel==0.45.1
wrapt==1.17.2
xxhash==3.5.0
yarl==1.20.1
zstandard==0.23.0
