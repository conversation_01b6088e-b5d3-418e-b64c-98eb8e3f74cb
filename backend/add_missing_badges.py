#!/usr/bin/env python3

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.db.session import SessionLocal
from sqlalchemy import text

def add_missing_badges():
    """Add missing badges without deleting existing ones"""
    db = SessionLocal()
    
    try:
        print("=== ADDING MISSING BADGES TO SUPABASE ===")
        
        # Check current badges
        existing_badges = db.execute(text("SELECT name FROM badge")).fetchall()
        existing_names = [badge[0] for badge in existing_badges]
        print(f"Current badges: {len(existing_names)}")
        
        # Badges to add (only ones that don't exist)
        new_badges = [
            {
                "name": "Speed Demon",
                "description": "Answer 10 questions in under 30 seconds each",
                "badge_type": "SPEED_DEMON",
                "icon_url": "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMzIiIGN5PSIzMiIgcj0iMzAiIGZpbGw9IiNGRkVCM0IiIHN0cm9rZT0iI0Y1OUUwQiIgc3Ryb2tlLXdpZHRoPSI0Ii8+CjxwYXRoIGQ9Ik0yMCAzMkwzMiAyMEw0NCAzMkwzMiA0NEwyMCAzMloiIGZpbGw9IiNGRkZGRkYiLz4KPC9zdmc+Cg==",
                "points_value": 4
            },
            {
                "name": "Sharpshooter",
                "description": "Maintain 90% accuracy over 100 questions",
                "badge_type": "SHARPSHOOTER",
                "icon_url": "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMzIiIGN5PSIzMiIgcj0iMzAiIGZpbGw9IiNFRjQ0NDQiIHN0cm9rZT0iI0RDMjYyNiIgc3Ryb2tlLXdpZHRoPSI0Ii8+CjxjaXJjbGUgY3g9IjMyIiBjeT0iMzIiIHI9IjE4IiBmaWxsPSIjRkZGRkZGIi8+CjxjaXJjbGUgY3g9IjMyIiBjeT0iMzIiIHI9IjEyIiBmaWxsPSIjRUY0NDQ0Ii8+CjxjaXJjbGUgY3g9IjMyIiBjeT0iMzIiIHI9IjYiIGZpbGw9IiNGRkZGRkYiLz4KPC9zdmc+Cg==",
                "points_value": 5
            },
            {
                "name": "Lightning Fast",
                "description": "Answer 25 questions in under 15 seconds each",
                "badge_type": "LIGHTNING_FAST",
                "icon_url": "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMzIiIGN5PSIzMiIgcj0iMzAiIGZpbGw9IiNGRkVCM0IiIHN0cm9rZT0iI0Y1OUUwQiIgc3Ryb2tlLXdpZHRoPSI0Ii8+CjxwYXRoIGQ9Ik0zMiAxNkwzNiAyOEgzMkwyOCAyOEwzMiAxNloiIGZpbGw9IiNGRkZGRkYiLz4KPHBhdGggZD0iTTMyIDM2TDM2IDQ4SDMyTDI4IDQ4TDMyIDM2WiIgZmlsbD0iI0ZGRkZGRiIvPgo8L3N2Zz4K",
                "points_value": 6
            },
            {
                "name": "Streak Warrior",
                "description": "Maintained a 30-day learning streak",
                "badge_type": "STREAK_WARRIOR",
                "icon_url": "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMzIiIGN5PSIzMiIgcj0iMzAiIGZpbGw9IiNEQzI2MjYiIHN0cm9rZT0iI0I5MTQxNCIgc3Ryb2tlLXdpZHRoPSI0Ii8+CjxwYXRoIGQ9Ik0yOCAyMEgzNlYyOEgyOFYyMFoiIGZpbGw9IiNGRkZGRkYiLz4KPHBhdGggZD0iTTI0IDMySDQwVjQwSDI0VjMyWiIgZmlsbD0iI0ZGRkZGRiIvPgo8cGF0aCBkPSJNMjggNDRIMzZWNTJIMjhWNDRaIiBmaWxsPSIjRkZGRkZGIi8+Cjwvc3ZnPgo=",
                "points_value": 8
            },
            {
                "name": "Early Bird",
                "description": "Complete 50 sessions before 9 AM",
                "badge_type": "EARLY_BIRD",
                "icon_url": "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMzIiIGN5PSIzMiIgcj0iMzAiIGZpbGw9IiNGRkVCM0IiIHN0cm9rZT0iI0Y1OUUwQiIgc3Ryb2tlLXdpZHRoPSI0Ii8+CjxjaXJjbGUgY3g9IjMyIiBjeT0iMzIiIHI9IjE4IiBmaWxsPSIjRkZGRkZGIi8+CjxwYXRoIGQ9Ik0zMiAyMFYzMkw0MCAzNiIgc3Ryb2tlPSIjRjU5RTBCIiBzdHJva2Utd2lkdGg9IjMiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIvPgo8L3N2Zz4K",
                "points_value": 4
            },
            {
                "name": "Night Owl",
                "description": "Complete 50 sessions after 10 PM",
                "badge_type": "NIGHT_OWL",
                "icon_url": "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMzIiIGN5PSIzMiIgcj0iMzAiIGZpbGw9IiMzNzMwNjYiIHN0cm9rZT0iIzI1MjA0QSIgc3Ryb2tlLXdpZHRoPSI0Ii8+CjxjaXJjbGUgY3g9IjMyIiBjeT0iMzIiIHI9IjE4IiBmaWxsPSIjRkZGRkZGIi8+CjxwYXRoIGQ9Ik0zMiAyMFYzMkwyNCAzNiIgc3Ryb2tlPSIjMzczMDY2IiBzdHJva2Utd2lkdGg9IjMiIHN0cm9rZS1saW5lY2FwPSJyb3VuZCIvPgo8Y2lyY2xlIGN4PSI0NCIgY3k9IjIwIiByPSI0IiBmaWxsPSIjRkZGRkZGIi8+Cjwvc3ZnPgo=",
                "points_value": 4
            },
            {
                "name": "Explorer",
                "description": "Visit all sections of the platform",
                "badge_type": "EXPLORER",
                "icon_url": "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMzIiIGN5PSIzMiIgcj0iMzAiIGZpbGw9IiMwNTk2NjkiIHN0cm9rZT0iIzA0N0M1NyIgc3Ryb2tlLXdpZHRoPSI0Ii8+CjxjaXJjbGUgY3g9IjMyIiBjeT0iMzIiIHI9IjE4IiBmaWxsPSIjRkZGRkZGIi8+CjxwYXRoIGQ9Ik0zMiAyMEwzNiAzMkgzMkwyOCAzMkwzMiAyMFoiIGZpbGw9IiMwNTk2NjkiLz4KPC9zdmc+Cg==",
                "points_value": 3
            },
            {
                "name": "Collector",
                "description": "Earn 10 different badges",
                "badge_type": "COLLECTOR",
                "icon_url": "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMzIiIGN5PSIzMiIgcj0iMzAiIGZpbGw9IiNGNTlFMEIiIHN0cm9rZT0iI0Q5N0YwNiIgc3Ryb2tlLXdpZHRoPSI0Ii8+CjxyZWN0IHg9IjIwIiB5PSIyMCIgd2lkdGg9IjI0IiBoZWlnaHQ9IjI0IiBmaWxsPSIjRkZGRkZGIi8+CjxyZWN0IHg9IjI0IiB5PSIyNCIgd2lkdGg9IjE2IiBoZWlnaHQ9IjE2IiBmaWxsPSIjRjU5RTBCIi8+Cjwvc3ZnPgo=",
                "points_value": 8
            }
        ]
        
        # Add only badges that don't exist
        added_count = 0
        for badge_data in new_badges:
            if badge_data["name"] not in existing_names:
                try:
                    db.execute(
                        text("""
                            INSERT INTO badge (name, description, badge_type, icon_url, points_value, created_at, updated_at)
                            VALUES (:name, :description, :badge_type, :icon_url, :points_value, NOW(), NOW())
                        """),
                        badge_data
                    )
                    added_count += 1
                    print(f"✅ Added: {badge_data['name']} ({badge_data['points_value']} pts)")
                        
                except Exception as e:
                    print(f"❌ Error with badge {badge_data['name']}: {e}")
                    continue
            else:
                print(f"⚠️  Already exists: {badge_data['name']}")
        
        db.commit()
        
        # Update existing badge point values to match the lower system
        print(f"\n=== UPDATING EXISTING BADGE POINT VALUES ===")
        point_updates = [
            ("First Perfect Score", 5),
            ("Practice Master", 3),
            ("Course Completion", 5),
            ("Quick Learner", 4),
            ("Consistent Learner", 3),
            ("Streak Champion", 3),
            ("First Login", 2),
            ("Hundred Club", 1),
            ("Thousand Club", 5)
        ]
        
        updated_count = 0
        for badge_name, new_points in point_updates:
            try:
                result = db.execute(
                    text("UPDATE badge SET points_value = :points WHERE name = :name"),
                    {"points": new_points, "name": badge_name}
                )
                if result.rowcount > 0:
                    updated_count += 1
                    print(f"✅ Updated: {badge_name} → {new_points} pts")
            except Exception as e:
                print(f"❌ Error updating {badge_name}: {e}")
        
        db.commit()
        
        # Final verification
        final_count = db.execute(text("SELECT COUNT(*) FROM badge")).scalar()
        
        print(f"\n✅ BADGE MIGRATION COMPLETED!")
        print(f"   Added: {added_count} new badges")
        print(f"   Updated: {updated_count} existing badges")
        print(f"   Total badges: {final_count}")
        
        print(f"\n🎯 SUPABASE GAMIFICATION SYSTEM IS READY!")
        print(f"   📊 Point System: 1-5 pts per action")
        print(f"   🎯 20 Levels: 0-11,750+ pts")
        print(f"   🏆 {final_count} Badges with SVG designs")
        print(f"   🏅 Lower point values for sustainable progression")
        
    except Exception as e:
        print(f"❌ Error adding badges: {e}")
        db.rollback()
        import traceback
        traceback.print_exc()
    finally:
        db.close()

if __name__ == "__main__":
    add_missing_badges()
