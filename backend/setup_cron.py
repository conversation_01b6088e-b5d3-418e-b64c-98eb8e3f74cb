#!/usr/bin/env python3
"""
Setup script for cron jobs to handle monthly usage limit resets
"""

import os
import sys
from pathlib import Path


def create_cron_script():
    """Create a shell script that can be run by cron"""
    
    # Get the absolute path to the backend directory
    backend_dir = Path(__file__).parent.absolute()
    
    # Create the cron script content
    cron_script_content = f"""#!/bin/bash

# Monthly usage limits reset script
# This script should be run on the 1st of each month

# Set environment variables
export PYTHONPATH="{backend_dir}"
export DATABASE_URL="${{DATABASE_URL:-sqlite:///./campuspq.db}}"

# Change to the backend directory
cd "{backend_dir}"

# Activate virtual environment if it exists
if [ -f "venv/bin/activate" ]; then
    source venv/bin/activate
elif [ -f "../venv/bin/activate" ]; then
    source ../venv/bin/activate
fi

# Run the monthly maintenance task
python -m app.tasks.monthly_reset

# Log the execution
echo "Monthly usage limits reset completed at $(date)" >> /var/log/campuspq_monthly_reset.log
"""

    # Write the script to a file
    script_path = backend_dir / "monthly_reset_cron.sh"
    
    with open(script_path, 'w') as f:
        f.write(cron_script_content)
    
    # Make the script executable
    os.chmod(script_path, 0o755)
    
    print(f"Created cron script: {script_path}")
    return script_path


def print_cron_instructions(script_path):
    """Print instructions for setting up the cron job"""
    
    print("\n" + "="*60)
    print("CRON JOB SETUP INSTRUCTIONS")
    print("="*60)
    print()
    print("To set up automatic monthly usage limit resets, add the following")
    print("cron job to your system:")
    print()
    print("1. Open your crontab:")
    print("   crontab -e")
    print()
    print("2. Add this line to run the script on the 1st of each month at 2 AM:")
    print(f"   0 2 1 * * {script_path}")
    print()
    print("3. Save and exit the crontab editor")
    print()
    print("Alternative: Run manually for testing:")
    print(f"   {script_path}")
    print()
    print("The script will:")
    print("- Reset usage limits for all students")
    print("- Clean up old usage records (older than 12 months)")
    print("- Log the execution to /var/log/campuspq_monthly_reset.log")
    print()
    print("="*60)


def main():
    """Main function"""
    print("Setting up monthly usage limits reset cron job...")
    
    try:
        script_path = create_cron_script()
        print_cron_instructions(script_path)
        
        print("\n✓ Cron job setup completed successfully!")
        
    except Exception as e:
        print(f"Error setting up cron job: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()
