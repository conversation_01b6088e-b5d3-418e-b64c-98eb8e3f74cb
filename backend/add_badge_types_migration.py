#!/usr/bin/env python3

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.db.session import SessionLocal
from sqlalchemy import text

def add_new_badge_types():
    """Add new badge types to the badgetype enum"""
    db = SessionLocal()
    
    try:
        print("=== ADDING NEW BADGE TYPES ===")
        
        # List of new badge types to add
        new_badge_types = [
            'STREAK_WARRIOR',
            'STREAK_LEGEND', 
            'STREAK_IMMORTAL',
            'SPEED_DEMON',
            'LIGHTNING_FAST',
            'FLASH_MASTER',
            'SHARPSHOOTER',
            'PRECISION_MASTER',
            'PERFECTIONIST',
            'EARLY_BIRD',
            'NIGHT_OWL',
            'WEEKEND_WARRIOR',
            'COMEBACK_KID',
            'HELPFUL_PEER',
            'MENTOR',
            'COMMUNITY_CHAMPION',
            'FIRST_LOGIN',
            'PROFILE_COMPLETE',
            'EXPLORER',
            'COLLECTOR',
            'OVERACHIEVER',
            'HUNDRED_CLUB',
            'THOUSAND_CLUB',
            'ELITE_CLUB',
            'LEGENDARY_CLUB'
        ]
        
        # Check existing badge types
        result = db.execute(text("SELECT unnest(enum_range(NULL::badgetype))")).fetchall()
        existing_types = [row[0] for row in result]
        print(f"Existing badge types: {existing_types}")
        
        # Add new badge types one by one
        for badge_type in new_badge_types:
            if badge_type not in existing_types:
                try:
                    print(f"Adding badge type: {badge_type}")
                    db.execute(text(f"ALTER TYPE badgetype ADD VALUE '{badge_type}'"))
                    db.commit()
                except Exception as e:
                    print(f"Error adding {badge_type}: {e}")
                    db.rollback()
            else:
                print(f"Badge type {badge_type} already exists")
        
        # Verify all types were added
        result = db.execute(text("SELECT unnest(enum_range(NULL::badgetype))")).fetchall()
        final_types = [row[0] for row in result]
        print(f"\nFinal badge types ({len(final_types)}): {final_types}")
        
        print("✅ Badge types updated successfully!")
        
    except Exception as e:
        print(f"❌ Error updating badge types: {e}")
        db.rollback()
        import traceback
        traceback.print_exc()
    finally:
        db.close()

if __name__ == "__main__":
    add_new_badge_types()
