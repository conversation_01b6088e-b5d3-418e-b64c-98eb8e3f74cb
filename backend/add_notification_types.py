#!/usr/bin/env python3
"""
Script to add missing notification types to the database enum
"""

from app.db.session import engine
from sqlalchemy import text

def add_notification_types():
    """Add missing notification types to the enum"""
    try:
        with engine.connect() as conn:
            # Check current enum values
            result = conn.execute(text("""
                SELECT enumlabel 
                FROM pg_enum 
                WHERE enumtypid = (
                    SELECT oid 
                    FROM pg_type 
                    WHERE typname = 'notificationtype'
                )
            """))
            current_values = [row[0] for row in result.fetchall()]
            print(f"📋 Current notification types: {current_values}")
            
            # Add missing values
            missing_values = [
                'content_shared',
                'content_share_accepted', 
                'content_share_rejected'
            ]
            
            for value in missing_values:
                if value not in current_values:
                    try:
                        conn.execute(text(f"ALTER TYPE notificationtype ADD VALUE '{value}'"))
                        conn.commit()
                        print(f"✅ Added notification type: {value}")
                    except Exception as e:
                        if "already exists" in str(e):
                            print(f"⚠️  Notification type {value} already exists")
                        else:
                            print(f"❌ Error adding {value}: {e}")
                else:
                    print(f"✅ Notification type {value} already exists")
            
            # Verify final state
            result = conn.execute(text("""
                SELECT enumlabel 
                FROM pg_enum 
                WHERE enumtypid = (
                    SELECT oid 
                    FROM pg_type 
                    WHERE typname = 'notificationtype'
                )
            """))
            final_values = [row[0] for row in result.fetchall()]
            print(f"📋 Final notification types: {final_values}")
            
    except Exception as e:
        print(f"❌ Error: {e}")
        raise

if __name__ == "__main__":
    add_notification_types()
