#!/usr/bin/env python3
"""
Fix question data to include options and explanations
"""

import sys
import os
import json

# Add the app directory to Python path
sys.path.insert(0, os.path.dirname(__file__))

from sqlalchemy.orm import Session
from sqlalchemy import text
from app.db.session import SessionLocal

def check_and_fix_questions():
    """Check and fix question data"""
    db: Session = SessionLocal()
    
    try:
        # Check existing questions
        print("🔍 Checking existing questions...")
        result = db.execute(text("""
            SELECT id, content, options, answer, explanation 
            FROM question 
            WHERE id IN (
                SELECT DISTINCT question_id FROM question_flag
            )
            LIMIT 5
        """))
        
        questions_to_fix = []
        for row in result:
            print(f"\nQuestion {row.id}:")
            print(f"  Content: {row.content[:50]}...")
            print(f"  Options: {row.options}")
            print(f"  Answer: {row.answer}")
            print(f"  Explanation: {bool(row.explanation)}")
            
            if not row.options or not row.explanation:
                questions_to_fix.append(row.id)
        
        # Fix questions that are missing data
        if questions_to_fix:
            print(f"\n🔧 Fixing {len(questions_to_fix)} questions...")
            
            for question_id in questions_to_fix:
                # Add sample options and explanation
                sample_options = {
                    "A": "Option A - First choice",
                    "B": "Option B - Second choice", 
                    "C": "Option C - Third choice",
                    "D": "Option D - Fourth choice"
                }
                
                sample_explanation = "This is a sample explanation for the question. The correct answer is A because it represents the most accurate choice among the given options."
                
                db.execute(text("""
                    UPDATE question 
                    SET 
                        options = :options,
                        answer = COALESCE(answer, 'A'),
                        explanation = COALESCE(explanation, :explanation)
                    WHERE id = :question_id
                """), {
                    "options": json.dumps(sample_options),
                    "explanation": sample_explanation,
                    "question_id": question_id
                })
            
            db.commit()
            print(f"✅ Fixed {len(questions_to_fix)} questions")
        else:
            print("✅ All flagged questions already have complete data")
        
        # Verify the fix
        print("\n🔍 Verifying fixed data...")
        result = db.execute(text("""
            SELECT id, content, options, answer, explanation 
            FROM question 
            WHERE id IN (
                SELECT DISTINCT question_id FROM question_flag
            )
            LIMIT 3
        """))
        
        for row in result:
            print(f"\nQuestion {row.id}:")
            print(f"  Has options: {bool(row.options)}")
            print(f"  Has answer: {bool(row.answer)}")
            print(f"  Has explanation: {bool(row.explanation)}")
            
            if row.options:
                try:
                    options = json.loads(row.options) if isinstance(row.options, str) else row.options
                    print(f"  Options: {list(options.keys())}")
                except:
                    print(f"  Options: {row.options}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        db.rollback()
        return False
    finally:
        db.close()

if __name__ == "__main__":
    print("🔧 Fixing Question Data for Flagged Questions\n")
    success = check_and_fix_questions()
    
    if success:
        print("\n🎉 Question data fix completed!")
        print("Now the edit modal should show:")
        print("  ✅ Question content")
        print("  ✅ Answer options (A, B, C, D)")
        print("  ✅ Correct answer")
        print("  ✅ Explanation")
        print("\nRefresh your browser and try editing a flagged question!")
    else:
        print("\n❌ Question data fix failed!")
