#!/usr/bin/env python3
"""
Quick script to make a user an admin
"""

import sys
import os

# Add the app directory to Python path
sys.path.insert(0, os.path.dirname(__file__))

from sqlalchemy.orm import Session
from app.db.session import SessionLocal
from app.models.user import User

def make_user_admin(email: str):
    """Make a user an admin by email"""
    db: Session = SessionLocal()
    
    try:
        # Find user by email
        user = db.query(User).filter(User.email == email).first()
        
        if not user:
            print(f"❌ User with email '{email}' not found")
            return False
        
        # Update role to admin
        user.role = 'admin'
        db.commit()
        
        print(f"✅ User '{user.full_name}' ({email}) is now an admin!")
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        db.rollback()
        return False
    finally:
        db.close()

def list_users():
    """List all users"""
    db: Session = SessionLocal()
    
    try:
        users = db.query(User).all()
        
        print("\n=== All Users ===")
        for user in users:
            print(f"ID: {user.id} | Email: {user.email} | Name: {user.full_name} | Role: {user.role}")
        
    except Exception as e:
        print(f"❌ Error: {e}")
    finally:
        db.close()

def main():
    if len(sys.argv) < 2:
        print("Usage:")
        print("  python make_admin.py <email>          # Make user admin")
        print("  python make_admin.py --list           # List all users")
        sys.exit(1)
    
    if sys.argv[1] == '--list':
        list_users()
    else:
        email = sys.argv[1]
        make_user_admin(email)

if __name__ == "__main__":
    main()
