# 🚀 Automatic Database Management System

This system automatically handles database setup, migration, and initialization for both **local PostgreSQL** and **Supabase** databases.

## ✨ Features

- **🔄 Automatic Setup**: Database tables created automatically on server startup
- **🌐 Multi-Database Support**: Works with both local PostgreSQL and Supabase
- **🛡️ Safe Operations**: Prevents data loss with confirmation prompts
- **📊 Status Monitoring**: Real-time database status and health checks
- **🎯 Environment-Based**: Different behaviors for development/production
- **🔧 CLI Tools**: Easy command-line database management

## 🚀 Quick Start

### 1. Configure Environment

Copy and edit the environment file:
```bash
cp .env.example .env
```

**For Local PostgreSQL:**
```env
DATABASE_URL=postgresql://username:password@localhost:5432/campuspq
DB_AUTO_INIT=true
DB_FORCE_RECREATE=false
```

**For Supabase:**
```env
DATABASE_URL=postgresql://postgres:<EMAIL>:5432/postgres
DB_AUTO_INIT=true
DB_FORCE_RECREATE=false
```

### 2. Start the Server (Automatic Setup)

**Simple Start:**
```bash
python start_server.py
```

**With Options:**
```bash
# Force recreate all tables (DESTRUCTIVE!)
python start_server.py --force-recreate

# Custom host/port
python start_server.py --host 127.0.0.1 --port 8080

# Production mode (no auto-reload)
python start_server.py --no-reload
```

### 3. Manual Database Management

**Check Database Status:**
```bash
python manage_db.py status
```

**Initialize Database:**
```bash
python manage_db.py init
```

**Recreate All Tables (DESTRUCTIVE!):**
```bash
python manage_db.py recreate
```

## 🔧 Environment Variables

| Variable | Default | Description |
|----------|---------|-------------|
| `DATABASE_URL` | Required | PostgreSQL connection string |
| `DB_AUTO_INIT` | `true` | Auto-initialize database on startup |
| `DB_FORCE_RECREATE` | `false` | Force recreate tables on startup |

## 📊 Database Status Monitoring

### Health Check Endpoints

**Basic Health Check:**
```
GET /health
```

**Detailed Status:**
```
GET /health/detailed
```

**Response Example:**
```json
{
  "startup_complete": true,
  "database": {
    "connection": true,
    "database_url": "postgresql://...",
    "is_supabase": false,
    "database_name": "campuspq",
    "existing_tables": ["user", "question", "question_flag", ...],
    "table_count": 15
  },
  "environment": {
    "debug": true,
    "environment": "development"
  }
}
```

## 🛠️ CLI Commands

### Database Management Tool

```bash
python manage_db.py <command>
```

**Available Commands:**
- `status` - Show database status and table list
- `init` - Initialize database (create missing tables)
- `create` - Create all tables
- `drop` - Drop all tables (DESTRUCTIVE!)
- `recreate` - Drop and recreate all tables (DESTRUCTIVE!)
- `check` - Test database connection

### Server Startup Tool

```bash
python start_server.py [options]
```

**Options:**
- `--host HOST` - Host to bind to (default: 0.0.0.0)
- `--port PORT` - Port to bind to (default: 8000)
- `--no-reload` - Disable auto-reload
- `--force-recreate` - Force recreate database tables
- `--check-only` - Only check database status

## 🔄 How It Works

### Automatic Initialization

1. **Server Startup**: FastAPI startup event triggers initialization
2. **Database Detection**: Automatically detects local vs Supabase
3. **Connection Check**: Verifies database connectivity
4. **Table Analysis**: Compares existing vs required tables
5. **Smart Creation**: Creates only missing tables
6. **Validation**: Confirms successful setup

### Safety Features

- **Confirmation Prompts**: Destructive operations require explicit confirmation
- **Environment Awareness**: Different behaviors for dev/prod
- **Error Handling**: Graceful failure with detailed error messages
- **Rollback Support**: Safe operation patterns

## 🎯 Usage Scenarios

### Development Workflow

```bash
# Fresh start with clean database
python start_server.py --force-recreate

# Normal development
python start_server.py

# Check what's in the database
python manage_db.py status
```

### Production Deployment

```bash
# Set production environment
export DB_AUTO_INIT=true
export DB_FORCE_RECREATE=false

# Start server (will auto-initialize safely)
python start_server.py --no-reload
```

### Database Switching

**Switch from Local to Supabase:**
1. Update `DATABASE_URL` in `.env`
2. Restart server - tables will be created automatically

**Switch from Supabase to Local:**
1. Update `DATABASE_URL` in `.env`
2. Restart server - tables will be created automatically

## 🚨 Important Notes

- **DESTRUCTIVE OPERATIONS**: Commands with `recreate`, `drop`, or `--force-recreate` will delete ALL data
- **Supabase**: Database creation is handled by Supabase, only table creation is managed
- **Local PostgreSQL**: Database will be created automatically if it doesn't exist
- **Backup**: Always backup important data before destructive operations

## 🐛 Troubleshooting

### Common Issues

**Connection Failed:**
- Check `DATABASE_URL` format
- Verify database server is running
- Check network connectivity

**Permission Denied:**
- Verify database user has CREATE privileges
- Check authentication credentials

**Table Already Exists:**
- Use `python manage_db.py status` to check current state
- Use `--force-recreate` only if you want to lose data

### Getting Help

```bash
# Check database status
python manage_db.py status

# Test connection only
python manage_db.py check

# Get detailed server status
curl http://localhost:8000/health/detailed
```

## 🎉 Benefits

- **Zero Configuration**: Works out of the box
- **Database Agnostic**: Switch between local and cloud databases easily
- **Development Friendly**: Quick setup and teardown
- **Production Ready**: Safe, automatic initialization
- **Debugging Support**: Comprehensive status reporting
