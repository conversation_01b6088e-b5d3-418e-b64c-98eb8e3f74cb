#!/usr/bin/env python3
"""
Database Management CLI Tool
Provides easy commands for database operations
"""

import argparse
import sys
import os
import logging

# Add the app directory to Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__)))

from app.core.database_manager import db_manager, get_database_status
from app.core.config import settings

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)
logger = logging.getLogger(__name__)

def status_command():
    """Show database status"""
    print("=== Database Status ===")
    status = get_database_status()
    
    for key, value in status.items():
        if key == "existing_tables" and isinstance(value, list):
            print(f"{key}: {len(value)} tables")
            for table in sorted(value):
                print(f"  - {table}")
        else:
            print(f"{key}: {value}")

def init_command():
    """Initialize database"""
    print("=== Initializing Database ===")
    success = db_manager.initialize_database(force_recreate=False)
    
    if success:
        print("✅ Database initialized successfully!")
    else:
        print("❌ Database initialization failed!")
        sys.exit(1)

def recreate_command():
    """Recreate all tables"""
    print("=== Recreating All Tables ===")
    print("⚠️  WARNING: This will delete ALL data!")
    
    confirm = input("Are you sure? Type 'yes' to continue: ")
    if confirm.lower() != 'yes':
        print("Operation cancelled.")
        return
    
    success = db_manager.recreate_all_tables()
    
    if success:
        print("✅ Tables recreated successfully!")
    else:
        print("❌ Table recreation failed!")
        sys.exit(1)

def create_command():
    """Create all tables"""
    print("=== Creating Tables ===")
    success = db_manager.create_all_tables()
    
    if success:
        print("✅ Tables created successfully!")
    else:
        print("❌ Table creation failed!")
        sys.exit(1)

def drop_command():
    """Drop all tables"""
    print("=== Dropping All Tables ===")
    print("⚠️  WARNING: This will delete ALL data!")
    
    confirm = input("Are you sure? Type 'yes' to continue: ")
    if confirm.lower() != 'yes':
        print("Operation cancelled.")
        return
    
    success = db_manager.drop_all_tables()
    
    if success:
        print("✅ Tables dropped successfully!")
    else:
        print("❌ Table dropping failed!")
        sys.exit(1)

def check_command():
    """Check database connection"""
    print("=== Checking Database Connection ===")
    success = db_manager.check_connection()
    
    if success:
        print("✅ Database connection successful!")
    else:
        print("❌ Database connection failed!")
        sys.exit(1)

def main():
    parser = argparse.ArgumentParser(description="Database Management Tool")
    subparsers = parser.add_subparsers(dest='command', help='Available commands')
    
    # Status command
    subparsers.add_parser('status', help='Show database status')
    
    # Initialize command
    subparsers.add_parser('init', help='Initialize database (create missing tables)')
    
    # Recreate command
    subparsers.add_parser('recreate', help='Drop and recreate all tables (DESTRUCTIVE)')
    
    # Create command
    subparsers.add_parser('create', help='Create all tables')
    
    # Drop command
    subparsers.add_parser('drop', help='Drop all tables (DESTRUCTIVE)')
    
    # Check command
    subparsers.add_parser('check', help='Check database connection')
    
    args = parser.parse_args()
    
    if not args.command:
        parser.print_help()
        return
    
    print(f"Database URL: {settings.DATABASE_URL}")
    print(f"Environment: {getattr(settings, 'ENVIRONMENT', 'development')}")
    print()
    
    try:
        if args.command == 'status':
            status_command()
        elif args.command == 'init':
            init_command()
        elif args.command == 'recreate':
            recreate_command()
        elif args.command == 'create':
            create_command()
        elif args.command == 'drop':
            drop_command()
        elif args.command == 'check':
            check_command()
        else:
            print(f"Unknown command: {args.command}")
            parser.print_help()
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\nOperation cancelled by user.")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Command failed: {e}")
        sys.exit(1)

if __name__ == "__main__":
    main()
