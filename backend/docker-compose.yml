version: '3.8'

networks:
  campuspq-network:
    driver: bridge

services:
  # PostgreSQL Database
  db:
    image: postgres:14-alpine
    container_name: campuspq-db
    restart: unless-stopped
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: postgres
      POSTGRES_DB: campuspq
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql:ro
    ports:
      - "5432:5432"
    networks:
      - campuspq-network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U postgres -d campuspq"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Redis for Celery
  redis:
    image: redis:7-alpine
    container_name: campuspq-redis
    restart: unless-stopped
    command: redis-server --appendonly yes --maxmemory 256mb --maxmemory-policy allkeys-lru
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - campuspq-network
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Qdrant Vector Database
  qdrant:
    image: qdrant/qdrant:latest
    container_name: campuspq-qdrant
    restart: unless-stopped
    ports:
      - "6333:6333"
      - "6334:6334"
    volumes:
      - qdrant_data:/qdrant/storage
    networks:
      - campuspq-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:6333/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # FastAPI Backend
  api:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
    container_name: campuspq-api
    restart: unless-stopped
    ports:
      - "8000:8000"
    volumes:
      - ./uploads:/app/uploads
      - ./logs:/app/logs
    environment:
      - DATABASE_URL=**************************************/campuspq
      - REDIS_URL=redis://redis:6379/0
      - QDRANT_URL=http://qdrant:6333
      - QDRANT_COLLECTION=campuspq_embeddings
      - SECRET_KEY=dev-secret-key-change-in-production
      - OPENAI_API_KEY=${OPENAI_API_KEY:-}
      - GEMINI_API_KEY=${GEMINI_API_KEY:-}
      - AI_PROVIDER=${AI_PROVIDER:-gemini}
      - CLOUDINARY_URL=${CLOUDINARY_URL:-}
      - CLOUDINARY_API_KEY=${CLOUDINARY_API_KEY:-}
      - CLOUDINARY_API_SECRET=${CLOUDINARY_API_SECRET:-}
      - ENVIRONMENT=development
      - DEBUG=true
    depends_on:
      db:
        condition: service_healthy
      redis:
        condition: service_healthy
      qdrant:
        condition: service_healthy
    networks:
      - campuspq-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/api/v1/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Celery Worker
  worker:
    build:
      context: .
      dockerfile: Dockerfile.worker
      target: production
    container_name: campuspq-worker
    restart: unless-stopped
    volumes:
      - ./uploads:/app/uploads
      - ./logs:/app/logs
    environment:
      - DATABASE_URL=**************************************/campuspq
      - REDIS_URL=redis://redis:6379/0
      - QDRANT_URL=http://qdrant:6333
      - QDRANT_COLLECTION=campuspq_embeddings
      - SECRET_KEY=dev-secret-key-change-in-production
      - OPENAI_API_KEY=${OPENAI_API_KEY:-}
      - GEMINI_API_KEY=${GEMINI_API_KEY:-}
      - AI_PROVIDER=${AI_PROVIDER:-gemini}
      - CLOUDINARY_URL=${CLOUDINARY_URL:-}
      - CLOUDINARY_API_KEY=${CLOUDINARY_API_KEY:-}
      - CLOUDINARY_API_SECRET=${CLOUDINARY_API_SECRET:-}
      - ENVIRONMENT=development
      - DEBUG=true
    depends_on:
      api:
        condition: service_healthy
      redis:
        condition: service_healthy
    networks:
      - campuspq-network
    healthcheck:
      test: ["CMD", "celery", "-A", "app.core.celery_app", "inspect", "ping"]
      interval: 60s
      timeout: 30s
      retries: 3
      start_period: 60s

volumes:
  postgres_data:
    driver: local
  redis_data:
    driver: local
  qdrant_data:
    driver: local
