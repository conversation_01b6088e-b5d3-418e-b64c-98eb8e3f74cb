#!/usr/bin/env python3

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.db.session import SessionLocal
from app.models.gamification import Badge, UserLevel, BadgeType
from app.services.gamification_service import DEFAULT_BADGES, LE<PERSON><PERSON>

def update_gamification_system():
    """Update the gamification system with new levels and badges"""
    db = SessionLocal()
    
    try:
        print("=== UPDATING GAMIFICATION SYSTEM ===")
        
        # Clear existing badges and levels
        print("Clearing existing badges and levels...")
        db.query(Badge).delete()
        db.query(UserLevel).delete()
        db.commit()
        
        # Add new levels
        print(f"Adding {len(LEVELS)} levels...")
        for level_data in LEVELS:
            level = UserLevel(
                level_number=level_data["level_number"],
                name=level_data["name"],
                min_points=level_data["min_points"],
                max_points=level_data["max_points"],
                icon_url=level_data["icon_url"]
            )
            db.add(level)
        
        # Add new badges
        print(f"Adding {len(DEFAULT_BADGES)} badges...")
        for badge_data in DEFAULT_BADGES:
            badge = Badge(
                name=badge_data["name"],
                description=badge_data["description"],
                badge_type=badge_data["badge_type"],
                icon_url=badge_data["icon_url"],
                points_value=badge_data["points_value"]
            )
            db.add(badge)
        
        db.commit()
        
        print("✅ Gamification system updated successfully!")
        print(f"✅ Added {len(LEVELS)} levels (1-20)")
        print(f"✅ Added {len(DEFAULT_BADGES)} badges with SVG designs")
        print("✅ Enhanced point values for better progression")
        
        # Display level progression
        print("\n=== LEVEL PROGRESSION ===")
        for level in LEVELS:
            print(f"Level {level['level_number']:2d}: {level['name']:12s} ({level['min_points']:6,} - {level['max_points']:7,} points)")
        
        # Display badge categories
        print("\n=== BADGE CATEGORIES ===")
        categories = {}
        for badge in DEFAULT_BADGES:
            badge_type = badge["badge_type"].value
            category = badge_type.split('_')[0] if '_' in badge_type else badge_type
            if category not in categories:
                categories[category] = []
            categories[category].append(badge["name"])
        
        for category, badges in categories.items():
            print(f"{category.title()}: {len(badges)} badges")
            for badge in badges[:3]:  # Show first 3 badges in each category
                print(f"  - {badge}")
            if len(badges) > 3:
                print(f"  ... and {len(badges) - 3} more")
        
    except Exception as e:
        print(f"❌ Error updating gamification system: {e}")
        db.rollback()
        import traceback
        traceback.print_exc()
    finally:
        db.close()

if __name__ == "__main__":
    update_gamification_system()
