#!/usr/bin/env python
"""
Start the Celery worker for background tasks.
"""
from app.core.celery_app import celery_app

if __name__ == "__main__":
    celery_app.worker_main([
        "worker",
        "--loglevel=info",
        "--concurrency=2",  # Reduced concurrency to minimize Redis connections
        "--max-memory-per-child=300000",  # Reduced memory limit
        "--max-tasks-per-child=10",  # Force worker restart to prevent connection buildup
        "-Q", "lightning_queue,celery"  # Only essential queues
    ])
