#!/usr/bin/env python3

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.db.session import SessionLocal
from sqlalchemy import text

def check_enum():
    db = SessionLocal()
    
    try:
        # Check what enum values exist
        result = db.execute(text("SELECT unnest(enum_range(NULL::userrole))")).fetchall()
        print("Available UserRole enum values:")
        for row in result:
            print(f"  '{row[0]}'")
            
        # Check actual user roles in database
        result = db.execute(text("SELECT DISTINCT role FROM \"user\"")).fetchall()
        print("\nActual user roles in database:")
        for row in result:
            print(f"  '{row[0]}'")
            
    except Exception as e:
        print(f"Error: {e}")
        import traceback
        traceback.print_exc()
    finally:
        db.close()

if __name__ == "__main__":
    check_enum()
