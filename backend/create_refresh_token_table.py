#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to manually create the refresh_token table
"""
import os
import sys
import psycopg2
from psycopg2.extensions import ISOLATION_LEVEL_AUTOCOMMIT

# Add the backend directory to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.core.config import settings

def create_refresh_token_table():
    """Create the refresh_token table manually"""
    try:
        # Parse the DATABASE_URL to get connection parameters
        db_url = settings.DATABASE_URL
        
        # Connect to PostgreSQL
        conn = psycopg2.connect(db_url)
        conn.set_isolation_level(ISOLATION_LEVEL_AUTOCOMMIT)
        cursor = conn.cursor()
        
        # Check if table already exists
        cursor.execute("""
            SELECT EXISTS (
                SELECT FROM information_schema.tables 
                WHERE table_name = 'refresh_token'
            );
        """)
        
        table_exists = cursor.fetchone()[0]
        
        if table_exists:
            print("✅ refresh_token table already exists")
            return True
        
        # Create the refresh_token table
        create_table_sql = """
        CREATE TABLE refresh_token (
            id SERIAL PRIMARY KEY,
            token VARCHAR UNIQUE NOT NULL,
            user_id INTEGER NOT NULL REFERENCES "user"(id),
            expires_at TIMESTAMP NOT NULL,
            is_revoked BOOLEAN NOT NULL DEFAULT FALSE,
            created_at TIMESTAMP NOT NULL DEFAULT NOW(),
            updated_at TIMESTAMP NOT NULL DEFAULT NOW()
        );
        """
        
        cursor.execute(create_table_sql)
        
        # Create indexes
        cursor.execute("CREATE INDEX ix_refresh_token_id ON refresh_token (id);")
        cursor.execute("CREATE UNIQUE INDEX ix_refresh_token_token ON refresh_token (token);")
        
        print("✅ refresh_token table created successfully!")
        
        # Verify table creation
        cursor.execute("""
            SELECT column_name, data_type 
            FROM information_schema.columns 
            WHERE table_name = 'refresh_token'
            ORDER BY ordinal_position;
        """)
        
        columns = cursor.fetchall()
        print("📋 Table structure:")
        for col_name, col_type in columns:
            print(f"  - {col_name}: {col_type}")
        
        cursor.close()
        conn.close()
        return True
        
    except Exception as e:
        print(f"❌ Error creating refresh_token table: {e}")
        return False

if __name__ == "__main__":
    success = create_refresh_token_table()
    sys.exit(0 if success else 1)
