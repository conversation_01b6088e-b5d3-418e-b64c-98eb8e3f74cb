#!/usr/bin/env python3

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from app.db.session import SessionLocal
from sqlalchemy import text
from app.services.gamification_service import DEFAULT_BADGES, <PERSON><PERSON><PERSON><PERSON>

def migrate_gamification_to_supabase():
    """Apply all gamification enhancements to Supabase database"""
    db = SessionLocal()
    
    try:
        print("=== MIGRATING GAMIFICATION SYSTEM TO SUPABASE ===")
        
        # Step 1: Add new badge types to enum
        print("\n1. Adding new badge types to enum...")
        new_badge_types = [
            'STREAK_WARRIOR', 'STREAK_LEGEND', 'STREAK_IMMORTAL',
            'SPEED_DEMON', 'LIGHTNING_FAST', 'FLASH_MASTER',
            'SHARPSHOOTER', 'PRECISION_MASTER', 'PERFECTIONIST',
            'EARLY_BIRD', 'NIGHT_OWL', 'WEEKEND_WARRIOR', 'COMEBACK_KID',
            'HELPFUL_PEER', 'MENT<PERSON>', 'COMMUNITY_CHAMPION',
            'FIRST_LOGIN', 'PROFILE_COMPLETE', 'EXPLORER',
            'COLLECTOR', 'OVERACHIEVER',
            'HUNDRED_CLUB', 'THOUSAND_CLUB', 'ELITE_CLUB', 'LEGENDARY_CLUB'
        ]
        
        # Check existing badge types
        try:
            result = db.execute(text("SELECT unnest(enum_range(NULL::badgetype))")).fetchall()
            existing_types = [row[0] for row in result]
            print(f"   Existing badge types: {len(existing_types)}")
        except Exception as e:
            print(f"   Note: Could not check existing types: {e}")
            existing_types = []
        
        # Add new badge types
        added_count = 0
        for badge_type in new_badge_types:
            if badge_type not in existing_types:
                try:
                    db.execute(text(f"ALTER TYPE badgetype ADD VALUE '{badge_type}'"))
                    db.commit()
                    added_count += 1
                    print(f"   ✅ Added: {badge_type}")
                except Exception as e:
                    print(f"   ⚠️  Could not add {badge_type}: {e}")
                    db.rollback()
        
        print(f"   Added {added_count} new badge types")
        
        # Step 2: Clear and recreate levels
        print("\n2. Updating levels...")
        db.execute(text("DELETE FROM userlevel"))
        db.commit()
        
        for level_data in LEVELS:
            db.execute(
                text("""
                    INSERT INTO userlevel (level_number, name, min_points, max_points, icon_url, created_at, updated_at)
                    VALUES (:level_number, :name, :min_points, :max_points, :icon_url, NOW(), NOW())
                """),
                {
                    "level_number": level_data["level_number"],
                    "name": level_data["name"],
                    "min_points": level_data["min_points"],
                    "max_points": level_data["max_points"],
                    "icon_url": level_data["icon_url"]
                }
            )
        
        db.commit()
        print(f"   ✅ Created {len(LEVELS)} levels with updated point requirements")
        
        # Step 3: Clear and recreate badges
        print("\n3. Updating badges...")
        db.execute(text("DELETE FROM badge"))
        db.commit()
        
        for badge_data in DEFAULT_BADGES:
            db.execute(
                text("""
                    INSERT INTO badge (name, description, badge_type, icon_url, points_value, created_at, updated_at)
                    VALUES (:name, :description, :badge_type, :icon_url, :points_value, NOW(), NOW())
                """),
                {
                    "name": badge_data["name"],
                    "description": badge_data["description"],
                    "badge_type": badge_data["badge_type"],
                    "icon_url": badge_data["icon_url"],
                    "points_value": badge_data["points_value"]
                }
            )
        
        db.commit()
        print(f"   ✅ Created {len(DEFAULT_BADGES)} badges with SVG designs and updated point values")
        
        # Step 4: Update level icons with SVG designs
        print("\n4. Updating level icons...")
        level_icons = {
            1: "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMzIiIGN5PSIzMiIgcj0iMzAiIGZpbGw9IiNGM0Y0RjYiIHN0cm9rZT0iI0Q5REFFMSIgc3Ryb2tlLXdpZHRoPSI0Ii8+Cjx0ZXh0IHg9IjMyIiB5PSI0MCIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjI0IiBmb250LXdlaWdodD0iYm9sZCIgZmlsbD0iIzM3NDE1MSIgdGV4dC1hbmNob3I9Im1pZGRsZSI+MTwvdGV4dD4KPC9zdmc+Cg==",
            2: "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMzIiIGN5PSIzMiIgcj0iMzAiIGZpbGw9IiNGRUY5RkYiIHN0cm9rZT0iIzM5OEVGNyIgc3Ryb2tlLXdpZHRoPSI0Ii8+Cjx0ZXh0IHg9IjMyIiB5PSI0MCIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjI0IiBmb250LXdlaWdodD0iYm9sZCIgZmlsbD0iIzM5OEVGNyIgdGV4dC1hbmNob3I9Im1pZGRsZSI+MjwvdGV4dD4KPC9zdmc+Cg==",
            3: "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMzIiIGN5PSIzMiIgcj0iMzAiIGZpbGw9IiNGRUZERkIiIHN0cm9rZT0iI0Y1OUUwQiIgc3Ryb2tlLXdpZHRoPSI0Ii8+Cjx0ZXh0IHg9IjMyIiB5PSI0MCIgZm9udC1mYW1pbHk9IkFyaWFsIiBmb250LXNpemU9IjI0IiBmb250LXdlaWdodD0iYm9sZCIgZmlsbD0iI0Y1OUUwQiIgdGV4dC1hbmNob3I9Im1pZGRsZSI+MzwvdGV4dD4KPC9zdmc+Cg==",
            10: "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMzIiIGN5PSIzMiIgcj0iMzAiIGZpbGw9InVybCgjZ3JhZDEwKSIgc3Ryb2tlPSIjRkZEQjAwIiBzdHJva2Utd2lkdGg9IjQiLz4KPGRlZnM+CjxsaW5lYXJHcmFkaWVudCBpZD0iZ3JhZDEwIj4KPHN0b3Agb2Zmc2V0PSIwJSIgc3RvcC1jb2xvcj0iI0ZGRkJGRiIvPgo8c3RvcCBvZmZzZXQ9IjEwMCUiIHN0b3AtY29sb3I9IiNGRkVCM0IiLz4KPC9saW5lYXJHcmFkaWVudD4KPC9kZWZzPgo8dGV4dCB4PSIzMiIgeT0iNDAiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIyMCIgZm9udC13ZWlnaHQ9ImJvbGQiIGZpbGw9IiNGRkQ3MDAiIHRleHQtYW5jaG9yPSJtaWRkbGUiPjEwPC90ZXh0Pgo8L3N2Zz4K",
            20: "data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNjQiIGhlaWdodD0iNjQiIHZpZXdCb3g9IjAgMCA2NCA2NCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPGNpcmNsZSBjeD0iMzIiIGN5PSIzMiIgcj0iMzAiIGZpbGw9InVybCgjZ3JhZDIwKSIgc3Ryb2tlPSIjRkZEQjAwIiBzdHJva2Utd2lkdGg9IjQiLz4KPGRlZnM+CjxyYWRpYWxHcmFkaWVudCBpZD0iZ3JhZDIwIj4KPHN0b3Agb2Zmc2V0PSIwJSIgc3RvcC1jb2xvcj0iI0ZGRDcwMCIvPgo8c3RvcCBvZmZzZXQ9IjE2JSIgc3RvcC1jb2xvcj0iI0ZGNTcyMiIvPgo8c3RvcCBvZmZzZXQ9IjMzJSIgc3RvcC1jb2xvcj0iIzkzMzNFQSIvPgo8c3RvcCBvZmZzZXQ9IjUwJSIgc3RvcC1jb2xvcj0iI0RDMjYyNiIvPgo8c3RvcCBvZmZzZXQ9IjY2JSIgc3RvcC1jb2xvcj0iIzEwQjk4MSIvPgo8c3RvcCBvZmZzZXQ9IjgzJSIgc3RvcC1jb2xvcj0iIzhCNUNGNiIvPgo8c3RvcCBvZmZzZXQ9IjEwMCUiIHN0b3AtY29sb3I9IiNGRkZCRkYiLz4KPC9yYWRpYWxHcmFkaWVudD4KPC9kZWZzPgo8dGV4dCB4PSIzMiIgeT0iNDAiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZm9udC1zaXplPSIxOCIgZm9udC13ZWlnaHQ9ImJvbGQiIGZpbGw9IiNGRkZGRkYiIHRleHQtYW5jaG9yPSJtaWRkbGUiPjIwPC90ZXh0Pgo8L3N2Zz4K"
        }
        
        # Update key level icons (sample)
        for level_num, icon_data in level_icons.items():
            db.execute(
                text("UPDATE userlevel SET icon_url = :icon_url WHERE level_number = :level_num"),
                {"icon_url": icon_data, "level_num": level_num}
            )
        
        db.commit()
        print(f"   ✅ Updated {len(level_icons)} level icons with SVG designs")
        
        # Step 5: Verify migration
        print("\n5. Verifying migration...")
        
        # Check levels
        level_count = db.execute(text("SELECT COUNT(*) FROM userlevel")).scalar()
        print(f"   ✅ Levels: {level_count}")
        
        # Check badges  
        badge_count = db.execute(text("SELECT COUNT(*) FROM badge")).scalar()
        print(f"   ✅ Badges: {badge_count}")
        
        # Check badge types
        try:
            result = db.execute(text("SELECT unnest(enum_range(NULL::badgetype))")).fetchall()
            badge_type_count = len(result)
            print(f"   ✅ Badge types: {badge_type_count}")
        except:
            print(f"   ⚠️  Could not verify badge types")
        
        print("\n✅ MIGRATION TO SUPABASE COMPLETED SUCCESSFULLY!")
        print("\n=== SUMMARY ===")
        print("🎯 20 levels with lower point requirements (0-11,750+ pts)")
        print("🏆 28 badges with SVG designs and lower point values (1-50 pts)")
        print("📊 Lower point system: 1-5 pts per action")
        print("🏅 Updated milestones: 50/500/2,500/10,000 pts")
        print("🎨 Beautiful SVG icons for levels and badges")
        
    except Exception as e:
        print(f"❌ Error during migration: {e}")
        db.rollback()
        import traceback
        traceback.print_exc()
    finally:
        db.close()

if __name__ == "__main__":
    migrate_gamification_to_supabase()
