import random
import string
from typing import Dict, Optional
from sqlalchemy.orm import Session

from app.models.user import User, UserRole
from app.models.generated_flashcard import GeneratedFlashcard
from app.models.question import Question, QuestionType, DifficultyLevel
from app.models.generated_note_explanation import GeneratedNoteExplanation
from app.services import user_service
from app.core.security import get_password_hash


def random_lower_string() -> str:
    return "".join(random.choices(string.ascii_lowercase, k=32))


def random_email() -> str:
    return f"{random_lower_string()}@{random_lower_string()}.com"


def create_random_user(
    db: Session, 
    *, 
    email: Optional[str] = None,
    role: UserRole = UserRole.STUDENT,
    is_active: bool = True,
    is_verified: bool = True
) -> User:
    """Create a random user for testing"""
    if email is None:
        email = random_email()
    
    password = random_lower_string()
    user_in = {
        "email": email,
        "full_name": f"Test User {random_lower_string()[:8]}",
        "password": password,
        "role": role,
        "is_active": is_active,
        "is_verified": is_verified
    }
    
    user = User(
        email=user_in["email"],
        full_name=user_in["full_name"],
        hashed_password=get_password_hash(user_in["password"]),
        role=user_in["role"],
        is_active=user_in["is_active"],
        is_verified=user_in["is_verified"]
    )
    
    db.add(user)
    db.commit()
    db.refresh(user)
    return user


def create_random_flashcard(
    db: Session,
    *,
    student_id: int,
    course_name: Optional[str] = None
) -> GeneratedFlashcard:
    """Create a random flashcard for testing"""
    if course_name is None:
        course_name = f"Test Course {random_lower_string()[:8]}"
    
    flashcard = GeneratedFlashcard(
        front_content=f"Question: {random_lower_string()}",
        back_content=f"Answer: {random_lower_string()}",
        topic=f"Topic {random_lower_string()[:8]}",
        difficulty="medium",
        card_type="definition",
        student_id=student_id,
        course_name=course_name,
        is_active=True
    )
    
    db.add(flashcard)
    db.commit()
    db.refresh(flashcard)
    return flashcard


def create_random_question(
    db: Session,
    *,
    created_by_id: int,
    course_name: Optional[str] = None
) -> Question:
    """Create a random MCQ question for testing"""
    if course_name is None:
        course_name = f"Test Course {random_lower_string()[:8]}"
    
    question = Question(
        content=f"What is {random_lower_string()}?",
        question_type=QuestionType.MULTIPLE_CHOICE,
        difficulty=DifficultyLevel.MEDIUM,
        topic=f"Topic {random_lower_string()[:8]}",
        options={
            "a": f"Option A: {random_lower_string()}",
            "b": f"Option B: {random_lower_string()}",
            "c": f"Option C: {random_lower_string()}",
            "d": f"Option D: {random_lower_string()}"
        },
        answer="a",
        explanation=f"Explanation: {random_lower_string()}",
        course_name=course_name,
        created_by_id=created_by_id,
        is_active=True
    )
    
    db.add(question)
    db.commit()
    db.refresh(question)
    return question


def create_random_summary(
    db: Session,
    *,
    student_id: int,
    course_name: Optional[str] = None
) -> GeneratedNoteExplanation:
    """Create a random summary/note explanation for testing"""
    if course_name is None:
        course_name = f"Test Course {random_lower_string()[:8]}"
    
    summary = GeneratedNoteExplanation(
        title=f"Summary: {random_lower_string()}",
        overview=f"Overview: {random_lower_string()}",
        detailed_explanation=f"Detailed explanation: {random_lower_string()}",
        comprehensive_summary=f"Comprehensive summary: {random_lower_string()}",
        key_concepts=["concept1", "concept2", "concept3"],
        main_topics=["topic1", "topic2", "topic3"],
        word_count=150,
        student_id=student_id,
        course_name=course_name,
        original_filename=f"test_file_{random_lower_string()[:8]}.pdf",
        is_active=True
    )
    
    db.add(summary)
    db.commit()
    db.refresh(summary)
    return summary


def get_user_authentication_headers(*, email: str, password: str) -> Dict[str, str]:
    """Get authentication headers for a user"""
    from app.core.security import create_access_token
    from app.services import user_service
    from tests.conftest import TestingSessionLocal
    
    db = TestingSessionLocal()
    try:
        user = user_service.authenticate(db, email=email, password=password)
        if not user:
            raise ValueError("Invalid credentials")
        
        access_token = create_access_token(user.id)
        return {"Authorization": f"Bearer {access_token}"}
    finally:
        db.close()


def get_superuser_token_headers() -> Dict[str, str]:
    """Get authentication headers for superuser"""
    from app.core.config import settings
    return get_user_authentication_headers(
        email=settings.FIRST_SUPERUSER, 
        password=settings.FIRST_SUPERUSER_PASSWORD
    )
