#!/usr/bin/env python3
"""
<PERSON>rip<PERSON> to create sharing system tables manually
"""

from app.db.session import engine
from app.models.shared_content import SharedContent, ShareInvitation, SharedContentAccess
from app.db.base_class import Base

def create_sharing_tables():
    """Create the sharing system tables"""
    try:
        # Create only the sharing tables
        SharedContent.__table__.create(engine, checkfirst=True)
        ShareInvitation.__table__.create(engine, checkfirst=True)
        SharedContentAccess.__table__.create(engine, checkfirst=True)
        
        print("✅ Sharing tables created successfully!")
        
        # Verify tables were created
        from sqlalchemy import text
        with engine.connect() as conn:
            result = conn.execute(text("SELECT table_name FROM information_schema.tables WHERE table_schema = 'public' AND table_name LIKE '%shared%'"))
            tables = result.fetchall()
            print(f"📋 Created tables: {[table[0] for table in tables]}")
            
    except Exception as e:
        print(f"❌ Error creating tables: {e}")
        raise

if __name__ == "__main__":
    create_sharing_tables()
