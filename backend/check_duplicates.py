#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to check for and clean up duplicate Gemini file entries
"""

import asyncio
from sqlalchemy import create_engine, text
from sqlalchemy.orm import sessionmaker
from app.core.config import settings
from app.models.gemini_file import GeminiFile
from app.db.session import SessionLocal

def check_duplicates():
    """Check for duplicate entries in the geminifile table"""
    db = SessionLocal()
    
    try:
        # Check for duplicates by filename (same file uploaded multiple times)
        filename_duplicates_query = text("""
            SELECT original_filename, COUNT(*) as count,
                   array_agg(gemini_file_id) as file_ids,
                   array_agg(id) as db_ids,
                   array_agg(created_at) as created_times
            FROM geminifile
            WHERE is_deleted = false
            GROUP BY original_filename, student_id
            HAVING COUNT(*) > 1
            ORDER BY count DESC
        """)

        filename_result = db.execute(filename_duplicates_query)
        filename_duplicates = filename_result.fetchall()

        if filename_duplicates:
            print("Found duplicate files by filename:")
            for row in filename_duplicates:
                print(f"  - Filename: {row.original_filename}")
                print(f"    Count: {row.count}")
                print(f"    File IDs: {row.file_ids}")
                print(f"    DB IDs: {row.db_ids}")
                print()

                # Keep the first entry (oldest), mark others as deleted
                if len(row.db_ids) > 1:
                    keep_id = row.db_ids[0]  # Keep the first (oldest) entry
                    delete_ids = row.db_ids[1:]  # Delete the rest

                    print(f"    Keeping entry ID: {keep_id}")
                    print(f"    Marking as deleted: {delete_ids}")

                    # Mark duplicates as deleted
                    for delete_id in delete_ids:
                        delete_query = text("""
                            UPDATE geminifile
                            SET is_deleted = true, updated_at = NOW()
                            WHERE id = :id
                        """)

                        db.execute(delete_query, {"id": delete_id})

                    db.commit()
                    print(f"    ✓ Cleaned up {len(delete_ids)} duplicate entries")

                print("-" * 50)

        # Check for duplicates by gemini_file_id (should not happen due to unique constraint)
        duplicates_query = text("""
            SELECT gemini_file_id, original_filename, COUNT(*) as count
            FROM geminifile
            WHERE is_deleted = false
            GROUP BY gemini_file_id, original_filename
            HAVING COUNT(*) > 1
            ORDER BY count DESC
        """)
        
        result = db.execute(duplicates_query)
        duplicates = result.fetchall()
        
        if duplicates:
            print("Found duplicate entries:")
            for row in duplicates:
                print(f"  - File ID: {row.gemini_file_id}")
                print(f"    Filename: {row.original_filename}")
                print(f"    Count: {row.count}")
                print()
                
                # Get all entries for this file_id
                entries_query = text("""
                    SELECT id, created_at, upload_time, auto_delete_time
                    FROM geminifile 
                    WHERE gemini_file_id = :file_id AND is_deleted = false
                    ORDER BY created_at ASC
                """)
                
                entries = db.execute(entries_query, {"file_id": row.gemini_file_id}).fetchall()
                
                print(f"    Entries for {row.gemini_file_id}:")
                for i, entry in enumerate(entries):
                    print(f"      {i+1}. ID: {entry.id}, Created: {entry.created_at}, Upload: {entry.upload_time}")
                
                # Keep the first entry, mark others as deleted
                if len(entries) > 1:
                    keep_id = entries[0].id
                    delete_ids = [entry.id for entry in entries[1:]]

                    print(f"    Keeping entry ID: {keep_id}")
                    print(f"    Marking as deleted: {delete_ids}")

                    # Mark duplicates as deleted
                    for delete_id in delete_ids:
                        delete_query = text("""
                            UPDATE geminifile
                            SET is_deleted = true, updated_at = NOW()
                            WHERE id = :id
                        """)

                        db.execute(delete_query, {"id": delete_id})

                    db.commit()
                    print(f"    ✓ Cleaned up {len(delete_ids)} duplicate entries")
                
                print("-" * 50)
        else:
            print("No duplicate entries found.")
            
        # Show current file count
        count_query = text("SELECT COUNT(*) FROM geminifile WHERE is_deleted = false")
        total_files = db.execute(count_query).scalar()
        print(f"\nTotal active files: {total_files}")
        
        # Show files with their expiry status
        files_query = text("""
            SELECT gemini_file_id, original_filename, upload_time, auto_delete_time,
                   CASE 
                       WHEN auto_delete_time < NOW() THEN 'EXPIRED'
                       ELSE 'ACTIVE'
                   END as status
            FROM geminifile 
            WHERE is_deleted = false
            ORDER BY upload_time DESC
            LIMIT 10
        """)
        
        files = db.execute(files_query).fetchall()
        
        if files:
            print("\nRecent files:")
            for file in files:
                print(f"  - {file.original_filename}")
                print(f"    ID: {file.gemini_file_id}")
                print(f"    Upload: {file.upload_time}")
                print(f"    Expires: {file.auto_delete_time}")
                print(f"    Status: {file.status}")
                print()
        
    except Exception as e:
        print(f"Error: {e}")
        db.rollback()
    finally:
        db.close()

if __name__ == "__main__":
    check_duplicates()
