-- Initialize CampusPQ database
-- This script runs when the PostgreSQL container starts for the first time

-- Create extensions if they don't exist
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- Set timezone
SET timezone = 'UTC';

-- Create database if it doesn't exist (this is handled by POSTGRES_DB env var)
-- But we can set some database-level configurations here

-- Set default search path
ALTER DATABASE campuspq SET search_path TO public;

-- Create a read-only user for monitoring/analytics (optional)
-- CREATE USER campuspq_readonly WITH PASSWORD 'readonly_password';
-- GRANT CONNECT ON DATABASE campuspq TO campuspq_readonly;
-- GRANT USAGE ON SCHEMA public TO campuspq_readonly;
-- GRANT SELECT ON ALL TABLES IN SCHEMA public TO campuspq_readonly;
-- ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT SELECT ON TABLES TO campuspq_readonly;
