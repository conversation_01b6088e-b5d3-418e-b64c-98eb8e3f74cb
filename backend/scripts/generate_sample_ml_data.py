#!/usr/bin/env python3
"""
Generate sample ML training data for testing the recommendation system.
This script creates realistic user behavior events to test the ML system.
"""

import sys
import os
import random
from datetime import datetime, timedelta, timezone
from typing import List, Dict, Any

# Add the parent directory to the path so we can import from app
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from app.db.session import SessionLocal
from app.models.user import User
from app.models.question import Question
from app.models.course import Course
from app.models.ml_recommendations import UserBehaviorEvent
from app.services.ml_recommendation_service import ml_recommendation_service
from sqlalchemy import func

def generate_realistic_behavior_data(db: SessionLocal, num_events_per_user: int = 50):
    """Generate realistic user behavior data for ML training"""
    
    print("🤖 Generating sample ML training data...")
    
    # Get all users and questions
    users = db.query(User).filter(User.role == 'STUDENT').all()
    questions = db.query(Question).filter(Question.is_active == True).all()
    courses = db.query(Course).all()
    
    if not users:
        print("❌ No student users found. Please create some users first.")
        return
    
    if not questions:
        print("❌ No active questions found. Please add some questions first.")
        return
    
    print(f"📊 Found {len(users)} users, {len(questions)} questions, {len(courses)} courses")
    
    # Define realistic behavior patterns
    topics = list(set([q.topic for q in questions if q.topic]))
    difficulties = ['easy', 'medium', 'hard']
    
    # User personas with different learning patterns
    user_personas = [
        {
            'name': 'High Performer',
            'accuracy_range': (0.8, 0.95),
            'response_time_range': (15, 45),
            'help_seeking_rate': 0.1,
            'preferred_difficulty': 'hard'
        },
        {
            'name': 'Average Learner',
            'accuracy_range': (0.6, 0.8),
            'response_time_range': (30, 90),
            'help_seeking_rate': 0.3,
            'preferred_difficulty': 'medium'
        },
        {
            'name': 'Struggling Student',
            'accuracy_range': (0.3, 0.6),
            'response_time_range': (60, 180),
            'help_seeking_rate': 0.5,
            'preferred_difficulty': 'easy'
        },
        {
            'name': 'Quick Learner',
            'accuracy_range': (0.7, 0.9),
            'response_time_range': (10, 30),
            'help_seeking_rate': 0.2,
            'preferred_difficulty': 'medium'
        }
    ]
    
    events_created = 0
    
    for user in users:
        # Assign a random persona to each user
        persona = random.choice(user_personas)
        print(f"👤 Generating data for user {user.id} ({user.email}) - {persona['name']}")
        
        # Generate events over the last 30 days
        start_date = datetime.now(timezone.utc) - timedelta(days=30)
        
        for i in range(num_events_per_user):
            # Random timestamp within the last 30 days
            event_time = start_date + timedelta(
                days=random.randint(0, 30),
                hours=random.randint(8, 22),  # Study hours
                minutes=random.randint(0, 59)
            )
            
            # Select a random question
            question = random.choice(questions)
            
            # Generate behavior based on persona
            is_correct = random.random() < random.uniform(*persona['accuracy_range'])
            response_time = random.uniform(*persona['response_time_range'])
            
            # Occasionally generate help requests
            if random.random() < persona['help_seeking_rate']:
                event_type = 'help_request'
                is_correct = None
                response_time = None
            else:
                event_type = 'mcq_attempt'
            
            # Create behavior event
            event = UserBehaviorEvent(
                user_id=user.id,
                event_type=event_type,
                content_type='question',
                content_id=question.id,
                course_id=question.course_id,
                topic=question.topic,
                difficulty=question.difficulty,
                is_correct=is_correct,
                response_time_seconds=response_time,
                confidence_level=random.randint(1, 5) if is_correct is not None else None,
                session_id=f"session_{user.id}_{i // 10}",  # Group events into sessions
                device_type=random.choice(['desktop', 'mobile']),
                time_of_day=event_time.hour,
                day_of_week=event_time.weekday(),
                created_at=event_time,
                event_metadata={'generated': True, 'persona': persona['name']}
            )
            
            db.add(event)
            events_created += 1
            
            # Commit in batches
            if events_created % 100 == 0:
                db.commit()
                print(f"  ✅ Created {events_created} events...")
    
    # Final commit
    db.commit()
    print(f"🎉 Successfully created {events_created} behavior events!")
    
    # Generate user features for all users with sufficient data
    print("\n🧠 Generating user features...")
    for user in users:
        try:
            ml_recommendation_service._update_user_features(db, user.id)
            print(f"  ✅ Generated features for user {user.id}")
        except Exception as e:
            print(f"  ❌ Failed to generate features for user {user.id}: {e}")
    
    # Check final statistics
    total_events = db.query(func.count(UserBehaviorEvent.id)).scalar()
    total_users_with_features = db.query(func.count(User.id)).join(
        UserBehaviorEvent, User.id == UserBehaviorEvent.user_id
    ).scalar()
    
    print(f"\n📈 Final Statistics:")
    print(f"  Total behavior events: {total_events}")
    print(f"  Users with behavior data: {total_users_with_features}")
    print(f"  Average events per user: {total_events / len(users):.1f}")

def main():
    """Main function to generate sample data"""
    db = SessionLocal()
    
    try:
        # Check if we already have behavior data
        existing_events = db.query(func.count(UserBehaviorEvent.id)).scalar()
        
        if existing_events > 0:
            print(f"⚠️  Found {existing_events} existing behavior events.")
            response = input("Do you want to add more data? (y/N): ")
            if response.lower() != 'y':
                print("Exiting without changes.")
                return
        
        # Generate the data
        generate_realistic_behavior_data(db, num_events_per_user=75)
        
        print("\n✨ Sample ML data generation complete!")
        print("You can now test the ML recommendation system with realistic data.")
        
    except Exception as e:
        print(f"❌ Error generating sample data: {e}")
        db.rollback()
    finally:
        db.close()

if __name__ == "__main__":
    main()
