#!/usr/bin/env python3
"""
Script to populate the database with Nigerian tertiary institutions, their departments and courses.
Usage: python scripts/populate_nigerian_schools.py
"""

import sys
import os
from pathlib import Path

# Add the backend directory to the Python path
backend_dir = Path(__file__).parent.parent
sys.path.insert(0, str(backend_dir))

from sqlalchemy.orm import Session
from app.db.session import SessionLocal
from app.models.school import School
from app.models.department import Department
from app.models.course import Course


# Nigerian Universities and their common departments/courses
NIGERIAN_UNIVERSITIES = {
    "University of Lagos": {
        "state": "Lagos",
        "type": "Federal",
        "departments": {
            "Computer Science": [
                "Introduction to Computer Science",
                "Data Structures and Algorithms",
                "Database Management Systems",
                "Software Engineering",
                "Computer Networks",
                "Artificial Intelligence",
                "Machine Learning",
                "Web Development",
                "Mobile App Development",
                "Cybersecurity"
            ],
            "Electrical Engineering": [
                "Circuit Analysis",
                "Digital Electronics",
                "Power Systems",
                "Control Systems",
                "Telecommunications",
                "Signal Processing",
                "Microprocessors",
                "Renewable Energy Systems"
            ],
            "Medicine": [
                "Anatomy",
                "Physiology",
                "Biochemistry",
                "Pharmacology",
                "Pathology",
                "Internal Medicine",
                "Surgery",
                "Pediatrics",
                "Obstetrics and Gynecology"
            ],
            "Law": [
                "Constitutional Law",
                "Criminal Law",
                "Contract Law",
                "Tort Law",
                "Commercial Law",
                "International Law",
                "Legal Research and Writing"
            ],
            "Economics": [
                "Microeconomics",
                "Macroeconomics",
                "Econometrics",
                "Development Economics",
                "International Economics",
                "Public Finance",
                "Monetary Economics"
            ],
            "Accounting": [
                "Financial Accounting",
                "Management Accounting",
                "Auditing",
                "Taxation",
                "Corporate Finance",
                "Financial Reporting",
                "Cost Accounting"
            ],
            "Business Administration": [
                "Principles of Management",
                "Marketing Management",
                "Human Resource Management",
                "Operations Management",
                "Strategic Management",
                "Organizational Behavior",
                "Business Ethics"
            ],
            "Mathematics": [
                "Calculus",
                "Linear Algebra",
                "Differential Equations",
                "Statistics",
                "Probability Theory",
                "Numerical Analysis",
                "Abstract Algebra",
                "Real Analysis"
            ],
            "Physics": [
                "Classical Mechanics",
                "Electromagnetism",
                "Thermodynamics",
                "Quantum Mechanics",
                "Optics",
                "Nuclear Physics",
                "Solid State Physics"
            ],
            "Chemistry": [
                "General Chemistry",
                "Organic Chemistry",
                "Inorganic Chemistry",
                "Physical Chemistry",
                "Analytical Chemistry",
                "Biochemistry",
                "Environmental Chemistry"
            ]
        }
    },
    "University of Ibadan": {
        "state": "Oyo",
        "type": "Federal",
        "departments": {
            "Computer Science": [
                "Programming Fundamentals",
                "Data Structures",
                "Computer Architecture",
                "Operating Systems",
                "Database Systems",
                "Software Engineering",
                "Computer Graphics",
                "Artificial Intelligence"
            ],
            "Medicine": [
                "Human Anatomy",
                "Physiology",
                "Medical Biochemistry",
                "Pharmacology",
                "Pathology",
                "Community Medicine",
                "Internal Medicine"
            ],
            "Engineering": [
                "Engineering Mathematics",
                "Mechanics of Materials",
                "Thermodynamics",
                "Fluid Mechanics",
                "Engineering Design",
                "Project Management"
            ],
            "Agriculture": [
                "Crop Production",
                "Animal Science",
                "Soil Science",
                "Agricultural Economics",
                "Plant Pathology",
                "Agricultural Extension"
            ],
            "Pharmacy": [
                "Pharmaceutical Chemistry",
                "Pharmacology",
                "Pharmaceutics",
                "Clinical Pharmacy",
                "Pharmacognosy",
                "Pharmaceutical Microbiology"
            ]
        }
    },
    "Ahmadu Bello University": {
        "state": "Kaduna",
        "type": "Federal",
        "departments": {
            "Computer Science": [
                "Computer Programming",
                "Data Structures and Algorithms",
                "Computer Networks",
                "Database Management",
                "Software Engineering",
                "Information Security"
            ],
            "Engineering": [
                "Engineering Mathematics",
                "Engineering Drawing",
                "Materials Science",
                "Structural Analysis",
                "Geotechnical Engineering"
            ],
            "Medicine": [
                "Basic Medical Sciences",
                "Clinical Medicine",
                "Community Health",
                "Medical Ethics"
            ],
            "Agriculture": [
                "Agronomy",
                "Animal Production",
                "Agricultural Engineering",
                "Food Science and Technology"
            ]
        }
    },
    "University of Nigeria, Nsukka": {
        "state": "Enugu",
        "type": "Federal",
        "departments": {
            "Computer Science": [
                "Introduction to Computing",
                "Programming Languages",
                "Systems Analysis and Design",
                "Computer Networks",
                "Database Systems"
            ],
            "Engineering": [
                "Engineering Mechanics",
                "Engineering Materials",
                "Engineering Design",
                "Manufacturing Processes"
            ],
            "Medicine": [
                "Medical Biochemistry",
                "Human Anatomy",
                "Physiology",
                "Pharmacology"
            ],
            "Education": [
                "Educational Psychology",
                "Curriculum and Instruction",
                "Educational Administration",
                "Measurement and Evaluation"
            ]
        }
    },
    "Obafemi Awolowo University": {
        "state": "Osun",
        "type": "Federal",
        "departments": {
            "Computer Science": [
                "Computer Programming",
                "Data Structures",
                "Computer Architecture",
                "Software Engineering"
            ],
            "Engineering": [
                "Engineering Mathematics",
                "Mechanics",
                "Electrical Circuits",
                "Materials Engineering"
            ],
            "Medicine": [
                "Anatomy",
                "Physiology",
                "Biochemistry",
                "Pharmacology"
            ]
        }
    }
}


def populate_schools():
    """Populate the database with Nigerian schools, departments, and courses."""
    print("=== Populating Nigerian Universities ===")
    
    db: Session = SessionLocal()
    
    try:
        total_schools = 0
        total_departments = 0
        total_courses = 0
        
        for school_name, school_data in NIGERIAN_UNIVERSITIES.items():
            print(f"\n📚 Processing {school_name}...")
            
            # Check if school already exists
            existing_school = db.query(School).filter(School.name == school_name).first()
            if existing_school:
                print(f"   ⚠️  School already exists, skipping...")
                continue
            
            # Create school
            school = School(
                name=school_name,
                state=school_data["state"],
                country="Nigeria",
                school_type=school_data["type"]
            )
            db.add(school)
            db.flush()  # Get the school ID
            total_schools += 1
            
            # Create departments and courses
            for dept_name, courses in school_data["departments"].items():
                print(f"   📖 Adding department: {dept_name}")
                
                department = Department(
                    name=dept_name,
                    school_id=school.id
                )
                db.add(department)
                db.flush()  # Get the department ID
                total_departments += 1
                
                # Create courses
                for course_name in courses:
                    course = Course(
                        name=course_name,
                        code=f"{dept_name[:3].upper()}{total_courses + 1:03d}",
                        department_id=department.id,
                        school_id=school.id
                    )
                    db.add(course)
                    total_courses += 1
                
                print(f"      ✅ Added {len(courses)} courses")
        
        # Commit all changes
        db.commit()
        
        print(f"\n🎉 Successfully populated database!")
        print(f"   📚 Schools: {total_schools}")
        print(f"   📖 Departments: {total_departments}")
        print(f"   📝 Courses: {total_courses}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error populating database: {str(e)}")
        db.rollback()
        return False
        
    finally:
        db.close()


def main():
    """Main function."""
    try:
        success = populate_schools()
        if success:
            print("\n✨ Database population completed successfully!")
        else:
            print("\n❌ Failed to populate database.")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n\n⚠️  Operation cancelled by user.")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Unexpected error: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()
