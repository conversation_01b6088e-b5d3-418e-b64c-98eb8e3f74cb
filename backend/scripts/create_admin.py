#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to create an admin user directly in the database.
Usage: python scripts/create_admin.py
"""

import sys
import os
from pathlib import Path

# Add the backend directory to the Python path
backend_dir = Path(__file__).parent.parent
sys.path.insert(0, str(backend_dir))

from sqlalchemy.orm import Session
from app.db.session import SessionLocal
from app.models.user import User
from app.core.security import get_password_hash
from app.schemas.user import UserRole
import getpass


def create_admin_user():
    """Create an admin user interactively."""
    print("=== Create Admin User ===")
    
    # Get user input
    email = input("Enter admin email: ").strip()
    if not email:
        print("Email is required!")
        return False
    
    full_name = input("Enter admin full name: ").strip()
    if not full_name:
        print("Full name is required!")
        return False
    
    # Get password securely
    password = getpass.getpass("Enter admin password: ")
    if not password:
        print("Password is required!")
        return False
    
    confirm_password = getpass.getpass("Confirm admin password: ")
    if password != confirm_password:
        print("Passwords do not match!")
        return False
    
    # Create database session
    db: Session = SessionLocal()
    
    try:
        # Check if user already exists
        existing_user = db.query(User).filter(User.email == email).first()
        if existing_user:
            print(f"User with email {email} already exists!")
            return False
        
        # Create admin user
        admin_user = User(
            email=email,
            full_name=full_name,
            hashed_password=get_password_hash(password),
            role=UserRole.ADMIN,
            is_active=True,
            is_profile_complete=True
        )
        
        db.add(admin_user)
        db.commit()
        db.refresh(admin_user)
        
        print(f"✅ Admin user created successfully!")
        print(f"   ID: {admin_user.id}")
        print(f"   Email: {admin_user.email}")
        print(f"   Name: {admin_user.full_name}")
        print(f"   Role: {admin_user.role}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error creating admin user: {str(e)}")
        db.rollback()
        return False
        
    finally:
        db.close()


def main():
    """Main function."""
    try:
        success = create_admin_user()
        if success:
            print("\n🎉 Admin user created successfully! You can now log in to the admin panel.")
        else:
            print("\n❌ Failed to create admin user.")
            sys.exit(1)
            
    except KeyboardInterrupt:
        print("\n\n⚠️  Operation cancelled by user.")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Unexpected error: {str(e)}")
        sys.exit(1)


if __name__ == "__main__":
    main()
