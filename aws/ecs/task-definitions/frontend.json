{"family": "campuspq-frontend", "networkMode": "awsvpc", "requiresCompatibilities": ["FARGATE"], "cpu": "512", "memory": "1024", "executionRoleArn": "arn:aws:iam::ACCOUNT_ID:role/ecsTaskExecutionRole", "taskRoleArn": "arn:aws:iam::ACCOUNT_ID:role/campuspq-task-role", "containerDefinitions": [{"name": "campuspq-frontend", "image": "ACCOUNT_ID.dkr.ecr.REGION.amazonaws.com/campuspq-frontend:latest", "essential": true, "portMappings": [{"containerPort": 80, "protocol": "tcp"}], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/campuspq-frontend", "awslogs-region": "REGION", "awslogs-stream-prefix": "ecs"}}, "healthCheck": {"command": ["CMD-SHELL", "curl -f http://localhost:80/health || exit 1"], "interval": 30, "timeout": 5, "retries": 3, "startPeriod": 30}}]}