{"family": "campuspq-celery-worker", "networkMode": "awsvpc", "requiresCompatibilities": ["FARGATE"], "cpu": "1024", "memory": "2048", "executionRoleArn": "arn:aws:iam::ACCOUNT_ID:role/ecsTaskExecutionRole", "taskRoleArn": "arn:aws:iam::ACCOUNT_ID:role/campuspq-task-role", "containerDefinitions": [{"name": "campuspq-worker", "image": "ACCOUNT_ID.dkr.ecr.REGION.amazonaws.com/campuspq-worker:latest", "essential": true, "environment": [{"name": "ENVIRONMENT", "value": "production"}, {"name": "DEBUG", "value": "false"}, {"name": "QDRANT_COLLECTION", "value": "campuspq_embeddings"}, {"name": "AI_PROVIDER", "value": "gemini"}, {"name": "CACHE_ENABLED", "value": "true"}, {"name": "CACHE_TTL_HOURS", "value": "24"}], "secrets": [{"name": "DATABASE_URL", "valueFrom": "arn:aws:ssm:REGION:ACCOUNT_ID:parameter/campuspq/database-url"}, {"name": "REDIS_URL", "valueFrom": "arn:aws:ssm:REGION:ACCOUNT_ID:parameter/campuspq/redis-url"}, {"name": "QDRANT_URL", "valueFrom": "arn:aws:ssm:REGION:ACCOUNT_ID:parameter/campuspq/qdrant-url"}, {"name": "SECRET_KEY", "valueFrom": "arn:aws:ssm:REGION:ACCOUNT_ID:parameter/campuspq/secret-key"}, {"name": "OPENAI_API_KEY", "valueFrom": "arn:aws:ssm:REGION:ACCOUNT_ID:parameter/campuspq/openai-api-key"}, {"name": "GEMINI_API_KEY", "valueFrom": "arn:aws:ssm:REGION:ACCOUNT_ID:parameter/campuspq/gemini-api-key"}, {"name": "CLOUDINARY_URL", "valueFrom": "arn:aws:ssm:REGION:ACCOUNT_ID:parameter/campuspq/cloudinary-url"}, {"name": "CLOUDINARY_API_KEY", "valueFrom": "arn:aws:ssm:REGION:ACCOUNT_ID:parameter/campuspq/cloudinary-api-key"}, {"name": "CLOUDINARY_API_SECRET", "valueFrom": "arn:aws:ssm:REGION:ACCOUNT_ID:parameter/campuspq/cloudinary-api-secret"}], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/campuspq-celery-worker", "awslogs-region": "REGION", "awslogs-stream-prefix": "ecs"}}, "healthCheck": {"command": ["CMD-SHELL", "celery -A app.core.celery_app inspect ping || exit 1"], "interval": 60, "timeout": 30, "retries": 3, "startPeriod": 120}, "mountPoints": [{"sourceVolume": "uploads", "containerPath": "/app/uploads"}]}], "volumes": [{"name": "uploads", "efsVolumeConfiguration": {"fileSystemId": "fs-XXXXXXXXX", "transitEncryption": "ENABLED", "authorizationConfig": {"accessPointId": "fsap-XXXXXXXXX"}}}]}