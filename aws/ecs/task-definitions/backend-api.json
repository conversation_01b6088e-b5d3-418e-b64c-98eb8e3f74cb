{"family": "campuspq-backend-api", "networkMode": "awsvpc", "requiresCompatibilities": ["FARGATE"], "cpu": "1024", "memory": "2048", "executionRoleArn": "arn:aws:iam::ACCOUNT_ID:role/ecsTaskExecutionRole", "taskRoleArn": "arn:aws:iam::ACCOUNT_ID:role/campuspq-task-role", "containerDefinitions": [{"name": "campuspq-api", "image": "ACCOUNT_ID.dkr.ecr.REGION.amazonaws.com/campuspq-backend:latest", "essential": true, "portMappings": [{"containerPort": 8000, "protocol": "tcp"}], "environment": [{"name": "ENVIRONMENT", "value": "production"}, {"name": "DEBUG", "value": "false"}, {"name": "API_V1_PREFIX", "value": "/api/v1"}, {"name": "PROJECT_NAME", "value": "CampusPQ"}, {"name": "ALGORITHM", "value": "HS256"}, {"name": "ACCESS_TOKEN_EXPIRE_MINUTES", "value": "30"}, {"name": "QDRANT_COLLECTION", "value": "campuspq_embeddings"}, {"name": "AI_PROVIDER", "value": "gemini"}, {"name": "CACHE_ENABLED", "value": "true"}, {"name": "CACHE_TTL_HOURS", "value": "24"}], "secrets": [{"name": "DATABASE_URL", "valueFrom": "arn:aws:ssm:REGION:ACCOUNT_ID:parameter/campuspq/database-url"}, {"name": "REDIS_URL", "valueFrom": "arn:aws:ssm:REGION:ACCOUNT_ID:parameter/campuspq/redis-url"}, {"name": "QDRANT_URL", "valueFrom": "arn:aws:ssm:REGION:ACCOUNT_ID:parameter/campuspq/qdrant-url"}, {"name": "SECRET_KEY", "valueFrom": "arn:aws:ssm:REGION:ACCOUNT_ID:parameter/campuspq/secret-key"}, {"name": "OPENAI_API_KEY", "valueFrom": "arn:aws:ssm:REGION:ACCOUNT_ID:parameter/campuspq/openai-api-key"}, {"name": "GEMINI_API_KEY", "valueFrom": "arn:aws:ssm:REGION:ACCOUNT_ID:parameter/campuspq/gemini-api-key"}, {"name": "CLOUDINARY_URL", "valueFrom": "arn:aws:ssm:REGION:ACCOUNT_ID:parameter/campuspq/cloudinary-url"}, {"name": "CLOUDINARY_API_KEY", "valueFrom": "arn:aws:ssm:REGION:ACCOUNT_ID:parameter/campuspq/cloudinary-api-key"}, {"name": "CLOUDINARY_API_SECRET", "valueFrom": "arn:aws:ssm:REGION:ACCOUNT_ID:parameter/campuspq/cloudinary-api-secret"}, {"name": "SMTP_USER", "valueFrom": "arn:aws:ssm:REGION:ACCOUNT_ID:parameter/campuspq/smtp-user"}, {"name": "SMTP_PASSWORD", "valueFrom": "arn:aws:ssm:REGION:ACCOUNT_ID:parameter/campuspq/smtp-password"}, {"name": "EMAILS_FROM_EMAIL", "valueFrom": "arn:aws:ssm:REGION:ACCOUNT_ID:parameter/campuspq/emails-from-email"}, {"name": "FRONTEND_URL", "valueFrom": "arn:aws:ssm:REGION:ACCOUNT_ID:parameter/campuspq/frontend-url"}], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/campuspq-backend-api", "awslogs-region": "REGION", "awslogs-stream-prefix": "ecs"}}, "healthCheck": {"command": ["CMD-SHELL", "curl -f http://localhost:8000/api/v1/health || exit 1"], "interval": 30, "timeout": 5, "retries": 3, "startPeriod": 60}, "mountPoints": [{"sourceVolume": "uploads", "containerPath": "/app/uploads"}]}], "volumes": [{"name": "uploads", "efsVolumeConfiguration": {"fileSystemId": "fs-XXXXXXXXX", "transitEncryption": "ENABLED", "authorizationConfig": {"accessPointId": "fsap-XXXXXXXXX"}}}]}