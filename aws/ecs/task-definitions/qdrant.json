{"family": "campuspq-qdrant", "networkMode": "awsvpc", "requiresCompatibilities": ["FARGATE"], "cpu": "1024", "memory": "2048", "executionRoleArn": "arn:aws:iam::ACCOUNT_ID:role/ecsTaskExecutionRole", "taskRoleArn": "arn:aws:iam::ACCOUNT_ID:role/campuspq-task-role", "containerDefinitions": [{"name": "campuspq-qdrant", "image": "qdrant/qdrant:latest", "essential": true, "portMappings": [{"containerPort": 6333, "protocol": "tcp"}, {"containerPort": 6334, "protocol": "tcp"}], "logConfiguration": {"logDriver": "awslogs", "options": {"awslogs-group": "/ecs/campuspq-qdrant", "awslogs-region": "REGION", "awslogs-stream-prefix": "ecs"}}, "healthCheck": {"command": ["CMD-SHELL", "curl -f http://localhost:6333/health || exit 1"], "interval": 30, "timeout": 10, "retries": 3, "startPeriod": 60}, "mountPoints": [{"sourceVolume": "qdrant-storage", "containerPath": "/qdrant/storage"}]}], "volumes": [{"name": "qdrant-storage", "efsVolumeConfiguration": {"fileSystemId": "fs-XXXXXXXXX", "transitEncryption": "ENABLED", "authorizationConfig": {"accessPointId": "fsap-XXXXXXXXX"}}}]}