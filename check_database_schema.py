#!/usr/bin/env python3
"""
Script to check the actual database schema
"""

import sys
import os
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from sqlalchemy import text
from app.db.session import engine

def check_schema():
    """Check the actual database schema"""
    
    print("🔍 Checking actual database schema...")
    
    with engine.connect() as connection:
        # Check the actual columns
        result = connection.execute(text("""
            SELECT column_name, data_type, is_nullable
            FROM information_schema.columns 
            WHERE table_name = 'shared_content_access'
            ORDER BY ordinal_position;
        """))
        
        columns = result.fetchall()
        print("Actual columns in shared_content_access:")
        for col in columns:
            print(f"  - {col[0]} ({col[1]}) {'NULL' if col[2] == 'YES' else 'NOT NULL'}")
        
        # Check if we can query with user_id
        try:
            result = connection.execute(text("SELECT user_id FROM shared_content_access LIMIT 1;"))
            print("✅ user_id column exists and is queryable")
        except Exception as e:
            print(f"❌ user_id column issue: {e}")
            
        # Check if accessed_by_id still exists
        try:
            result = connection.execute(text("SELECT accessed_by_id FROM shared_content_access LIMIT 1;"))
            print("⚠️ accessed_by_id column still exists")
        except Exception as e:
            print("✅ accessed_by_id column does not exist (good)")

def fix_schema_properly():
    """Fix the schema properly"""
    
    print("\n🔧 Fixing schema properly...")
    
    with engine.connect() as connection:
        trans = connection.begin()
        
        try:
            # Check current state
            result = connection.execute(text("""
                SELECT column_name 
                FROM information_schema.columns 
                WHERE table_name = 'shared_content_access'
                AND column_name IN ('accessed_by_id', 'user_id');
            """))
            
            existing_columns = [row[0] for row in result.fetchall()]
            print(f"Found columns: {existing_columns}")
            
            if 'accessed_by_id' in existing_columns and 'user_id' not in existing_columns:
                print("✅ Renaming accessed_by_id to user_id...")
                
                # Drop old index
                connection.execute(text("DROP INDEX IF EXISTS ix_shared_content_access_accessed_by_id;"))
                
                # Rename column
                connection.execute(text("ALTER TABLE shared_content_access RENAME COLUMN accessed_by_id TO user_id;"))
                
                # Create new index
                connection.execute(text("CREATE INDEX ix_shared_content_access_user_id ON shared_content_access (user_id);"))
                
                print("✅ Successfully renamed column")
                
            elif 'user_id' in existing_columns:
                print("✅ user_id column already exists")
                
            else:
                print("❌ Unexpected column state")
                return False
            
            trans.commit()
            return True
            
        except Exception as e:
            trans.rollback()
            print(f"❌ Error: {e}")
            return False

if __name__ == "__main__":
    print("🚀 Checking Database Schema")
    print("=" * 50)
    
    check_schema()
    
    # Try to fix if needed
    success = fix_schema_properly()
    
    if success:
        print("\n" + "=" * 50)
        print("✅ Checking schema after fix...")
        check_schema()
    
    print("\n" + "=" * 50)
    print("Done!")
