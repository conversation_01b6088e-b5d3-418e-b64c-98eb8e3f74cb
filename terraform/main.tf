terraform {
  required_version = ">= 1.0"
  required_providers {
    aws = {
      source  = "hashicorp/aws"
      version = "~> 5.0"
    }
  }
  
  backend "s3" {
    # Configure this with your own S3 bucket for Terraform state
    bucket = "campuspq-terraform-state"
    key    = "infrastructure/terraform.tfstate"
    region = "us-east-1"
    
    # Enable state locking
    dynamodb_table = "campuspq-terraform-locks"
    encrypt        = true
  }
}

provider "aws" {
  region = var.aws_region
  
  default_tags {
    tags = {
      Project     = "CampusPQ"
      Environment = var.environment
      ManagedBy   = "Terraform"
    }
  }
}

# Data sources
data "aws_availability_zones" "available" {
  state = "available"
}

data "aws_caller_identity" "current" {}

# Local values
locals {
  name_prefix = "${var.project_name}-${var.environment}"
  
  common_tags = {
    Project     = var.project_name
    Environment = var.environment
    ManagedBy   = "Terraform"
  }
}

# VPC Module
module "vpc" {
  source = "./modules/vpc"
  
  name_prefix         = local.name_prefix
  vpc_cidr           = var.vpc_cidr
  availability_zones = slice(data.aws_availability_zones.available.names, 0, 3)
  
  tags = local.common_tags
}

# Security Groups Module
module "security_groups" {
  source = "./modules/security"
  
  name_prefix = local.name_prefix
  vpc_id      = module.vpc.vpc_id
  
  tags = local.common_tags
}

# RDS Module
module "rds" {
  source = "./modules/rds"
  
  name_prefix           = local.name_prefix
  vpc_id               = module.vpc.vpc_id
  private_subnet_ids   = module.vpc.private_subnet_ids
  security_group_ids   = [module.security_groups.rds_security_group_id]
  
  db_instance_class    = var.db_instance_class
  db_allocated_storage = var.db_allocated_storage
  db_name             = var.db_name
  db_username         = var.db_username
  db_password         = var.db_password
  
  backup_retention_period = var.backup_retention_period
  backup_window          = var.backup_window
  maintenance_window     = var.maintenance_window
  
  tags = local.common_tags
}

# ElastiCache Module
module "elasticache" {
  source = "./modules/elasticache"
  
  name_prefix        = local.name_prefix
  vpc_id            = module.vpc.vpc_id
  private_subnet_ids = module.vpc.private_subnet_ids
  security_group_ids = [module.security_groups.redis_security_group_id]
  
  node_type         = var.redis_node_type
  num_cache_nodes   = var.redis_num_nodes
  parameter_group   = var.redis_parameter_group
  
  tags = local.common_tags
}

# EFS Module
module "efs" {
  source = "./modules/efs"
  
  name_prefix        = local.name_prefix
  vpc_id            = module.vpc.vpc_id
  private_subnet_ids = module.vpc.private_subnet_ids
  security_group_ids = [module.security_groups.efs_security_group_id]
  
  tags = local.common_tags
}

# ECR Module
module "ecr" {
  source = "./modules/ecr"
  
  name_prefix = local.name_prefix
  
  repositories = [
    "campuspq-backend",
    "campuspq-worker", 
    "campuspq-frontend"
  ]
  
  tags = local.common_tags
}

# ECS Module
module "ecs" {
  source = "./modules/ecs"
  
  name_prefix           = local.name_prefix
  vpc_id               = module.vpc.vpc_id
  public_subnet_ids    = module.vpc.public_subnet_ids
  private_subnet_ids   = module.vpc.private_subnet_ids
  
  # Security groups
  alb_security_group_id     = module.security_groups.alb_security_group_id
  ecs_security_group_id     = module.security_groups.ecs_security_group_id
  
  # Database and cache
  database_url = "postgresql://${var.db_username}:${var.db_password}@${module.rds.db_endpoint}:5432/${var.db_name}"
  redis_url    = "redis://${module.elasticache.redis_endpoint}:6379/0"
  
  # EFS
  efs_file_system_id = module.efs.file_system_id
  efs_access_point_id = module.efs.access_point_id
  
  # ECR
  ecr_repository_urls = module.ecr.repository_urls
  
  # SSL Certificate ARN (you'll need to create this manually or with ACM)
  ssl_certificate_arn = var.ssl_certificate_arn
  
  # Domain name
  domain_name = var.domain_name
  
  tags = local.common_tags
}

# CloudWatch Module
module "cloudwatch" {
  source = "./modules/cloudwatch"
  
  name_prefix = local.name_prefix
  
  # ECS cluster name for monitoring
  ecs_cluster_name = module.ecs.cluster_name
  
  # SNS topic for alerts
  alert_email = var.alert_email
  
  tags = local.common_tags
}

# Parameter Store for secrets
module "parameter_store" {
  source = "./modules/parameter_store"
  
  name_prefix = local.name_prefix
  
  # Database connection
  database_url = "postgresql://${var.db_username}:${var.db_password}@${module.rds.db_endpoint}:5432/${var.db_name}"
  redis_url    = "redis://${module.elasticache.redis_endpoint}:6379/0"
  
  # Application secrets
  secret_key           = var.secret_key
  openai_api_key      = var.openai_api_key
  gemini_api_key      = var.gemini_api_key
  cloudinary_url      = var.cloudinary_url
  cloudinary_api_key  = var.cloudinary_api_key
  cloudinary_api_secret = var.cloudinary_api_secret
  smtp_user           = var.smtp_user
  smtp_password       = var.smtp_password
  emails_from_email   = var.emails_from_email
  frontend_url        = var.frontend_url
  
  # Qdrant URL (will be internal ECS service)
  qdrant_url = "http://campuspq-qdrant.${local.name_prefix}.local:6333"

  tags = local.common_tags
}

# Route53 for DNS (optional)
module "route53" {
  source = "./modules/route53"
  count  = var.create_route53_zone ? 1 : 0

  domain_name = var.domain_name
  alb_dns_name = module.ecs.alb_dns_name
  alb_zone_id  = module.ecs.alb_zone_id

  tags = local.common_tags
}
