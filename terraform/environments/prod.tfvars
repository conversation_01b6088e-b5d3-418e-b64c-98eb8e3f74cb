# Production Environment Configuration

# General
aws_region   = "us-east-1"
environment  = "prod"
project_name = "campuspq"

# Networking
vpc_cidr = "10.0.0.0/16"

# Database Configuration
db_instance_class       = "db.t3.small"
db_allocated_storage    = 100
db_name                = "campuspq"
db_username            = "campuspq_user"
backup_retention_period = 7
backup_window          = "03:00-04:00"
maintenance_window     = "sun:04:00-sun:05:00"

# Redis Configuration
redis_node_type       = "cache.t3.micro"
redis_num_nodes       = 1
redis_parameter_group = "default.redis7"

# Domain Configuration
domain_name          = "campuspq.com"
create_route53_zone  = false  # Set to true if you want Terraform to manage DNS

# SSL Certificate (you'll need to create this in ACM first)
ssl_certificate_arn = ""  # Add your ACM certificate ARN here

# Application Configuration
emails_from_email = "<EMAIL>"
frontend_url     = "https://campuspq.com"

# Monitoring
alert_email = "<EMAIL>"

# Note: Sensitive variables like passwords and API keys are passed via environment variables
# in the GitHub Actions workflow for security
