output "log_groups" {
  description = "CloudWatch log groups created"
  value = {
    backend_api    = aws_cloudwatch_log_group.backend_api.name
    celery_worker  = aws_cloudwatch_log_group.celery_worker.name
    frontend       = aws_cloudwatch_log_group.frontend.name
    qdrant         = aws_cloudwatch_log_group.qdrant.name
  }
}

output "sns_topic_arn" {
  description = "SNS topic ARN for alerts"
  value       = aws_sns_topic.alerts.arn
}

output "dashboard_url" {
  description = "CloudWatch dashboard URL"
  value       = "https://${data.aws_region.current.name}.console.aws.amazon.com/cloudwatch/home?region=${data.aws_region.current.name}#dashboards:name=${aws_cloudwatch_dashboard.main.dashboard_name}"
}
