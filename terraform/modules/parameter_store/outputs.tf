output "parameter_arns" {
  description = "ARNs of all created parameters"
  value = {
    database_url         = aws_ssm_parameter.database_url.arn
    redis_url           = aws_ssm_parameter.redis_url.arn
    qdrant_url          = aws_ssm_parameter.qdrant_url.arn
    secret_key          = aws_ssm_parameter.secret_key.arn
    openai_api_key      = length(aws_ssm_parameter.openai_api_key) > 0 ? aws_ssm_parameter.openai_api_key[0].arn : null
    gemini_api_key      = length(aws_ssm_parameter.gemini_api_key) > 0 ? aws_ssm_parameter.gemini_api_key[0].arn : null
    cloudinary_url      = length(aws_ssm_parameter.cloudinary_url) > 0 ? aws_ssm_parameter.cloudinary_url[0].arn : null
    cloudinary_api_key  = length(aws_ssm_parameter.cloudinary_api_key) > 0 ? aws_ssm_parameter.cloudinary_api_key[0].arn : null
    cloudinary_api_secret = length(aws_ssm_parameter.cloudinary_api_secret) > 0 ? aws_ssm_parameter.cloudinary_api_secret[0].arn : null
    smtp_user           = length(aws_ssm_parameter.smtp_user) > 0 ? aws_ssm_parameter.smtp_user[0].arn : null
    smtp_password       = length(aws_ssm_parameter.smtp_password) > 0 ? aws_ssm_parameter.smtp_password[0].arn : null
    emails_from_email   = aws_ssm_parameter.emails_from_email.arn
    frontend_url        = aws_ssm_parameter.frontend_url.arn
  }
}
