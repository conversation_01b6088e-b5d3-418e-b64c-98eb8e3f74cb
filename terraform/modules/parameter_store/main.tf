# Database Configuration
resource "aws_ssm_parameter" "database_url" {
  name  = "/${var.name_prefix}/database-url"
  type  = "SecureString"
  value = var.database_url

  tags = merge(var.tags, {
    Name = "${var.name_prefix}-database-url"
  })
}

resource "aws_ssm_parameter" "redis_url" {
  name  = "/${var.name_prefix}/redis-url"
  type  = "SecureString"
  value = var.redis_url

  tags = merge(var.tags, {
    Name = "${var.name_prefix}-redis-url"
  })
}

resource "aws_ssm_parameter" "qdrant_url" {
  name  = "/${var.name_prefix}/qdrant-url"
  type  = "String"
  value = var.qdrant_url

  tags = merge(var.tags, {
    Name = "${var.name_prefix}-qdrant-url"
  })
}

# Application Secrets
resource "aws_ssm_parameter" "secret_key" {
  name  = "/${var.name_prefix}/secret-key"
  type  = "SecureString"
  value = var.secret_key

  tags = merge(var.tags, {
    Name = "${var.name_prefix}-secret-key"
  })
}

resource "aws_ssm_parameter" "openai_api_key" {
  count = var.openai_api_key != "" ? 1 : 0
  name  = "/${var.name_prefix}/openai-api-key"
  type  = "SecureString"
  value = var.openai_api_key

  tags = merge(var.tags, {
    Name = "${var.name_prefix}-openai-api-key"
  })
}

resource "aws_ssm_parameter" "gemini_api_key" {
  count = var.gemini_api_key != "" ? 1 : 0
  name  = "/${var.name_prefix}/gemini-api-key"
  type  = "SecureString"
  value = var.gemini_api_key

  tags = merge(var.tags, {
    Name = "${var.name_prefix}-gemini-api-key"
  })
}

# Cloudinary Configuration
resource "aws_ssm_parameter" "cloudinary_url" {
  count = var.cloudinary_url != "" ? 1 : 0
  name  = "/${var.name_prefix}/cloudinary-url"
  type  = "SecureString"
  value = var.cloudinary_url

  tags = merge(var.tags, {
    Name = "${var.name_prefix}-cloudinary-url"
  })
}

resource "aws_ssm_parameter" "cloudinary_api_key" {
  count = var.cloudinary_api_key != "" ? 1 : 0
  name  = "/${var.name_prefix}/cloudinary-api-key"
  type  = "SecureString"
  value = var.cloudinary_api_key

  tags = merge(var.tags, {
    Name = "${var.name_prefix}-cloudinary-api-key"
  })
}

resource "aws_ssm_parameter" "cloudinary_api_secret" {
  count = var.cloudinary_api_secret != "" ? 1 : 0
  name  = "/${var.name_prefix}/cloudinary-api-secret"
  type  = "SecureString"
  value = var.cloudinary_api_secret

  tags = merge(var.tags, {
    Name = "${var.name_prefix}-cloudinary-api-secret"
  })
}

# Email Configuration
resource "aws_ssm_parameter" "smtp_user" {
  count = var.smtp_user != "" ? 1 : 0
  name  = "/${var.name_prefix}/smtp-user"
  type  = "SecureString"
  value = var.smtp_user

  tags = merge(var.tags, {
    Name = "${var.name_prefix}-smtp-user"
  })
}

resource "aws_ssm_parameter" "smtp_password" {
  count = var.smtp_password != "" ? 1 : 0
  name  = "/${var.name_prefix}/smtp-password"
  type  = "SecureString"
  value = var.smtp_password

  tags = merge(var.tags, {
    Name = "${var.name_prefix}-smtp-password"
  })
}

resource "aws_ssm_parameter" "emails_from_email" {
  name  = "/${var.name_prefix}/emails-from-email"
  type  = "String"
  value = var.emails_from_email

  tags = merge(var.tags, {
    Name = "${var.name_prefix}-emails-from-email"
  })
}

resource "aws_ssm_parameter" "frontend_url" {
  name  = "/${var.name_prefix}/frontend-url"
  type  = "String"
  value = var.frontend_url

  tags = merge(var.tags, {
    Name = "${var.name_prefix}-frontend-url"
  })
}
