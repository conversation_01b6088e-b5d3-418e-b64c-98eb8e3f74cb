# CampusPQ EC2 Production Environment Configuration
# Copy this file to .env and fill in your actual values

# Database Configuration (Required)
# Use your Supabase database URL or any PostgreSQL database
DATABASE_URL=postgresql://username:password@host:port/database_name

# Redis Configuration (Automatically configured for Docker Compose)
REDIS_URL=redis://redis:6379/0

# Security (Required)
# Generate a strong secret key for JWT tokens
SECRET_KEY=your-super-secret-key-change-this-in-production

# AI Provider Configuration (Required for AI features)
# Get your Gemini API key from Google AI Studio
GEMINI_API_KEY=your-gemini-api-key-here

# Optional: OpenAI API key (if you want to use OpenAI instead of Gemini)
OPENAI_API_KEY=your-openai-api-key-here

# AI Provider Selection (gemini or openai)
AI_PROVIDER=gemini

# Application Configuration
ENVIRONMENT=production
DEBUG=false
PROJECT_NAME=CampusPQ
API_V1_PREFIX=/api/v1

# Frontend Configuration (Required)
# URL of your frontend application
FRONTEND_URL=https://your-frontend-domain.com

# Payment Provider Configuration (Optional)
# Paystack (Nigerian payment provider)
PAYSTACK_SECRET_KEY=sk_live_your_paystack_secret_key
PAYSTACK_PUBLIC_KEY=pk_live_your_paystack_public_key

# Flutterwave (African payment provider)
FLUTTERWAVE_SECRET_KEY=FLWSECK_TEST-your_flutterwave_secret_key
FLUTTERWAVE_PUBLIC_KEY=FLWPUBK_TEST-your_flutterwave_public_key
FLUTTERWAVE_ENCRYPTION_KEY=FLWSECK_TEST-your_flutterwave_encryption_key

# Email Configuration (Optional)
# For sending verification emails and notifications
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-app-password
EMAILS_FROM_EMAIL=<EMAIL>
EMAILS_FROM_NAME=CampusPQ

# File Upload Configuration
MAX_UPLOAD_SIZE=31457280  # 30MB in bytes
UPLOAD_FOLDER=uploads

# Subscription Limits
NUM_FREE_QUESTIONS=20
NUM_FREE_FLASHCARDS=10

# CORS Configuration (for production, specify your frontend domain)
BACKEND_CORS_ORIGINS=["https://your-frontend-domain.com", "http://localhost:3000"]

# Logging Configuration
LOG_LEVEL=INFO

# Health Check Configuration
HEALTH_CHECK_INTERVAL=30
HEALTH_CHECK_TIMEOUT=10

# Rate Limiting
RATE_LIMIT_PER_MINUTE=60
UPLOAD_RATE_LIMIT_PER_MINUTE=10

# Session Configuration
SESSION_TIMEOUT_MINUTES=30
REFRESH_TOKEN_EXPIRE_DAYS=7

# Cache Configuration
CACHE_TTL_HOURS=24
ENABLE_CACHING=true

# Feature Flags
ENABLE_REGISTRATION=true
ENABLE_EMAIL_VERIFICATION=true
ENABLE_PASSWORD_RESET=true
ENABLE_FILE_UPLOAD=true
ENABLE_AI_FEATURES=true

# Monitoring and Analytics (Optional)
# Add your monitoring service keys here
SENTRY_DSN=your-sentry-dsn-here
ANALYTICS_KEY=your-analytics-key-here
