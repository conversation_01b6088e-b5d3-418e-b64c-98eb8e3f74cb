# Railway.app deployment configuration
[build]
builder = "DOCKERFILE"
dockerfilePath = "backend/Dockerfile.railway"

[deploy]
healthcheckPath = "/api/v1/health"
healthcheckTimeout = 300
restartPolicyType = "ON_FAILURE"
restartPolicyMaxRetries = 5
startCommand = "uvicorn app.main:app --host 0.0.0.0 --port $PORT --workers 1"

# Environment variables for Railway
[env]
PORT = { default = "8000" }
ENVIRONMENT = { default = "production" }
DEBUG = { default = "false" }
API_V1_PREFIX = { default = "/api/v1" }
PROJECT_NAME = { default = "CampusPQ" }

# Database (your Supabase instance)
DATABASE_URL = { default = "postgresql://postgres:GKNvV4kL28Li!<EMAIL>:5432/postgres" }

# Redis Cloud (free tier)
REDIS_URL = "${{REDIS_CLOUD_URL}}"

# Vector Database (deploy separately or use hosted)
QDRANT_URL = "${{QDRANT_URL}}"
QDRANT_COLLECTION = { default = "campuspq_embeddings" }

# Application secrets
SECRET_KEY = "${{SECRET_KEY}}"
OPENAI_API_KEY = "${{OPENAI_API_KEY}}"
GEMINI_API_KEY = "${{GEMINI_API_KEY}}"
AI_PROVIDER = { default = "gemini" }

# File storage (Cloudinary free tier)
CLOUDINARY_URL = "${{CLOUDINARY_URL}}"
CLOUDINARY_API_KEY = "${{CLOUDINARY_API_KEY}}"
CLOUDINARY_API_SECRET = "${{CLOUDINARY_API_SECRET}}"

# Email (free SMTP)
SMTP_USER = "${{SMTP_USER}}"
SMTP_PASSWORD = "${{SMTP_PASSWORD}}"
EMAILS_FROM_EMAIL = { default = "<EMAIL>" }

# Frontend URL (Vercel)
FRONTEND_URL = { default = "https://campuspq.vercel.app" }

# CORS settings for free hosting
BACKEND_CORS_ORIGINS = { default = '["https://campuspq.vercel.app", "http://localhost:3000", "http://localhost:5173"]' }
