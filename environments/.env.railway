# Railway Production Environment Configuration
# This file contains non-sensitive environment variables for Railway deployment
# Sensitive variables should be set in Railway dashboard

# Environment
ENVIRONMENT=production
DEBUG=false

# API Configuration
API_V1_PREFIX=/api/v1
PROJECT_NAME=CampusPQ

# Security
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=30

# AI Configuration
AI_PROVIDER=gemini
QDRANT_COLLECTION=campuspq_embeddings

# Performance Settings (optimized for Railway free tier)
CACHE_ENABLED=true
CACHE_TTL_HOURS=24
MAX_PARALLEL_CHUNKS=3
BATCH_SIZE=2

# Enhanced PDF Processing Settings
OCR_CONFIDENCE_THRESHOLD=60.0
OCR_LOW_CONFIDENCE_THRESHOLD=40.0
DEFAULT_CHUNK_SIZE=1200
CHUNK_OVERLAP=200
ENABLE_HANDWRITING_OCR=true
ENABLE_ENHANCED_PIPELINE=true

# Email Settings
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
EMAILS_FROM_NAME=CampusPQ
EMAIL_VERIFICATION_TOKEN_EXPIRE_HOURS=24
PASSWORD_RESET_TOKEN_EXPIRE_HOURS=1

# CORS Settings (Railway deployment)
BACKEND_CORS_ORIGINS=["*"]

# Railway-specific settings
PORT=8000
HEALTHCHECK_PATH=/api/v1/health

# Note: Set these sensitive variables in Railway dashboard:
# - DATABASE_URL (your Supabase URL)
# - SECRET_KEY (generate with: openssl rand -base64 32)
# - REDIS_URL (from Redis Cloud)
# - QDRANT_URL (from Qdrant Cloud)
# - OPENAI_API_KEY (optional)
# - GEMINI_API_KEY (optional)
# - CLOUDINARY_URL (optional)
# - CLOUDINARY_API_KEY (optional)
# - CLOUDINARY_API_SECRET (optional)
# - SMTP_USER (optional)
# - SMTP_PASSWORD (optional)
# - EMAILS_FROM_EMAIL (optional)
# - FRONTEND_URL (your frontend URL)
