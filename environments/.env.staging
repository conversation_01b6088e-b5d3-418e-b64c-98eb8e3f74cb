# Staging Environment Configuration
# This file contains non-sensitive environment variables for staging
# Sensitive variables are managed through AWS Parameter Store

# Environment
ENVIRONMENT=staging
DEBUG=true

# API Configuration
API_V1_PREFIX=/api/v1
PROJECT_NAME=CampusPQ-Staging

# Security
ALGORITHM=HS256
ACCESS_TOKEN_EXPIRE_MINUTES=60

# AI Configuration
AI_PROVIDER=gemini
QDRANT_COLLECTION=campuspq_staging_embeddings

# Performance Settings
CACHE_ENABLED=true
CACHE_TTL_HOURS=12
MAX_PARALLEL_CHUNKS=3
BATCH_SIZE=2

# Enhanced PDF Processing Settings
OCR_CONFIDENCE_THRESHOLD=60.0
OCR_LOW_CONFIDENCE_THRESHOLD=40.0
DEFAULT_CHUNK_SIZE=1200
CHUNK_OVERLAP=200
ENABLE_HANDWRITING_OCR=true
ENABLE_ENHANCED_PIPELINE=true

# Email Settings
SMTP_HOST=smtp.gmail.com
SMTP_PORT=587
EMAILS_FROM_NAME=CampusPQ-Staging
EMAIL_VERIFICATION_TOKEN_EXPIRE_HOURS=24
PASSWORD_RESET_TOKEN_EXPIRE_HOURS=1

# CORS Settings (more permissive for staging)
BACKEND_CORS_ORIGINS=["https://staging.campuspq.com", "http://localhost:3000", "http://localhost:5173"]

# Note: Sensitive variables like DATABASE_URL, SECRET_KEY, API keys, etc.
# are injected from AWS Parameter Store via ECS task definitions
