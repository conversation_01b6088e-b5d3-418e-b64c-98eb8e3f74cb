#!/bin/bash

# Test script for CampusPQ EC2 deployment
# This script tests various endpoints and functionality after deployment

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
API_BASE_URL="${API_BASE_URL:-http://localhost:8000}"
TIMEOUT=10

# Function to print colored output
print_status() {
    echo -e "${GREEN}[✓]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[!]${NC} $1"
}

print_error() {
    echo -e "${RED}[✗]${NC} $1"
}

print_info() {
    echo -e "${BLUE}[i]${NC} $1"
}

# Function to test HTTP endpoint
test_endpoint() {
    local endpoint="$1"
    local expected_status="${2:-200}"
    local description="$3"
    
    print_info "Testing: $description"
    print_info "URL: $API_BASE_URL$endpoint"
    
    local response=$(curl -s -w "%{http_code}" -o /tmp/response.json --max-time $TIMEOUT "$API_BASE_URL$endpoint" || echo "000")
    
    if [ "$response" = "$expected_status" ]; then
        print_status "✅ $description - Status: $response"
        return 0
    else
        print_error "❌ $description - Expected: $expected_status, Got: $response"
        if [ -f /tmp/response.json ]; then
            echo "Response body:"
            cat /tmp/response.json
            echo ""
        fi
        return 1
    fi
}

# Function to test JSON response
test_json_endpoint() {
    local endpoint="$1"
    local description="$2"
    
    print_info "Testing JSON: $description"
    print_info "URL: $API_BASE_URL$endpoint"
    
    local response=$(curl -s --max-time $TIMEOUT "$API_BASE_URL$endpoint" || echo "")
    
    if echo "$response" | jq . >/dev/null 2>&1; then
        print_status "✅ $description - Valid JSON response"
        echo "Response: $(echo "$response" | jq -c .)"
        return 0
    else
        print_error "❌ $description - Invalid JSON response"
        echo "Response: $response"
        return 1
    fi
}

# Main test function
run_tests() {
    local failed_tests=0
    
    print_info "🚀 Starting CampusPQ API Tests"
    print_info "API Base URL: $API_BASE_URL"
    print_info "Timeout: ${TIMEOUT}s"
    echo ""
    
    # Test 1: Basic connectivity
    print_info "=== Basic Connectivity Tests ==="
    test_endpoint "/" 301 "Root endpoint redirect" || ((failed_tests++))
    
    # Test 2: Health checks
    print_info "=== Health Check Tests ==="
    test_json_endpoint "/api/v1/health" "Main health check" || ((failed_tests++))
    test_json_endpoint "/health" "Simple health check" || ((failed_tests++))
    
    # Test 3: API Documentation
    print_info "=== API Documentation Tests ==="
    test_endpoint "/api/v1/docs" 200 "Swagger UI documentation" || ((failed_tests++))
    test_endpoint "/api/v1/redoc" 200 "ReDoc documentation" || ((failed_tests++))
    test_endpoint "/api/v1/openapi.json" 200 "OpenAPI schema" || ((failed_tests++))
    
    # Test 4: Authentication endpoints
    print_info "=== Authentication Tests ==="
    test_endpoint "/api/v1/auth/register" 422 "Register endpoint (no data)" || ((failed_tests++))
    test_endpoint "/api/v1/auth/login" 422 "Login endpoint (no data)" || ((failed_tests++))
    
    # Test 5: Public endpoints
    print_info "=== Public Endpoint Tests ==="
    test_endpoint "/api/v1/departments" 200 "Departments list" || ((failed_tests++))
    
    # Test 6: Protected endpoints (should return 401)
    print_info "=== Protected Endpoint Tests ==="
    test_endpoint "/api/v1/users/me" 401 "User profile (unauthorized)" || ((failed_tests++))
    test_endpoint "/api/v1/courses" 401 "Courses list (unauthorized)" || ((failed_tests++))
    
    # Test 7: File upload endpoint
    print_info "=== File Upload Tests ==="
    test_endpoint "/api/v1/upload/pdf" 401 "PDF upload (unauthorized)" || ((failed_tests++))
    
    echo ""
    print_info "=== Test Summary ==="
    if [ $failed_tests -eq 0 ]; then
        print_status "🎉 All tests passed! Your API is working correctly."
        return 0
    else
        print_error "❌ $failed_tests test(s) failed. Please check the issues above."
        return 1
    fi
}

# Function to test Docker containers
test_docker_containers() {
    print_info "=== Docker Container Tests ==="
    
    if command -v docker >/dev/null 2>&1; then
        print_info "Checking Docker containers..."
        docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}"
        
        # Check if API container is running
        if docker ps | grep -q "campuspq-api"; then
            print_status "✅ CampusPQ API container is running"
        else
            print_error "❌ CampusPQ API container is not running"
        fi
        
        # Check if Redis container is running
        if docker ps | grep -q "campuspq-redis"; then
            print_status "✅ Redis container is running"
        else
            print_warning "⚠️ Redis container is not running"
        fi
        
        # Check if Nginx container is running
        if docker ps | grep -q "campuspq-nginx"; then
            print_status "✅ Nginx container is running"
        else
            print_warning "⚠️ Nginx container is not running"
        fi
    else
        print_warning "Docker not available for container testing"
    fi
}

# Function to test system resources
test_system_resources() {
    print_info "=== System Resource Tests ==="
    
    # Check disk space
    local disk_usage=$(df -h / | awk 'NR==2 {print $5}' | sed 's/%//')
    if [ "$disk_usage" -lt 80 ]; then
        print_status "✅ Disk usage: ${disk_usage}% (healthy)"
    else
        print_warning "⚠️ Disk usage: ${disk_usage}% (consider cleanup)"
    fi
    
    # Check memory usage
    if command -v free >/dev/null 2>&1; then
        local mem_usage=$(free | awk 'NR==2{printf "%.0f", $3*100/$2}')
        if [ "$mem_usage" -lt 80 ]; then
            print_status "✅ Memory usage: ${mem_usage}% (healthy)"
        else
            print_warning "⚠️ Memory usage: ${mem_usage}% (high)"
        fi
    fi
}

# Function to show useful information
show_info() {
    echo ""
    print_info "=== Deployment Information ==="
    print_info "API Documentation: $API_BASE_URL/api/v1/docs"
    print_info "Health Check: $API_BASE_URL/api/v1/health"
    print_info "Alternative Health Check: $API_BASE_URL/health"
    echo ""
    print_info "=== Useful Commands ==="
    echo "  View logs: docker-compose logs -f"
    echo "  Restart services: docker-compose restart"
    echo "  Check status: docker-compose ps"
    echo "  Update deployment: git pull && docker-compose up -d --build"
}

# Main execution
main() {
    # Parse command line arguments
    while [[ $# -gt 0 ]]; do
        case $1 in
            --url)
                API_BASE_URL="$2"
                shift 2
                ;;
            --timeout)
                TIMEOUT="$2"
                shift 2
                ;;
            --help)
                echo "Usage: $0 [--url API_URL] [--timeout SECONDS]"
                echo "  --url: API base URL (default: http://localhost:8000)"
                echo "  --timeout: Request timeout in seconds (default: 10)"
                exit 0
                ;;
            *)
                print_error "Unknown option: $1"
                exit 1
                ;;
        esac
    done
    
    # Run all tests
    run_tests
    local api_tests_result=$?
    
    test_docker_containers
    test_system_resources
    show_info
    
    # Exit with appropriate code
    exit $api_tests_result
}

# Check if jq is available
if ! command -v jq >/dev/null 2>&1; then
    print_warning "jq is not installed. JSON response validation will be limited."
    print_info "Install jq with: sudo apt-get install jq"
fi

# Run main function
main "$@"
