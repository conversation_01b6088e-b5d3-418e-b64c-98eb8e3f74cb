#!/bin/bash

# EC2 Deployment Script for CampusPQ Backend
# This script helps you deploy your application manually if needed

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
NC='\033[0m' # No Color

# Configuration
EC2_HOST="${EC2_HOST:-}"
EC2_USER="${EC2_USER:-ubuntu}"
DEPLOYMENT_DIR="~/campuspq-deployment"

# Function to print colored output
print_status() {
    echo -e "${GREEN}[INFO]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

print_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

# Check if required variables are set
check_requirements() {
    if [ -z "$EC2_HOST" ]; then
        print_error "EC2_HOST environment variable is not set"
        echo "Usage: EC2_HOST=your-ec2-ip ./deploy-ec2.sh"
        exit 1
    fi
    
    if [ ! -f "docker-compose.ec2.yml" ]; then
        print_error "docker-compose.ec2.yml not found"
        exit 1
    fi
    
    if [ ! -f "nginx.conf" ]; then
        print_error "nginx.conf not found"
        exit 1
    fi
    
    if [ ! -d "backend" ]; then
        print_error "backend directory not found"
        exit 1
    fi
}

# Test SSH connection
test_ssh() {
    print_status "Testing SSH connection to $EC2_HOST..."
    if ssh -o ConnectTimeout=10 -o BatchMode=yes $EC2_USER@$EC2_HOST exit 2>/dev/null; then
        print_status "SSH connection successful"
    else
        print_error "Cannot connect to $EC2_HOST via SSH"
        print_warning "Make sure:"
        echo "  1. Your EC2 instance is running"
        echo "  2. Security group allows SSH (port 22) from your IP"
        echo "  3. Your SSH key is properly configured"
        exit 1
    fi
}

# Deploy application
deploy() {
    print_status "Starting deployment to $EC2_HOST..."
    
    # Create deployment directory
    print_status "Creating deployment directory..."
    ssh $EC2_USER@$EC2_HOST "mkdir -p $DEPLOYMENT_DIR"
    
    # Copy files
    print_status "Copying application files..."
    scp -r backend/ $EC2_USER@$EC2_HOST:$DEPLOYMENT_DIR/
    scp docker-compose.ec2.yml $EC2_USER@$EC2_HOST:$DEPLOYMENT_DIR/docker-compose.yml
    scp nginx.conf $EC2_USER@$EC2_HOST:$DEPLOYMENT_DIR/
    
    # Create environment file (you'll need to set these variables)
    print_status "Creating environment configuration..."
    ssh $EC2_USER@$EC2_HOST "
        cd $DEPLOYMENT_DIR
        cat > .env << 'EOF'
DATABASE_URL=${DATABASE_URL:-********************************/db}
REDIS_URL=redis://redis:6379/0
SECRET_KEY=${SECRET_KEY:-change-this-secret-key}
GEMINI_API_KEY=${GEMINI_API_KEY:-your-gemini-key}
OPENAI_API_KEY=${OPENAI_API_KEY:-}
AI_PROVIDER=gemini
ENVIRONMENT=production
DEBUG=false
FRONTEND_URL=${FRONTEND_URL:-http://localhost:3000}
PAYSTACK_SECRET_KEY=${PAYSTACK_SECRET_KEY:-}
PAYSTACK_PUBLIC_KEY=${PAYSTACK_PUBLIC_KEY:-}
FLUTTERWAVE_SECRET_KEY=${FLUTTERWAVE_SECRET_KEY:-}
FLUTTERWAVE_PUBLIC_KEY=${FLUTTERWAVE_PUBLIC_KEY:-}
FLUTTERWAVE_ENCRYPTION_KEY=${FLUTTERWAVE_ENCRYPTION_KEY:-}
SMTP_USER=${SMTP_USER:-}
SMTP_PASSWORD=${SMTP_PASSWORD:-}
EMAILS_FROM_EMAIL=${EMAILS_FROM_EMAIL:-}
EOF
    "
    
    # Deploy with Docker Compose
    print_status "Deploying application with Docker Compose..."
    ssh $EC2_USER@$EC2_HOST "
        cd $DEPLOYMENT_DIR
        
        # Stop existing containers
        docker-compose down || true
        
        # Remove old images to save space
        docker image prune -f
        
        # Build and start new containers
        docker-compose up -d --build
        
        # Wait for services to start
        echo 'Waiting for services to start...'
        sleep 30
        
        # Check if services are running
        docker-compose ps
    "
    
    # Test deployment
    print_status "Testing deployment..."
    if ssh $EC2_USER@$EC2_HOST "curl -f http://localhost:8000/api/v1/health" >/dev/null 2>&1; then
        print_status "✅ Deployment successful!"
        print_status "Your API is available at: http://$EC2_HOST:8000/api/v1/docs"
    else
        print_error "❌ Deployment failed - API health check failed"
        print_warning "Check logs with: ssh $EC2_USER@$EC2_HOST 'cd $DEPLOYMENT_DIR && docker-compose logs'"
        exit 1
    fi
}

# Cleanup function
cleanup() {
    print_status "Cleaning up old Docker images..."
    ssh $EC2_USER@$EC2_HOST "
        docker image prune -a -f --filter 'until=24h'
        docker volume prune -f
    "
}

# Main execution
main() {
    print_status "CampusPQ EC2 Deployment Script"
    print_status "==============================="
    
    check_requirements
    test_ssh
    deploy
    cleanup
    
    print_status "🎉 Deployment completed successfully!"
    echo ""
    print_status "Next steps:"
    echo "  1. Visit http://$EC2_HOST:8000/api/v1/docs to see your API documentation"
    echo "  2. Test your API endpoints"
    echo "  3. Configure your frontend to use http://$EC2_HOST:8000 as the API base URL"
    echo "  4. Consider setting up a domain name and SSL certificate"
}

# Run main function
main "$@"
