#!/bin/bash

# Debug script for CampusPQ EC2 deployment issues
# Run this on your EC2 instance to diagnose problems

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${GREEN}[✓]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[!]${NC} $1"
}

print_error() {
    echo -e "${RED}[✗]${NC} $1"
}

print_info() {
    echo -e "${BLUE}[i]${NC} $1"
}

print_header() {
    echo ""
    echo -e "${BLUE}=== $1 ===${NC}"
}

# Check if we're in the right directory
check_directory() {
    if [ ! -f "docker-compose.yml" ]; then
        print_error "docker-compose.yml not found. Please run this script from ~/campuspq-deployment"
        exit 1
    fi
}

# Check container status
check_containers() {
    print_header "Container Status"
    docker-compose ps
    echo ""
    
    # Check if API container is running
    if docker-compose ps | grep -q "campuspq-api.*Up"; then
        print_status "API container is running"
    else
        print_error "API container is not running properly"
        
        print_info "Checking API container logs..."
        echo ""
        docker-compose logs --tail=50 api
    fi
}

# Check logs for all services
check_logs() {
    print_header "Service Logs"
    
    print_info "API Logs (last 20 lines):"
    docker-compose logs --tail=20 api || echo "No API logs available"
    echo ""
    
    print_info "Redis Logs (last 10 lines):"
    docker-compose logs --tail=10 redis || echo "No Redis logs available"
    echo ""
    
    print_info "Nginx Logs (last 10 lines):"
    docker-compose logs --tail=10 nginx || echo "No Nginx logs available"
    echo ""
}

# Check environment variables
check_environment() {
    print_header "Environment Configuration"
    
    if [ -f ".env" ]; then
        print_status ".env file exists"
        print_info "Environment variables (sensitive values hidden):"
        cat .env | sed 's/=.*/=***HIDDEN***/' | head -10
        echo "... (showing first 10 variables)"
    else
        print_error ".env file not found"
    fi
}

# Check network connectivity
check_network() {
    print_header "Network Connectivity"
    
    # Check if containers can communicate
    print_info "Testing Redis connectivity from API container..."
    if docker-compose exec -T api ping -c 1 redis >/dev/null 2>&1; then
        print_status "API can reach Redis"
    else
        print_warning "API cannot reach Redis"
    fi
    
    # Check if API is responding internally
    print_info "Testing API health check internally..."
    if docker-compose exec -T api curl -f http://localhost:8000/api/v1/health >/dev/null 2>&1; then
        print_status "API health check passes internally"
    else
        print_error "API health check fails internally"
    fi
}

# Check system resources
check_resources() {
    print_header "System Resources"
    
    # Check disk space
    print_info "Disk usage:"
    df -h /
    echo ""
    
    # Check memory
    print_info "Memory usage:"
    free -h
    echo ""
    
    # Check Docker resources
    print_info "Docker system info:"
    docker system df
}

# Check database connectivity
check_database() {
    print_header "Database Connectivity"
    
    print_info "Testing database connection from API container..."
    if docker-compose exec -T api python -c "
import os
import psycopg2
try:
    conn = psycopg2.connect(os.environ['DATABASE_URL'])
    print('✓ Database connection successful')
    conn.close()
except Exception as e:
    print(f'✗ Database connection failed: {e}')
" 2>/dev/null; then
        print_status "Database connection test completed"
    else
        print_error "Could not test database connection"
    fi
}

# Restart services
restart_services() {
    print_header "Restarting Services"
    
    print_info "Stopping all services..."
    docker-compose down
    
    print_info "Starting services again..."
    docker-compose up -d
    
    print_info "Waiting for services to start..."
    sleep 30
    
    print_info "New container status:"
    docker-compose ps
}

# Main diagnostic function
main() {
    print_info "🔍 CampusPQ Deployment Diagnostic Tool"
    print_info "======================================"
    
    check_directory
    check_containers
    check_environment
    check_logs
    check_network
    check_resources
    check_database
    
    echo ""
    print_info "🔧 Troubleshooting Suggestions:"
    echo "1. Check API logs above for specific error messages"
    echo "2. Verify all environment variables are set correctly"
    echo "3. Ensure database URL is accessible from EC2"
    echo "4. Check if ports 8000, 80, 443 are open in security group"
    echo "5. Verify EC2 instance has enough memory (API needs ~512MB)"
    echo ""
    
    read -p "Would you like to restart all services? (y/N): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        restart_services
    fi
    
    print_info "Diagnostic complete. Check the output above for issues."
}

# Run main function
main "$@"
