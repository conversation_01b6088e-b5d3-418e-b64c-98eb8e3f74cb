version: '3.8'

networks:
  campuspq-network:
    driver: bridge

services:
  # Frontend (React + Nginx)
  frontend:
    build:
      context: ./frontend
      dockerfile: Dockerfile
      target: production
    container_name: campuspq-frontend
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    networks:
      - campuspq-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:80/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # FastAPI Backend
  api:
    build:
      context: ./backend
      dockerfile: Dockerfile
      target: production
    container_name: campuspq-api
    restart: unless-stopped
    ports:
      - "8000:8000"
    volumes:
      - api_uploads:/app/uploads
      - api_logs:/app/logs
      - temp_files:/app/temp_files
    environment:
      - DATABASE_URL=${DATABASE_URL}
      - REDIS_URL=${REDIS_URL}
      - QDRANT_URL=${QDRANT_URL}
      - QDRANT_COLLECTION=${QDRANT_COLLECTION:-campuspq_embeddings}
      - SECRET_KEY=${SECRET_KEY}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - GEMINI_API_KEY=${GEMINI_API_KEY}
      - AI_PROVIDER=${AI_PROVIDER:-gemini}
      - CLOUDINARY_URL=${CLOUDINARY_URL}
      - CLOUDINARY_API_KEY=${CLOUDINARY_API_KEY}
      - CLOUDINARY_API_SECRET=${CLOUDINARY_API_SECRET}
      - SMTP_USER=${SMTP_USER}
      - SMTP_PASSWORD=${SMTP_PASSWORD}
      - EMAILS_FROM_EMAIL=${EMAILS_FROM_EMAIL}
      - FRONTEND_URL=${FRONTEND_URL}
      - ENVIRONMENT=production
      - DEBUG=false
    networks:
      - campuspq-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/api/v1/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Celery Worker
  worker:
    build:
      context: ./backend
      dockerfile: Dockerfile.worker
      target: production
    container_name: campuspq-worker
    restart: unless-stopped
    volumes:
      - api_uploads:/app/uploads
      - api_logs:/app/logs
      - temp_files:/app/temp_files
    environment:
      - DATABASE_URL=${DATABASE_URL}
      - REDIS_URL=${REDIS_URL}
      - QDRANT_URL=${QDRANT_URL}
      - QDRANT_COLLECTION=${QDRANT_COLLECTION:-campuspq_embeddings}
      - SECRET_KEY=${SECRET_KEY}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - GEMINI_API_KEY=${GEMINI_API_KEY}
      - AI_PROVIDER=${AI_PROVIDER:-gemini}
      - CLOUDINARY_URL=${CLOUDINARY_URL}
      - CLOUDINARY_API_KEY=${CLOUDINARY_API_KEY}
      - CLOUDINARY_API_SECRET=${CLOUDINARY_API_SECRET}
      - SMTP_USER=${SMTP_USER}
      - SMTP_PASSWORD=${SMTP_PASSWORD}
      - EMAILS_FROM_EMAIL=${EMAILS_FROM_EMAIL}
      - FRONTEND_URL=${FRONTEND_URL}
      - ENVIRONMENT=production
      - DEBUG=false
    networks:
      - campuspq-network
    healthcheck:
      test: ["CMD", "celery", "-A", "app.core.celery_app", "inspect", "ping"]
      interval: 60s
      timeout: 30s
      retries: 3
      start_period: 60s

  # Celery Beat (for scheduled tasks)
  beat:
    build:
      context: ./backend
      dockerfile: Dockerfile.worker
      target: production
    container_name: campuspq-beat
    restart: unless-stopped
    volumes:
      - api_uploads:/app/uploads
      - api_logs:/app/logs
      - temp_files:/app/temp_files
    environment:
      - DATABASE_URL=${DATABASE_URL}
      - REDIS_URL=${REDIS_URL}
      - QDRANT_URL=${QDRANT_URL}
      - QDRANT_COLLECTION=${QDRANT_COLLECTION:-campuspq_embeddings}
      - SECRET_KEY=${SECRET_KEY}
      - OPENAI_API_KEY=${OPENAI_API_KEY}
      - GEMINI_API_KEY=${GEMINI_API_KEY}
      - AI_PROVIDER=${AI_PROVIDER:-gemini}
      - CLOUDINARY_URL=${CLOUDINARY_URL}
      - CLOUDINARY_API_KEY=${CLOUDINARY_API_KEY}
      - CLOUDINARY_API_SECRET=${CLOUDINARY_API_SECRET}
      - SMTP_USER=${SMTP_USER}
      - SMTP_PASSWORD=${SMTP_PASSWORD}
      - EMAILS_FROM_EMAIL=${EMAILS_FROM_EMAIL}
      - FRONTEND_URL=${FRONTEND_URL}
      - ENVIRONMENT=production
      - DEBUG=false
    command: ["celery", "-A", "app.core.celery_app", "beat", "--loglevel=info"]
    networks:
      - campuspq-network

volumes:
  api_uploads:
    driver: local
  api_logs:
    driver: local
  temp_files:
    driver: local
