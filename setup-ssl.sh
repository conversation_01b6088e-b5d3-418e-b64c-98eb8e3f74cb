#!/bin/bash

# SSL Setup Script for CampusPQ API Domain
# This script sets up Let's Encrypt SSL certificates for api.campuspq.com

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${GREEN}[✓]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[!]${NC} $1"
}

print_error() {
    echo -e "${RED}[✗]${NC} $1"
}

print_info() {
    echo -e "${BLUE}[i]${NC} $1"
}

print_header() {
    echo ""
    echo -e "${BLUE}=== $1 ===${NC}"
}

# Configuration
DOMAIN="api.campuspq.com"
EMAIL="<EMAIL>"  # Change this to your email

# Check if running as root
check_root() {
    if [ "$EUID" -ne 0 ]; then
        print_error "This script must be run as root (use sudo)"
        exit 1
    fi
}

# Install Certbot
install_certbot() {
    print_header "Installing Certbot"
    
    # Update package list
    apt update
    
    # Install Certbot and Nginx plugin
    apt install -y certbot python3-certbot-nginx
    
    print_status "Certbot installed successfully"
}

# Stop Nginx temporarily
stop_nginx() {
    print_header "Stopping Nginx for Certificate Generation"

    cd /home/<USER>/campuspq-deployment
    docker-compose stop nginx

    print_status "Nginx stopped"
}

# Generate SSL certificate
generate_certificate() {
    print_header "Generating SSL Certificate"
    
    print_info "Generating certificate for: $DOMAIN"
    print_info "Email: $EMAIL"
    
    # Generate certificate using standalone mode
    certbot certonly \
        --standalone \
        --email $EMAIL \
        --agree-tos \
        --no-eff-email \
        -d $DOMAIN
    
    if [ $? -eq 0 ]; then
        print_status "SSL certificate generated successfully"
    else
        print_error "Failed to generate SSL certificate"
        exit 1
    fi
}

# Update Nginx configuration
update_nginx_config() {
    print_header "Updating Nginx Configuration"

    cd /home/<USER>/campuspq-deployment
    
    # Create SSL directory in container
    mkdir -p ssl
    
    # Copy certificates to a location accessible by Docker
    cp /etc/letsencrypt/live/$DOMAIN/fullchain.pem ssl/
    cp /etc/letsencrypt/live/$DOMAIN/privkey.pem ssl/
    
    # Set proper permissions
    chmod 644 ssl/fullchain.pem
    chmod 600 ssl/privkey.pem
    
    print_status "SSL certificates copied to Docker volume"
}

# Update docker-compose to mount SSL certificates
update_docker_compose() {
    print_header "Updating Docker Compose Configuration"

    cd /home/<USER>/campuspq-deployment
    
    # Create backup of docker-compose.yml
    cp docker-compose.yml docker-compose.yml.backup
    
    # Update nginx service to mount SSL certificates
    cat > docker-compose.yml << 'EOF'
version: '3.8'

services:
  # Redis for caching and session management
  redis:
    image: redis:7-alpine
    container_name: campuspq-redis
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    command: redis-server --appendonly yes
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5

  # FastAPI Backend
  api:
    build:
      context: ./backend
      dockerfile: Dockerfile.ec2
      target: production
    container_name: campuspq-api
    restart: unless-stopped
    ports:
      - "8000:8000"
    volumes:
      - ./backend/uploads:/app/uploads
      - ./backend/logs:/app/logs
    environment:
      - DATABASE_URL=${DATABASE_URL}
      - REDIS_URL=redis://redis:6379/0
      - SECRET_KEY=${SECRET_KEY}
      - GEMINI_API_KEY=${GEMINI_API_KEY}
      - OPENAI_API_KEY=${OPENAI_API_KEY:-}
      - AI_PROVIDER=${AI_PROVIDER:-gemini}
      - ENVIRONMENT=production
      - DEBUG=false
      - FRONTEND_URL=${FRONTEND_URL}
      - PAYSTACK_SECRET_KEY=${PAYSTACK_SECRET_KEY:-}
      - PAYSTACK_PUBLIC_KEY=${PAYSTACK_PUBLIC_KEY:-}
      - FLUTTERWAVE_SECRET_KEY=${FLUTTERWAVE_SECRET_KEY:-}
      - FLUTTERWAVE_PUBLIC_KEY=${FLUTTERWAVE_PUBLIC_KEY:-}
      - FLUTTERWAVE_ENCRYPTION_KEY=${FLUTTERWAVE_ENCRYPTION_KEY:-}
      - SMTP_USER=${SMTP_USER:-}
      - SMTP_PASSWORD=${SMTP_PASSWORD:-}
      - EMAILS_FROM_EMAIL=${EMAILS_FROM_EMAIL:-}
      - CLOUDINARY_URL=${CLOUDINARY_URL:-}
      - CLOUDINARY_API_KEY=${CLOUDINARY_API_KEY:-}
      - CLOUDINARY_API_SECRET=${CLOUDINARY_API_SECRET:-}
    depends_on:
      redis:
        condition: service_healthy
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/api/v1/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Nginx reverse proxy with SSL
  nginx:
    image: nginx:alpine
    container_name: campuspq-nginx
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx.conf:/etc/nginx/nginx.conf:ro
      - /etc/letsencrypt:/etc/letsencrypt:ro
    depends_on:
      - api
    healthcheck:
      test: ["CMD", "wget", "--quiet", "--tries=1", "--spider", "http://localhost/health"]
      interval: 30s
      timeout: 10s
      retries: 3

volumes:
  redis_data:
    driver: local
EOF

    print_status "Docker Compose configuration updated with SSL support"
}

# Start services
start_services() {
    print_header "Starting Services with SSL"

    cd /home/<USER>/campuspq-deployment
    
    # Start all services
    docker-compose up -d
    
    # Wait for services to start
    sleep 30
    
    # Check status
    docker-compose ps
    
    print_status "Services started with SSL support"
}

# Set up automatic certificate renewal
setup_renewal() {
    print_header "Setting Up Automatic Certificate Renewal"
    
    # Create renewal script
    cat > /etc/cron.d/certbot-renewal << EOF
# Renew Let's Encrypt certificates twice daily
0 */12 * * * root certbot renew --quiet --deploy-hook "cd /home/<USER>/campuspq-deployment && docker-compose restart nginx"
EOF
    
    print_status "Automatic certificate renewal configured"
}

# Test SSL configuration
test_ssl() {
    print_header "Testing SSL Configuration"
    
    print_info "Testing HTTPS connection..."
    
    # Wait a moment for services to be ready
    sleep 10
    
    if curl -f https://$DOMAIN/api/v1/health >/dev/null 2>&1; then
        print_status "✅ HTTPS is working correctly!"
        print_status "Your API is now available at: https://$DOMAIN"
    else
        print_warning "HTTPS test failed. Check the configuration."
        print_info "You can manually test with: curl -k https://$DOMAIN/api/v1/health"
    fi
}

# Main function
main() {
    print_info "🔒 CampusPQ SSL Setup Script"
    print_info "============================"
    print_info "Domain: $DOMAIN"
    print_info "Email: $EMAIL"
    echo ""
    
    read -p "Continue with SSL setup? (y/N): " -n 1 -r
    echo
    if [[ ! $REPLY =~ ^[Yy]$ ]]; then
        print_info "SSL setup cancelled"
        exit 0
    fi
    
    check_root
    install_certbot
    stop_nginx
    generate_certificate
    update_docker_compose
    start_services
    setup_renewal
    test_ssl
    
    echo ""
    print_status "🎉 SSL setup completed successfully!"
    print_info "Your API is now available at:"
    print_info "  - HTTPS: https://$DOMAIN"
    print_info "  - API Docs: https://$DOMAIN/api/v1/docs"
    print_info "  - Health Check: https://$DOMAIN/api/v1/health"
}

# Run main function
main "$@"
