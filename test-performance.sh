#!/bin/bash

# Performance testing script for CampusPQ API
# Tests response times and identifies hanging endpoints

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

print_status() {
    echo -e "${GREEN}[✓]${NC} $1"
}

print_warning() {
    echo -e "${YELLOW}[!]${NC} $1"
}

print_error() {
    echo -e "${RED}[✗]${NC} $1"
}

print_info() {
    echo -e "${BLUE}[i]${NC} $1"
}

# Configuration
API_BASE_URL="${API_BASE_URL:-http://localhost:8000}"
TIMEOUT=10

# Test endpoint with timing
test_endpoint() {
    local endpoint="$1"
    local description="$2"
    local method="${3:-GET}"
    
    print_info "Testing: $description"
    
    local start_time=$(date +%s.%N)
    local response_code=$(curl -s -w "%{http_code}" -o /dev/null --max-time $TIMEOUT -X $method "$API_BASE_URL$endpoint" || echo "000")
    local end_time=$(date +%s.%N)
    local duration=$(echo "$end_time - $start_time" | bc -l)
    
    if [ "$response_code" = "000" ]; then
        print_error "❌ $description - TIMEOUT (>${TIMEOUT}s)"
        return 1
    elif [ "$response_code" -ge 200 ] && [ "$response_code" -lt 300 ]; then
        if (( $(echo "$duration > 5.0" | bc -l) )); then
            print_warning "⚠️ $description - SLOW (${duration}s) - Status: $response_code"
        else
            print_status "✅ $description - FAST (${duration}s) - Status: $response_code"
        fi
        return 0
    else
        print_warning "⚠️ $description - Status: $response_code (${duration}s)"
        return 0
    fi
}

# Test database-heavy endpoints
test_database_endpoints() {
    print_info "=== Testing Database-Heavy Endpoints ==="
    
    test_endpoint "/api/v1/health" "Health Check"
    test_endpoint "/api/v1/departments" "Departments List"
    test_endpoint "/api/v1/courses" "Courses List (requires auth)" 
    test_endpoint "/api/v1/auth/login" "Login Endpoint" "POST"
}

# Test concurrent requests
test_concurrent_requests() {
    print_info "=== Testing Concurrent Requests ==="
    
    local pids=()
    local results=()
    
    # Start 5 concurrent health checks
    for i in {1..5}; do
        (
            local start_time=$(date +%s.%N)
            local response_code=$(curl -s -w "%{http_code}" -o /dev/null --max-time $TIMEOUT "$API_BASE_URL/api/v1/health" || echo "000")
            local end_time=$(date +%s.%N)
            local duration=$(echo "$end_time - $start_time" | bc -l)
            echo "$i:$response_code:$duration"
        ) &
        pids+=($!)
    done
    
    # Wait for all requests to complete
    for pid in "${pids[@]}"; do
        wait $pid
        results+=($(jobs -p))
    done
    
    print_info "Concurrent test completed"
}

# Monitor system resources
check_system_resources() {
    print_info "=== System Resources ==="
    
    # Memory usage
    local mem_usage=$(free | awk 'NR==2{printf "%.1f", $3*100/$2}')
    if (( $(echo "$mem_usage > 80.0" | bc -l) )); then
        print_warning "High memory usage: ${mem_usage}%"
    else
        print_status "Memory usage: ${mem_usage}%"
    fi
    
    # Disk usage
    local disk_usage=$(df -h / | awk 'NR==2 {print $5}' | sed 's/%//')
    if [ "$disk_usage" -gt 80 ]; then
        print_warning "High disk usage: ${disk_usage}%"
    else
        print_status "Disk usage: ${disk_usage}%"
    fi
    
    # Load average
    local load_avg=$(uptime | awk -F'load average:' '{print $2}' | awk '{print $1}' | sed 's/,//')
    print_info "Load average: $load_avg"
}

# Check Docker containers
check_containers() {
    print_info "=== Docker Container Status ==="
    
    if command -v docker >/dev/null 2>&1; then
        docker ps --format "table {{.Names}}\t{{.Status}}\t{{.Ports}}" | grep campuspq || echo "No CampusPQ containers found"
        
        # Check container resource usage
        if docker ps | grep -q "campuspq-api"; then
            local container_stats=$(docker stats --no-stream --format "table {{.Container}}\t{{.CPUPerc}}\t{{.MemUsage}}" | grep campuspq-api)
            print_info "API Container Stats: $container_stats"
        fi
    else
        print_warning "Docker not available"
    fi
}

# Main test function
main() {
    print_info "🔍 CampusPQ Performance Test"
    print_info "=========================="
    print_info "API Base URL: $API_BASE_URL"
    print_info "Timeout: ${TIMEOUT}s"
    echo ""
    
    check_system_resources
    echo ""
    
    check_containers
    echo ""
    
    test_database_endpoints
    echo ""
    
    test_concurrent_requests
    echo ""
    
    print_info "=== Performance Test Complete ==="
    print_info "If you see many SLOW or TIMEOUT results, consider:"
    print_info "1. Increasing EC2 instance size (t3.micro or t3.small)"
    print_info "2. Optimizing database queries"
    print_info "3. Adding database connection pooling"
    print_info "4. Using a closer database region"
}

# Check if bc is available for calculations
if ! command -v bc >/dev/null 2>&1; then
    print_warning "bc not installed. Installing..."
    sudo apt-get update && sudo apt-get install -y bc
fi

# Run main function
main "$@"
