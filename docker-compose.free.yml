version: '3.8'

# Free-tier deployment configuration
# Uses external services: Supabase (DB), Redis Cloud, Vercel (Frontend)

networks:
  campuspq-free:
    driver: bridge

services:
  # Backend API - Deploy to Railway/Render
  api:
    build:
      context: ./backend
      dockerfile: Dockerfile.free
      target: production
    container_name: campuspq-api-free
    restart: unless-stopped
    ports:
      - "8000:8000"
    environment:
      # Database - Using your Supabase instance
      - DATABASE_URL=postgresql://postgres:GKNvV4kL28Li!<EMAIL>:5432/postgres
      
      # Redis - Using Redis Cloud free tier
      - REDIS_URL=${REDIS_CLOUD_URL:-redis://localhost:6379/0}
      
      # Vector Database - Using local Qdrant for development
      - QDRANT_URL=http://qdrant:6333
      - QDRANT_COLLECTION=campuspq_embeddings
      
      # Application settings
      - SECRET_KEY=${SECRET_KEY:-dev-secret-key-change-in-production}
      - ENVIRONMENT=production
      - DEBUG=false
      - API_V1_PREFIX=/api/v1
      
      # AI Configuration
      - OPENAI_API_KEY=${OPENAI_API_KEY:-}
      - GEMINI_API_KEY=${GEMINI_API_KEY:-}
      - AI_PROVIDER=${AI_PROVIDER:-gemini}
      
      # File storage - Using Cloudinary free tier
      - CLOUDINARY_URL=${CLOUDINARY_URL:-}
      - CLOUDINARY_API_KEY=${CLOUDINARY_API_KEY:-}
      - CLOUDINARY_API_SECRET=${CLOUDINARY_API_SECRET:-}
      
      # Email - Using free SMTP services
      - SMTP_USER=${SMTP_USER:-}
      - SMTP_PASSWORD=${SMTP_PASSWORD:-}
      - EMAILS_FROM_EMAIL=${EMAILS_FROM_EMAIL:-<EMAIL>}
      
      # Frontend URL
      - FRONTEND_URL=${FRONTEND_URL:-https://campuspq.vercel.app}
      
      # CORS for free hosting
      - BACKEND_CORS_ORIGINS=["https://campuspq.vercel.app", "http://localhost:3000", "http://localhost:5173"]
    
    volumes:
      - ./uploads:/app/uploads
      - ./logs:/app/logs
      - temp_files:/app/temp_files
    
    depends_on:
      - qdrant
    
    networks:
      - campuspq-free
    
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:8000/api/v1/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Celery Worker - Deploy to Railway/Render
  worker:
    build:
      context: ./backend
      dockerfile: Dockerfile.worker.free
      target: production
    container_name: campuspq-worker-free
    restart: unless-stopped
    environment:
      # Same environment as API
      - DATABASE_URL=postgresql://postgres:GKNvV4kL28Li!<EMAIL>:5432/postgres
      - REDIS_URL=${REDIS_CLOUD_URL:-redis://localhost:6379/0}
      - QDRANT_URL=http://qdrant:6333
      - QDRANT_COLLECTION=campuspq_embeddings
      - SECRET_KEY=${SECRET_KEY:-dev-secret-key-change-in-production}
      - ENVIRONMENT=production
      - DEBUG=false
      - OPENAI_API_KEY=${OPENAI_API_KEY:-}
      - GEMINI_API_KEY=${GEMINI_API_KEY:-}
      - AI_PROVIDER=${AI_PROVIDER:-gemini}
      - CLOUDINARY_URL=${CLOUDINARY_URL:-}
      - CLOUDINARY_API_KEY=${CLOUDINARY_API_KEY:-}
      - CLOUDINARY_API_SECRET=${CLOUDINARY_API_SECRET:-}
    
    volumes:
      - ./uploads:/app/uploads
      - ./logs:/app/logs
      - temp_files:/app/temp_files
    
    depends_on:
      - api
      - qdrant
    
    networks:
      - campuspq-free
    
    healthcheck:
      test: ["CMD", "celery", "-A", "app.core.celery_app", "inspect", "ping"]
      interval: 60s
      timeout: 30s
      retries: 3
      start_period: 60s

  # Qdrant Vector Database - Free local instance
  qdrant:
    image: qdrant/qdrant:latest
    container_name: campuspq-qdrant-free
    restart: unless-stopped
    ports:
      - "6333:6333"
      - "6334:6334"
    volumes:
      - qdrant_data:/qdrant/storage
    networks:
      - campuspq-free
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:6333/health"]
      interval: 30s
      timeout: 10s
      retries: 3

  # Redis (for local development only - use Redis Cloud in production)
  redis:
    image: redis:7-alpine
    container_name: campuspq-redis-free
    restart: unless-stopped
    command: redis-server --appendonly yes --maxmemory 100mb --maxmemory-policy allkeys-lru
    volumes:
      - redis_data:/data
    ports:
      - "6379:6379"
    networks:
      - campuspq-free
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
    profiles:
      - local  # Only run locally, not in production

volumes:
  qdrant_data:
    driver: local
  redis_data:
    driver: local
  temp_files:
    driver: local
