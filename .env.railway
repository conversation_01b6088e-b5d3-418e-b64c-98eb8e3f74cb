# Railway Environment Variables Template
# Copy these values to your Railway dashboard: https://railway.app/dashboard

# =============================================================================
# DATABASE CONFIGURATION
# =============================================================================
# Your existing Supabase database (already configured)
DATABASE_URL=postgresql://postgres:GKNvV4kL28Li!<EMAIL>:5432/postgres

# =============================================================================
# APPLICATION SECURITY
# =============================================================================
# Generate with: openssl rand -base64 32
SECRET_KEY=REPLACE_WITH_GENERATED_SECRET_KEY

# =============================================================================
# EXTERNAL SERVICES (Set these up first)
# =============================================================================

# Redis Cloud (Free tier: https://redis.com/try-free)
# Format: redis://default:password@host:port
REDIS_URL=redis://default:YOUR_PASSWORD@YOUR_HOST:PORT

# Qdrant Cloud (Free tier: https://cloud.qdrant.io)
# Format: https://your-cluster.qdrant.io:6333
QDRANT_URL=https://YOUR_CLUSTER.qdrant.io:6333
QDRANT_COLLECTION=campuspq_embeddings

# =============================================================================
# AI API KEYS
# =============================================================================
# OpenAI API (https://platform.openai.com/api-keys)
OPENAI_API_KEY=sk-YOUR_OPENAI_API_KEY

# Google Gemini API (https://makersuite.google.com/app/apikey)
GEMINI_API_KEY=YOUR_GEMINI_API_KEY

# AI Provider (gemini or openai)
AI_PROVIDER=gemini

# =============================================================================
# FILE STORAGE - CLOUDINARY (Free tier: https://cloudinary.com)
# =============================================================================
# Get from Cloudinary dashboard
CLOUDINARY_URL=cloudinary://API_KEY:API_SECRET@CLOUD_NAME
CLOUDINARY_API_KEY=YOUR_CLOUDINARY_API_KEY
CLOUDINARY_API_SECRET=YOUR_CLOUDINARY_API_SECRET

# =============================================================================
# EMAIL CONFIGURATION (Optional)
# =============================================================================
# Use Gmail, SendGrid, or any SMTP service
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-app-password
EMAILS_FROM_EMAIL=<EMAIL>

# =============================================================================
# FRONTEND CONFIGURATION
# =============================================================================
# Your existing frontend URL
FRONTEND_URL=https://your-frontend-domain.com

# CORS origins (include your frontend domain)
BACKEND_CORS_ORIGINS=["https://your-frontend-domain.com", "http://localhost:3000", "http://localhost:5173"]

# =============================================================================
# APPLICATION SETTINGS
# =============================================================================
ENVIRONMENT=production
DEBUG=false
API_V1_PREFIX=/api/v1
PROJECT_NAME=CampusPQ

# Performance settings for Railway free tier
CACHE_ENABLED=true
CACHE_TTL_HOURS=24
MAX_PARALLEL_CHUNKS=3
BATCH_SIZE=2

# =============================================================================
# RAILWAY-SPECIFIC SETTINGS
# =============================================================================
# Railway automatically sets PORT, but you can override
# PORT=8000

# Railway health check settings
HEALTHCHECK_PATH=/api/v1/health

# =============================================================================
# INSTRUCTIONS
# =============================================================================
# 1. Sign up for external services (Redis Cloud, Qdrant Cloud, Cloudinary)
# 2. Replace ALL placeholder values above with actual credentials
# 3. Copy these variables to Railway dashboard:
#    - Go to https://railway.app/dashboard
#    - Click your project
#    - Go to Variables tab
#    - Add each variable one by one
# 4. Deploy with: railway up
# 5. Test at: https://your-app.up.railway.app/api/v1/health
