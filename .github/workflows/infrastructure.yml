name: Deploy Infrastructure

on:
  push:
    branches: [main]
    paths: ['terraform/**']
  pull_request:
    branches: [main]
    paths: ['terraform/**']
  workflow_dispatch:
    inputs:
      action:
        description: 'Terraform action to perform'
        required: true
        default: 'plan'
        type: choice
        options:
          - plan
          - apply
          - destroy

env:
  AWS_REGION: us-east-1
  TF_VERSION: 1.6.0

jobs:
  terraform-check:
    name: Terraform Check
    runs-on: ubuntu-latest
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      
      - name: Setup Terraform
        uses: hashicorp/setup-terraform@v3
        with:
          terraform_version: ${{ env.TF_VERSION }}
      
      - name: Configure A<PERSON> credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ env.AWS_REGION }}
      
      - name: Terraform Format Check
        run: |
          cd terraform
          terraform fmt -check -recursive
      
      - name: Terraform Init
        run: |
          cd terraform
          terraform init
      
      - name: Terraform Validate
        run: |
          cd terraform
          terraform validate
      
      - name: Terraform Plan
        if: github.event_name == 'pull_request'
        run: |
          cd terraform
          terraform plan -var-file="environments/prod.tfvars" -no-color
        env:
          TF_VAR_db_password: ${{ secrets.DB_PASSWORD }}
          TF_VAR_secret_key: ${{ secrets.SECRET_KEY }}
          TF_VAR_openai_api_key: ${{ secrets.OPENAI_API_KEY }}
          TF_VAR_gemini_api_key: ${{ secrets.GEMINI_API_KEY }}
          TF_VAR_cloudinary_url: ${{ secrets.CLOUDINARY_URL }}
          TF_VAR_cloudinary_api_key: ${{ secrets.CLOUDINARY_API_KEY }}
          TF_VAR_cloudinary_api_secret: ${{ secrets.CLOUDINARY_API_SECRET }}
          TF_VAR_smtp_user: ${{ secrets.SMTP_USER }}
          TF_VAR_smtp_password: ${{ secrets.SMTP_PASSWORD }}

  terraform-apply:
    name: Terraform Apply
    runs-on: ubuntu-latest
    needs: terraform-check
    if: github.ref == 'refs/heads/main' && (github.event_name == 'push' || github.event.inputs.action == 'apply')
    environment: production
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      
      - name: Setup Terraform
        uses: hashicorp/setup-terraform@v3
        with:
          terraform_version: ${{ env.TF_VERSION }}
      
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ env.AWS_REGION }}
      
      - name: Terraform Init
        run: |
          cd terraform
          terraform init
      
      - name: Terraform Plan
        run: |
          cd terraform
          terraform plan -var-file="environments/prod.tfvars" -out=tfplan
        env:
          TF_VAR_db_password: ${{ secrets.DB_PASSWORD }}
          TF_VAR_secret_key: ${{ secrets.SECRET_KEY }}
          TF_VAR_openai_api_key: ${{ secrets.OPENAI_API_KEY }}
          TF_VAR_gemini_api_key: ${{ secrets.GEMINI_API_KEY }}
          TF_VAR_cloudinary_url: ${{ secrets.CLOUDINARY_URL }}
          TF_VAR_cloudinary_api_key: ${{ secrets.CLOUDINARY_API_KEY }}
          TF_VAR_cloudinary_api_secret: ${{ secrets.CLOUDINARY_API_SECRET }}
          TF_VAR_smtp_user: ${{ secrets.SMTP_USER }}
          TF_VAR_smtp_password: ${{ secrets.SMTP_PASSWORD }}
      
      - name: Terraform Apply
        if: github.event.inputs.action != 'plan'
        run: |
          cd terraform
          terraform apply tfplan
      
      - name: Output Infrastructure Info
        if: github.event.inputs.action != 'plan'
        run: |
          cd terraform
          echo "## Infrastructure Outputs" >> $GITHUB_STEP_SUMMARY
          echo "### Application URLs" >> $GITHUB_STEP_SUMMARY
          echo "- Application: $(terraform output -raw application_url)" >> $GITHUB_STEP_SUMMARY
          echo "- API: $(terraform output -raw api_url)" >> $GITHUB_STEP_SUMMARY
          echo "### ECS Cluster" >> $GITHUB_STEP_SUMMARY
          echo "- Cluster: $(terraform output -raw ecs_cluster_name)" >> $GITHUB_STEP_SUMMARY
          echo "- Load Balancer: $(terraform output -raw alb_dns_name)" >> $GITHUB_STEP_SUMMARY

  terraform-destroy:
    name: Terraform Destroy
    runs-on: ubuntu-latest
    if: github.event.inputs.action == 'destroy'
    environment: production
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      
      - name: Setup Terraform
        uses: hashicorp/setup-terraform@v3
        with:
          terraform_version: ${{ env.TF_VERSION }}
      
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ env.AWS_REGION }}
      
      - name: Terraform Init
        run: |
          cd terraform
          terraform init
      
      - name: Terraform Destroy
        run: |
          cd terraform
          terraform destroy -var-file="environments/prod.tfvars" -auto-approve
        env:
          TF_VAR_db_password: ${{ secrets.DB_PASSWORD }}
          TF_VAR_secret_key: ${{ secrets.SECRET_KEY }}
          TF_VAR_openai_api_key: ${{ secrets.OPENAI_API_KEY }}
          TF_VAR_gemini_api_key: ${{ secrets.GEMINI_API_KEY }}
          TF_VAR_cloudinary_url: ${{ secrets.CLOUDINARY_URL }}
          TF_VAR_cloudinary_api_key: ${{ secrets.CLOUDINARY_API_KEY }}
          TF_VAR_cloudinary_api_secret: ${{ secrets.CLOUDINARY_API_SECRET }}
          TF_VAR_smtp_user: ${{ secrets.SMTP_USER }}
          TF_VAR_smtp_password: ${{ secrets.SMTP_PASSWORD }}
