name: Deploy to AWS ECS

on:
  push:
    branches: [main, develop]
  pull_request:
    branches: [main]

env:
  AWS_REGION: us-east-1
  ECR_REGISTRY: ${{ secrets.AWS_ACCOUNT_ID }}.dkr.ecr.us-east-1.amazonaws.com
  ECS_CLUSTER: campuspq-prod-cluster
  
jobs:
  test:
    name: Run Tests
    runs-on: ubuntu-latest
    
    services:
      postgres:
        image: postgres:14
        env:
          POSTGRES_PASSWORD: postgres
          POSTGRES_DB: campuspq_test
        options: >-
          --health-cmd pg_isready
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 5432:5432
      
      redis:
        image: redis:7
        options: >-
          --health-cmd "redis-cli ping"
          --health-interval 10s
          --health-timeout 5s
          --health-retries 5
        ports:
          - 6379:6379
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      
      - name: Set up Python
        uses: actions/setup-python@v4
        with:
          python-version: '3.10'
      
      - name: Cache Python dependencies
        uses: actions/cache@v3
        with:
          path: ~/.cache/pip
          key: ${{ runner.os }}-pip-${{ hashFiles('backend/requirements.txt') }}
          restore-keys: |
            ${{ runner.os }}-pip-
      
      - name: Install Python dependencies
        run: |
          cd backend
          python -m pip install --upgrade pip
          pip install -r requirements.txt
          pip install pytest pytest-asyncio httpx
      
      - name: Run Python tests
        env:
          DATABASE_URL: postgresql://postgres:postgres@localhost:5432/campuspq_test
          REDIS_URL: redis://localhost:6379/0
          SECRET_KEY: test-secret-key
          OPENAI_API_KEY: test-key
          GEMINI_API_KEY: test-key
        run: |
          cd backend
          pytest tests/ -v
      
      - name: Set up Node.js
        uses: actions/setup-node@v4
        with:
          node-version: '18'
          cache: 'npm'
          cache-dependency-path: frontend/package-lock.json
      
      - name: Install Node.js dependencies
        run: |
          cd frontend
          npm ci
      
      - name: Run frontend tests
        run: |
          cd frontend
          npm run lint
          # npm run test # Add when you have frontend tests
      
      - name: Build frontend
        run: |
          cd frontend
          npm run build:production

  build-and-push:
    name: Build and Push Images
    runs-on: ubuntu-latest
    needs: test
    if: github.ref == 'refs/heads/main' || github.ref == 'refs/heads/develop'
    
    strategy:
      matrix:
        service: [backend, worker, frontend]
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ env.AWS_REGION }}
      
      - name: Login to Amazon ECR
        id: login-ecr
        uses: aws-actions/amazon-ecr-login@v2
      
      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
      
      - name: Build and push backend image
        if: matrix.service == 'backend'
        uses: docker/build-push-action@v5
        with:
          context: ./backend
          file: ./backend/Dockerfile
          push: true
          tags: |
            ${{ env.ECR_REGISTRY }}/campuspq-backend:latest
            ${{ env.ECR_REGISTRY }}/campuspq-backend:${{ github.sha }}
          cache-from: type=gha
          cache-to: type=gha,mode=max
          target: production
      
      - name: Build and push worker image
        if: matrix.service == 'worker'
        uses: docker/build-push-action@v5
        with:
          context: ./backend
          file: ./backend/Dockerfile.worker
          push: true
          tags: |
            ${{ env.ECR_REGISTRY }}/campuspq-worker:latest
            ${{ env.ECR_REGISTRY }}/campuspq-worker:${{ github.sha }}
          cache-from: type=gha
          cache-to: type=gha,mode=max
          target: production
      
      - name: Build and push frontend image
        if: matrix.service == 'frontend'
        uses: docker/build-push-action@v5
        with:
          context: ./frontend
          file: ./frontend/Dockerfile
          push: true
          tags: |
            ${{ env.ECR_REGISTRY }}/campuspq-frontend:latest
            ${{ env.ECR_REGISTRY }}/campuspq-frontend:${{ github.sha }}
          cache-from: type=gha
          cache-to: type=gha,mode=max
          target: production

  deploy:
    name: Deploy to ECS
    runs-on: ubuntu-latest
    needs: build-and-push
    if: github.ref == 'refs/heads/main'
    
    steps:
      - name: Checkout code
        uses: actions/checkout@v4
      
      - name: Configure AWS credentials
        uses: aws-actions/configure-aws-credentials@v4
        with:
          aws-access-key-id: ${{ secrets.AWS_ACCESS_KEY_ID }}
          aws-secret-access-key: ${{ secrets.AWS_SECRET_ACCESS_KEY }}
          aws-region: ${{ env.AWS_REGION }}
      
      - name: Update backend task definition
        id: backend-task-def
        uses: aws-actions/amazon-ecs-render-task-definition@v1
        with:
          task-definition: aws/ecs/task-definitions/backend-api.json
          container-name: campuspq-api
          image: ${{ env.ECR_REGISTRY }}/campuspq-backend:${{ github.sha }}
      
      - name: Deploy backend to ECS
        uses: aws-actions/amazon-ecs-deploy-task-definition@v1
        with:
          task-definition: ${{ steps.backend-task-def.outputs.task-definition }}
          service: campuspq-backend-service
          cluster: ${{ env.ECS_CLUSTER }}
          wait-for-service-stability: true
      
      - name: Update worker task definition
        id: worker-task-def
        uses: aws-actions/amazon-ecs-render-task-definition@v1
        with:
          task-definition: aws/ecs/task-definitions/celery-worker.json
          container-name: campuspq-worker
          image: ${{ env.ECR_REGISTRY }}/campuspq-worker:${{ github.sha }}
      
      - name: Deploy worker to ECS
        uses: aws-actions/amazon-ecs-deploy-task-definition@v1
        with:
          task-definition: ${{ steps.worker-task-def.outputs.task-definition }}
          service: campuspq-worker-service
          cluster: ${{ env.ECS_CLUSTER }}
          wait-for-service-stability: true
      
      - name: Update frontend task definition
        id: frontend-task-def
        uses: aws-actions/amazon-ecs-render-task-definition@v1
        with:
          task-definition: aws/ecs/task-definitions/frontend.json
          container-name: campuspq-frontend
          image: ${{ env.ECR_REGISTRY }}/campuspq-frontend:${{ github.sha }}
      
      - name: Deploy frontend to ECS
        uses: aws-actions/amazon-ecs-deploy-task-definition@v1
        with:
          task-definition: ${{ steps.frontend-task-def.outputs.task-definition }}
          service: campuspq-frontend-service
          cluster: ${{ env.ECS_CLUSTER }}
          wait-for-service-stability: true
      
      - name: Notify deployment success
        if: success()
        run: |
          echo "🚀 Deployment successful!"
          echo "Application URL: https://${{ secrets.DOMAIN_NAME }}"
      
      - name: Notify deployment failure
        if: failure()
        run: |
          echo "❌ Deployment failed!"
          exit 1
