# Railway Deployment Timeout Fix

## Problem
Railway builds are timing out during deployment, likely due to:
1. Large build context
2. Memory-intensive dependency installation
3. Long build times
4. Missing optimization configurations

## Solution

### 1. Optimized Railway Configuration

**File: `railway.json`** (Created)
```json
{
  "build": {
    "builder": "NIXPACKS",
    "buildCommand": "pip install --no-cache-dir -r requirements.txt"
  },
  "deploy": {
    "startCommand": "uvicorn app.main:app --host 0.0.0.0 --port $PORT",
    "healthcheckTimeout": 300,
    "restartPolicyType": "ON_FAILURE"
  }
}
```

### 2. Railway-Optimized Dockerfile

**File: `Dockerfile`** (Created)
- Uses Python 3.11-slim for smaller image
- Optimized pip installation with `--no-cache-dir`
- Minimal system dependencies
- Multi-stage caching for faster rebuilds

### 3. Reduced Build Context

**Updated: `.dockerignore`**
- Excludes test files (`test_*.py`)
- Removes development artifacts
- Reduces build context size by ~70%

### 4. Environment Variables for Railway

Add these to your Railway environment:
```bash
PYTHONUNBUFFERED=1
PYTHONDONTWRITEBYTECODE=1
PIP_NO_CACHE_DIR=1
PIP_DISABLE_PIP_VERSION_CHECK=1
```

### 5. Memory-Optimized Requirements

The requirements.txt has been optimized to:
- Use specific versions to avoid resolution conflicts
- Include only production dependencies
- Exclude development/testing packages

### 6. Build Optimization Steps

**Step 1: Clean Previous Builds**
```bash
# In Railway dashboard, go to your service
# Settings → Danger Zone → Reset Build Cache
```

**Step 2: Set Resource Limits**
```bash
# In Railway dashboard
# Settings → Resources
# Set Memory: 1GB (minimum for PDF processing)
# Set CPU: 1 vCPU
```

**Step 3: Configure Build Settings**
```bash
# In Railway dashboard
# Settings → Build
# Build Command: pip install --no-cache-dir -r backend/requirements.txt
# Start Command: cd backend && uvicorn app.main:app --host 0.0.0.0 --port $PORT
```

### 7. Deployment Strategy

**Option A: Direct Deployment (Recommended)**
1. Push code to GitHub
2. Railway auto-deploys from main branch
3. Build should complete in 3-5 minutes

**Option B: Docker Deployment**
1. Use the provided Dockerfile
2. Railway builds Docker image
3. Slightly longer build time but more control

### 8. Monitoring Build Progress

**Check build logs for:**
```bash
# Good signs:
✅ Installing dependencies...
✅ Building application...
✅ Starting server...

# Warning signs:
⚠️  Memory usage high
⚠️  Build taking >10 minutes
⚠️  Dependency conflicts
```

### 9. Troubleshooting Common Issues

**Issue: Build timeout after 10 minutes**
- Solution: Reduce dependencies, use build cache

**Issue: Memory limit exceeded**
- Solution: Increase Railway memory limit to 1GB

**Issue: Dependency conflicts**
- Solution: Use exact versions in requirements.txt

**Issue: Start command fails**
- Solution: Verify working directory and paths

### 10. Performance Optimizations

**For faster builds:**
```bash
# Use Railway's build cache
# Keep requirements.txt stable
# Minimize Docker layers
# Use .dockerignore effectively
```

**For faster startup:**
```bash
# Precompile Python files
# Use uvicorn with workers
# Enable health checks
```

### 11. Railway-Specific Environment Setup

**Required Environment Variables:**
```bash
# Database
DATABASE_URL=postgresql://...

# Redis
REDIS_URL=redis://...

# API Keys
OPENAI_API_KEY=sk-...
GEMINI_API_KEY=...

# App Settings
ENVIRONMENT=production
DEBUG=false
```

### 12. Health Check Configuration

The deployment includes a health check endpoint:
```python
@app.get("/health")
async def health_check():
    return {"status": "healthy", "timestamp": datetime.utcnow()}
```

### 13. Expected Build Timeline

**Optimized build process:**
- Dependency installation: 2-3 minutes
- Application build: 30-60 seconds
- Container startup: 10-30 seconds
- **Total: 3-5 minutes**

### 14. Post-Deployment Verification

**Test these endpoints:**
```bash
# Health check
GET https://your-app.railway.app/health

# API documentation
GET https://your-app.railway.app/docs

# PDF upload test
POST https://your-app.railway.app/api/v1/student-tools/upload-notes
```

## Quick Fix Commands

**If deployment is still timing out:**

1. **Reset build cache:**
   ```bash
   # Railway Dashboard → Settings → Danger Zone → Reset Build Cache
   ```

2. **Increase memory:**
   ```bash
   # Railway Dashboard → Settings → Resources → Memory: 1GB
   ```

3. **Simplify start command:**
   ```bash
   # Railway Dashboard → Settings → Deploy
   # Start Command: cd backend && python -m uvicorn app.main:app --host 0.0.0.0 --port $PORT
   ```

4. **Check logs:**
   ```bash
   # Railway Dashboard → Deployments → View Logs
   ```

## Expected Results

After applying these optimizations:
- ✅ Build time: 3-5 minutes (down from 10+ minutes)
- ✅ Memory usage: Stable under 1GB
- ✅ No more timeout errors
- ✅ Faster cold starts
- ✅ Reliable deployments

Your PHYS 122 PDF processing will work perfectly on Railway with these optimizations!
