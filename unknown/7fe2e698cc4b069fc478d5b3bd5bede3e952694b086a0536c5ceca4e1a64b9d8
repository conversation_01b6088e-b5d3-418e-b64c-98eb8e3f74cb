from fastapi import APIRouter, Depends, HTTPException, UploadFile, File, Form, status
from sqlalchemy.orm import Session
from typing import List

from app.api import deps
from app.models.user import User

router = APIRouter()


@router.post("/echo-upload")
async def echo_upload(
    files: List[UploadFile] = File(...),
    db: Session = Depends(deps.get_db),
    current_user: User = Depends(deps.get_current_active_user)
):
    """
    Debug endpoint to echo back file upload information.
    """
    result = []
    for file in files:
        # Read a small part of the file to verify it's accessible
        content = await file.read(1024)  # Read first 1KB
        await file.seek(0)  # Reset file position
        
        result.append({
            "filename": file.filename,
            "content_type": file.content_type,
            "size": file.size if hasattr(file, "size") else "unknown",
            "content_preview": content[:20].hex(),
            "user_id": current_user.id,
            "username": current_user.username
        })
    
    return {
        "message": "Files received successfully",
        "file_count": len(files),
        "files": result
    }
