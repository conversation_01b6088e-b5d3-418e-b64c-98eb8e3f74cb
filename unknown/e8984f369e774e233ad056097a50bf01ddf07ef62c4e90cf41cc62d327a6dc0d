import React, { useState } from 'react';
import { 
  Box, 
  SpeedDial, 
  SpeedDialIcon, 
  SpeedDialAction,
  useTheme,
  Zoom
} from '@mui/material';
import { 
  Add as AddIcon,
  Close as CloseIcon,
  PlayArrow as PracticeIcon,
  Assessment as ExamIcon,
  Bookmark as BookmarkIcon,
  School as CourseIcon
} from '@mui/icons-material';
import { useNavigate } from 'react-router-dom';

interface MCQFloatingActionButtonProps {
  courseId?: number;
  onPracticeClick?: (courseId: number) => void;
  onExamClick?: (courseId: number) => void;
  onBookmarksClick?: () => void;
  onCourseSelectClick?: () => void;
}

const MCQFloatingActionButton: React.FC<MCQFloatingActionButtonProps> = ({
  courseId,
  onPracticeClick,
  onExamClick,
  onBookmarksClick,
  onCourseSelectClick
}) => {
  const theme = useTheme();
  const navigate = useNavigate();
  const [open, setOpen] = useState(false);
  
  const handleOpen = () => setOpen(true);
  const handleClose = () => setOpen(false);

  // Define actions based on whether a course is selected
  const actions = courseId 
    ? [
        { 
          icon: <PracticeIcon />, 
          name: 'Practice Mode', 
          action: () => onPracticeClick ? onPracticeClick(courseId) : navigate(`/mcq/practice/${courseId}`)
        },
        { 
          icon: <ExamIcon />, 
          name: 'Exam Mode', 
          action: () => onExamClick ? onExamClick(courseId) : navigate(`/mcq/exam/${courseId}`)
        },
        { 
          icon: <BookmarkIcon />, 
          name: 'Bookmarks', 
          action: () => onBookmarksClick ? onBookmarksClick() : null
        }
      ]
    : [
        { 
          icon: <CourseIcon />, 
          name: 'Select Course', 
          action: () => onCourseSelectClick ? onCourseSelectClick() : null
        },
        { 
          icon: <BookmarkIcon />, 
          name: 'Bookmarks', 
          action: () => onBookmarksClick ? onBookmarksClick() : null
        }
      ];

  return (
    <Zoom in={true}>
      <Box
        sx={{
          position: 'fixed',
          bottom: 80, // Position above bottom navigation
          right: 16,
          zIndex: 1000,
        }}
      >
        <SpeedDial
          ariaLabel="MCQ Quick Actions"
          icon={<SpeedDialIcon icon={<AddIcon />} openIcon={<CloseIcon />} />}
          onClose={handleClose}
          onOpen={handleOpen}
          open={open}
          direction="up"
          FabProps={{
            sx: {
              bgcolor: theme.palette.primary.main,
              '&:hover': {
                bgcolor: theme.palette.primary.dark,
              },
              boxShadow: '0 4px 20px rgba(0,0,0,0.15)',
            }
          }}
        >
          {actions.map((action) => (
            <SpeedDialAction
              key={action.name}
              icon={action.icon}
              tooltipTitle={action.name}
              tooltipOpen
              onClick={() => {
                action.action();
                handleClose();
              }}
              FabProps={{
                sx: {
                  bgcolor: theme.palette.background.paper,
                  boxShadow: '0 2px 10px rgba(0,0,0,0.1)',
                }
              }}
            />
          ))}
        </SpeedDial>
      </Box>
    </Zoom>
  );
};

export default MCQFloatingActionButton;
