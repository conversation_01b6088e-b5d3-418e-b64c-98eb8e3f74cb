import csv
import logging
from typing import List, Dict, <PERSON>, <PERSON><PERSON>, Optional
from io import <PERSON><PERSON>

from fastapi import UploadFile
from sqlalchemy.orm import Session

from app import schemas
from app.models.question import QuestionType, DifficultyLevel
from app.services import question_service, course_service
from app.services.cloudinary_service import cloudinary_service

logger = logging.getLogger(__name__)

class CSVQuestionService:
    """Service for processing CSV files containing questions."""
    
    # Required CSV columns
    REQUIRED_COLUMNS = ['content', 'difficulty', 'answer']

    # Optional CSV columns
    OPTIONAL_COLUMNS = ['topic', 'explanation', 'option_a', 'option_b', 'option_c', 'option_d']
    
    # Valid question types
    VALID_QUESTION_TYPES = [qt.value for qt in QuestionType]
    
    # Valid difficulty levels
    VALID_DIFFICULTIES = [dl.value for dl in DifficultyLevel]

    async def validate_csv_file(self, file: UploadFile) -> Tuple[bool, str]:
        """
        Validate the uploaded CSV file.
        
        Args:
            file: The uploaded CSV file
            
        Returns:
            Tuple of (is_valid, error_message)
        """
        # Check file extension
        if not file.filename or not file.filename.lower().endswith('.csv'):
            return False, "File must be a CSV file"
        
        # Check file size (max 10MB for CSV)
        if hasattr(file, 'size') and file.size and file.size > 10 * 1024 * 1024:
            return False, "CSV file size must be less than 10MB"
        
        return True, ""

    async def parse_csv_content(
        self,
        file: UploadFile,
        course_id: int,
        question_type: QuestionType
    ) -> Tuple[List[Dict[str, Any]], List[str]]:
        """
        Parse CSV content and return rows with validation errors.
        
        Args:
            file: The uploaded CSV file
            
        Returns:
            Tuple of (parsed_rows, validation_errors)
        """
        try:
            # Read file content
            content = await file.read()
            csv_content = content.decode('utf-8')
            
            # Parse CSV
            csv_reader = csv.DictReader(StringIO(csv_content))
            
            # Validate headers
            headers = csv_reader.fieldnames or []
            missing_columns = [col for col in self.REQUIRED_COLUMNS if col not in headers]
            if missing_columns:
                return [], [f"Missing required columns: {', '.join(missing_columns)}"]
            
            # Parse rows
            rows = []
            errors = []
            
            for row_num, row in enumerate(csv_reader, start=2):  # Start at 2 because row 1 is headers
                try:
                    parsed_row = self._parse_row(row, course_id, question_type)
                    if parsed_row:
                        rows.append(parsed_row)
                except ValueError as e:
                    errors.append(f"Row {row_num}: {str(e)}")
            
            return rows, errors
            
        except UnicodeDecodeError:
            return [], ["File encoding error. Please ensure the CSV file is UTF-8 encoded"]
        except Exception as e:
            logger.error(f"Error parsing CSV: {str(e)}")
            return [], [f"Error parsing CSV file: {str(e)}"]

    def _parse_row(
        self,
        row: Dict[str, str],
        course_id: int,
        question_type: QuestionType
    ) -> Optional[Dict[str, Any]]:
        """
        Parse and validate a single CSV row.

        Args:
            row: CSV row data

        Returns:
            Parsed row data or None if row should be skipped

        Raises:
            ValueError: If row validation fails
        """
        # Skip empty rows
        if not any(row.values()):
            return None
        
        # Validate required fields
        for col in self.REQUIRED_COLUMNS:
            if not row.get(col, '').strip():
                raise ValueError(f"Missing required field '{col}'")
        
        # Parse and validate difficulty
        difficulty = row['difficulty'].strip().lower()
        if difficulty not in self.VALID_DIFFICULTIES:
            raise ValueError(f"Invalid difficulty '{difficulty}'. Must be one of: {', '.join(self.VALID_DIFFICULTIES)}")

        # Parse options for multiple choice questions
        options = None
        if question_type == QuestionType.MULTIPLE_CHOICE:
            option_a = row.get('option_a', '').strip()
            option_b = row.get('option_b', '').strip()
            option_c = row.get('option_c', '').strip()
            option_d = row.get('option_d', '').strip()

            # Check if at least option_a and option_b are provided
            if not option_a or not option_b:
                raise ValueError("Multiple choice questions must have at least option_a and option_b")

            options = {'A': option_a, 'B': option_b}
            if option_c:
                options['C'] = option_c
            if option_d:
                options['D'] = option_d
        
        return {
            'content': row['content'].strip(),
            'question_type': question_type.value,
            'difficulty': difficulty,
            'answer': row['answer'].strip(),
            'course_id': course_id,
            'topic': row.get('topic', '').strip() or None,
            'explanation': row.get('explanation', '').strip() or None,
            'options': options
        }

    async def validate_course(self, db: Session, course_id: int) -> List[str]:
        """
        Validate that the course ID exists.

        Args:
            db: Database session
            course_id: Course ID to validate

        Returns:
            List of validation errors
        """
        errors = []
        course = course_service.get(db, id=course_id)
        if not course:
            errors.append(f"Course with ID {course_id} does not exist")

        return errors

    async def create_questions_from_csv(
        self,
        db: Session,
        rows: List[Dict[str, Any]],
        created_by_id: int
    ) -> Tuple[List[schemas.Question], List[str]]:
        """
        Create questions from parsed CSV rows.

        Args:
            db: Database session
            rows: Parsed and validated CSV rows
            created_by_id: ID of the user creating the questions

        Returns:
            Tuple of (created_questions, creation_errors)
        """
        created_questions = []
        errors = []

        for i, row in enumerate(rows, start=1):
            try:
                question_data = schemas.QuestionCreate(
                    content=row['content'],
                    question_type=QuestionType(row['question_type']),
                    difficulty=DifficultyLevel(row['difficulty']),
                    answer=row['answer'],
                    course_id=row['course_id'],
                    topic=row['topic'],
                    explanation=row['explanation'],
                    options=row['options']
                )

                question = question_service.create(
                    db,
                    obj_in=question_data,
                    created_by_id=created_by_id
                )
                created_questions.append(question)

            except Exception as e:
                logger.error(f"Error creating question from row {i}: {str(e)}")
                errors.append(f"Row {i}: Failed to create question - {str(e)}")

        return created_questions, errors

    async def create_questions_with_images(
        self,
        db: Session,
        rows: List[Dict[str, Any]],
        created_by_id: int,
        image_files: Optional[List[UploadFile]] = None,
        image_mapping: Optional[Dict[str, str]] = None
    ) -> Tuple[List[schemas.Question], List[str]]:
        """
        Create questions from parsed CSV rows with optional image uploads.

        Args:
            db: Database session
            rows: Parsed and validated CSV rows
            created_by_id: ID of the user creating the questions
            image_files: Optional list of image files
            image_mapping: Optional mapping of image filenames to question content/index

        Returns:
            Tuple of (created_questions, creation_errors)
        """
        created_questions = []
        errors = []

        # Create a mapping of image files by filename for easy lookup
        image_file_map = {}
        if image_files:
            for img_file in image_files:
                if img_file.filename:
                    image_file_map[img_file.filename] = img_file

        for i, row in enumerate(rows, start=1):
            try:
                # Create question first
                question_data = schemas.QuestionCreate(
                    content=row['content'],
                    question_type=QuestionType(row['question_type']),
                    difficulty=DifficultyLevel(row['difficulty']),
                    answer=row['answer'],
                    course_id=row['course_id'],
                    topic=row['topic'],
                    explanation=row['explanation'],
                    options=row['options']
                )

                question = question_service.create(
                    db,
                    obj_in=question_data,
                    created_by_id=created_by_id
                )

                # Check if there's an image for this question
                image_url = None
                if image_mapping and image_files:
                    # Look for image mapping by row index or content
                    image_filename = image_mapping.get(str(i)) or image_mapping.get(row['content'])
                    if image_filename and image_filename in image_file_map:
                        try:
                            # Upload image to Cloudinary
                            upload_result = await cloudinary_service.upload_image(
                                image_file_map[image_filename],
                                folder=f"questions/{question.id}",
                                transformation=cloudinary_service.get_optimized_transformations()["question_display"]
                            )
                            image_url = upload_result["url"]

                            # Update question with image URL
                            question_update = schemas.QuestionUpdate(media_url=image_url)
                            question = question_service.update(db, db_obj=question, obj_in=question_update)

                        except Exception as e:
                            logger.error(f"Error uploading image for question {i}: {str(e)}")
                            errors.append(f"Row {i}: Failed to upload image - {str(e)}")

                created_questions.append(question)

            except Exception as e:
                logger.error(f"Error creating question from row {i}: {str(e)}")
                errors.append(f"Row {i}: Failed to create question - {str(e)}")

        return created_questions, errors

    def generate_csv_template(self, question_type: QuestionType) -> str:
        """
        Generate a CSV template with sample data based on question type.

        Args:
            question_type: The type of questions for the template

        Returns:
            CSV template as string
        """
        if question_type == QuestionType.MULTIPLE_CHOICE:
            template_data = [
                {
                    'content': 'What is the capital of France?',
                    'difficulty': 'easy',
                    'answer': 'C',
                    'topic': 'Geography',
                    'explanation': 'Paris is the capital and largest city of France.',
                    'option_a': 'London',
                    'option_b': 'Berlin',
                    'option_c': 'Paris',
                    'option_d': 'Madrid'
                },
                {
                    'content': 'Which planet is closest to the Sun?',
                    'difficulty': 'medium',
                    'answer': 'A',
                    'topic': 'Astronomy',
                    'explanation': 'Mercury is the innermost planet in our solar system.',
                    'option_a': 'Mercury',
                    'option_b': 'Venus',
                    'option_c': 'Earth',
                    'option_d': 'Mars'
                }
            ]
        else:
            # For flashcard and open_ended questions
            template_data = [
                {
                    'content': 'Define photosynthesis',
                    'difficulty': 'medium',
                    'answer': 'The process by which plants convert light energy into chemical energy',
                    'topic': 'Biology',
                    'explanation': 'Photosynthesis is crucial for plant survival and oxygen production.',
                    'option_a': '',
                    'option_b': '',
                    'option_c': '',
                    'option_d': ''
                },
                {
                    'content': 'What is 2 + 2?',
                    'difficulty': 'easy',
                    'answer': '4',
                    'topic': 'Mathematics',
                    'explanation': 'Basic addition',
                    'option_a': '',
                    'option_b': '',
                    'option_c': '',
                    'option_d': ''
                }
            ]

        output = StringIO()
        writer = csv.DictWriter(output, fieldnames=self.REQUIRED_COLUMNS + self.OPTIONAL_COLUMNS)
        writer.writeheader()
        writer.writerows(template_data)

        return output.getvalue()

csv_question_service = CSVQuestionService()
