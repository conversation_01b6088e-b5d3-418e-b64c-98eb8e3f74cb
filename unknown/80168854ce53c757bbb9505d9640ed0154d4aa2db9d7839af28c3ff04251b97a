import logging
from sqlalchemy.orm import Session

from app import models, schemas
from app.core.config import settings
from app.services import user_service
from app.services.gamification_service import initialize_gamification_data
from app.models.user import UserRole, Gender

logger = logging.getLogger(__name__)


def init_db(db: Session) -> None:
    # Create default admin user if it doesn't exist
    admin_user = user_service.get_by_email(db, email="<EMAIL>")
    if not admin_user:
        user_in = schemas.UserCreate(
            email="<EMAIL>",
            password="admin",  # In production, use a secure password
            full_name="Admin User",
            role=UserRole.ADMIN,
            is_active=True,
        )
        user = user_service.create(db, obj_in=user_in)

        # Update with profile data
        profile_data = {
            "gender": Gender.MALE,
            "phone_number": "1234567890",
            "department": "Administration",
            "level": "Senior",
            "date_of_birth": "1990-01-01",
            "state_of_origin": "Admin State",
            "profile_completed": True,
            "email_verified": True  # Admin user is pre-verified
        }
        user_service.update(db, db_obj=user, obj_in=profile_data)
        logger.info(f"Created admin user: {user.email}")

    # Seed Nigerian institutions, departments, courses, and questions
    seed_nigerian_data(db)

    # Initialize gamification data
    initialize_gamification_data(db)


def seed_nigerian_data(db: Session) -> None:
    """Seed database with Nigerian educational institutions and data"""
    logger.info("Starting to seed Nigerian educational data...")

    # Check if we already have seeded data
    existing_schools = db.query(models.School).count()
    if existing_schools > 1:  # More than just the default institution
        logger.info("Nigerian data already seeded, skipping...")
        return

    # Nigerian Universities Data
    nigerian_universities = [
        {
            "name": "University of Lagos",
            "description": "Premier university in Lagos State, Nigeria",
            "location": "Lagos, Nigeria",
            "departments": [
                {
                    "name": "Computer Science",
                    "description": "Department of Computer Science and Information Technology",
                    "courses": [
                        {"name": "Introduction to Programming", "code": "CSC101", "description": "Basic programming concepts using Python"},
                        {"name": "Data Structures and Algorithms", "code": "CSC201", "description": "Fundamental data structures and algorithms"},
                        {"name": "Database Systems", "code": "CSC301", "description": "Database design and management systems"},
                        {"name": "Software Engineering", "code": "CSC401", "description": "Software development methodologies and practices"},
                    ]
                },
                {
                    "name": "Electrical Engineering",
                    "description": "Department of Electrical and Electronics Engineering",
                    "courses": [
                        {"name": "Circuit Analysis", "code": "EEE101", "description": "Basic electrical circuit analysis"},
                        {"name": "Digital Electronics", "code": "EEE201", "description": "Digital logic and electronics"},
                        {"name": "Power Systems", "code": "EEE301", "description": "Electrical power generation and distribution"},
                        {"name": "Control Systems", "code": "EEE401", "description": "Automatic control systems design"},
                    ]
                },
                {
                    "name": "Medicine",
                    "description": "College of Medicine",
                    "courses": [
                        {"name": "Human Anatomy", "code": "MED101", "description": "Structure of the human body"},
                        {"name": "Physiology", "code": "MED102", "description": "Functions of the human body"},
                        {"name": "Pharmacology", "code": "MED201", "description": "Study of drugs and their effects"},
                        {"name": "Pathology", "code": "MED301", "description": "Study of disease processes"},
                    ]
                }
            ]
        },
        {
            "name": "University of Ibadan",
            "description": "First university in Nigeria, located in Ibadan",
            "location": "Ibadan, Oyo State, Nigeria",
            "departments": [
                {
                    "name": "Mathematics",
                    "description": "Department of Mathematics",
                    "courses": [
                        {"name": "Calculus I", "code": "MAT101", "description": "Differential and integral calculus"},
                        {"name": "Linear Algebra", "code": "MAT201", "description": "Vector spaces and linear transformations"},
                        {"name": "Statistics", "code": "MAT301", "description": "Statistical methods and analysis"},
                        {"name": "Real Analysis", "code": "MAT401", "description": "Advanced mathematical analysis"},
                    ]
                },
                {
                    "name": "Physics",
                    "description": "Department of Physics",
                    "courses": [
                        {"name": "Mechanics", "code": "PHY101", "description": "Classical mechanics and motion"},
                        {"name": "Thermodynamics", "code": "PHY201", "description": "Heat and energy transfer"},
                        {"name": "Quantum Physics", "code": "PHY301", "description": "Quantum mechanics principles"},
                        {"name": "Electromagnetism", "code": "PHY401", "description": "Electric and magnetic fields"},
                    ]
                }
            ]
        },
        {
            "name": "Ahmadu Bello University",
            "description": "Leading university in Northern Nigeria",
            "location": "Zaria, Kaduna State, Nigeria",
            "departments": [
                {
                    "name": "Agricultural Engineering",
                    "description": "Department of Agricultural and Bioresources Engineering",
                    "courses": [
                        {"name": "Farm Machinery", "code": "AGE101", "description": "Agricultural machinery and equipment"},
                        {"name": "Irrigation Engineering", "code": "AGE201", "description": "Water management for agriculture"},
                        {"name": "Food Processing", "code": "AGE301", "description": "Food processing and preservation"},
                        {"name": "Soil and Water Conservation", "code": "AGE401", "description": "Sustainable farming practices"},
                    ]
                },
                {
                    "name": "Economics",
                    "description": "Department of Economics",
                    "courses": [
                        {"name": "Microeconomics", "code": "ECO101", "description": "Individual economic behavior"},
                        {"name": "Macroeconomics", "code": "ECO201", "description": "National economic systems"},
                        {"name": "Development Economics", "code": "ECO301", "description": "Economic development theories"},
                        {"name": "International Trade", "code": "ECO401", "description": "Global trade and economics"},
                    ]
                }
            ]
        }
    ]

    # Create institutions, departments, and courses
    for uni_data in nigerian_universities:
        # Create school
        school = models.School(
            name=uni_data["name"],
            description=uni_data["description"],
            location=uni_data["location"],
            is_active=True
        )
        db.add(school)
        db.commit()
        db.refresh(school)
        logger.info(f"Created school: {school.name}")

        # Create departments and courses
        for dept_data in uni_data["departments"]:
            department = models.Department(
                name=dept_data["name"],
                description=dept_data["description"],
                school_id=school.id,
                is_active=True
            )
            db.add(department)
            db.commit()
            db.refresh(department)
            logger.info(f"Created department: {department.name}")

            # Create courses
            for course_data in dept_data["courses"]:
                course = models.Course(
                    name=course_data["name"],
                    code=course_data["code"],
                    description=course_data["description"],
                    school_id=school.id,
                    department_id=department.id,
                    is_active=True
                )
                db.add(course)
                db.commit()
                db.refresh(course)
                logger.info(f"Created course: {course.name} ({course.code})")

    # Seed sample MCQ questions
    seed_sample_questions(db)

    logger.info("Completed seeding Nigerian educational data")


def seed_sample_questions(db: Session) -> None:
    """Seed sample MCQ questions for different courses"""
    logger.info("Starting to seed sample MCQ questions...")

    # Check if we already have questions
    existing_questions = db.query(models.Question).count()
    if existing_questions > 0:
        logger.info("Questions already exist, skipping question seeding...")
        return

    # Get some courses to add questions to
    courses = db.query(models.Course).limit(5).all()

    if not courses:
        logger.warning("No courses found, skipping question seeding")
        return

    # Get admin user to be the creator of questions
    admin_user = user_service.get_by_email(db, email="<EMAIL>")
    if not admin_user:
        logger.warning("Admin user not found, skipping question seeding")
        return

    # Sample questions for different subjects
    sample_questions = [
        {
            "content": "What is the time complexity of binary search algorithm?",
            "options": {
                "A": "O(n)",
                "B": "O(log n)",
                "C": "O(n²)",
                "D": "O(1)"
            },
            "answer": "B",
            "explanation": "Binary search divides the search space in half with each comparison, resulting in O(log n) time complexity.",
            "topic": "Algorithms",
            "difficulty": "MEDIUM",
            "subject_keywords": ["computer", "programming", "algorithm", "data structure"]
        },
        {
            "content": "Which of the following is NOT a fundamental data structure?",
            "options": {
                "A": "Array",
                "B": "Linked List",
                "C": "Binary Tree",
                "D": "Database"
            },
            "answer": "D",
            "explanation": "Database is a collection of data structures, not a fundamental data structure itself.",
            "topic": "Data Structures",
            "difficulty": "EASY",
            "subject_keywords": ["computer", "programming", "data structure"]
        },
        {
            "content": "What is Ohm's Law?",
            "options": {
                "A": "V = I × R",
                "B": "P = V × I",
                "C": "E = mc²",
                "D": "F = ma"
            },
            "answer": "A",
            "explanation": "Ohm's Law states that voltage (V) equals current (I) multiplied by resistance (R).",
            "topic": "Basic Electronics",
            "difficulty": "EASY",
            "subject_keywords": ["electrical", "electronics", "circuit"]
        },
        {
            "content": "Which organ is responsible for filtering blood in the human body?",
            "options": {
                "A": "Heart",
                "B": "Liver",
                "C": "Kidney",
                "D": "Lungs"
            },
            "answer": "C",
            "explanation": "The kidneys filter waste products and excess water from the blood to form urine.",
            "topic": "Human Anatomy",
            "difficulty": "EASY",
            "subject_keywords": ["medicine", "anatomy", "physiology"]
        },
        {
            "content": "What is the derivative of x² with respect to x?",
            "options": {
                "A": "x",
                "B": "2x",
                "C": "x²",
                "D": "2"
            },
            "answer": "B",
            "explanation": "Using the power rule, the derivative of x² is 2x¹ = 2x.",
            "topic": "Calculus",
            "difficulty": "EASY",
            "subject_keywords": ["mathematics", "calculus"]
        },
        {
            "content": "Which law of thermodynamics states that energy cannot be created or destroyed?",
            "options": {
                "A": "Zeroth Law",
                "B": "First Law",
                "C": "Second Law",
                "D": "Third Law"
            },
            "answer": "B",
            "explanation": "The First Law of Thermodynamics is the law of conservation of energy.",
            "topic": "Thermodynamics",
            "difficulty": "MEDIUM",
            "subject_keywords": ["physics", "thermodynamics", "energy"]
        }
    ]

    # Add questions to appropriate courses
    for question_data in sample_questions:
        # Find a suitable course based on keywords
        suitable_course = None
        for course in courses:
            course_name_lower = course.name.lower()
            course_desc_lower = course.description.lower()

            # Check if any keyword matches the course
            for keyword in question_data["subject_keywords"]:
                if keyword in course_name_lower or keyword in course_desc_lower:
                    suitable_course = course
                    break

            if suitable_course:
                break

        # If no suitable course found, use the first course
        if not suitable_course:
            suitable_course = courses[0]

        # Create the question
        question = models.Question(
            content=question_data["content"],
            question_type="MULTIPLE_CHOICE",
            difficulty=question_data["difficulty"],
            topic=question_data["topic"],
            options=question_data["options"],
            answer=question_data["answer"],
            explanation=question_data["explanation"],
            course_id=suitable_course.id,
            created_by_id=admin_user.id,
            is_active=True
        )
        db.add(question)
        db.commit()
        db.refresh(question)
        logger.info(f"Created question for {suitable_course.name}: {question.content[:50]}...")

    logger.info("Completed seeding sample MCQ questions")
