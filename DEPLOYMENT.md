# CampusPQ Backend - AWS EC2 Deployment Guide

This guide will help you deploy your CampusPQ FastAPI backend to AWS EC2 using GitHub Actions for **completely free** using AWS Free Tier.

## 🎯 Overview

- **Cost**: $0/month (AWS Free Tier)
- **Instance**: t2.micro (1 vCPU, 1GB RAM)
- **Storage**: 8GB SSD (Free Tier includes 30GB)
- **Deployment**: Automated via GitHub Actions
- **Architecture**: Docker + Docker Compose + Nginx

## 📋 Prerequisites

1. AWS Account (Free Tier eligible)
2. GitHub repository with your code
3. Domain name (optional, can use EC2 public IP)
4. Basic knowledge of SSH and command line

## 🚀 Step-by-Step Deployment

### Step 1: Set up AWS EC2 Instance

1. **Launch EC2 Instance**:
   - Go to [AWS EC2 Console](https://console.aws.amazon.com/ec2/)
   - Click "Launch Instance"
   - **Name**: `campuspq-backend`
   - **AMI**: Ubuntu Server 22.04 LTS (Free tier eligible)
   - **Instance Type**: `t2.micro` (Free tier eligible)
   - **Key Pair**: Create new key pair named `campuspq-key` and download `.pem` file
   - **Security Group**: Create new with these rules:
     - SSH (22) - Your IP only
     - HTTP (80) - Anywhere (0.0.0.0/0)
     - HTTPS (443) - Anywhere (0.0.0.0/0)
     - Custom TCP (8000) - Anywhere (0.0.0.0/0)

2. **Connect and Install Docker**:
   ```bash
   # Connect to your instance
   ssh -i campuspq-key.pem ubuntu@YOUR_EC2_PUBLIC_IP
   
   # Update system
   sudo apt update && sudo apt upgrade -y
   
   # Install Docker
   sudo apt install docker.io -y
   sudo systemctl start docker
   sudo systemctl enable docker
   sudo usermod -aG docker ubuntu
   
   # Install Docker Compose
   sudo curl -L "https://github.com/docker/compose/releases/download/v2.24.0/docker-compose-$(uname -s)-$(uname -m)" -o /usr/local/bin/docker-compose
   sudo chmod +x /usr/local/bin/docker-compose
   
   # Logout and login again
   exit
   ```

### Step 2: Configure GitHub Secrets

Go to your GitHub repository → Settings → Secrets and variables → Actions

Add these secrets:

```
# EC2 Connection
EC2_HOST=YOUR_EC2_PUBLIC_IP
EC2_USERNAME=ubuntu
EC2_PRIVATE_KEY=<content of your campuspq-key.pem file>

# Database (use your Supabase URL)
DATABASE_URL=postgresql://user:pass@host:port/database

# Security
SECRET_KEY=your-super-secret-production-key

# AI Provider
GEMINI_API_KEY=your-gemini-api-key

# Frontend
FRONTEND_URL=https://your-frontend-domain.com

# Optional: Payment providers
PAYSTACK_SECRET_KEY=your-paystack-secret
PAYSTACK_PUBLIC_KEY=your-paystack-public
FLUTTERWAVE_SECRET_KEY=your-flutterwave-secret

# Optional: Email
SMTP_USER=<EMAIL>
SMTP_PASSWORD=your-app-password
EMAILS_FROM_EMAIL=<EMAIL>
```

### Step 3: Deploy via GitHub Actions

1. **Push to main branch**:
   ```bash
   git add .
   git commit -m "Add EC2 deployment configuration"
   git push origin main
   ```

2. **Monitor deployment**:
   - Go to your GitHub repository
   - Click "Actions" tab
   - Watch the "Deploy to AWS EC2" workflow

### Step 4: Manual Deployment (Alternative)

If you prefer manual deployment:

```bash
# Set environment variables
export EC2_HOST=your-ec2-ip
export DATABASE_URL=your-database-url
export SECRET_KEY=your-secret-key
export GEMINI_API_KEY=your-gemini-key
export FRONTEND_URL=your-frontend-url

# Run deployment script
chmod +x deploy-ec2.sh
./deploy-ec2.sh
```

### Step 5: Test Your Deployment

```bash
# Test locally
chmod +x test-deployment.sh
./test-deployment.sh --url http://YOUR_EC2_IP:8000

# Or test remotely
curl http://YOUR_EC2_IP:8000/api/v1/health
```

## 🔧 Configuration Files

The deployment includes these key files:

- `backend/Dockerfile.ec2` - Optimized Docker image for EC2
- `docker-compose.ec2.yml` - Multi-service setup with Redis and Nginx
- `nginx.conf` - Reverse proxy configuration
- `.github/workflows/deploy-ec2.yml` - GitHub Actions workflow
- `.env.ec2.template` - Environment variables template

## 📊 Monitoring and Maintenance

### Check Application Status
```bash
# SSH into your EC2 instance
ssh -i campuspq-key.pem ubuntu@YOUR_EC2_IP

# Check running containers
cd ~/campuspq-deployment
docker-compose ps

# View logs
docker-compose logs -f api
docker-compose logs -f redis
docker-compose logs -f nginx
```

### Update Deployment
```bash
# On your local machine
git push origin main  # Triggers automatic deployment

# Or manually on EC2
ssh -i campuspq-key.pem ubuntu@YOUR_EC2_IP
cd ~/campuspq-deployment
git pull  # If you have git repo on EC2
docker-compose up -d --build
```

### Cleanup and Optimization
```bash
# Remove old Docker images to save space
docker image prune -a -f
docker volume prune -f

# Check disk usage
df -h
```

## 🌐 Access Your API

After successful deployment:

- **API Documentation**: `http://YOUR_EC2_IP:8000/api/v1/docs`
- **Health Check**: `http://YOUR_EC2_IP:8000/api/v1/health`
- **Alternative Health**: `http://YOUR_EC2_IP:8000/health`

## 🔒 Security Considerations

1. **Change default passwords** in production
2. **Use environment variables** for all secrets
3. **Restrict security group** to specific IPs when possible
4. **Enable HTTPS** with SSL certificates (Let's Encrypt)
5. **Regular updates** of system packages and Docker images

## 💰 Cost Optimization

- **Free Tier Limits**: 750 hours/month of t2.micro (enough for 24/7)
- **Storage**: 30GB free per month
- **Data Transfer**: 15GB free per month
- **Monitor usage** in AWS Billing Dashboard

## 🆘 Troubleshooting

### Common Issues

1. **Deployment fails**:
   ```bash
   # Check GitHub Actions logs
   # Verify all secrets are set correctly
   # Test SSH connection manually
   ```

2. **API not responding**:
   ```bash
   # Check container status
   docker-compose ps
   
   # Check logs
   docker-compose logs api
   
   # Restart services
   docker-compose restart
   ```

3. **Out of disk space**:
   ```bash
   # Clean up Docker
   docker system prune -a -f
   
   # Check disk usage
   df -h
   ```

4. **Memory issues**:
   ```bash
   # Check memory usage
   free -h
   
   # Restart containers
   docker-compose restart
   ```

## 📞 Support

If you encounter issues:

1. Check the GitHub Actions logs
2. Review EC2 instance logs
3. Test individual components
4. Verify environment variables
5. Check security group settings

## 🎉 Next Steps

After successful deployment:

1. **Set up domain name** and SSL certificate
2. **Configure monitoring** and alerts
3. **Set up automated backups**
4. **Implement CI/CD improvements**
5. **Scale as needed** (upgrade instance type when you outgrow free tier)

Your CampusPQ backend is now running on AWS EC2 for free! 🚀
